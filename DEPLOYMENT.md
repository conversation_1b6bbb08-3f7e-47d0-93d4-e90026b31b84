# 部署指南：將應用程序部署到 *************** 並連接到 192.168.200.167 的資料庫

本文檔提供了如何將 Spring Boot 應用程序部署到 *************** 伺服器並連接到 192.168.200.167 資料庫的詳細步驟。

## 已完成的配置更改

以下配置文件已經更新，以確保應用程序可以連接到 192.168.200.167 上的資料庫：

1. `application-dev.properties` - 開發環境配置
2. `application-prod.properties` - 生產環境配置

這兩個文件中的資料庫連接設定已更新為指向 192.168.200.167 伺服器。

## 部署步驟

### 1. 建置應用程序

在您的開發環境中，使用 Maven 建置應用程序 JAR 文件：

```bash
# 使用 dev 配置文件建置
mvn clean package -P dev

# 或者使用 prod 配置文件建置
# mvn clean package -P prod
```

建置完成後，JAR 文件將位於 `target` 目錄中，通常名為 `eastking-0.0.1-SNAPSHOT.jar`。

### 2. 將 JAR 文件傳輸到目標伺服器

使用 SCP 或其他文件傳輸工具將 JAR 文件傳輸到 *************** 伺服器：

```bash
scp target/eastking-0.0.1-SNAPSHOT.jar user@***************:/path/to/deployment/
```

請將 `user` 和 `/path/to/deployment/` 替換為您的實際用戶名和目標目錄。

### 3. 在目標伺服器上啟動應用程序

SSH 連接到 *************** 伺服器並啟動應用程序：

```bash
ssh user@***************
cd /path/to/deployment/
java -jar eastking-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev

# 背景執行
nohup java -jar eastking-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev > app.log 2>&1 &

# kill port
kill -9 $(lsof -ti:9700)
```

這將使用 dev 配置文件啟動應用程序。如果您想使用 prod 配置文件，請將 `dev` 替換為 `prod`。

### 4. 設定為系統服務（推薦）

為了確保應用程序在伺服器重啟後自動啟動，建議將其設定為系統服務。

對於 systemd（大多數現代 Linux 發行版）：

1. 創建服務文件：

```bash
sudo nano /etc/systemd/system/eastking.service
```

2. 添加以下內容：

```
[Unit]
Description=EastKing Application
After=network.target

[Service]
User=youruser
WorkingDirectory=/path/to/deployment
ExecStart=/usr/bin/java -jar eastking-0.0.1-SNAPSHOT.jar --spring.profiles.active=dev
SuccessExitStatus=143
TimeoutStopSec=10
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

3. 啟用並啟動服務：

```bash
sudo systemctl enable eastking.service
sudo systemctl start eastking.service
```

4. 檢查服務狀態：

```bash
sudo systemctl status eastking.service
```

## 驗證部署

部署完成後，您可以通過以下方式驗證應用程序是否正常運行：

1. 檢查應用程序日誌：
   ```bash
   sudo journalctl -u eastking.service -f
   ```

2. 測試 API 端點（假設您的應用程序提供 REST API）：
   ```bash
   curl http://***************:9700/api/health
   ```

3. 如果啟用了 Swagger UI（在 dev 配置中已啟用），可以通過瀏覽器訪問：
   ```
   http://***************:9700/swagger-ui.html
   ```

## 注意事項

1. 確保 *************** 和 192.168.200.167 之間的網絡連接正常
2. 確保資料庫用戶具有適當的權限
3. 檢查防火牆設定，確保端口 9700（應用程序）和 5432/1433（資料庫）是開放的
4. 在生產環境中，建議使用更安全的密碼管理方式，而不是在配置文件中硬編碼密碼