package com.eastking;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.eastking.repository.UserAccountRepository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;

@SpringBootApplication
@ComponentScan(basePackages = "com.eastking")
@Profile("test-transaction")
public class TransactionTest {

    public static void main(String[] args) {
        SpringApplication.run(TransactionTest.class, args);
    }

    @Component
    @Profile("test-transaction")
    public static class TransactionTestRunner implements CommandLineRunner {

        @Autowired
        private DataSource dataSource;

        @Autowired
        private UserAccountRepository userAccountRepository;

        @Override
        public void run(String... args) throws Exception {
            System.out.println("\n=== Testing Database Connection and Transaction ===");
            
            // Test connection
            try (Connection conn = dataSource.getConnection()) {
                DatabaseMetaData metaData = conn.getMetaData();
                System.out.println("✓ Database connection successful!");
                System.out.println("  Product: " + metaData.getDatabaseProductName());
                System.out.println("  Version: " + metaData.getDatabaseProductVersion());
                System.out.println("  Auto-commit: " + conn.getAutoCommit());
                System.out.println("  Transaction Isolation: " + conn.getTransactionIsolation());
            }

            // Test transaction
            System.out.println("\n=== Testing Transaction ===");
            try {
                testTransaction();
                System.out.println("✓ Transaction test passed!");
            } catch (Exception e) {
                System.err.println("✗ Transaction test failed!");
                System.err.println("  Error: " + e.getMessage());
                e.printStackTrace();
            }

            System.exit(0);
        }

        @Transactional
        public void testTransaction() {
            // Try to read some data
            long count = userAccountRepository.count();
            System.out.println("  User count: " + count);
            
            // If you want to test write operation, uncomment below:
            // UserAccount testUser = new UserAccount();
            // testUser.setEmployeeId("TEST001");
            // testUser.setUserName("Test User");
            // userAccountRepository.save(testUser);
            // System.out.println("  Test user saved successfully");
        }
    }
} 