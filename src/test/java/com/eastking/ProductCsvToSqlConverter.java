package com.eastking;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class ProductCsvToSqlConverter {

    private static final Set<String> IS_MAIN_KEYWORDS = new HashSet<>(Arrays.asList(
            "雲霄飛車三代-", "子彈列車三代-", "斜行高手三代-", "斜行高手3.3代-", "雲霄飛車【折疊機款】",
            "雲霄飛車二代【電動折疊款】", "雲霄飛車二代牛皮紙色-", "雲霄飛車三代牛皮紙色-", "子彈列車牛皮紙色-",
            "現金終結者牛皮紙色-", "笑傲江湖1.1代牛皮紙色-", "子彈列車三代牛皮紙色-", "2021-MOMO電購款-",
            "日本麻將機(28mm牌)-", "磁浮列車牛皮紙色-", "輸不起牛皮紙色-", "AMOS3.2代牛皮紙色-",
            "斜行高手3代牛皮紙色-", "磁浮列車1.1代牛皮紙色-", "樂雀台LQ-300-T6-", "磁浮列車二代奶茶色-",
            "2018年宣和【好市多/3K】-", "雲霄飛車四代奶茶色-", "四旋翼二代奶茶色-", "AMOS3.2代奶茶色-",
            "麻雀旅人二代奶茶色-", "斜行高手3.3代奶茶色-", "5Y-25/AMOS二代-", "5Y-28/智多星(恭喜D300)-",
            "5Y-32/AMOS二代-", "5Y-37/小神龍二代-", "5Y-42/ 3A五代-", "5Y-43/斜行高手-",
            "ST002電動實木麻將桌(柚木)/", "ST003電動實木麻將桌(核桃木)/", "小玲瓏小桌面-",
            "5y-53/斜行高手-", "5y-54/智多星四代-", "5y-55/鉑晶三代-", "紅盛實木餐桌-",
            "紅盛實木-套裝", "5Y-75/B900四腳套裝木紋", "5Y-78/雀友C200S-", "5Y-79/雀友C200S-",
            "5Y-80/雀友D500", "皮革框(素面/鱷魚皮)", "5Y-82/電動桌TT系列", "5Y-84/雀友D500",
            "5Y-85/雀友D900", "5Y-88/小神龍二代-", "5Y-90/B700餐桌型", "SY-001餐桌型",
            "5Y-93/小神龍三代", "5Y-94/小神龍三代", "5Y-95/斜行高手二代", "5Y-95-1斜行高手二代",
            "5Y-96/斜行高手二代", "5Y-96-1斜行高手二代"
    ));

    public static void main(String[] args) {
        String csvFilePath = "/Users/<USER>/Desktop/eastking10_dbo_comProduct.csv";
        String sqlFilePath = "/Users/<USER>/Desktop/erpProductData.sql";

        System.out.println("Starting CSV to SQL conversion...");

        try (BufferedReader reader = Files.newBufferedReader(Paths.get(csvFilePath));
             BufferedWriter writer = Files.newBufferedWriter(Paths.get(sqlFilePath))) {

            String line = reader.readLine(); // Skip header row

            while ((line = reader.readLine()) != null) {
                // Split by comma, but handle cases where a comma might be inside quotes (basic handling)
                String[] columns = line.split(",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)", -1);
                String sql = convertCsvRowToInsertStatement(columns);
                if (sql != null) {
                    writer.write(sql);
                    writer.newLine();
                }
            }

            System.out.println("Conversion completed successfully. SQL file created at: " + sqlFilePath);

        } catch (IOException e) {
            System.err.println("An error occurred during file processing.");
            e.printStackTrace();
        }
    }

    private static String convertCsvRowToInsertStatement(String[] columns) {
        if (columns.length < 9) {
            System.err.println("Skipping malformed row: " + Arrays.toString(columns));
            return null;
        }

        // Assign columns to variables for clarity
        String prodId = columns[0];
        // String subId = columns[1]; // Not used
        String classId = columns[2];
        String barCodeId = columns[3];
        String prodName = columns[4];
        String suggestPrice = columns[5];
        // String salesPriceA = columns[6]; // Not used directly, suggestPrice is used instead
        String inPackUnit = columns[7];
        String invoProdName = columns[8];

        // Apply rules
        String productBarcode = formatValue(barCodeId);
        String productName = formatValue(prodName);
        String productShortName = formatValue(invoProdName);
        
        String priceValue = formatValue(suggestPrice, true);

        String saleUnit = formatUnit(inPackUnit);
        String erpProductCategory = formatValue(classId);
        String erpProductCode = formatValue(prodId);

        int erpCompanyDivision = 9;
        int isDispatchProduct = "D5Y".equalsIgnoreCase(classId) ? 1 : 0;
        int isActive = 1;

        int isMain = 0;
        if ("D5Y".equalsIgnoreCase(classId)) {
            for (String keyword : IS_MAIN_KEYWORDS) {
                if (prodName.contains(keyword)) {
                    isMain = 1;
                    break;
                }
            }
        }
        
        LocalDate today = LocalDate.now();
        String createTime = formatValue(today.atStartOfDay().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        String startDate = createTime;
        String endDate = "'2999-12-31T00:00:00'";
        String queyouStartDate = createTime;
        String queyouEndDate = "'2999-12-31T00:00:00'";
        String createBy = formatValue("6301a875-31ce-43ab-b7ea-be11326f1b0a"); // System user placeholder

        int warrantyMonths = 12;
        int isDeleted = 0;
        String currency = formatValue("TWD");
        
        String productSettingId = formatValue(UUID.randomUUID().toString());

        return String.format(
                "INSERT INTO sm_product_setting (product_setting_id, product_barcode, product_name, product_short_name, sale_unit, " +
                        "currency, warranty_months, is_dispatch_product, is_active, list_price, " +
                        "sale_price, cost_price, erp_company_division, erp_product_code, erp_product_category, " +
                        "erp_eastking_price, erp_queyou_price, queyou_list_price, queyou_sale_price, queyou_cost_price, " +
                        "is_main, create_by, create_time, update_by, update_time, " +
                        "is_deleted, sale_effective_start_date, sale_effective_end_date, queyou_sale_effective_start_date, queyou_sale_effective_end_date) " +
                        "VALUES ( %s, %s, %s, %s, %s, " +
                        "%s, %d, %d, %d, %s, " +
                        "%s, %s, %d, %s, %s, " +
                        "%s, %s, %s, %s, %s, " +
                        "%d, %s, %s, %s, %s, " +
                        "%d, %s, %s, %s, %s);",
                productSettingId, productBarcode, productName, productShortName, saleUnit,
                currency, warrantyMonths, isDispatchProduct, isActive, priceValue,
                priceValue, priceValue, erpCompanyDivision, erpProductCode, erpProductCategory,
                priceValue, priceValue, priceValue, priceValue, priceValue,
                isMain, createBy, createTime, createBy, createTime,
                isDeleted, startDate, endDate,queyouStartDate, queyouEndDate
        );
    }

    private static String formatValue(String value) {
        return formatValue(value, false);
    }

    private static String formatUnit(String value) {
        if (value == null || value.equals("個")) {
            return "'PIECE1'";
        } else if (value.equals("張")) {
            return "'PIECE2'";
        } else if (value.equals("片")) {
            return "'PIECE3'";
        } else if (value.equals("件")) {
            return "'PIECE4'";
        } else if (value.equals("組")) {
            return "'SET1'";
        } else if (value.equals("套")) {
            return "'SET2'";
        } else if (value.equals("副")) {
            return "'SET3'";
        } else if (value.equals("台")) {
            return "'UNIT1'";
        } else if (value.equals("臺")) {
            return "'UNIT2'";
        } else if (value.equals("盒")) {
            return "'BOX'";
        } else if (value.equals("雙")) {
            return "'PAIR'";
        } else if (value.equals("打")) {
            return "'DOZEN'";
        } else {
            return "'PIECE1'";
        }
    }

    private static String formatValue(String value, boolean isNumeric) {
        if (value == null || value.trim().isEmpty() || value.equals("\"\"")) {
            return "NULL";
        }
        
        // Remove quotes if they exist
        String cleanedValue = value.trim();
        if (cleanedValue.startsWith("\"") && cleanedValue.endsWith("\"")) {
            cleanedValue = cleanedValue.substring(1, cleanedValue.length() - 1);
        }

        if (cleanedValue.trim().isEmpty()){
            return "NULL";
        }

        if (isNumeric) {
            return cleanedValue;
        } else {
            // Escape single quotes for SQL
            return "'" + cleanedValue.replace("'", "''") + "'";
        }
    }
} 