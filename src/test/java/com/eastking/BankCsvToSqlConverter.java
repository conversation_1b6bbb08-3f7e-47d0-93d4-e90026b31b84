package com.eastking;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;

//用來把 csv 的信用卡資料轉成 sql 的程式(只用一次)
public class BankCsvToSqlConverter {

    public static void main(String[] args) {
        String csvFilePath = "/Volumes/Disk2/AppTest/AI_Test/EastKing/bin-list-data.csv";
        String sqlFilePath = "/Volumes/Disk2/AppTest/AI_Test/EastKing/sql/992_insert_bin_list_data.sql";
        String tableName = "sm_bin_list_data";
        
        // CSV header to DB column mapping
        String columns = "bin, brand, card_type, category, issuer, issuer_phone, issuer_url, iso_code2, iso_code3, country_name";

        try (BufferedReader reader = Files.newBufferedReader(Paths.get(csvFilePath), StandardCharsets.UTF_8);
             BufferedWriter writer = Files.newBufferedWriter(Paths.get(sqlFilePath), StandardCharsets.UTF_8, 
                                                              StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {

            writer.write("-- Auto-generated SQL insert statements from " + csvFilePath + "\n\n");
            
            String line;
            reader.readLine(); // Skip header row

            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue;
                }

                List<String> values = parseCsvLine(line);

                if (values.size() != 10) {
                    System.err.println("Skipping malformed line (field count != 10): " + line);
                    continue;
                }

                StringBuilder valueString = new StringBuilder();
                for (String value : values) {
                    // Escape single quotes and wrap in single quotes for SQL
                    valueString.append("'").append(value.replace("'", "''")).append("', ");
                }
                
                // Remove the last comma and space
                if (valueString.length() > 2) {
                    valueString.setLength(valueString.length() - 2);
                }

                String insertStatement = String.format("INSERT INTO %s (%s) VALUES (%s);\n",
                        tableName, columns, valueString.toString());
                
                writer.write(insertStatement);
            }
            System.out.println("Successfully generated SQL file: " + sqlFilePath);

        } catch (IOException e) {
            System.err.println("An error occurred during file processing: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static List<String> parseCsvLine(String line) {
        List<String> values = new ArrayList<>();
        if (line == null || line.isEmpty()) {
            return values;
        }

        StringBuilder currentValue = new StringBuilder();
        boolean inQuotes = false;

        for (char c : line.toCharArray()) {
            if (c == '\"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                values.add(currentValue.toString());
                currentValue.setLength(0); // Reset for next value
            } else {
                currentValue.append(c);
            }
        }
        values.add(currentValue.toString()); // Add the last value

        return values;
    }
} 