package com.eastking;

import java.io.BufferedWriter;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import java.util.UUID;

public class GenerateWarehouseInventorySql {

    private static class Product {
        String barcode;
        String name;

        Product(String barcode, String name) {
            this.barcode = barcode;
            this.name = name;
        }
    }

    public static void main(String[] args) {
        String sqlFilePath = "/Users/<USER>/Desktop/erpWarehouseData.sql";
        Properties prop = new Properties();
        String dbUrl = "";
        String dbUser = "";
        String dbPassword = "";

        // 1. Load database configuration from application.properties
        try (InputStream input = new FileInputStream("/Volumes/Disk2/AppTest/AI_Test/EastKing/source/eastking/src/main/resources/application.properties")) {
            prop.load(input);
            dbUrl = prop.getProperty("spring.datasource.url");
            dbUser = prop.getProperty("spring.datasource.username");
            dbPassword = prop.getProperty("spring.datasource.password");
            System.out.println("Successfully loaded database configuration.");
        } catch (IOException ex) {
            System.err.println("Error reading application.properties file.");
            ex.printStackTrace();
            return;
        }

        // 2. Fetch products from the database
        List<Product> products = new ArrayList<>();
        String selectSql = "SELECT product_barcode, product_name FROM sm_product_setting WHERE LENGTH(product_barcode) > 10 AND is_deleted = 0";
        try (Connection conn = DriverManager.getConnection(dbUrl, dbUser, dbPassword);
             PreparedStatement pstmt = conn.prepareStatement(selectSql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                products.add(new Product(rs.getString("product_barcode"), rs.getString("product_name")));
            }
            System.out.println("Successfully fetched " + products.size() + " products from the database.");
        } catch (SQLException e) {
            System.err.println("Database error occurred while fetching products.");
            e.printStackTrace();
            return;
        }

        if (products.isEmpty()) {
            System.out.println("No products found with barcode length > 10. Exiting.");
            return;
        }

        // 3. Generate SQL insert statements
        try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(sqlFilePath))) {
            writer.write("-- Generated SQL for sm_warehouse_inventory table --\n\n");

            String createBy = "6301a875-31ce-43ab-b7ea-be11326f1b0a";
            String now = OffsetDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);

            // Group 1: Warehouses that get all products
            String[] allProductWarehouses = {"9fe3702b-5149-42d8-b2bb-037069ae30bb", "3ef6fb04-9ef1-4eae-bd56-9b56a704ceb0"};
            for (String warehouseId : allProductWarehouses) {
                for (Product p : products) {
                    writer.write(generateInsertSql(warehouseId, p, 100000, now, createBy));
                    writer.newLine();
                }
            }
            System.out.println("Generated SQL for the first group of warehouses.");

            // Group 2: Warehouses that get a random third of products
            String[] randomWarehouses = {
                "d0d8654a-f88a-4639-bfd4-62757b0303c7", 
                "67da2bcf-629a-403f-9dbc-3cd98f122c72", 
                "71f93005-4558-4061-be60-05ced69efdbb"
            };
            Collections.shuffle(products);
            int partitionSize = products.size() / 3;
            
            for (int i = 0; i < randomWarehouses.length; i++) {
                int start = i * partitionSize;
                // For the last warehouse, take all remaining products to handle rounding issues
                int end = (i == randomWarehouses.length - 1) ? products.size() : start + partitionSize;
                List<Product> partition = products.subList(start, end);
                
                for (Product p : partition) {
                    writer.write(generateInsertSql(randomWarehouses[i], p, 500000, now, createBy));
                    writer.newLine();
                }
            }
            System.out.println("Generated SQL for the second group of warehouses.");

            System.out.println("SQL file generation completed successfully. File located at: " + sqlFilePath);

        } catch (IOException e) {
            System.err.println("Error writing to SQL file.");
            e.printStackTrace();
        }
    }

    private static String generateInsertSql(String warehouseId, Product product, int quantity, String timestamp, String userId) {
        String template = "INSERT INTO sm_warehouse_inventory (warehouse_inventory_id, warehouse_id, product_barcode, product_name, quantity_on_hand, last_stock_update_time, create_by, create_time, update_by, update_time, is_deleted) VALUES ('%s', '%s', '%s', '%s', %d, '%s', '%s', '%s', '%s', '%s', 0);";
        return String.format(template,
                UUID.randomUUID().toString(),
                warehouseId,
                product.barcode,
                product.name.replace("'", "''"), // Escape single quotes
                quantity,
                timestamp,
                userId,
                timestamp,
                userId,
                timestamp
        );
    }
} 