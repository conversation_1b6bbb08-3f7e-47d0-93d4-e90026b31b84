package com.eastking;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        // Test PostgreSQL connection
        testPostgreSQLConnection();
        
        // Test SQL Server connection
        testSQLServerConnection();
    }
    
    private static void testPostgreSQLConnection() {
        System.out.println("=== Testing PostgreSQL Connection ===");
        String url = "******************************************";
        String username = "postgres";
        String password = "!!!!!!!!";
        
        try {
            Class.forName("org.postgresql.Driver");
            System.out.println("PostgreSQL Driver loaded successfully");
            
            try (Connection conn = DriverManager.getConnection(url, username, password)) {
                if (conn != null) {
                    System.out.println("✓ PostgreSQL connection successful!");
                    System.out.println("  Database: " + conn.getCatalog());
                    System.out.println("  URL: " + conn.getMetaData().getURL());
                }
            }
        } catch (ClassNotFoundException e) {
            System.err.println("✗ PostgreSQL Driver not found: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("✗ PostgreSQL connection failed!");
            System.err.println("  Error Code: " + e.getErrorCode());
            System.err.println("  SQL State: " + e.getSQLState());
            System.err.println("  Message: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println();
    }
    
    private static void testSQLServerConnection() {
        System.out.println("=== Testing SQL Server Connection ===");
        String url = "******************************************************************************************************";
        String username = "sa";
        String password = "!qaZ24759432";
        
        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            System.out.println("SQL Server Driver loaded successfully");
            
            try (Connection conn = DriverManager.getConnection(url, username, password)) {
                if (conn != null) {
                    System.out.println("✓ SQL Server connection successful!");
                    System.out.println("  Database: " + conn.getCatalog());
                    System.out.println("  URL: " + conn.getMetaData().getURL());
                }
            }
        } catch (ClassNotFoundException e) {
            System.err.println("✗ SQL Server Driver not found: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("✗ SQL Server connection failed!");
            System.err.println("  Error Code: " + e.getErrorCode());
            System.err.println("  SQL State: " + e.getSQLState());
            System.err.println("  Message: " + e.getMessage());
        }
    }
} 