package com.eastking;

import java.io.BufferedWriter;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import java.util.UUID;

public class GenerateStoreInventorySql {

    private static class Product {
        String barcode;
        String name;

        Product(String barcode, String name) {
            this.barcode = barcode;
            this.name = name;
        }
    }

    public static void main(String[] args) {
        String sqlFilePath = "/Users/<USER>/Desktop/erpStoreData.sql";
        Properties prop = new Properties();
        String dbUrl = "";
        String dbUser = "";
        String dbPassword = "";

        // 1. Load database configuration
        try (InputStream input = new FileInputStream("/Volumes/Disk2/AppTest/AI_Test/EastKing/source/eastking/src/main/resources/application.properties")) {
            prop.load(input);
            dbUrl = prop.getProperty("spring.datasource.url");
            dbUser = prop.getProperty("spring.datasource.username");
            dbPassword = prop.getProperty("spring.datasource.password");
            System.out.println("Successfully loaded database configuration.");
        } catch (IOException ex) {
            System.err.println("Error reading application.properties file.");
            ex.printStackTrace();
            return;
        }

        // 2. Fetch dispatch products from the database
        List<Product> products = new ArrayList<>();
        String selectSql = "SELECT product_barcode, product_name FROM sm_product_setting WHERE LENGTH(product_barcode) > 10 AND is_dispatch_product = 0 AND is_deleted = 0";
        try (Connection conn = DriverManager.getConnection(dbUrl, dbUser, dbPassword);
             PreparedStatement pstmt = conn.prepareStatement(selectSql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                products.add(new Product(rs.getString("product_barcode"), rs.getString("product_name")));
            }
            System.out.println("Successfully fetched " + products.size() + " dispatch products from the database.");
        } catch (SQLException e) {
            System.err.println("Database error occurred while fetching products.");
            e.printStackTrace();
            return;
        }

        if (products.isEmpty()) {
            System.out.println("No dispatch products found with barcode length > 10. Exiting.");
            return;
        }

        // 3. Generate SQL insert statements for store inventory
        try (BufferedWriter writer = Files.newBufferedWriter(Paths.get(sqlFilePath))) {
            writer.write("-- Generated SQL for sm_store_inventory table --\n\n");

            String createBy = "6301a875-31ce-43ab-b7ea-be11326f1b0a";
            String now = OffsetDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            
            String[] storeIds = {
                "aeb77256-a408-461e-9269-0a08745b8002",
                "ead01c03-45be-4492-9862-7ffee71f070b",
                "ead01c03-45be-4492-9862-7ffee71f070a",
                "ead01c03-45be-4492-9862-7ffee71f070c"
            };

            Collections.shuffle(products);
            int partitionSize = products.size() / storeIds.length;
            
            for (int i = 0; i < storeIds.length; i++) {
                int start = i * partitionSize;
                // For the last store, take all remaining products to handle rounding issues
                int end = (i == storeIds.length - 1) ? products.size() : start + partitionSize;
                List<Product> partition = products.subList(start, end);
                
                for (Product p : partition) {
                    writer.write(generateInsertSql(storeIds[i], p, 10000, now, createBy));
                    writer.newLine();
                }
                 System.out.println("Generated SQL for store ID: " + storeIds[i]);
            }

            System.out.println("SQL file generation completed successfully. File located at: " + sqlFilePath);

        } catch (IOException e) {
            System.err.println("Error writing to SQL file.");
            e.printStackTrace();
        }
    }

    private static String generateInsertSql(String storeId, Product product, int quantity, String timestamp, String userId) {
        String template = "INSERT INTO sm_store_inventory (store_inventory_id, store_id, product_barcode, product_name, quantity_on_hand, last_stock_update_time, create_by, create_time, update_by, update_time, is_deleted) VALUES ('%s', '%s', '%s', '%s', %d, '%s', '%s', '%s', '%s', '%s', 0);";
        return String.format(template,
                UUID.randomUUID().toString(),
                storeId,
                product.barcode,
                product.name.replace("'", "''"), // Escape single quotes for SQL
                quantity,
                timestamp,
                userId,
                timestamp,
                userId,
                timestamp
        );
    }
} 