# Server Configuration
server.port=9700

# Spring Application Name
spring.application.name=eastking

# ===================================================================
# = LOCAL (local) PROFILE SETTINGS
# ===================================================================

# Primary PostgreSQL Datasource for local
spring.datasource.jdbc-url=******************************************
spring.datasource.username=postgres
spring.datasource.password=!!!!!!!!

# Secondary ERP (MSSQL) Datasource for local
erp.datasource.jdbc-url=******************************************************************************************************
erp.datasource.username=sa
erp.datasource.password=!qaZ24759432

# ===================================================================
# = HIKARI CONNECTION POOL SETTINGS
# ===================================================================
# --- Settings for Primary Datasource ---
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# --- Settings for ERP Datasource ---
erp.datasource.hikari.maximum-pool-size=10
erp.datasource.hikari.minimum-idle=2
erp.datasource.hikari.connection-timeout=30000
erp.datasource.hikari.idle-timeout=600000
erp.datasource.hikari.max-lifetime=1800000

# ===================================================================
# = JPA & HIBERNATE SETTINGS
# ===================================================================
# --- For Primary Datasource ---
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.show_sql=true
spring.jpa.properties.hibernate.jdbc.time_zone=Asia/Taipei

# --- For ERP Datasource (Handled in Java Config) ---
erp.jpa.properties.hibernate.dialect=org.hibernate.dialect.SQLServerDialect
erp.jpa.hibernate.ddl-auto=validate
erp.jpa.open-in-view=false
erp.jpa.properties.hibernate.show_sql=true
erp.jpa.properties.hibernate.jdbc.time_zone=Asia/Taipei

# ===================================================================
# = OTHER APPLICATION SETTINGS
# ===================================================================
# Logging Configuration
# Default logging level for the application
logging.level.root=INFO
# Logging level for Spring Framework
logging.level.org.springframework=INFO
# Logging level for Hibernate
logging.level.org.hibernate=INFO
# Logging level for our application's base package (assuming com.eastking)
logging.level.com.eastking=DEBUG
logging.level.org.springframework.security=DEBUG

# Specify the log file name (optional, Spring Boot logs to console by default)
# logging.file.name=logs/eastking.log

# Specify the log file path (optional, if logging.file.name is used)
# logging.file.path=./

# Pattern for log messages
# logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
# logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# Security Configurations
# JWT token secret key (SHOULD BE a long, random, and strong string in a real application, keep it outside version control)
# For local localelopment, a simpler key can be used, but ensure it's strong for production.
app.security.jwt.secret=DefaultSecretKeyForEastKingProjectWhichShouldBeReplacedInProductionAndBeAtLeast256BitsLong

# JWT token expiration time in milliseconds (default: 30 minutes)
# backend_rules.md specifies default 30 minutes. 30 * 60 * 1000 = 1800000
app.security.jwt.expiration-ms=3600000

# JWT issuer
app.security.jwt.issuer=eastking-app

# Swagger / SpringDoc OpenAPI UI settings
# Enable/disable Swagger UI
springdoc.swagger-ui.enabled=true
# Path for Swagger UI (default is /swagger-ui.html)
springdoc.swagger-ui.path=/swagger-ui.html
# Path for OpenAPI spec (default is /v3/api-docs)
springdoc.api-docs.path=/v3/api-docs

# Configure if login is required for Swagger UI (true/false)
# This is a custom requirement from backend_rules.md. SpringDoc doesn't directly support this via properties.
# This would typically be handled by Spring Security rules for the swagger paths.
# For now, we allow swagger paths in SecurityConfig. To protect it, remove from permitAll and add specific role/auth.
app.swagger.login-required=false # Placeholder for the rule, actual enforcement in SecurityConfig
app.swagger.display-docs=true    # Placeholder for the rule

# Basic OpenAPI Info
springdoc.api-docs.info.title=æ±æ¹ä¸æ å¾å°ç®¡ç API
springdoc.api-docs.info.version=v1.0.0
springdoc.api-docs.info.description=æ±æ¹ä¸æ é»åéº»å°æ¡ãé±éååè¨å®åæ´¾å·¥å®æ´æµç¨ç®¡çå¾å° API æä»¶ã

# Image Upload Settings
app.image.upload-max-size-kb=1024