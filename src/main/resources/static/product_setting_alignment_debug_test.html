<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>職權折扣設定置底對齊問題修正測試 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        body { padding: 20px; }
        .demo-section { margin-bottom: 30px; }
        .discount-row {
            padding-bottom: 15px;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .discount-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .comparison-bg {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
        }
        /* 用於視覺化對齊的輔助線 */
        .debug-border {
            border: 1px dashed #dc3545 !important;
        }
        .debug-bg {
            background-color: rgba(220, 53, 69, 0.1) !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>職權折扣設定置底對齊問題修正測試</h2>
        <p class="text-muted">分析並解決移除按鈕的置底對齊問題</p>

        <div class="demo-section">
            <h4>問題分析</h4>
            <div class="alert alert-warning">
                <ul class="mb-0">
                    <li><strong>問題：</strong>按鈕位置卡在標籤（label）和輸入欄位之間的中間位置</li>
                    <li><strong>原因：</strong><code>align-items-end</code> 是針對 flex 容器內的所有項目，不是針對單一項目</li>
                    <li><strong>解決方案：</strong>使用 <code>align-self-end</code> 讓特定的 flex 項目自己對齊到底部</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h4>方法 1：使用 align-items-end（問題版本）</h4>
            <div class="comparison-bg">
                <div class="row g-3 align-items-center mb-3">
                    <div class="col-md-5">
                        <label class="form-label">角色</label>
                        <select class="form-select">
                            <option selected>系統管理員</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">折扣金額 (NT $)</label>
                        <div class="input-group">
                            <span class="input-group-text">NT $</span>
                            <input type="number" class="form-control" value="500">
                        </div>
                    </div>
                    <div class="col-md-2 d-flex align-items-end debug-border debug-bg">
                        <button type="button" class="btn btn-danger btn-sm" title="移除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <p class="text-danger small mb-0">
                    <i class="bi bi-exclamation-triangle"></i> 
                    問題：<code>align-items-end</code> 對整個 flex 容器生效，按鈕仍然卡在中間
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>方法 2：使用 align-self-end（修正版本）</h4>
            <div class="comparison-bg">
                <div class="row g-3 align-items-center mb-3">
                    <div class="col-md-5">
                        <label class="form-label">角色</label>
                        <select class="form-select">
                            <option selected>系統管理員</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">折扣金額 (NT $)</label>
                        <div class="input-group">
                            <span class="input-group-text">NT $</span>
                            <input type="number" class="form-control" value="500">
                        </div>
                    </div>
                    <div class="col-md-2 align-self-end debug-border debug-bg">
                        <button type="button" class="btn btn-danger btn-sm" title="移除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <p class="text-success small mb-0">
                    <i class="bi bi-check-circle"></i> 
                    解決：<code>align-self-end</code> 讓按鈕容器自己對齊到底部
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>方法 3：使用 margin-top auto（替代方案）</h4>
            <div class="comparison-bg">
                <div class="row g-3 align-items-center mb-3">
                    <div class="col-md-5">
                        <label class="form-label">角色</label>
                        <select class="form-select">
                            <option selected>系統管理員</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">折扣金額 (NT $)</label>
                        <div class="input-group">
                            <span class="input-group-text">NT $</span>
                            <input type="number" class="form-control" value="500">
                        </div>
                    </div>
                    <div class="col-md-2 d-flex flex-column debug-border debug-bg">
                        <button type="button" class="btn btn-danger btn-sm mt-auto" title="移除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <p class="text-info small mb-0">
                    <i class="bi bi-info-circle"></i> 
                    替代方案：使用 <code>d-flex flex-column</code> + <code>mt-auto</code>
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>完整的職權折扣設定區塊（最終版本）</h4>
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    職權折扣設定
                    <button type="button" class="btn btn-sm btn-outline-success">
                        <i class="bi bi-plus-circle"></i> 新增角色折扣
                    </button>
                </div>
                <div class="card-body">
                    <!-- 第一個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option selected>系統管理員</option>
                                <option>部門主管</option>
                                <option>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="500" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 align-self-end">
                            <button type="button" class="btn btn-danger btn-sm" title="移除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第二個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option>系統管理員</option>
                                <option selected>部門主管</option>
                                <option>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="300" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 align-self-end">
                            <button type="button" class="btn btn-danger btn-sm" title="移除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第三個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option>系統管理員</option>
                                <option>部門主管</option>
                                <option selected>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="100" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 align-self-end">
                            <button type="button" class="btn btn-danger btn-sm" title="移除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>技術解釋</h4>
            <div class="card">
                <div class="card-body">
                    <h6>問題根源：</h6>
                    <ul>
                        <li><code>align-items-end</code> 是針對 flex 容器內的<strong>所有</strong>項目進行對齊</li>
                        <li>在 Bootstrap 的 <code>.row</code> 中，每個 <code>.col-*</code> 都是 flex 項目</li>
                        <li>當整個 row 使用 <code>align-items-center</code> 時，所有欄位都會垂直置中</li>
                    </ul>
                    
                    <h6>解決方案：</h6>
                    <ul>
                        <li><code>align-self-end</code> 讓<strong>特定的</strong> flex 項目覆蓋父容器的對齊設定</li>
                        <li>只有按鈕所在的欄位會對齊到底部，其他欄位保持置中</li>
                        <li>這樣按鈕就會與輸入欄位的底部完美對齊</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>程式碼修正對比</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header text-bg-danger">修正前（問題版本）</div>
                        <div class="card-body">
                            <pre><code>&lt;div class="col-md-2 d-flex align-items-end"&gt;
    &lt;button type="button" class="btn btn-danger btn-sm"&gt;
        &lt;i class="bi bi-trash"&gt;&lt;/i&gt;
    &lt;/button&gt;
&lt;/div&gt;</code></pre>
                            <p class="text-danger small mb-0">❌ 按鈕仍然卡在中間位置</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header text-bg-success">修正後（解決版本）</div>
                        <div class="card-body">
                            <pre><code>&lt;div class="col-md-2 align-self-end"&gt;
    &lt;button type="button" class="btn btn-danger btn-sm"&gt;
        &lt;i class="bi bi-trash"&gt;&lt;/i&gt;
    &lt;/button&gt;
&lt;/div&gt;</code></pre>
                            <p class="text-success small mb-0">✅ 按鈕正確對齊到底部</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-success">
            <h5>✅ 修正完成</h5>
            <p class="mb-0">職權折扣設定區塊的移除按鈕現在：</p>
            <ul class="mb-0">
                <li>使用 <code>align-self-end</code> 實現真正的置底對齊</li>
                <li>按鈕與右側折扣金額輸入框的底部完全對齊</li>
                <li>不再卡在標籤和輸入欄位之間的中間位置</li>
                <li>視覺上看起來協調且專業</li>
            </ul>
        </div>

        <div class="alert alert-info">
            <h5>📋 修正檔案</h5>
            <p>本次修正的檔案：</p>
            <ul class="mb-0">
                <li><strong>product_setting_form.js</strong>：將 <code>d-flex align-items-end</code> 改為 <code>align-self-end</code></li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
