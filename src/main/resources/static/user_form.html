<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用者表單 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a href="user_management.html">使用者管理</a></li>
                            <li class="breadcrumb-item active" aria-current="page" id="user-form-mode-breadcrumb">新增使用者</li>
                        </ol>
                    </nav>

                    <h3 id="user-form-title" class="mb-4">新增使用者</h3>
                    <div id="user-form-error-message" class="alert alert-danger d-none" role="alert"></div>

                    <form id="user-account-form">
                        <input type="hidden" id="userAccountId">
                        <div class="card">
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="employeeId" class="form-label">員工編號 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="employeeId" required maxlength="50">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="userName" class="form-label">使用者姓名 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="userName" required maxlength="100">
                                    </div>
                                    <div class="col-md-6" id="password-group">
                                        <label for="password" class="form-label">密碼 <span id="password-required-indicator" class="text-danger">*</span></label>
                                        <input type="password" class="form-control" id="password" autocomplete="new-password">
                                        <div class="form-text" id="password-help-text">新增使用者時為必填。編輯時留空則不變更密碼。</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">帳號狀態 <span class="text-danger">*</span></label>
                                        <div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="isActive" id="isActiveYes" value="true" checked>
                                                <label class="form-check-label" for="isActiveYes">啟用</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="isActive" id="isActiveNo" value="false">
                                                <label class="form-check-label" for="isActiveNo">停用</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="erpCompanyDivision" class="form-label">歸屬公司別 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="erpCompanyDivision" required>
                                            <!-- Options to be populated by JS -->
                                            <option value="">請選擇公司別...</option>
                                        </select>
                                        <div class="invalid-feedback">請選擇歸屬公司別。</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="accountType" class="form-label">帳號類型 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="accountType" required>
                                            <!-- Options to be populated by JS -->
                                            <option value="">載入中...</option>
                                        </select>
                                        <div class="invalid-feedback">請選擇帳號類型。</div>
                                    </div>
                                </div>

                                <hr class="my-4">
                                <h4>角色指派</h4>
                                <div id="roles-checkbox-group" class="mb-3">
                                    <!-- Role checkboxes will be populated by JS -->
                                    <p class="text-muted">正在載入角色列表...</p>
                                </div>

                                <div class="mt-4 d-flex justify-content-end">
                                    <a href="user_management.html" class="btn btn-outline-secondary me-2">返回列表</a>
                                    <button type="submit" class="btn btn-primary" id="saveUserBtn">儲存使用者</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script> 
    <script src="js/user_form.js"></script> 
</body>
</html> 