document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.getElementById('material-orders-table-body');
    const searchInput = document.getElementById('keyword-search');
    const paginationControls = document.getElementById('pagination-controls');
    let searchTimeout;

    async function searchMaterialOrders(page = 1) {
        const keyword = searchInput.value.trim();
        const params = new URLSearchParams({
            page: page - 1,
            size: 15,
            sort: 'createTime,desc'
        });
        if (keyword) {
            params.append('orderNumber', keyword);
        }

        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">查詢中...</td></tr>';
        
        try {
            const response = await window.fetchAuthenticated(`/api/v1/material-orders/mgr?${params.toString()}`);
            if (!response.ok) throw new Error("查詢失敗");
            const result = await response.json();
            
            renderTable(result.data.list || []); 
            renderPagination(result.data.page);
        } catch (error) {
            tableBody.innerHTML = `<tr><td colspan="6" class="text-center text-danger">查詢失敗: ${error.message}</td></tr>`;
        }
    }

    function getStatusBadgeClass(statusCode) {
        switch (statusCode) {
            case 10: // 待揀料
                return 'bg-secondary';
            case 20: // 已揀料
                return 'bg-primary';
            case 30: // 領料完成
                return 'bg-success';
            case 40: // 已取消
                return 'bg-danger';
            default:
                return 'bg-info text-dark';
        }
    }

    function renderTable(orders) {
        tableBody.innerHTML = '';
        if (!orders || orders.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center">查無任何領料單資料</td></tr>';
            return;
        }
        orders.forEach(order => {
            const row = tableBody.insertRow();
            const detailLink = `<a href="material_mgr_detail.html?id=${order.materialOrderId}">${order.materialOrderNumber}</a>`;
            const viewButton = `<a href="material_mgr_detail.html?id=${order.materialOrderId}" class="btn btn-sm btn-outline-dark"><i class="bi bi-search"></i> 查看</a>`;

            row.innerHTML = `
                <td>${detailLink}</td>
                <td>${order.requestingTechnicianName || 'N/A'}</td>
                <td>${order.warehouseName || 'N/A'}</td>
                <td>${order.createTime ? new Date(order.createTime).toLocaleString() : '-'}</td>
                <td><span class="badge ${getStatusBadgeClass(order.statusCode)}">${order.statusDescription}</span></td>
                <td>${viewButton}</td>
            `;
        });
    }

    function renderPagination(pageData) {
        paginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 1) return;

        const { number: page, totalPages } = pageData;
        let paginationHtml = '<ul class="pagination justify-content-center">';
        paginationHtml += `<li class="page-item ${page === 0 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page}">‹</a></li>`;
        for (let i = 0; i < totalPages; i++) {
            paginationHtml += `<li class="page-item ${i === page ? 'active' : ''}"><a class="page-link" href="#" data-page="${i + 1}">${i + 1}</a></li>`;
        }
        paginationHtml += `<li class="page-item ${page >= totalPages - 1 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page + 2}">›</a></li>`;
        paginationHtml += '</ul>';
        paginationControls.innerHTML = paginationHtml;

        paginationControls.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                if (!this.parentElement.classList.contains('disabled')) {
                    searchMaterialOrders(parseInt(this.dataset.page));
                }
            });
        });
    }

    searchInput.addEventListener('input', () => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => searchMaterialOrders(1), 500);
    });

    // Initial load
    searchMaterialOrders(1);
}); 