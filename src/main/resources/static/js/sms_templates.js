// js/sms_templates.js // Path comment updated
document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.getElementById('sms-templates-table-body');
    const paginationControls = document.getElementById('sms-pagination-controls');
    const filterForm = document.getElementById('sms-filter-form');
    const filterTemplateType = document.getElementById('filterTemplateType');
    const filterKeyword = document.getElementById('filterTemplateKeyword');
    const noDataMessage = document.getElementById('no-sms-templates-message');
    const clearFiltersBtn = document.getElementById('clearSmsFiltersBtn');

    let currentPage = 1; // 1-indexed for UI logic
    const pageSize = 10;

    async function fetchSmsTemplates(page = 1) {
        currentPage = page;
        const type = filterTemplateType.value.trim();
        const keyword = filterKeyword.value.trim();

        let queryParams = `page=${page - 1}&size=${pageSize}&sort=templateName,asc`; // API is 0-indexed
        if (type) queryParams += `&templateType=${encodeURIComponent(type)}`;
        if (keyword) queryParams += `&keyword=${encodeURIComponent(keyword)}`;

        try {
            const response = await window.fetchAuthenticated(`/api/v1/sms-templates?${queryParams}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResult = await response.json();

            tableBody.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && apiResult.data.list && Array.isArray(apiResult.data.list)) {
                 if (apiResult.data.list.length > 0) {
                    noDataMessage.classList.add('d-none');
                    apiResult.data.list.forEach(template => {
                        const row = tableBody.insertRow();
                        row.insertCell().textContent = template.templateName;
                        row.insertCell().textContent = template.templateType || '-';
                        row.insertCell().textContent = template.updateTime ? new Date(template.updateTime).toLocaleString() : '-';
                        
                        const actionsCell = row.insertCell();
                        actionsCell.innerHTML = `
                            <a href="sms_template_form.html?id=${template.smsTemplateId}&mode=edit" class="btn btn-sm btn-outline-primary me-1" title="編輯">
                                <i class="bi bi-pencil-fill"></i> 編輯
                            </a>
                            <button class="btn btn-sm btn-outline-danger delete-template-btn" data-id="${template.smsTemplateId}" data-name="${template.templateName}" title="刪除">
                                <i class="bi bi-trash-fill"></i> 刪除
                            </button>
                        `;
                    });
                    renderPagination(apiResult.data.page.total, apiResult.data.page.page, apiResult.data.page.pageSize);
                    addDeleteEventListeners();
                } else {
                    noDataMessage.classList.remove('d-none');
                    tableBody.innerHTML = `<tr><td colspan="4" class="text-center">查無簡訊模板資料</td></tr>`;
                    paginationControls.innerHTML = '';
                }
            } else {
                noDataMessage.classList.remove('d-none');
                tableBody.innerHTML = `<tr><td colspan="4" class="text-center">${apiResult.message || '查無簡訊模板資料或回應錯誤'}</td></tr>`;
                paginationControls.innerHTML = '';
                console.error("Error or unexpected format in fetchSmsTemplates response:", apiResult.message || apiResult);
            }
        } catch (error) {
            console.error('Error fetching SMS templates:', error);
            noDataMessage.classList.remove('d-none');
            tableBody.innerHTML = `<tr><td colspan="4" class="text-center">無法載入資料: ${error.message}</td></tr>`;
            paginationControls.innerHTML = '';
        }
    }

    function renderPagination(totalItems, currentPageBackend, itemsPerPage) {
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        const uiCurrentPage = currentPageBackend + 1; 
        paginationControls.innerHTML = '';
        if (totalPages <= 1) return;

        const createPageItem = (pageToFetch, text, isDisabled, isActive) => { 
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = text;
            if (!isDisabled) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    fetchSmsTemplates(pageToFetch); 
                });
            }
            li.appendChild(link);
            return li;
        };
        
        paginationControls.appendChild(createPageItem(uiCurrentPage - 1, '上一頁', uiCurrentPage <= 1, false));
        let startPage = Math.max(1, uiCurrentPage - 2);
        let endPage = Math.min(totalPages, uiCurrentPage + 2);
        if (uiCurrentPage <= 3) endPage = Math.min(totalPages, 5);
        if (uiCurrentPage > totalPages - 2) startPage = Math.max(1, totalPages - 4);

        if (startPage > 1) {
            paginationControls.appendChild(createPageItem(1, '1', false, 1 === uiCurrentPage));
            if (startPage > 2) {
                 const ellipsis = document.createElement('li');
                 ellipsis.className = 'page-item disabled';
                 ellipsis.innerHTML = '<a class="page-link" href="#">...</a>';
                 paginationControls.appendChild(ellipsis);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i, i, false, i === uiCurrentPage));
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('li');
                ellipsis.className = 'page-item disabled';
                ellipsis.innerHTML = '<a class="page-link" href="#">...</a>';
                paginationControls.appendChild(ellipsis);
            }
            paginationControls.appendChild(createPageItem(totalPages, totalPages, false, totalPages === uiCurrentPage));
        }
        paginationControls.appendChild(createPageItem(uiCurrentPage + 1, '下一頁', uiCurrentPage >= totalPages, false));
    }

    function addDeleteEventListeners() {
        document.querySelectorAll('.delete-template-btn').forEach(button => {
            button.addEventListener('click', async function () {
                const id = this.dataset.id;
                const name = this.dataset.name;
                if (confirm(`確定要刪除模板「${name}」嗎？`)) {
                    try {
                        const response = await window.fetchAuthenticated(`/api/v1/sms-templates/${id}`, { method: 'DELETE' });
                        const result = await response.json();
                        if (response.ok && result.code === 200) {
                            // Assuming showGlobalSuccess is available or use alert
                            alert(result.message || '模板已刪除'); 
                            fetchSmsTemplates(currentPage);
                        } else {
                            // Assuming showGlobalError is available or use alert
                            alert(`刪除失敗: ${result.message || '未知錯誤'}`); 
                        }
                    } catch (err) {
                        alert('刪除模板時發生錯誤。');
                        console.error('Delete error:', err);
                    }
                }
            });
        });
    }

    filterForm.addEventListener('submit', function (event) {
        event.preventDefault();
        fetchSmsTemplates(1);
    });

    clearFiltersBtn.addEventListener('click', function() {
        filterTemplateType.value = '';
        filterKeyword.value = '';
        fetchSmsTemplates(1);
    });

    fetchSmsTemplates(); 
}); 