// static/js/store_inventory_list.js
document.addEventListener('DOMContentLoaded', function () {
    const filterForm = document.getElementById('inventory-filter-form');
    const filterStoreSelect = document.getElementById('filterStore');
    const filterKeywordInput = document.getElementById('filterKeyword');
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    const inventoryTableBody = document.getElementById('inventory-table-body');
    const noInventoryMessage = document.getElementById('no-inventory-message');
    const paginationControls = document.getElementById('pagination-controls');

    const API_URL_STORES_SELECTABLE = '/api/v1/stores/selectable'; // Corrected endpoint
    const API_URL_INVENTORY = '/api/v1/store-inventory';

    let currentPage = 0; // 0-indexed for API
    const pageSize = 15;

    async function populateStoreFilter() {
        try {
            const response = await window.fetchAuthenticated(API_URL_STORES_SELECTABLE);
            if (!response.ok) throw new Error('Failed to fetch stores for filter');
            const apiResult = await response.json();
            // The /api/v1/stores/selectable endpoint returns List<StoreDto> directly in apiResult.data
            if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
                filterStoreSelect.innerHTML = '<option value="">請選擇門市...</option>';
                apiResult.data.forEach(store => {
                    const option = document.createElement('option');
                    option.value = store.storeId;
                    option.textContent = store.storeName + (store.storeCode ? ` (${store.storeCode})` : '');
                    filterStoreSelect.appendChild(option);
                });
                const selectedStoreStr = localStorage.getItem('selectedStore');
                if (selectedStoreStr) {
                    const selectedStore = JSON.parse(selectedStoreStr);
                    if(selectedStore && selectedStore.storeId) {
                        filterStoreSelect.value = selectedStore.storeId;
                        fetchInventory(0); 
                    }
                }
            } else {
                console.error("Error fetching stores for filter, API response:", apiResult);
                filterStoreSelect.innerHTML = '<option value="">門市載入失敗</option>';
                showToast(apiResult.message || '載入門市選項失敗', 'error');
            }
        } catch (error) {
            console.error('Error populating store filter:', error);
            filterStoreSelect.innerHTML = '<option value="">門市載入錯誤</option>';
            showToast(`載入門市失敗: ${error.message}`, 'error');
        }
    }

    async function fetchInventory(page = 0) {
        currentPage = page;
        const storeId = filterStoreSelect.value;
        const keyword = filterKeywordInput.value.trim();

        if (!storeId) {
            inventoryTableBody.innerHTML = '';
            if(noInventoryMessage) noInventoryMessage.classList.remove('d-none');
            if(paginationControls) paginationControls.innerHTML = '';
            // Do not show toast if it's initial load and no store is selected yet
            // showToast('請先選擇一個門市進行查詢。', 'info'); 
            return;
        }

        let queryParams = `?storeId=${storeId}&page=${page}&size=${pageSize}&sort=productName,asc`;
        if (keyword) queryParams += `&keyword=${encodeURIComponent(keyword)}`;

        inventoryTableBody.innerHTML = `<tr><td colspan="5" class="text-center">載入庫存中...</td></tr>`;
        if(noInventoryMessage) noInventoryMessage.classList.add('d-none');

        try {
            const response = await window.fetchAuthenticated(API_URL_INVENTORY + queryParams);
            if (!response.ok) {
                const errData = await response.json().catch(() => ({ message: '獲取庫存列表失敗' }));
                throw new Error(errData.message || `HTTP error! Status: ${response.status}`);
            }
            const apiResult = await response.json();

            inventoryTableBody.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && apiResult.data.list && apiResult.data.list.length > 0) {
                apiResult.data.list.forEach((item, index) => {
                    const row = inventoryTableBody.insertRow();
                    row.insertCell().textContent = (currentPage * pageSize) + index + 1;
                    row.insertCell().textContent = item.productBarcode;
                    row.insertCell().textContent = item.productName;
                    row.insertCell().textContent = item.quantityOnHand;
                    if(row.cells[3]) row.cells[3].classList.add('text-end');
                    row.insertCell().textContent = item.lastStockUpdateTime ? new Date(item.lastStockUpdateTime).toLocaleString() : '-';
                });
                renderPagination(apiResult.data.page);
            } else {
                inventoryTableBody.innerHTML = `<tr><td colspan="5" class="text-center">${apiResult.message || '查無庫存資料'}</td></tr>`;
                if(noInventoryMessage) noInventoryMessage.classList.remove('d-none');
                if(paginationControls) paginationControls.innerHTML = '';
            }
        } catch (error) {
            console.error('Error fetching inventory:', error);
            inventoryTableBody.innerHTML = `<tr><td colspan="5" class="text-center text-danger">無法載入庫存: ${error.message}</td></tr>`;
            if(noInventoryMessage) noInventoryMessage.classList.remove('d-none');
        }
    }

    function renderPagination(pageData) {
        if(paginationControls) paginationControls.innerHTML = '';
        if (!pageData || !paginationControls || pageData.totalPages <= 1) return;

        const createPageItem = (pageNum, text, isDisabled = false, isActive = false) => {
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = text;
            if (!isDisabled) {
                link.addEventListener('click', (e) => { e.preventDefault(); fetchInventory(pageNum); });
            }
            li.appendChild(link);
            return li;
        };

        paginationControls.appendChild(createPageItem(pageData.page - 1, '上一頁', pageData.page === 0));
        
        const maxPagesToShow = 5;
        let startPage = Math.max(0, pageData.page - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(pageData.totalPages - 1, startPage + maxPagesToShow - 1);
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(0, endPage - maxPagesToShow + 1);
        }

        if (startPage > 0) {
            paginationControls.appendChild(createPageItem(0, '1'));
            if (startPage > 1) paginationControls.appendChild(createPageItem(-1, '...', true));
        }
        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i, i + 1, false, i === pageData.page));
        }
        if (endPage < pageData.totalPages - 1) {
            if (endPage < pageData.totalPages - 2) paginationControls.appendChild(createPageItem(-1, '...', true));
            paginationControls.appendChild(createPageItem(pageData.totalPages - 1, pageData.totalPages));
        }

        paginationControls.appendChild(createPageItem(pageData.page + 1, '下一頁', pageData.page >= pageData.totalPages - 1));
    }
    if(filterForm){
        filterForm.addEventListener('submit', function(event) {
            event.preventDefault();
            fetchInventory(0);
        });
    }
    if(clearFiltersBtn){
        clearFiltersBtn.addEventListener('click', function() {
            filterKeywordInput.value = '';
             // Do not reset store filter by default, user might want to requery same store
            fetchInventory(0); 
        });
    }

    async function init() {
        // loadSharedHTML is called by main.js
        if (filterStoreSelect) {
            await populateStoreFilter(); 
        } else {
             console.warn("Store filter dropdown not found on this page.");
        }
        // Initial fetch might be triggered by populateStoreFilter if store is auto-selected
        // or user needs to select a store first.
        if (!filterStoreSelect || !filterStoreSelect.value) {
             if(inventoryTableBody) inventoryTableBody.innerHTML = '<tr><td colspan="5" class="text-center">請選擇門市以查詢庫存。</td></tr>';
             if(noInventoryMessage) noInventoryMessage.classList.remove('d-none');
        }
    }

    init();
}); 