// Placeholder for electric_mahjong_table_stock_list.js
document.addEventListener('DOMContentLoaded', function () {
    console.log('electric_mahjong_table_stock_list.js loaded');

    const erpWarehouseFilter = document.getElementById('erpWarehouseFilter');
    const productCategoryFilter = document.getElementById('productCategoryFilter');
    const keywordFilter = document.getElementById('keywordFilter');
    const searchButton = document.getElementById('searchButton');
    const erpStockTableBody = document.getElementById('erpStockTableBody');
    const paginationControls = document.getElementById('paginationControls');

    let currentPage = 0;
    const DEFAULT_PAGE_SIZE = 20;

    async function loadErpWarehouses() {
        try {
            console.log("Fetching ERP warehouses...");
            const response = await fetchAuthenticated('/api/v1/erp-mahjong-stock/erp-warehouses');
            if (!response.ok) {
                const errData = await response.json().catch(() => ({ message: `HTTP error ${response.status}` }));
                throw new Error(errData.message);
            }
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                erpWarehouseFilter.innerHTML = '<option value="" selected>所有倉庫</option>'; 
                apiResponse.data.forEach(warehouse => {
                    const option = document.createElement('option');
                    option.value = warehouse.erpWarehouseCode;
                    option.textContent = warehouse.erpWarehouseName ? `${warehouse.erpWarehouseName} (${warehouse.erpWarehouseCode})` : warehouse.erpWarehouseCode;
                    erpWarehouseFilter.appendChild(option);
                });
                console.log("ERP warehouses loaded.");
            } else {
                console.error('Error fetching ERP warehouses from API:', apiResponse.message);
                showToast(`無法載入ERP倉庫: ${apiResponse.message || '未知錯誤'}`, 'error');
            }
        } catch (error) {
            console.error('Failed to load ERP warehouses:', error);
            showToast(`載入ERP倉庫失敗: ${error.message}`, 'error');
        }
    }

    async function loadMahjongTableCategories() {
        console.log("Fetching Mahjong table categories...");
        try {
            // Corrected endpoint: use path variable for menuType
            const response = await fetchAuthenticated('/api/v1/product-menus/tree/DISPATCH'); 
            if (!response.ok) {
                const errData = await response.json().catch(() => ({ message: 'Failed to load product categories tree' }));
                throw new Error(errData.message || `HTTP Error ${response.status}`);
            }
            const apiResponse = await response.json();
            
            if (apiResponse.code === 200 && Array.isArray(apiResponse.data)) { 
                 productCategoryFilter.innerHTML = '<option value="" selected>全部分類(電動桌)</option>';
                 // Helper function to flatten the tree and extract categories
                 const extractCategories = (nodes, allCategories) => {
                     if (!nodes) return;
                     nodes.forEach(node => {
                         if (node.type === 'CATEGORY') { // Assuming ProductMenuNodeDto has a 'type' field
                             // TODO: Add more robust logic to identify "電動桌" categories if needed
                             // For now, let's assume all categories under DISPATCH menu might be relevant for user to filter
                             // or filter by name if truly necessary client-side, though backend filtering is better.
                             if (node.name.includes("電動桌") || node.name.includes("麻將桌") || true) { // Looser filter for now
                                 allCategories.push({ id: node.id, name: node.name }); 
                             }
                             if (node.children && node.children.length > 0) {
                                 extractCategories(node.children, allCategories);
                             }
                         }
                     });
                 };

                 let mahjongCategories = [];
                 extractCategories(apiResponse.data, mahjongCategories);

                 // Remove duplicates that might arise if a category appears multiple times (though tree should be structured)
                 const uniqueCategories = mahjongCategories.filter((cat, index, self) => 
                    index === self.findIndex((c) => c.id === cat.id)
                 );

                 uniqueCategories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id; // Use category ID (UUID)
                    option.textContent = category.name;
                    productCategoryFilter.appendChild(option);
                 });
                 console.log("Mahjong table categories populated from tree.");
            }
            else {
                console.error('Error or unexpected format in API response for categories:', apiResponse);
                showToast(apiResponse.message || '無法載入商品分類', 'error');
            }
        } catch (e) {
            console.error("Error loading categories:", e);
            showToast(`載入商品分類失敗: ${e.message}`, 'error');
        }
    }

    async function fetchAndDisplayStock(page = 0) {
        currentPage = page;
        const warehouseCode = erpWarehouseFilter.value;
        const categoryId = productCategoryFilter.value;
        const keyword = keywordFilter.value.trim();

        let url = `/api/v1/erp-mahjong-stock?page=${currentPage}&size=${DEFAULT_PAGE_SIZE}`;
        if (warehouseCode) url += `&erpWarehouseCode=${encodeURIComponent(warehouseCode)}`;
        if (categoryId) url += `&productCategoryId=${encodeURIComponent(categoryId)}`;
        if (keyword) url += `&productKeyword=${encodeURIComponent(keyword)}`;
        
        console.log(`Fetching ERP stock from: ${url}`);
        erpStockTableBody.innerHTML = '<tr><td colspan="6" class="text-center">載入中...</td></tr>';

        try {
            const response = await fetchAuthenticated(url);
            if (!response.ok) {
                 const errData = await response.json().catch(() => ({ message: '獲取庫存列表失敗' }));
                 throw new Error(errData.message || `HTTP Error ${response.status}`);
            }
            const apiResponse = await response.json();

            if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.list) {
                renderTable(apiResponse.data.list);
                renderPagination(apiResponse.data.page);
                 console.log("ERP stock data displayed.");
            } else {
                erpStockTableBody.innerHTML = '<tr><td colspan="6" class="text-center">查詢無資料或發生錯誤</td></tr>';
                renderPagination(null);
                console.warn('No stock data or error in API response:', apiResponse.message);
                showToast(apiResponse.message || '查詢無資料', 'info');
            }
        } catch (error) {
            console.error('Error fetching ERP stock data:', error);
            erpStockTableBody.innerHTML = '<tr><td colspan="6" class="text-center">載入資料失敗</td></tr>';
            showToast(`載入資料失敗: ${error.message}`, 'error');
        }
    }

    function renderTable(stockItems) {
        erpStockTableBody.innerHTML = ''; 
        if (!stockItems || stockItems.length === 0) {
            erpStockTableBody.innerHTML = '<tr><td colspan="6" class="text-center">無符合條件的庫存資料</td></tr>';
            return;
        }
        stockItems.forEach(item => {
            const row = erpStockTableBody.insertRow();
            row.insertCell().textContent = item.productCategory || 'N/A';
            row.insertCell().textContent = item.productBarcode;
            row.insertCell().textContent = item.productName;
            row.insertCell().textContent = item.erpWarehouseName ? `${item.erpWarehouseName} (${item.erpWarehouseCode})` : item.erpWarehouseCode;
            row.insertCell().textContent = item.quantity;
            row.insertCell().textContent = item.lastSyncTime ? new Date(item.lastSyncTime).toLocaleString() : 'N/A';
        });
    }

    function renderPagination(pageData) {
        paginationControls.innerHTML = ''; 
        if (!pageData || pageData.totalPages <= 0) { 
            return;
        }

        const createPageItem = (text, pageNumber, isDisabled = false, isActive = false) => {
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const a = document.createElement('a');
            a.className = 'page-link';
            a.href = '#';
            a.textContent = text;
            if (!isDisabled) {
                a.addEventListener('click', (e) => {
                    e.preventDefault();
                    fetchAndDisplayStock(pageNumber);
                });
            }
            li.appendChild(a);
            return li;
        };

        paginationControls.appendChild(createPageItem('上一頁', pageData.page - 1, pageData.page === 0));

        const maxPagesToShow = 5;
        let startPage = Math.max(0, pageData.page - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(pageData.totalPages - 1, startPage + maxPagesToShow - 1);
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(0, endPage - maxPagesToShow + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i + 1, i, false, i === pageData.page));
        }
        paginationControls.appendChild(createPageItem('下一頁', pageData.page + 1, pageData.page === pageData.totalPages - 1));
    }

    if(searchButton) {
        searchButton.addEventListener('click', () => fetchAndDisplayStock(0));
    } else {
        console.error("Search button not found!");
    }

    loadErpWarehouses();
    loadMahjongTableCategories(); 
    fetchAndDisplayStock(); 
}); 