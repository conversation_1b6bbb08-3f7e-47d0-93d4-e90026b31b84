document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.getElementById('bundles-table-body');
    const paginationControls = document.getElementById('pagination-controls');
    const filterForm = document.getElementById('filter-form');
    const noDataMessage = document.getElementById('no-data-message');
    const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const deleteConfirmMessage = document.getElementById('deleteConfirmMessage');
    let bundleToDeleteId = null;

    let currentPage = 0;
    const pageSize = 10;
    let currentSort = { key: 'updateTime', direction: 'desc' }; // Default sort

    async function fetchGiftBundles(page = 0, sortKey = currentSort.key, sortDirection = currentSort.direction) {
        currentPage = page;
        currentSort = {key: sortKey, direction: sortDirection};
        
        let url = `/api/v1/gift-bundles?page=${page}&size=${pageSize}&sort=${sortKey},${sortDirection}`;
        const keyword = document.getElementById('filterKeyword').value;
        const effectiveDateFrom = document.getElementById('filterEffectiveDateFrom').value;
        const effectiveDateTo = document.getElementById('filterEffectiveDateTo').value;
        // const isActive = document.getElementById('filterIsActive').value; // No active filter in this UI yet

        if (keyword) url += `&keyword=${encodeURIComponent(keyword)}`;
        if (effectiveDateFrom) url += `&effectiveDateFrom=${new Date(effectiveDateFrom).toISOString()}`;
        if (effectiveDateTo) url += `&effectiveDateTo=${new Date(effectiveDateTo).toISOString()}`;
        // if (isActive !== "") url += `&isActive=${isActive}`;

        try {
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
            const apiResult = await response.json();

            tableBody.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && apiResult.data.list && apiResult.data.list.length > 0) {
                noDataMessage.classList.add('d-none');
                apiResult.data.list.forEach((bundle, index) => {
                    const row = tableBody.insertRow();
                    row.insertCell().textContent = (currentPage * pageSize) + index + 1;
                    //row.insertCell().textContent = bundle.bundleName;
                    row.insertCell().textContent = `${bundle.mainProductName || 'N/A'} (${bundle.mainProductBarcode})`;
                    row.insertCell().textContent = bundle.startTime ? new Date(bundle.startTime).toLocaleString() : '-';
                    row.insertCell().textContent = bundle.endTime ? new Date(bundle.endTime).toLocaleString() : '-';
                    row.insertCell().innerHTML = bundle.isActive ? '<span class="badge bg-success">啟用</span>' : '<span class="badge bg-secondary">停用</span>';
                    row.insertCell().textContent = bundle.updateTime ? new Date(bundle.updateTime).toLocaleString() : '-';
                    
                    const actionsCell = row.insertCell();
                    actionsCell.innerHTML = `
                        <a href="gift_bundle_form.html?id=${bundle.bundleId}&mode=edit" class="btn btn-sm btn-outline-primary me-1" title="編輯"><i class="bi bi-pencil"></i></a>
                        <button class="btn btn-sm btn-outline-danger delete-btn" data-id="${bundle.bundleId}" data-name="${bundle.bundleName}" title="刪除"><i class="bi bi-trash"></i></button>
                    `;
                });
                renderPagination(apiResult.data.page);
                // updateSortIcons(); // Implement if sort icons are used
            } else {
                noDataMessage.classList.remove('d-none');
                paginationControls.innerHTML = '';
            }
        } catch (error) {
            console.error('Error fetching gift bundles:', error);
            tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">無法載入贈品套裝: ${error.message}</td></tr>`;
            noDataMessage.classList.remove('d-none');
        }
    }
    
    function renderPagination(pageData) {
        paginationControls.innerHTML = ''; 
        if (!pageData || pageData.totalPages <= 1) return;

        const createPageItem = (pageNum, text, isDisabled = false, isActive = false) => {
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = text;
            if (!isDisabled) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    fetchGiftBundles(pageNum, currentSort.key, currentSort.direction);
                });
            }
            li.appendChild(link);
            return li;
        };

        paginationControls.appendChild(createPageItem(pageData.page - 1, '上一頁', pageData.page === 0));
        const maxPagesToShow = 5;
        let startPage = Math.max(0, pageData.page - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(pageData.totalPages - 1, startPage + maxPagesToShow - 1);
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(0, endPage - maxPagesToShow + 1);
        }
        if (startPage > 0) {
            paginationControls.appendChild(createPageItem(0, '1'));
            if (startPage > 1) paginationControls.appendChild(createPageItem(-1, '...', true));
        }
        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i, i + 1, false, i === pageData.page));
        }
        if (endPage < pageData.totalPages - 1) {
            if (endPage < pageData.totalPages - 2) paginationControls.appendChild(createPageItem(-1, '...', true));
            paginationControls.appendChild(createPageItem(pageData.totalPages - 1, pageData.totalPages));
        }
        paginationControls.appendChild(createPageItem(pageData.page + 1, '下一頁', pageData.page >= pageData.totalPages - 1));
    }

    tableBody.addEventListener('click', function(event) {
        if (event.target.closest('.delete-btn')) {
            const button = event.target.closest('.delete-btn');
            bundleToDeleteId = button.dataset.id;
            deleteConfirmMessage.textContent = `您確定要刪除套裝「${button.dataset.name}」嗎？`;
            deleteConfirmModal.show();
        }
    });

    confirmDeleteBtn.addEventListener('click', async function() {
        if (!bundleToDeleteId) return;
        // Diagnostic: Directly use the string path instead of API_BASE_URL
        const deleteUrl = `/api/v1/gift-bundles/${bundleToDeleteId}`;
        console.log("Attempting to delete with URL:", deleteUrl); // Log the URL being used
        try {
            const response = await window.fetchAuthenticated(deleteUrl, { method: 'DELETE' }); 
            const result = await response.json();
            if (response.ok && result.code === 200) {
                showToast(result.message || '商品套裝已刪除', 'success');
                fetchGiftBundles(currentPage, currentSort.key, currentSort.direction);
            } else {
                showToast(result.message || '刪除失敗', 'error');
            }
        } catch (error) {
            console.error('Error deleting gift bundle:', error);
            showToast('刪除商品套裝時發生錯誤', 'error');
        }
        deleteConfirmModal.hide();
        bundleToDeleteId = null;
    });

    filterForm.addEventListener('submit', function(event) {
        event.preventDefault();
        fetchGiftBundles(0, currentSort.key, currentSort.direction);
    });

    // Initial load
    fetchGiftBundles();
}); 