document.addEventListener('DOMContentLoaded', function () {
    // --- DOM Elements ---
    const warehouseSelect = document.getElementById('warehouse-select');
    const productSearchInput = document.getElementById('product-search');
    const searchProductBtn = document.getElementById('search-product-btn');
    const dispatchItemsContainer = document.getElementById('dispatch-items-container');
    const materialItemsByWarehouseContainer = document.getElementById('material-items-by-warehouse');
    const submitBtn = document.getElementById('submit-btn');
    const accessorySearchModal = new bootstrap.Modal(document.getElementById('accessorySearchModal'));
    const accessorySearchResultsContainer = document.getElementById('accessory-search-results');

    // --- State ---
    let allPendingItems = []; 
    let manualItems = []; // To store manually added items

    // --- Initialization ---
    async function initializePage() {
        await loadWarehousesForSelect();
        await loadAndRenderAllPendingItems();
        attachEventListeners();
    }

    // --- Data Fetching & Rendering ---
    async function loadWarehousesForSelect() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/warehouses');
            if (!response.ok) throw new Error('無法載入倉庫列表');
            const result = await response.json();
            populateSelect(warehouseSelect, result.data, 'warehouseId', 'warehouseName', '請選擇預設領料倉庫');
        } catch (error) {
            console.error(error);
            showToast(error.message, 'danger');
        }
    }

    async function loadAndRenderAllPendingItems() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/dispatch-orders/pending-items');
            if (!response.ok) throw new Error('無法載入待領料品項');
            const result = await response.json();
            allPendingItems = result.data || [];
            renderDispatchItemRows(allPendingItems);
        } catch (error) {
            console.error(error);
            showToast(error.message, 'danger');
            dispatchItemsContainer.innerHTML = `<tr><td colspan="9" class="text-center text-danger">${error.message}</td></tr>`;
        }
    }

    function populateSelect(element, data, valueField, textField, defaultOptionText) {
        element.innerHTML = `<option value="">${defaultOptionText}</option>`;
        if (data && Array.isArray(data)) {
            data.forEach(item => {
                element.innerHTML += `<option value="${item[valueField]}">${item[textField]}</option>`;
            });
        }
    }

    function renderDispatchItemRows(items) {
        dispatchItemsContainer.innerHTML = '';
        if (!items || items.length === 0) {
            dispatchItemsContainer.innerHTML = '<p class="text-center text-muted">無待領料品項</p>';
            return;
        }

        // Add a header row
        const header = document.createElement('div');
        header.className = 'dispatch-item-row dispatch-item-header d-none d-lg-flex row gx-2';
        header.innerHTML = `
            <div class="col-2"><strong>派工單號</strong></div>
            <div class="col-3"><strong>商品名稱</strong></div>
            <div class="col-1 text-center"><strong>派工</strong></div>
            <div class="col-1 text-center"><strong>預領</strong></div>
            <div class="col-1 text-center"><strong>已領</strong></div>
            <div class="col-2"><strong>倉庫</strong></div>
            <div class="col-2"><strong>領料數量</strong></div>
        `;
        dispatchItemsContainer.appendChild(header);

        items.forEach(item => {
            const totalRequested = item.alreadyRequestedQuantity || 0;
            const dispatchQuantity = item.quantity || 0;
            const remainingQty = dispatchQuantity - totalRequested;
            const canPick = remainingQty > 0;

            const row = document.createElement('div');
            row.className = 'dispatch-item-row card card-body mb-2'; // Use card for styling
            row.dataset.itemId = item.dispatchRepairItemId;
            row.dataset.dispatchQuantity = dispatchQuantity;
            row.dataset.alreadyRequested = totalRequested;

            row.innerHTML = `
                <div class="row gx-2 align-items-center">
                    <div class="col-12 col-lg-2 mb-2 mb-lg-0">
                        <strong class="d-lg-none">派工單: </strong>
                        ${item.dispatchRepairNumber}
                        <span class="text-muted small">${item.scheduledDate}</span>
                    </div>
                    <div class="col-12 col-lg-3 mb-2 mb-lg-0">
                        <strong class="d-lg-none">商品: </strong>
                        ${item.productName}
                        <span class="text-muted small">(${item.productBarcode})</span>
                    </div>
                    <div class="col-4 col-lg-1 text-lg-center"><strong class="d-lg-none">派工: </strong>${dispatchQuantity}</div>
                    <div class="col-4 col-lg-1 text-lg-center"><strong class="d-lg-none">預領: </strong>${totalRequested}</div>
                    <div class="col-4 col-lg-1 text-lg-center"><strong class="d-lg-none">已領: </strong>${item.pickedQuantity || 0}</div>
                    <div class="col-12 col-lg-2 mb-2 mb-lg-0"><strong class="d-lg-none">倉庫: </strong>${item.warehouseName}</div>
                    <div class="col-12 col-lg-2">
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control quantity-to-pick" value="${remainingQty}" min="0" max="${remainingQty}" ${!canPick ? 'disabled' : ''} aria-label="領料數量">
                            <div class="input-group-text">
                                <input class="form-check-input mt-0 add-to-list-checkbox" type="checkbox" title="加入領料" ${!canPick ? 'disabled' : ''} aria-label="加入領料">
                            </div>
                        </div>
                    </div>
                </div>
            `;
            dispatchItemsContainer.appendChild(row);
        });
    }

    function renderAccessorySearchResults(items) {
        accessorySearchResultsContainer.innerHTML = '';
        if (!items || items.length === 0) {
            accessorySearchResultsContainer.innerHTML = '<p class="text-muted">查無相關品項</p>';
            return;
        }
        items.forEach(item => {
            const itemEl = document.createElement('div');
            itemEl.className = 'list-group-item d-flex justify-content-between align-items-center';
            itemEl.innerHTML = `
                <div>
                    <h6 class="mb-1">${item.productName}</h6>
                    <small class="text-muted">${item.productBarcode}</small>
                </div>
                <div class="input-group" style="width: 200px;">
                    <input type="number" class="form-control accessory-quantity" placeholder="數量" min="1">
                    <button class="btn btn-primary select-accessory-btn" data-barcode="${item.productBarcode}" data-name="${item.productName}">選定</button>
                </div>
            `;
            accessorySearchResultsContainer.appendChild(itemEl);
        });
    }

    function updateMaterialItemsDisplay() {
        materialItemsByWarehouseContainer.innerHTML = '';
        const itemsToSubmit = collectItemsToSubmit();
        const itemsByWarehouse = groupBy(itemsToSubmit, 'warehouseName');

        for (const warehouseName in itemsByWarehouse) {
            const warehouseDiv = document.createElement('div');
            warehouseDiv.innerHTML = `<h5>倉庫: ${warehouseName}</h5>`;
            const table = document.createElement('table');
            table.className = 'table table-striped';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>條碼</th>
                        <th>商品名稱</th>
                        <th>領料數量</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemsByWarehouse[warehouseName].map(item => `
                        <tr data-barcode="${item.productBarcode}" data-dispatch-item-id="${item.dispatchRepairItemId || ''}">
                            <td>${item.productBarcode}</td>
                            <td>${item.productName}</td>
                            <td>${item.requestedQuantity}</td>
                            <td><button class="btn btn-sm btn-danger delete-item-btn"><i class="bi bi-trash"></i></button></td>
                        </tr>
                    `).join('')}
                </tbody>
            `;
            warehouseDiv.appendChild(table);
            materialItemsByWarehouseContainer.appendChild(warehouseDiv);
        }
        
        // Control submit button state
        submitBtn.disabled = itemsToSubmit.length === 0;
    }

    // --- Event Listeners ---
    function attachEventListeners() {
        dispatchItemsContainer.addEventListener('input', handleDispatchItemChange);
        searchProductBtn.addEventListener('click', handleAccessorySearch);
        accessorySearchResultsContainer.addEventListener('click', handleAccessorySelect);
        materialItemsByWarehouseContainer.addEventListener('click', handleDeleteItem);
        submitBtn.addEventListener('click', handleSubmit);
    }
    
    function handleDispatchItemChange(event) {
        const target = event.target;
        if (target.classList.contains('add-to-list-checkbox') || target.classList.contains('quantity-to-pick')) {
            const row = target.closest('.dispatch-item-row');
            validateAndLockRow(row);
            updateMaterialItemsDisplay();
        }
    }

    async function handleAccessorySearch() {
        const selectedWarehouseId = warehouseSelect.value;
        const keyword = productSearchInput.value.trim();

        if (!selectedWarehouseId) {
            showToast('請先選擇領料倉庫', 'warning');
            return;
        }
        if (!keyword) {
            showToast('請輸入品項關鍵字', 'warning');
            return;
        }
        
        try {
            const response = await window.fetchAuthenticated(`/api/v1/product-settings/search-accessories?warehouseId=${selectedWarehouseId}&keyword=${keyword}`);
            if (!response.ok) throw new Error('搜尋配件失敗');
            const result = await response.json();
            renderAccessorySearchResults(result.data);
            accessorySearchModal.show();
        } catch (error) {
            showToast(error.message, 'danger');
        }
    }

    function handleAccessorySelect(event) {
        const target = event.target;
        if (!target.classList.contains('select-accessory-btn')) return;

        const itemRow = target.closest('.list-group-item');
        const quantityInput = itemRow.querySelector('.accessory-quantity');
        const quantity = parseInt(quantityInput.value, 10);

        if (isNaN(quantity) || quantity <= 0) {
            showToast('請輸入有效的領料數量', 'warning');
            return;
        }

        const selectedWarehouseText = warehouseSelect.options[warehouseSelect.selectedIndex].text;
        
        // Add to manualItems state
        manualItems.push({
            dispatchRepairItemId: null, // This is a manual item
            productBarcode: target.dataset.barcode,
            productName: target.dataset.name,
            requestedQuantity: quantity,
            warehouseName: selectedWarehouseText
        });

        updateMaterialItemsDisplay();
        accessorySearchModal.hide();
    }
    
    async function handleSubmit() {
        const itemsToSubmit = collectItemsToSubmit();
        if (itemsToSubmit.length === 0) {
            showToast('請至少選擇一個領料品項', 'warning');
            return;
        }

        const itemsByWarehouse = groupBy(itemsToSubmit, 'warehouseName');
        const submissionPromises = [];

        for (const warehouseName in itemsByWarehouse) {
            const warehouseId = warehouseSelect.options[Array.from(warehouseSelect.options).findIndex(opt => opt.text === warehouseName)].value;
            if(!warehouseId && itemsByWarehouse[warehouseName].some(item => !item.warehouseId)) {
                 const firstWarehouse = allPendingItems.find(p => p.warehouseName === warehouseName);
                 if(firstWarehouse) warehouseId = firstWarehouse.warehouseId; // A bit of a hack to get warehouseId
            }

            const payload = {
                targetWarehouseId: warehouseId,
                remarks: `領料單 for ${warehouseName}`,
                items: itemsByWarehouse[warehouseName].map(item => ({
                    dispatchRepairItemId: item.dispatchRepairItemId,
                    productBarcode: item.productBarcode,
                    requestedQuantity: item.requestedQuantity,
                }))
            };
            
            const promise = window.fetchAuthenticated('/api/v1/material-orders', {
                method: 'POST',
                body: JSON.stringify(payload)
            }).then(response => {
                if (!response.ok) {
                    return response.json().then(err => Promise.reject(err));
                }
                return response.json();
            });
            submissionPromises.push(promise);
        }

        try {
            await Promise.all(submissionPromises);
            showToast('所有領料單已成功建立！', 'success');
            setTimeout(() => { window.location.href = 'material_order_list.html'; }, 1500);
        } catch (error) {
            console.error('建立領料單失敗:', error);
            showToast(`建立領料單失敗: ${error.message || '未知錯誤'}`, 'danger');
        }
    }

    function handleDeleteItem(event) {
        const target = event.target;
        if (!target.closest('.delete-item-btn')) return;

        const rowToDelete = target.closest('tr');
        const dispatchItemId = rowToDelete.dataset.dispatchItemId;
        const productBarcode = rowToDelete.dataset.barcode;

        if (dispatchItemId && dispatchItemId !== 'null') {
            // It's a dispatch item, uncheck it in the top list
            const dispatchRow = dispatchItemsContainer.querySelector(`.dispatch-item-row[data-item-id="${dispatchItemId}"]`);
            if (dispatchRow) {
                const checkbox = dispatchRow.querySelector('.add-to-list-checkbox');
                const qtyInput = dispatchRow.querySelector('.quantity-to-pick');
                if (checkbox) checkbox.checked = false;
                // Trigger validation to re-enable fields
                validateAndLockRow(dispatchRow);
            }
        } else {
            // It's a manual item, remove it from the state array
            manualItems = manualItems.filter(item => item.productBarcode !== productBarcode);
        }

        // Re-render the display
        updateMaterialItemsDisplay();
    }

    // --- Helpers ---
    function collectItemsToSubmit() {
        const items = [];
        // From dispatch items table
        dispatchItemsContainer.querySelectorAll('.dispatch-item-row').forEach(row => {
            const checkbox = row.querySelector('.add-to-list-checkbox');
            if (checkbox && checkbox.checked) {
                const quantityInput = row.querySelector('.quantity-to-pick');
                const requestedQuantity = parseInt(quantityInput.value, 10);
                if (requestedQuantity > 0) {
                    const originalItem = allPendingItems.find(p => p.dispatchRepairItemId === row.dataset.itemId);
                    items.push({
                        dispatchRepairItemId: originalItem.dispatchRepairItemId,
                        productBarcode: originalItem.productBarcode,
                        productName: originalItem.productName,
                        requestedQuantity: requestedQuantity,
                        warehouseName: originalItem.warehouseName
                    });
                }
            }
        });
        // Add items from manual search
        manualItems.forEach(item => items.push(item));
        return items;
    }

    function groupBy(array, key) {
        return array.reduce((result, currentValue) => {
            (result[currentValue[key]] = result[currentValue[key]] || []).push(currentValue);
            return result;
        }, {});
    }
    
    function showToast(message, type = 'info') {
        if(window.showToast) {
            window.showToast(message, type);
        } else {
            alert(message);
        }
    }

    // --- Logic & Validation ---
    function validateAndLockRow(row) {
        if (!row) return;

        const dispatchQty = parseInt(row.dataset.dispatchQuantity, 10);
        const alreadyReqQty = parseInt(row.dataset.alreadyRequested, 10);
        const qtyInput = row.querySelector('.quantity-to-pick');
        const checkbox = row.querySelector('.add-to-list-checkbox');
        let currentQty = parseInt(qtyInput.value, 10);

        // Validate current input quantity
        if (isNaN(currentQty) || currentQty < 0) {
            currentQty = 0;
            qtyInput.value = 0;
        }

        const maxAllowed = dispatchQty - alreadyReqQty;
        if (currentQty > maxAllowed) {
            showToast(`領料數量超過可領取上限 (${maxAllowed})`, 'warning');
            currentQty = maxAllowed;
            qtyInput.value = maxAllowed;
        }

        // Lock/unlock fields
        const isFullyRequested = (currentQty + alreadyReqQty) >= dispatchQty;
        if (isFullyRequested && checkbox.checked) {
            qtyInput.disabled = true;
            checkbox.disabled = true;
        } else {
            qtyInput.disabled = (maxAllowed <= 0);
            checkbox.disabled = (maxAllowed <= 0);
        }
    }

    // --- Start ---
    initializePage();
}); 