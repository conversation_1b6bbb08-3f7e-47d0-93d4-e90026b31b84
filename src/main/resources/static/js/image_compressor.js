/**
 * Reusable Image Compressor
 * This script provides a function to compress image files on the client-side.
 */

window.compressImage = async function(file, maxSizeKB) {
    if (!file || !file.type.startsWith('image/')) {
        console.error("Invalid file type provided for compression.");
        return null;
    }

    const maxSizeBytes = maxSizeKB * 1024;

    // If the image is already small enough, just return its base64 string
    if (file.size <= maxSizeBytes) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = (error) => reject(error);
            reader.readAsDataURL(file);
        });
    }

    // --- Compression Logic ---
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // Keep original aspect ratio
                let width = img.width;
                let height = img.height;
                
                // Initial resize if dimensions are very large
                const MAX_DIMENSION = 1920;
                if (width > height) {
                    if (width > MAX_DIMENSION) {
                        height *= MAX_DIMENSION / width;
                        width = MAX_DIMENSION;
                    }
                } else {
                    if (height > MAX_DIMENSION) {
                        width *= MAX_DIMENSION / height;
                        height = MAX_DIMENSION;
                    }
                }
                
                canvas.width = width;
                canvas.height = height;
                ctx.drawImage(img, 0, 0, width, height);

                // Iterative compression
                let quality = 0.9;
                let compressedDataUrl = canvas.toDataURL('image/jpeg', quality);

                // Simple iterative reduction, could be optimized with binary search
                while (compressedDataUrl.length > maxSizeBytes && quality > 0.1) {
                    quality -= 0.1;
                    compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
                }

                resolve(compressedDataUrl);
            };
            img.onerror = (error) => reject(error);
            img.src = e.target.result;
        };
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(file);
    });
}; 