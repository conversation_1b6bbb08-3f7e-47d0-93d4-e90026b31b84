// announcements.js
document.addEventListener('DOMContentLoaded', async function () {
    const tableBody = document.getElementById('announcements-table-body');
    const paginationControls = document.getElementById('pagination-controls');
    const filterForm = document.getElementById('filter-form');
    const filterCategory = document.getElementById('filterCategory');
    const filterStartDate = document.getElementById('filterStartDate');
    const filterEndDate = document.getElementById('filterEndDate');
    const filterKeyword = document.getElementById('filterKeyword');
    const filterIsEnabled = document.getElementById('filterIsEnabled');
    const filterIsImportant = document.getElementById('filterIsImportant');
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    const noDataMessage = document.getElementById('no-data-message');

    const API_URL = '/api/v1/announcements';
    let currentPage = 0;
    const pageSize = 10; // Should match backend or be configurable

    // Populate Category Filter Dropdown
    async function populateCategoryFilter() {
        console.log("ANNOUNCEMENTS.JS: Populating category filter...");
        if (!filterCategory) {
            console.warn("ANNOUNCEMENTS.JS: filterCategory select element not found.");
            return;
        }
        filterCategory.innerHTML = '<option value="">載入分類中...</option>'; // Loading state
        try {
            const response = await window.fetchAuthenticated('/api/v1/enums/announcement-categories');
            if (!response.ok) {
                throw new Error('Failed to fetch announcement categories: ' + response.status);
            }
            const apiResp = await response.json();
            const categories = apiResp.data || [];

            filterCategory.innerHTML = '<option value="">所有分類</option>'; // Default option
            if (categories.length > 0) {
                categories.forEach(cat => {
                    const option = document.createElement('option');
                    option.value = cat.value; // EnumValueDto uses 'value' field for the code
                    option.textContent = cat.label; // EnumValueDto uses 'label' field for the description
                    filterCategory.appendChild(option);
                });
                console.log("ANNOUNCEMENTS.JS: Category filter populated.", categories);
            } else {
                console.warn("ANNOUNCEMENTS.JS: No categories received from API.");
                 filterCategory.innerHTML = '<option value="">無可用分類</option>';
            }
        } catch (error) {
            console.error("ANNOUNCEMENTS.JS: Error populating category filter:", error);
            filterCategory.innerHTML = '<option value="">分類載入失敗</option>';
            if(window.showToast) window.showToast('載入公告分類失敗: ' + error.message, 'error');
        }
    }

    async function fetchAnnouncements(page = 0) {
        currentPage = page;
        let queryParams = `?page=${page}&size=${pageSize}`; 
        // Default sort is now handled by @PageableDefault in controller
        // queryParams += `&sort=createTime,desc`; // Removed this line

        // Add filters
        if (filterCategory.value) queryParams += `&categoryCode=${filterCategory.value}`;
        if (filterStartDate.value) queryParams += `&announcementDateFrom=${new Date(filterStartDate.value).toISOString()}`;
        if (filterEndDate.value) queryParams += `&announcementDateTo=${new Date(filterEndDate.value).toISOString()}`;
        if (filterKeyword.value) queryParams += `&keyword=${encodeURIComponent(filterKeyword.value)}`;
        if (filterIsEnabled.value) queryParams += `&isEnabled=${filterIsEnabled.value}`;
        if (filterIsImportant.value) queryParams += `&isImportant=${filterIsImportant.value}`;

        try {
            const response = await window.fetchAuthenticated(API_URL + queryParams);
            if (!response.ok) {
                // Try to parse error from backend if available
                let errorDetail = response.statusText;
                try {
                    const errJson = await response.json();
                    errorDetail = errJson.message || (errJson.error ? `${errJson.error} - ${errJson.detail}` : errorDetail);
                } catch (e) { /* ignore if not json */ }
                throw new Error(`HTTP error! status: ${response.status} - ${errorDetail}`);
            }
            const apiResult = await response.json();

            tableBody.innerHTML = ''; // Clear existing rows
            if (apiResult.code === 200 && apiResult.data && apiResult.data.list && apiResult.data.list.length > 0) {
                noDataMessage.classList.add('d-none');
                const announcements = apiResult.data.list; // Access list via apiResult.data.list
                announcements.forEach(ann => {
                    const row = tableBody.insertRow();
                    row.insertCell().textContent = ann.categoryDescription || '-';
                    row.insertCell().textContent = ann.title;
                    row.insertCell().textContent = ann.startTime ? new Date(ann.startTime).toLocaleString() : '-';
                    row.insertCell().textContent = ann.endTime ? new Date(ann.endTime).toLocaleString() : '-';
                    row.insertCell().innerHTML = ann.isEnabled ? '<span class="badge bg-success">是</span>' : '<span class="badge bg-danger">否</span>';
                    row.insertCell().innerHTML = ann.isImportant ? '<span class="badge bg-warning text-dark">是</span>' : '<span class="badge bg-secondary">否</span>';
                    row.insertCell().textContent = ann.updateTime ? new Date(ann.updateTime).toLocaleString() : '-';
                    
                    const actionsCell = row.insertCell();
                    actionsCell.classList.add('text-nowrap');
                    const viewBtn = document.createElement('a');
                    viewBtn.href = `announcement_form.html?id=${ann.announcementId}&mode=view`;
                    viewBtn.className = 'btn btn-sm btn-outline-info me-1';
                    viewBtn.innerHTML = '<i class="bi bi-eye"></i> 查看';
                    actionsCell.appendChild(viewBtn);

                    const editBtn = document.createElement('a');
                    editBtn.href = `announcement_form.html?id=${ann.announcementId}&mode=edit`;
                    editBtn.className = 'btn btn-sm btn-outline-primary me-1';
                    editBtn.innerHTML = '<i class="bi bi-pencil"></i> 編輯';
                    actionsCell.appendChild(editBtn);

                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'btn btn-sm btn-outline-danger';
                    deleteBtn.innerHTML = '<i class="bi bi-trash"></i> 刪除';
                    deleteBtn.onclick = () => confirmDelete(ann.announcementId, ann.title);
                    actionsCell.appendChild(deleteBtn);
                });
                renderPagination(apiResult.data.page.total, apiResult.data.page.page, apiResult.data.page.pageSize);
            } else {
                noDataMessage.classList.remove('d-none');
                tableBody.innerHTML = `<tr><td colspan="${tableBody.closest('table').querySelector('thead tr').cells.length}" class="text-center">查無公告</td></tr>`;
                paginationControls.innerHTML = '';
            }
        } catch (error) {
            console.error('Error fetching announcements:', error);
            tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">無法載入公告資料: ${error.message}</td></tr>`;
            noDataMessage.classList.add('d-none');
            paginationControls.innerHTML = ''; // Clear pagination on error
        }
    }

    function renderPagination(totalItems, currentPageBackend, itemsPerPage) {
        // currentPageBackend is 0-indexed from Spring Page, adjust if needed for display
        const currentPageForDisplay = currentPageBackend + 1; 
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        paginationControls.innerHTML = '';

        if (totalPages <= 1) return;

        // Previous button
        const prevLi = document.createElement('li');
        // Use currentPage which is 1-indexed for UI logic here
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        const prevLink = document.createElement('a');
        prevLink.className = 'page-link';
        prevLink.href = '#';
        prevLink.textContent = '上一頁';
        // currentPage is 0-indexed here from the fetchAnnouncements call
        prevLink.addEventListener('click', (e) => { e.preventDefault(); if (currentPage > 0) fetchAnnouncements(currentPage - 1); });
        prevLi.appendChild(prevLink);
        paginationControls.appendChild(prevLi);

        for (let i = 1; i <= totalPages; i++) { // i is 1-indexed for display
            const li = document.createElement('li');
            li.className = `page-item ${i === (currentPage + 1) ? 'active' : ''}`; // currentPage is 0-indexed, so +1 for comparison
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = i;
            link.addEventListener('click', (e) => { e.preventDefault(); fetchAnnouncements(i - 1); }); // Pass 0-indexed i-1
            li.appendChild(link);
            paginationControls.appendChild(li);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage >= totalPages - 1 ? 'disabled' : ''}`; // currentPage is 0-indexed
        const nextLink = document.createElement('a');
        nextLink.className = 'page-link';
        nextLink.href = '#';
        nextLink.textContent = '下一頁';
        // currentPage is 0-indexed
        nextLink.addEventListener('click', (e) => { e.preventDefault(); if (currentPage < totalPages - 1) fetchAnnouncements(currentPage + 1); });
        nextLi.appendChild(nextLink);
        paginationControls.appendChild(nextLi);
    }

    async function confirmDelete(id, title) {
        if (confirm(`確定要刪除公告 "${title}" 嗎？此操作無法復原。`)) {
            try {
                const response = await window.fetchAuthenticated(`${API_URL}/${id}`, { method: 'DELETE' });
                if (!response.ok) {
                    let errorMsg = `刪除失敗: ${response.status}`;
                    try {
                        const errData = await response.json();
                        errorMsg = errData.message || errorMsg;
                    } catch (e) { /* ignore */ }
                    throw new Error(errorMsg);
                }
                alert('公告已刪除。');
                fetchAnnouncements(currentPage); // Refresh the list
            } catch (error) {
                console.error('Error deleting announcement:', error);
                alert(`刪除公告時發生錯誤: ${error.message}`);
            }
        }
    }

    filterForm.addEventListener('submit', function(event) {
        event.preventDefault();
        fetchAnnouncements(0); // Fetch with new filters, reset to page 0
    });

    clearFiltersBtn.addEventListener('click', function() {
        filterForm.reset();
        fetchAnnouncements(0);
    });

    // Initial load
    async function loadInitialFiltersAndData() {
        await populateCategoryFilter(); // Wait for categories to load
        fetchAnnouncements(); // Then fetch announcements which might depend on filter values
    }

    loadInitialFiltersAndData();
}); 