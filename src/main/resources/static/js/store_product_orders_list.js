document.addEventListener('DOMContentLoaded', function () {
    initStoreOrderListPage();
});

async function initStoreOrderListPage() {
    await Promise.all([
        populateStoreSelect(),
        populateOrderStatusSelect()
    ]);

    const searchForm = document.getElementById('search-orders-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function (event) {
            event.preventDefault();
            searchOrders(1); // Search for the first page
        });
    }
    
    const resetBtn = document.getElementById('resetSearchBtn');
    if(resetBtn) {
        resetBtn.addEventListener('click', function() {
            searchForm.reset();
            searchOrders(1);
        });
    }

    searchOrders(1); // Initial search on page load
}

async function populateStoreSelect() {
    const selectElement = document.getElementById('searchStore');
    if (!selectElement) return;

    try {
        const response = await window.fetchAuthenticated('/api/v1/auth/operable-stores');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        
        const apiResponse = await response.json();
        if (apiResponse.code === 200 && Array.isArray(apiResponse.data)) {
            selectElement.innerHTML = '<option value="" selected>所有門市</option>'; // Reset
            apiResponse.data.forEach(store => {
                const option = document.createElement('option');
                option.value = store.storeId;
                option.textContent = store.storeName;
                selectElement.appendChild(option);
            });
        } else {
            console.error('Failed to populate stores, API response error:', apiResponse.message);
            selectElement.innerHTML = '<option value="">無法載入門市</option>';
        }
    } catch (error) {
        console.error('Error fetching operable stores:', error);
        if (selectElement) {
            selectElement.innerHTML = '<option value="">載入門市失敗</option>';
        }
    }
}

async function populateOrderStatusSelect() {
    const selectElement = document.getElementById('searchOrderStatus');
    if (!selectElement) return;
    
    selectElement.innerHTML = '<option value="" selected>所有狀態</option>'; 

    try {
        const response = await window.fetchAuthenticated('/api/v1/enums/order-statuses');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const apiResponse = await response.json();

        if (apiResponse.code === 200 && Array.isArray(apiResponse.data)) {
            // Filter statuses relevant for Store and Dispatch orders if needed.
            // For now, showing all.
            apiResponse.data.forEach(status => {
                const option = new Option(status.label, status.value);
                selectElement.add(option);
            });
        } else {
            console.error('Failed to populate order statuses, API response error:', apiResponse.message);
            selectElement.innerHTML = '<option value="">無法載入狀態</option>';
        }
    } catch (error) {
        console.error('Error fetching order statuses:', error);
        if (selectElement) {
            selectElement.innerHTML = '<option value="">載入狀態失敗</option>';
        }
    }
}

async function searchOrders(pageNumber = 1) {
    const storeId = document.getElementById('searchStore')?.value;
    const orderNumber = document.getElementById('searchOrderNumber')?.value;
    const customerKeyword = document.getElementById('searchCustomerKeyword')?.value;
    const orderStatusCode = document.getElementById('searchOrderStatus')?.value;
    const dateFrom = document.getElementById('searchOrderDateFrom')?.value;
    const dateTo = document.getElementById('searchOrderDateTo')?.value;
    const tableBody = document.getElementById('orders-table-body');
    const paginationControls = document.getElementById('pagination-controls');

    if (!tableBody || !paginationControls) return;

    tableBody.innerHTML = '<tr><td colspan="11" class="text-center p-4"><span class="spinner-border spinner-border-sm"></span> 查詢中...</td></tr>';
    paginationControls.innerHTML = '';

    const params = new URLSearchParams({
        page: pageNumber - 1,
        size: 10,
        sort: 'updateTime,desc'
    });

    if (storeId) params.append('storeId', storeId);
    if (orderNumber) params.append('orderNumber', orderNumber);
    if (customerKeyword) params.append('customerKeyword', customerKeyword);
    if (orderStatusCode) params.append('orderStatusCodes', orderStatusCode); // Note: API might expect plural
    if (dateFrom) params.append('orderDateFrom', new Date(dateFrom).toISOString());
    if (dateTo) {
        const endDate = new Date(dateTo);
        endDate.setHours(23, 59, 59, 999);
        params.append('orderDateTo', endDate.toISOString());
    }

    const companyDivisionCode = window.getCompanyDivisionCode();
    if (companyDivisionCode !== null) {
        params.append('companyDivisionCode', companyDivisionCode);
    } else {
        console.error("無法獲取公司別，查詢中止。");
        tableBody.innerHTML = `<tr><td colspan="11" class="text-center p-4 text-danger">錯誤: 無法確定公司別，請重新登入。</td></tr>`;
        return;
    }
    
    // We are querying for Store orders here
    params.append('orderTypeCode', '1'); //門市商品訂單


    try {
        const response = await window.fetchAuthenticated(`/api/v1/orders?${params.toString()}`);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        
        const apiResponse = await response.json();
        if (apiResponse.code === 200 && apiResponse.data) {
            renderTable(apiResponse.data.list);
            renderPagination(pageNumber, apiResponse.data.totalPages);
        } else {
            throw new Error(apiResponse.message || '查詢訂單失敗');
        }

    } catch (error) {
        console.error('查詢訂單時發生錯誤:', error);
        tableBody.innerHTML = `<tr><td colspan="11" class="text-center p-4 text-danger">查詢失敗: ${error.message}</td></tr>`;
    }
}

function renderTable(orders) {
    const tableBody = document.getElementById('orders-table-body');
    tableBody.innerHTML = '';

    if (!orders || orders.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="11" class="text-center p-4">查無資料</td></tr>';
        return;
    }

    orders.forEach(order => {
        const row = document.createElement('tr');
        const mainProduct = order.items && order.items.length > 0 ? order.items[0].productName : 'N/A';
        const totalAmount = order.grandTotalAmount != null ? `NT$ ${order.grandTotalAmount.toFixed(2)}` : 'N/A';
        
        row.innerHTML = `
            <td><a href="order_detail.html?orderId=${order.orderId}">${order.orderNumber}</a></td>
            <td>${order.orderDate ? new Date(order.orderDate).toLocaleDateString() : 'N/A'}</td>
            <td>${order.customerName || 'N/A'}</td>
            <td>${order.customerPhone || 'N/A'}</td>
            <td>${mainProduct}</td>
            <td>${totalAmount}</td>
            <td><span class="badge ${getPaymentStatusBadgeClass(order.paymentStatusCode)}">${order.paymentStatusDescription || 'N/A'}</span></td>
            <td><span class="badge ${getStatusBadgeClass(order.orderStatusCode)}">${order.orderStatusDescription || 'N/A'}</span></td>
            <td>${order.updatedByName || 'N/A'}</td>
            <td>${order.updateTime ? new Date(order.updateTime).toLocaleString() : 'N/A'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary view-btn" data-id="${order.orderId}" data-status="${order.orderStatusCode}">檢視</button>
            </td>
        `;
        tableBody.appendChild(row);
    });

    // Add event listeners to view buttons
    document.querySelectorAll('.view-btn').forEach(button => {
        button.addEventListener('click', function() {
            const orderId = this.dataset.id;
            const orderStatus = this.dataset.status;

            // Check if status is 'DRAFT' (code 10)
            if (orderStatus === '10') {
                window.location.href = `store_product_order_form.html?orderId=${orderId}`;
            } else {
                window.location.href = `order_detail.html?orderId=${orderId}`;
            }
        });
    });
}

function renderPagination(currentPage, totalPages) {
    const paginationControls = document.getElementById('pagination-controls');
    paginationControls.innerHTML = '';

    if (totalPages <= 1) return;

    const createPageItem = (text, page, isDisabled = false, isActive = false) => {
        const li = document.createElement('li');
        li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        if (!isDisabled) {
            a.onclick = (e) => {
                e.preventDefault();
                searchOrders(page);
            };
        }
        li.appendChild(a);
        return li;
    };

    paginationControls.appendChild(createPageItem('‹', currentPage - 1, currentPage === 1));
    for (let i = 1; i <= totalPages; i++) {
        paginationControls.appendChild(createPageItem(i, i, false, i === currentPage));
    }
    paginationControls.appendChild(createPageItem('›', currentPage + 1, currentPage === totalPages));
}


function getStatusBadgeClass(statusCode) {
    if(statusCode >= 70) return 'bg-success';
    if(statusCode < 0) return 'bg-danger';
    if(statusCode === 48 || statusCode === 30) return 'bg-primary';
    if(statusCode === 43 || statusCode === 46) return 'bg-warning text-dark';
    return 'bg-secondary';
}

function getPaymentStatusBadgeClass(paymentStatusCode) {
    // Assuming 0=Unpaid, 1=Paid, 2=Partial, 3=Refunded
    if(paymentStatusCode === 1) return 'bg-success'; // Paid
    if(paymentStatusCode === 3) return 'bg-info'; // Refunded
    if(paymentStatusCode === 0) return 'bg-danger'; // Unpaid
    if(paymentStatusCode === 2) return 'bg-warning text-dark'; // Partial
    return 'bg-light text-dark';
}

// End of script 