document.addEventListener('DOMContentLoaded', function () {
    const API_BASE_URL = '/api/v1/product-settings';
    const settingsTableBody = document.getElementById('settings-table-body');
    const noDataMessage = document.getElementById('no-data-message');
    const paginationControls = document.getElementById('pagination-controls');
    const filterForm = document.getElementById('filter-form');
    const clearFiltersBtn = document.getElementById('clear-filters-btn');

    const deleteConfirmModal = new bootstrap.Modal(document.getElementById('delete-confirm-modal'));
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    let currentSettingIdToDelete = null;

    let currentPage = 0;
    const pageSize = 10; // Should match PageableDefault in controller or be configurable
    const companyContext = localStorage.getItem('selectedCompanyContext'); // Get company context

    function updateTableHeaders() {
        const eastkingPriceCol = document.querySelector('.eastking-price-col');
        const queyouPriceCol = document.querySelector('.queyou-price-col');
        // const erpCompanyCol = document.querySelector('.erp-company-col'); // This column might always be useful

        if (eastkingPriceCol && queyouPriceCol) {
            if (companyContext === 'EASTKING') {
                eastkingPriceCol.classList.remove('d-none');
                queyouPriceCol.classList.add('d-none');
            } else if (companyContext === 'QUEYOU') {
                eastkingPriceCol.classList.add('d-none');
                queyouPriceCol.classList.remove('d-none');
            } else {
                // Default: show EastKing, hide QueYou if no specific context or unknown
                eastkingPriceCol.classList.remove('d-none');
                queyouPriceCol.classList.add('d-none');
            }
        }
    }

    async function fetchProductSettings(page = 0, queryParams = {}) {
        currentPage = page; // Update current page for pagination logic
        let url = `/api/v1/product-settings/dispatch-only?page=${page}&size=${pageSize}&sort=updateTime,desc`;
        Object.keys(queryParams).forEach(key => {
            if (queryParams[key]) {
                url += `&${key}=${encodeURIComponent(queryParams[key])}`;
            }
        });

        try {
            // Use window.fetchAuthenticated to include necessary headers (JWT, X-Company-Context)
            const response = await window.fetchAuthenticated(url);
            
            if (!response.ok) {
                // fetchAuthenticated already handles 401 redirection.
                // For other errors, try to parse a message.
                if (response.status === 403) {
                    showToast('權限不足，無法查看商品設定。', 'error');
                } else {
                    const errData = await response.json().catch(() => ({ message: `HTTP error! status: ${response.status}` }));
                    showToast(errData.message || '無法載入商品設定', 'error');
                }
                renderTable([]);
                renderPagination(null);
                return; // Stop further processing
            }

            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.list) {
                renderTable(apiResponse.data.list);
                renderPagination(apiResponse.data.page);
            } else {
                showToast(apiResponse.message || '無法載入商品設定', 'error');
                renderTable([]);
                renderPagination(null);
            }
        } catch (error) {
            console.error('Fetch error:', error);
            showToast('載入商品設定失敗，請稍後再試', 'error');
            renderTable([]);
            renderPagination(null);
        }
    }

    function renderTable(settings) {
        settingsTableBody.innerHTML = ''; // Clear existing rows
        if (!settings || settings.length === 0) {
            noDataMessage.classList.remove('d-none');
            return;
        }
        noDataMessage.classList.add('d-none');

        settings.forEach((setting, index) => {
            const row = settingsTableBody.insertRow();
            row.insertCell().textContent = (currentPage * pageSize) + index + 1;
            row.insertCell().textContent = setting.productBarcode || '-';
            row.insertCell().textContent = setting.productName || '-';
            row.insertCell().textContent = setting.erpCompanyDivisionDescription || '-'; // ERP Company
            row.insertCell().textContent = setting.warrantyPeriodDescription || (setting.warrantyMonths !== null ? `${setting.warrantyMonths} 個月` : '-');
            
            let salePriceToShow = '-';
            if (companyContext === 'EASTKING' && setting.listPrice !== null) {
                salePriceToShow = parseFloat(setting.listPrice).toLocaleString('en-US', { style: 'currency', currency: 'TWD' });
            } else if (companyContext === 'QUEYOU' && setting.queyouListPrice !== null) {
                salePriceToShow = parseFloat(setting.queyouListPrice).toLocaleString('en-US', { style: 'currency', currency: 'TWD' });
            } else if (setting.listPrice !== null) { // Fallback to EastKing if no context or product doesn't have specific QueYou price
                salePriceToShow = parseFloat(setting.listPrice).toLocaleString('en-US', { style: 'currency', currency: 'TWD' });
            }
            
            const eastkingPriceCell = row.insertCell();
            eastkingPriceCell.textContent = (companyContext === 'EASTKING' || !companyContext) && setting.listPrice !== null ? parseFloat(setting.listPrice).toLocaleString('en-US', { style: 'currency', currency: 'TWD' }) : '-';
            eastkingPriceCell.classList.add('eastking-price-col');

            // const queyouPriceCell = row.insertCell();
            // queyouPriceCell.textContent = companyContext === 'QUEYOU' && setting.queyouListPrice !== null ? parseFloat(setting.queyouListPrice).toLocaleString('en-US', { style: 'currency', currency: 'TWD' }) : '-';
            // queyouPriceCell.classList.add('queyou-price-col');

            updateTableHeaders(); // Call this after adding cells to ensure classes are applied correctly for visibility

            row.insertCell().innerHTML = setting.isActive ? '<span class="badge bg-success">啟用</span>' : '<span class="badge bg-danger">停用</span>';
            row.insertCell().textContent = setting.updateTime ? new Date(setting.updateTime).toLocaleString() : '-';
            
            const actionsCell = row.insertCell();
            actionsCell.classList.add('action-column');
            actionsCell.innerHTML = `
                <a href="product_setting_form.html?id=${setting.productSettingId}&mode=edit" class="btn btn-sm btn-outline-primary" title="編輯">
                    <i class="bi bi-pencil-square"></i>
                </a>
                <button class="btn btn-sm btn-outline-danger delete-btn" data-id="${setting.productSettingId}" title="刪除">
                    <i class="bi bi-trash"></i>
                </button>
            `;
        });
        addDeleteEventListeners();
    }
    
    function addDeleteEventListeners() {
        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', function() {
                currentSettingIdToDelete = this.dataset.id;
                deleteConfirmModal.show();
            });
        });
    }

    confirmDeleteBtn.addEventListener('click', async function() {
        if (!currentSettingIdToDelete) return;
        try {
            const response = await fetch(`${API_BASE_URL}/${currentSettingIdToDelete}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('jwtToken')}`
                }
            });
            if (response.ok) {
                showToast('商品設定已成功刪除', 'success');
                fetchProductSettings(currentPage, getCurrentFilters());
            } else {
                 if (response.status === 401 || response.status === 403) {
                    showToast('權限不足或登入逾時，請重新登入。', 'error');
                    localStorage.removeItem('jwtToken');
                    window.location.href = 'login.html';
                } else {
                    const errorData = await response.json();
                    showToast(errorData.message || '刪除失敗', 'error');
                }
            }
        } catch (error) {
            console.error('Delete error:', error);
            showToast('刪除商品設定時發生錯誤', 'error');
        }
        deleteConfirmModal.hide();
        currentSettingIdToDelete = null;
    });

    function renderPagination(pageData) {
        paginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 1) return;

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${pageData.page === 0 ? 'disabled' : ''}`;
        const prevLink = document.createElement('a');
        prevLink.className = 'page-link';
        prevLink.href = '#';
        prevLink.textContent = '上一頁';
        prevLink.addEventListener('click', (e) => { e.preventDefault(); if (pageData.page > 0) fetchProductSettings(pageData.page - 1, getCurrentFilters()); });
        prevLi.appendChild(prevLink);
        paginationControls.appendChild(prevLi);

        // Page numbers (simplified)
        for (let i = 0; i < pageData.totalPages; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === pageData.page ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = i + 1;
            link.addEventListener('click', (e) => { e.preventDefault(); fetchProductSettings(i, getCurrentFilters()); });
            li.appendChild(link);
            paginationControls.appendChild(li);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${pageData.page === pageData.totalPages - 1 ? 'disabled' : ''}`;
        const nextLink = document.createElement('a');
        nextLink.className = 'page-link';
        nextLink.href = '#';
        nextLink.textContent = '下一頁';
        nextLink.addEventListener('click', (e) => { e.preventDefault(); if (pageData.page < pageData.totalPages - 1) fetchProductSettings(pageData.page + 1, getCurrentFilters()); });
        nextLi.appendChild(nextLink);
        paginationControls.appendChild(nextLi);
    }
    
    function getCurrentFilters() {
        return {
            productBarcode: document.getElementById('filterProductBarcode').value,
            productName: document.getElementById('filterProductName').value,
            startDate: document.getElementById('filterStartDate').value ? new Date(document.getElementById('filterStartDate').value).toISOString() : '',
            endDate: document.getElementById('filterEndDate').value ? new Date(document.getElementById('filterEndDate').value).toISOString() : ''
        };
    }

    filterForm.addEventListener('submit', function(event) {
        event.preventDefault();
        currentPage = 0; // Reset to first page on new filter
        fetchProductSettings(currentPage, getCurrentFilters());
    });

    clearFiltersBtn.addEventListener('click', function() {
        filterForm.reset();
        currentPage = 0;
        fetchProductSettings(currentPage);
    });

    // Initial fetch
    fetchProductSettings();
    updateTableHeaders(); // Initial call to set headers based on context
}); 