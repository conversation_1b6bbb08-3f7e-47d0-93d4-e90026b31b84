document.addEventListener('DOMContentLoaded', function () {
    const storeSelectForm = document.getElementById('select-store-form');
    const storeSelectElement = document.getElementById('storeSelect');
    const errorMessageDiv = document.getElementById('error-message-store');
    const backToLoginBtn = document.getElementById('backToLoginBtn');

    // Check for token and available stores
    const jwtToken = localStorage.getItem('jwtToken');
    const availableStoresString = localStorage.getItem('availableStores');

    if (!jwtToken) {
        // If no token, should not be on this page, redirect to login
        window.location.href = 'login.html';
        return;
    }

    if (!availableStoresString) {
        showError('沒有可選擇的門市資訊。');
        // Potentially redirect or disable form
        return;
    }

    try {
        const availableStores = JSON.parse(availableStoresString);
        if (Array.isArray(availableStores) && availableStores.length > 0) {
            availableStores.forEach(store => {
                const option = document.createElement('option');
                option.value = store.storeId; // Assuming StoreInfo has storeId and storeName
                option.textContent = store.storeName;
                storeSelectElement.appendChild(option);
            });
        } else {
            showError('門市列表格式不正確或為空。');
        }
    } catch (e) {
        console.error('Error parsing available stores:', e);
        showError('讀取門市列表時發生錯誤。');
    }

    storeSelectForm.addEventListener('submit', function(event) {
        event.preventDefault();
        const selectedStoreId = storeSelectElement.value;

        if (!selectedStoreId) {
            showError('請選擇一個門市。');
            return;
        }

        // TODO: Potentially make an API call to backend to confirm store selection
        // and get updated permissions or specific store-related JWT/info if needed.
        // For now, we assume the initial JWT is sufficient and we just store the selection locally.

        const selectedStore = JSON.parse(availableStoresString).find(s => s.storeId === selectedStoreId);
        if (selectedStore) {
            localStorage.setItem('selectedStore', JSON.stringify(selectedStore));
            localStorage.setItem('selectedCompanyContext', selectedStore.companyDivisionName);

            console.log('Store selected:', selectedStore);
            console.log('Company context set to:', selectedStore.companyDivisionName);
            window.location.href = 'home.html'; // Proceed to home page
        } else {
            showError('選擇的門市無效，請重新整理。');
        }
    });

    backToLoginBtn.addEventListener('click', function() {
        // Clear stored login data if going back to login
        localStorage.removeItem('jwtToken');
        localStorage.removeItem('sessionUuid');
        localStorage.removeItem('userName');
        localStorage.removeItem('userRoles');
        localStorage.removeItem('availableStores');
        localStorage.removeItem('selectedStore');
        localStorage.removeItem('selectedCompanyContext');
        window.location.href = 'login.html';
    });

    function showError(message) {
        errorMessageDiv.textContent = message;
        errorMessageDiv.classList.remove('d-none');
    }
}); 