document.addEventListener('DOMContentLoaded', function () {
    // --- DOM Elements ---
    const infoCardContainer = document.getElementById('info-card-container');
    const handlingForm = document.getElementById('handling-form');
    const backBtn = document.getElementById('back-btn');
    const detailBreadcrumbLink = document.getElementById('detail-breadcrumb-link');

    // --- State ---
    const urlParams = new URLSearchParams(window.location.search);
    const dispatchRepairId = urlParams.get('id');
    let originalOrderData = null;

    // --- Initialization ---
    async function initializePage() {
        if (!dispatchRepairId) { /* ... */ return; }

        if(detailBreadcrumbLink) detailBreadcrumbLink.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;
        
        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}`);
            if (!response.ok) throw new Error('無法獲取派工單資料');
            
            originalOrderData = await response.json();
            
            window.renderDispatchOrderHeader(originalOrderData, infoCardContainer);

            if (originalOrderData.handlingMethod) {
                document.getElementById('handlingMethod').value = originalOrderData.handlingMethod;
            }
            
            if (originalOrderData.statusCode !== 80) { // 80: 處理方式
                handlingForm.querySelectorAll('textarea, button[type="submit"]').forEach(el => el.disabled = true);
                if(window.showToast) window.showToast('此派工單狀態已變更，不可編輯。', 'warning');
            }

        } catch (error) {
            console.error('頁面初始化失敗:', error);
            infoCardContainer.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
        }
    }
    
    // --- Event Handling ---
    function setupEventListeners() {
        document.getElementById('handling-form').addEventListener('submit', handleFormSubmit);
        // ...
    }
    
    async function handleFormSubmit(event) {
        event.preventDefault();
        const handlingMethod = document.getElementById('handlingMethod').value;
        
        const payload = { handlingMethod: handlingMethod };
        const completeBtn = document.getElementById('complete-btn');
        completeBtn.disabled = true;
        completeBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 處理中...';

        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}/submit-handling`, {
                method: 'POST',
                body: JSON.stringify(payload)
            });
            
            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '操作失敗');
            }

            showToast('處理方式已提交！', 'success');
            setTimeout(() => { window.location.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`; }, 1500);

        } catch (error) {
            console.error('提交處理方式失敗:', error);
            showToast(`操作失敗: ${error.message}`, 'danger');
            completeBtn.disabled = false;
            completeBtn.innerHTML = '提交';
        }
    }
    
    initializePage();
    setupEventListeners();
}); 