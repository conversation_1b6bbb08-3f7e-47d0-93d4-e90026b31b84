// JavaScript for document_clause_form.html

document.addEventListener('DOMContentLoaded', function () {
    console.log('document_clause_form.js loaded');

    if (typeof window.fetchAuthenticated !== 'function') {
        console.error('Main.js or fetchAuthenticated function not loaded');
        return;
    }

    const form = document.getElementById('document-clause-form');
    const clauseIdInput = document.getElementById('documentClauseId');
    const clauseTitleInput = document.getElementById('clauseTitle');
    const clauseContentTextarea = document.getElementById('clauseContent');
    const isDefaultTrueRadio = document.getElementById('isDefaultTrue');
    const isDefaultFalseRadio = document.getElementById('isDefaultFalse');
    const startTimeInput = document.getElementById('startTime');
    const endTimeInput = document.getElementById('endTime');
    const isActiveSwitch = document.getElementById('isActive');
    const sequenceOrderInput = document.getElementById('sequenceOrder');
    const pageMainTitle = document.getElementById('page-main-title');
    const formTitleBreadcrumb = document.getElementById('form-title');

    const urlParams = new URLSearchParams(window.location.search);
    const currentClauseId = urlParams.get('id');
    const isEditMode = !!currentClauseId;

    // 格式化日期時間為後端期望的格式 (yyyy-MM-dd'T'HH:mm:ssXXX)
    function formatDateTimeForBackend(dateTimeLocalValue) {
        if (!dateTimeLocalValue) return null;

        // dateTimeLocalValue 格式: "2025-08-16T15:42"
        // 需要轉換為: "2025-08-16T15:42:00+08:00"

        // 創建Date對象，這會使用本地時區
        const date = new Date(dateTimeLocalValue);

        // 使用 Intl.DateTimeFormat 來格式化為 ISO 8601 格式，包含時區偏移量
        // 但由於瀏覽器支援度問題，我們使用手動格式化

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        // 獲取時區偏移量
        const timezoneOffset = -date.getTimezoneOffset(); // 注意：getTimezoneOffset返回的是相反的值
        const offsetHours = Math.floor(Math.abs(timezoneOffset) / 60);
        const offsetMinutes = Math.abs(timezoneOffset) % 60;
        const offsetSign = timezoneOffset >= 0 ? '+' : '-';
        const offsetString = `${offsetSign}${String(offsetHours).padStart(2, '0')}:${String(offsetMinutes).padStart(2, '0')}`;

        return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${offsetString}`;
    }

    async function initializeForm() {
        if (isEditMode) {
            pageMainTitle.textContent = '編輯表尾條文';
            formTitleBreadcrumb.textContent = '編輯表尾條文';
            await loadClauseForEditing(currentClauseId);
        } else {
            pageMainTitle.textContent = '新增表尾條文';
            formTitleBreadcrumb.textContent = '新增表尾條文';
            isActiveSwitch.checked = true; // Default to active for new clauses
            isDefaultFalseRadio.checked = true; // Default to not default
        }
    }

    async function loadClauseForEditing(id) {
        try {
            const response = await window.fetchAuthenticated(`/api/v1/document-clauses/${id}`);
            if (!response.ok) {
                throw new Error(`Failed to load clause: ${response.statusText}`);
            }
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                const clause = apiResponse.data;
                clauseIdInput.value = clause.documentClauseId;
                clauseTitleInput.value = clause.clauseTitle;
                clauseContentTextarea.value = clause.clauseContent;
                if (clause.isDefault) {
                    isDefaultTrueRadio.checked = true;
                } else {
                    isDefaultFalseRadio.checked = true;
                }
                startTimeInput.value = clause.startTime ? clause.startTime.slice(0, 16) : '';
                endTimeInput.value = clause.endTime ? clause.endTime.slice(0, 16) : '';
                isActiveSwitch.checked = clause.isActive === null ? true : clause.isActive; // Default to true if null from backend
                sequenceOrderInput.value = clause.sequenceOrder || 0;
            } else {
                window.showToast(`載入條文資料失敗: ${apiResponse.message || '未知錯誤'}`, 'error');
            }
        } catch (error) {
            console.error('Error loading clause data:', error);
            window.showToast('載入條文資料時發生錯誤', 'error');
        }
    }

    if (form) {
        form.addEventListener('submit', async function(event) {
            event.preventDefault();
            event.stopPropagation();

            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                return;
            }
            form.classList.add('was-validated');

            // 獲取提交按鈕並設置載入狀態
            const saveBtn = document.getElementById('saveBtn');
            const originalBtnText = saveBtn.textContent;
            const originalBtnDisabled = saveBtn.disabled;

            try {
                // 檢查公司別代碼
                const companyDivisionCode = window.getCompanyDivisionCode();
                if (companyDivisionCode === null || companyDivisionCode === undefined) {
                    window.showToast('無法取得公司別資訊，請重新登入', 'error');
                    return;
                }

                // 設置載入狀態
                saveBtn.disabled = true;
                saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>處理中...';

                // 顯示處理中訊息
                window.showToast('正在儲存表尾條文...', 'info');

                const clauseData = {
                    companyDivisionCode: companyDivisionCode,
                    clauseTitle: clauseTitleInput.value,
                    clauseContent: clauseContentTextarea.value,
                    isDefault: isDefaultTrueRadio.checked,
                    startTime: startTimeInput.value ? formatDateTimeForBackend(startTimeInput.value) : null,
                    endTime: endTimeInput.value ? formatDateTimeForBackend(endTimeInput.value) : null,
                    isActive: isActiveSwitch.checked,
                    sequenceOrder: parseInt(sequenceOrderInput.value) || 0
                };

                // 調試信息：檢查數據格式
                console.log('Document clause data:', {
                    companyDivisionCode: clauseData.companyDivisionCode,
                    startTimeInput: startTimeInput.value,
                    endTimeInput: endTimeInput.value,
                    formattedStartTime: clauseData.startTime,
                    formattedEndTime: clauseData.endTime,
                    isEditMode: isEditMode
                });

                if (isEditMode) {
                    clauseData.documentClauseId = currentClauseId;
                }

                const url = isEditMode ? `/api/v1/document-clauses/${currentClauseId}` : '/api/v1/document-clauses';
                const method = isEditMode ? 'PUT' : 'POST';

                const response = await window.fetchAuthenticated(url, {
                    method: method,
                    body: JSON.stringify(clauseData)
                });
                const apiResponse = await response.json();

                if (response.ok && (apiResponse.code === 200 || apiResponse.code === 201)) {
                    window.showToast(`✅ 表尾條文已${isEditMode ? '更新' : '新增'}成功！即將返回列表頁面`, 'success');

                    // 延遲跳轉，讓用戶看到成功訊息
                    setTimeout(() => {
                        window.location.href = 'document_clause_list.html';
                    }, 1500);
                } else {
                    throw new Error(apiResponse.message || response.statusText);
                }
            } catch (error) {
                console.error('Error saving clause:', error);
                const errorMessage = error.message || '儲存表尾條文時發生錯誤';
                window.showToast(`❌ 操作失敗: ${errorMessage}`, 'error');
            } finally {
                // 恢復按鈕狀態
                saveBtn.disabled = originalBtnDisabled;
                saveBtn.textContent = originalBtnText;
            }
        });
    }

    const cancelBtn = document.getElementById('cancelBtn');
    if(cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            // 檢查表單是否有未儲存的變更
            const hasChanges = checkForUnsavedChanges();

            if (hasChanges) {
                const confirmed = confirm('您有未儲存的變更，確定要離開嗎？');
                if (!confirmed) {
                    return;
                }
            }

            window.location.href = 'document_clause_list.html';
        });
    }

    // 檢查是否有未儲存的變更
    function checkForUnsavedChanges() {
        if (!isEditMode) {
            // 新增模式：檢查是否有任何輸入
            return clauseTitleInput.value.trim() !== '' ||
                   clauseContentTextarea.value.trim() !== '' ||
                   startTimeInput.value !== '' ||
                   endTimeInput.value !== '' ||
                   parseInt(sequenceOrderInput.value) !== 0;
        }
        // 編輯模式的變更檢查可以在這裡實現
        // 目前簡化處理，總是提示確認
        return true;
    }

    initializeForm();
}); 