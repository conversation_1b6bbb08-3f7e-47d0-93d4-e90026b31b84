let currentCustomerId = null;
let addressComponent = null; // 將地址元件實例設為全域變數
let originalMemberData = {}; // 用於儲存原始的會員資料

document.addEventListener('DOMContentLoaded', async function () {
    const API_BASE_URL = '/api/v1/customers';
    const form = document.getElementById('member-form');
    const memberLevelSelect = document.getElementById('memberLevel');
    const maritalStatusSelect = document.getElementById('maritalStatus');
    const occupationSelect = document.getElementById('occupation');
    const segmentsContainer = document.getElementById('customer-segments-container');
    const formTitle = document.getElementById('form-title');
    const pageMainTitle = document.getElementById('page-main-title');
    
    const urlParams = new URLSearchParams(window.location.search);
    currentCustomerId = urlParams.get('customerId');
    const isEditMode = !!currentCustomerId;

    // 初始化地址元件
    addressComponent = new AddressComponent('address-component-placeholder');
    
    // 監聽地址元件的變動，並更新"完整地址"欄位
    document.getElementById('address-component-placeholder').addEventListener('change', () => {
        const addressData = addressComponent.getData();
        document.getElementById('fullAddress').value = addressData.fullAddress;
    });

    async function initializeForm() {
        // 在編輯模式下，先獲取客戶資料
        if (isEditMode) {
            formTitle.textContent = '修改會員資料';
            pageMainTitle.textContent = '修改會員資料';
            await loadCustomerData(currentCustomerId); // 這會填充 originalMemberData
        } else {
            formTitle.textContent = '新增會員';
            pageMainTitle.textContent = '新增會員';
            document.querySelector('#genderMale').checked = true;
            document.querySelector('#marketingYes').checked = true;

            // 在新增模式下，為公司別和會員等級設定預設值
            const companyContext = localStorage.getItem('selectedCompanyContext');
            const companyDivisionCode = companyContext === 'QUEYOU' ? 1 : 0;
            originalMemberData.companyDivisionCode = companyDivisionCode;
        }

        // 使用獲取到的資料（如果存在）來填充下拉選單
        await Promise.all([
            populateDropdown('/api/v1/member-levels/selectable', memberLevelSelect, originalMemberData.memberLevelId, 'memberLevelId', 'levelName'),
            populateDropdown('/api/v1/enums/marital-statuses', maritalStatusSelect, originalMemberData.maritalStatusCode),
            populateDropdown('/api/v1/enums/occupations', occupationSelect, originalMemberData.occupationCode),
            populateCheckboxes('/api/v1/customer-segments/active', segmentsContainer)
        ]);

        if (!isEditMode) {
            // 新增模式下，找到"一般會員"並設為預設值
            const options = memberLevelSelect.options;
            for (let i = 0; i < options.length; i++) {
                if (options[i].text === '一般會員') {
                    memberLevelSelect.value = options[i].value;
                    break;
                }
            }
        }

        // 再次填充表單，確保下拉選單之外的欄位也被正確設置
        if(isEditMode) {
            populateFormFields(originalMemberData);
        }
    }

    async function populateDropdown(url, selectElement, selectedValue = null, valueKey = 'code', textKey = 'description') {
        try {
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) throw new Error(`Failed to load options for ${selectElement.id}`);
            const result = await response.json();
            if (result.code === 200 && Array.isArray(result.data)) {
                selectElement.innerHTML = '<option value="">請選擇...</option>'; // Default empty option
                result.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item[valueKey];
                    option.textContent = item[textKey];
                    selectElement.appendChild(option);
                });
                
                if (selectedValue) {
                    selectElement.value = selectedValue;
                }
            }
        } catch (error) {
            console.error(error.message);
        }
    }

    async function populateCheckboxes(url, container) {
        if (!container) return;
        try {
            const response = await fetchAuthenticated(url);
            if (!response.ok) throw new Error('無法載入客群管道選項');
            
            const apiResponse = await response.json();
            const segments = apiResponse.data; // Assuming it returns a list directly now

            if (segments && segments.length > 0) {
                container.innerHTML = ''; // Clear existing
                segments.forEach(segment => {
                    const div = document.createElement('div');
                    div.className = 'form-check form-check-inline';
                    
                    const input = document.createElement('input');
                    input.className = 'form-check-input';
                    input.type = 'checkbox';
                    input.value = segment.segmentId;
                    input.id = `segment-${segment.segmentId}`;
                    input.name = 'customerSegments';

                    const label = document.createElement('label');
                    label.className = 'form-check-label';
                    label.htmlFor = input.id;
                    label.textContent = segment.segmentName;

                    div.appendChild(input);
                    div.appendChild(label);
                    container.appendChild(div);
                });
            } else {
                container.textContent = '沒有可用的客群管道';
            }
        } catch (error) {
            console.error('載入客群管道失敗:', error);
            container.textContent = '載入失敗';
        }
    }

    async function loadCustomerData(id) {
        try {
            const response = await fetchAuthenticated(`${API_BASE_URL}/${id}`);
            if (!response.ok) throw new Error('無法載入會員資料');
            const apiResponse = await response.json();
            if (apiResponse.data) {
                originalMemberData = apiResponse.data; // 儲存原始資料
                populateFormFields(originalMemberData);
            } else {
                showToast('找不到指定的會員資料', 'error');
            }
        } catch (error) {
            console.error('載入會員資料失敗:', error);
            showToast(`載入會員資料失敗: ${error.message}`, 'danger');
        }
    }

    function populateFormFields(customer) {
        // --- Basic Info ---
        document.getElementById('customerName').value = customer.customerName || '';
        if (customer.memberLevelId) {
            document.getElementById('memberLevel').value = customer.memberLevelId;
        }
        if (customer.genderCode !== null) {
            const genderRadio = document.querySelector(`input[name="gender"][value="${customer.genderCode}"]`);
            if (genderRadio) genderRadio.checked = true;
        }
        if (customer.birthDate) {
            document.getElementById('birthDate').value = customer.birthDate;
        }
        if (customer.maritalStatusCode !== null) {
            document.getElementById('maritalStatus').value = customer.maritalStatusCode;
        }

        // --- Contact Info ---
        document.getElementById('homePhone').value = customer.homePhone || '';
        document.getElementById('phoneNumber').value = customer.phoneNumber || '';
        document.getElementById('email').value = customer.email || '';
        
        // 填充地址元件
        if (addressComponent) {
            addressComponent.setData(customer);
            // 手動觸發一次change事件以更新完整地址欄位
            setTimeout(() => {
                document.getElementById('address-component-placeholder').dispatchEvent(new Event('change'));
            }, 500); // 延遲以確保連動下拉選單已載入
        }

        // --- Professional & Preferences ---
        if (customer.occupationCode) {
            document.getElementById('occupation').value = customer.occupationCode;
        }
        document.getElementById('jobTitle').value = customer.jobTitle || '';
        document.getElementById('interests').value = customer.interests || '';
        if (customer.acceptsMarketingInfo !== null) {
            const marketingRadio = document.querySelector(`input[name="acceptsMarketing"][value="${customer.acceptsMarketingInfo}"]`);
            if (marketingRadio) marketingRadio.checked = true;
        }
        
        // --- Segments ---
        if (customer.segments && customer.segments.length > 0) {
            customer.segments.forEach(segment => {
                const checkbox = document.querySelector(`input[name="customerSegments"][value="${segment.segmentId}"]`);
                if (checkbox) checkbox.checked = true;
            });
        }

        // --- Remarks ---
        document.getElementById('remarks').value = customer.remarks || '';
    }

    async function handleFormSubmit(event) {
        event.preventDefault();

        const selectedSegments = Array.from(document.querySelectorAll('input[name="customerSegments"]:checked'))
                                      .map(cb => ({ segmentId: cb.value }));

        const addressData = addressComponent.getData();
        if (!addressData.addressCityName || !addressData.addressDistrictName) {
            showToast('請至少選擇縣市和鄉鎮市區。', 'error');
            return;
        }

        const payload = {
            customerId: currentCustomerId,
            memberLevelId: document.getElementById('memberLevel').value || null, // 從下拉選單直接獲取
            companyDivisionCode: originalMemberData.companyDivisionCode, // 從原始資料中獲取
            customerName: document.getElementById('customerName').value,
            genderCode: document.querySelector('input[name="gender"]:checked').value,
            birthDate: document.getElementById('birthDate').value || null,
            maritalStatusCode: document.getElementById('maritalStatus').value,
            homePhone: document.getElementById('homePhone').value,
            phoneNumber: document.getElementById('phoneNumber').value,
            email: document.getElementById('email').value,
            occupationCode: document.getElementById('occupation').value,
            jobTitle: document.getElementById('jobTitle').value,
            interests: document.getElementById('interests').value,
            acceptsMarketingInfo: document.querySelector('input[name="acceptsMarketing"]:checked').value,
            remarks: document.getElementById('remarks').value,
            segments: selectedSegments,
            ...addressData
        };

        const url = isEditMode ? `${API_BASE_URL}/${currentCustomerId}` : API_BASE_URL;
        const method = isEditMode ? 'PUT' : 'POST';

        try {
            const response = await window.fetchAuthenticated(url, {
                method: method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            const result = await response.json();
            if (response.ok && (result.code === 200 || result.code === 201)) {
                showToast('會員資料儲存成功！', 'success');
                setTimeout(() => window.location.href = 'member_list.html', 1500);
            } else {
                throw new Error(result.message || '儲存失敗');
            }
        } catch (error) {
            console.error('儲存會員失敗:', error);
            showToast(`儲存失敗: ${error.message}`, 'error');
        }
    }

    form.addEventListener('submit', handleFormSubmit);
    
    initializeForm();
}); 