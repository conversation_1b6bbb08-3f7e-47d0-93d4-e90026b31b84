// Fallback definitions for functions from main.js
if (typeof fetchWithAuth === 'undefined') {
    console.warn('fetchWithAuth not defined, creating a local fallback.');
    window.fetchWithAuth = function(url, options = {}) {
        const token = localStorage.getItem('jwtToken');
        const headers = { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json', ...options.headers };
        return fetch(url, { ...options, headers });
    };
}
if (typeof showToast === 'undefined') {
    console.warn('showToast not defined, creating a local fallback.');
    window.showToast = function(message, type = 'info') { alert(`[${type}] ${message}`); };
}
if (typeof loadSidebarAndHeader === 'undefined') {
    console.warn('loadSidebarAndHeader not defined, creating a local fallback.');
    window.loadSidebarAndHeader = function() { console.log("Sidebar/Header loading handled by main.js"); };
}

document.addEventListener('DOMContentLoaded', async () => {
    loadSidebarAndHeader();
        const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('id');
    const actionButtons = document.getElementById('action-buttons');
    const completeCollectionBtn = document.getElementById('complete-collection-btn');

    if (!orderId) {
        document.getElementById('detail-view').innerHTML = '<p class="text-danger">未提供領料單ID。</p>';
            return;
        }
        
        try {
        const response = await fetchWithAuth(`/api/v1/material-orders/${orderId}`);
            const result = await response.json();

        if (result.code === 200) {
            const order = result.data;
            
            const setText = (id, text) => {
                const el = document.getElementById(id);
                if (el) el.textContent = text || '';
            };

            setText('order-number', order.materialOrderNumber);
            setText('create-time', new Date(order.createTime).toLocaleString());
            setText('technician-name', order.requestingTechnicianName);
            setText('warehouse-name', order.targetWarehouseName);
            setText('remarks', order.remarks);

            const statusBadge = document.getElementById('order-status-badge');
            statusBadge.textContent = order.materialOrderStatusDescription;
            statusBadge.className = `badge ${getStatusBadgeClass(order.materialOrderStatusCode)}`;

            const itemList = document.getElementById('item-list');
            if(itemList) {
                itemList.innerHTML = '';
                order.items.forEach(item => {
                    const row = `<tr>
                            <td>${item.dispatchRepairNumber || '-'}</td>
                            <td>${item.productBarcode}</td>
                            <td>${item.productName}</td>
                            <td>${item.requestedQuantity}</td>
                        <td>${item.pickedQuantity || 0}</td>
                        <td>${item.collectedQuantity || 0}</td>
                    </tr>`;
                    itemList.innerHTML += row;
                });
            }
            
            // Display logic for the button
            if (order.materialOrderStatusCode === 20) { // 20: PICKING_COMPLETED
                actionButtons.style.display = 'block'; 
            }

        } else {
            document.getElementById('detail-view').innerHTML = `<p class="text-danger">載入失敗: ${result.message}</p>`;
        }
    } catch (error) {
        console.error('Error fetching details:', error);
        document.getElementById('detail-view').innerHTML = '<p class="text-danger">載入領料單詳情時發生錯誤。</p>';
    }

    function getStatusBadgeClass(statusCode) {
        switch (statusCode) {
            case 10: // 待揀料
                return 'bg-secondary';
            case 20: // 已揀料
                return 'bg-primary';
            case 30: // 領料完成
                return 'bg-success';
            case 40: // 已取消
                return 'bg-danger';
            default:
                return 'bg-info text-dark';
        }
    }
    
    completeCollectionBtn.addEventListener('click', async () => {
        try {
            const response = await fetchWithAuth(`/api/v1/material-orders/${orderId}/complete-collection`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.code === 200) {
                showToast('領料完成！', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast(result.message || '操作失敗', 'error');
            }
        } catch (error) {
            console.error('Error completing collection:', error);
            showToast('操作時發生錯誤', 'error');
        }
    });
}); 