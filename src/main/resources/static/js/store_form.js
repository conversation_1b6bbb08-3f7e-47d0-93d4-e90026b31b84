// js/store_form.js // Path comment updated
document.addEventListener('DOMContentLoaded', function () {
    const storeForm = document.getElementById('store-form');
    const formTitle = document.getElementById('form-title');
    const formModeBreadcrumb = document.getElementById('form-mode-breadcrumb');
    const storeIdInput = document.getElementById('storeId');
    const storeNameInput = document.getElementById('storeName');
    const storeCodeInput = document.getElementById('storeCode');
    // const regionNameInput = document.getElementById('regionName'); // This was likely an error, using regionNameDisplayInput
    const regionIdInput = document.getElementById('regionId');
    const isActiveYesRadio = document.getElementById('isActiveYes');
    const isActiveNoRadio = document.getElementById('isActiveNo');
    const storeAddressInput = document.getElementById('storeAddress');
    const storePhoneInput = document.getElementById('storePhone');
    const erpCompanyDivisionField = document.getElementById('erpCompanyDivision');

    const storeStaffTableBody = document.getElementById('storeStaffTableBody');
    const substituteStaffTableBody = document.getElementById('substituteStaffTableBody');
    const storeTaxIdsTableBody = document.getElementById('storeTaxIdsTableBody');

    const noStoreStaffMessage = document.getElementById('noStoreStaffMessage');
    const noSubstituteStaffMessage = document.getElementById('noSubstituteStaffMessage');
    const noStoreTaxIdsMessage = document.getElementById('noStoreTaxIdsMessage');
    const storeFormErrorMessage = document.getElementById('store-form-error-message');

    // Modal related elements
    const regionManagementModalEl = document.getElementById('regionManagementModal');
    const regionManagementModal = regionManagementModalEl ? new bootstrap.Modal(regionManagementModalEl) : null;
    const newRegionForm = document.getElementById('newRegionForm');
    const newRegionNameInput = document.getElementById('newRegionNameInput'); // Corrected ID
    const editingRegionIdInput = document.getElementById('editingRegionId');
    const existingRegionsList = document.getElementById('existingRegionsList');

    const regionNameDisplayInput = document.getElementById('regionNameDisplay'); 
    const regionSelectionModalEl = document.getElementById('regionSelectionModal');
    const regionSelectionModal = regionSelectionModalEl ? new bootstrap.Modal(regionSelectionModalEl) : null;
    const regionRadioGroupContainer = document.getElementById('regionRadioGroupContainer');
    const confirmRegionSelectionBtn = document.getElementById('confirmRegionSelectionBtn');
    const selectRegionBtn = document.getElementById('selectRegionBtn');

    const staffSelectionModalEl = document.getElementById('staffSelectionModal');
    const staffSelectionModal = staffSelectionModalEl ? new bootstrap.Modal(staffSelectionModalEl) : null;
    const staffSelectionModalLabel = document.getElementById('staffSelectionModalLabel');
    const staffSearchKeywordInput = document.getElementById('staffSearchKeywordInput');
    const staffListContainer = document.getElementById('staffListContainer');
    const confirmStaffSelectionBtn = document.getElementById('confirmStaffSelectionBtn');
    let currentStaffSelectionType = 'REGULAR_STAFF'; // 'REGULAR_STAFF' or 'SUBSTITUTE_STAFF'
    let allAvailableUsers = []; // Cache for users to avoid re-fetching, populate once

    const taxIdManagementModalEl = document.getElementById('taxIdManagementModal');
    const taxIdManagementModal = taxIdManagementModalEl ? new bootstrap.Modal(taxIdManagementModalEl) : null;
    const newTaxIdForm = document.getElementById('newTaxIdForm');
    const editingTaxIdEntryIdInput = document.getElementById('editingTaxIdEntryId');
    const newTaxIdNumberInput = document.getElementById('newTaxIdNumberInput');
    const newCompanyNameInput = document.getElementById('newCompanyNameInput');
    const existingTaxIdsTableBody = document.getElementById('existingTaxIdsTableBody');
    const manageTaxIdsBtn = document.getElementById('manageTaxIdsBtn');

    const taxIdSelectionModalEl = document.getElementById('taxIdSelectionModal');
    const taxIdSelectionModal = taxIdSelectionModalEl ? new bootstrap.Modal(taxIdSelectionModalEl) : null;
    const taxIdSearchKeywordInput = document.getElementById('taxIdSearchKeywordInput');
    const taxIdListContainer = document.getElementById('taxIdListContainer');
    const confirmSelectTaxIdModalBtn = document.getElementById('confirmSelectTaxIdModalBtn');
    const selectTaxIdsBtn = document.getElementById('selectTaxIdsBtn');

    let selectedStoreStaff = [];
    let selectedSubstituteStaff = [];
    let selectedStoreTaxIds = [];

    let allGlobalTaxIds = []; // Cache for global tax IDs
    let availableErpCompanyDivisions = []; // Added for caching

    const API_URL_STORES = '/api/v1/stores'; // Added for consistency
    const API_URL_REGIONS_LIST = '/api/v1/regions/list'; // Added for consistency
    const API_URL_STAFF_SELECTABLE = '/api/v1/users/selectable-staff'; // Added for consistency
    const API_URL_TAX_IDS_LIST = '/api/v1/tax-identifications/list'; // Added for consistency
    const API_URL_ERP_COMPANIES = '/api/v1/enums/erp-company-divisions'; // Added for ERP companies

    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get('mode') || 'add'; 
    const currentStoreId = urlParams.get('id');

    function showFormError(message) {
        if(storeFormErrorMessage) {
            storeFormErrorMessage.textContent = message;
            storeFormErrorMessage.classList.remove('d-none');
        }
    }
    function clearFormError() {
        if(storeFormErrorMessage) {
            storeFormErrorMessage.classList.add('d-none');
            storeFormErrorMessage.textContent = '';
        }
    }

    function initializeForm() {
        clearFormError();
        // Fetch company divisions and then proceed
        fetchErpCompanyDivisions().then(() => {
            if (mode === 'edit' && currentStoreId) {
                formTitle.textContent = '編輯門市';
                formModeBreadcrumb.textContent = '編輯門市';
                loadStoreData(currentStoreId);
            } else {
                formTitle.textContent = '新增門市';
                formModeBreadcrumb.textContent = '新增門市';
                // For 'add' mode, set the company dropdown to the current user's context
                const companyCode = window.getCompanyDivisionCode();
                if (companyCode !== null && erpCompanyDivisionField) {
                    erpCompanyDivisionField.value = companyCode;
                }
                updateStaffTable('REGULAR_STAFF');
                updateStaffTable('SUBSTITUTE_STAFF');
                updateTaxIdsTable();
            }
        });
    }

    async function loadStoreData(id) {
        clearFormError();
        try {
            const response = await window.fetchAuthenticated(`/api/v1/stores/${id}`);
            if (!response.ok) throw new Error('Failed to load store data.');
            const result = await response.json();
            if (result.code === 200 && result.data) {
                const store = result.data;
                console.log("Loaded Store Data for edit:", JSON.stringify(store, null, 2));

                if(storeIdInput) storeIdInput.value = store.storeId;
                if(storeNameInput) storeNameInput.value = store.storeName;
                if(storeCodeInput) storeCodeInput.value = store.storeCode || '';
                
                console.log("Store Region ID from data:", store.regionId);
                console.log("Store Region Name from data:", store.regionName);
                if (store.regionId && store.regionName) { 
                    if (regionIdInput) regionIdInput.value = store.regionId;
                    if (regionNameDisplayInput) regionNameDisplayInput.value = store.regionName;
                     console.log("Set region display to:", store.regionName);
                } else {
                    if (regionIdInput) regionIdInput.value = '';
                    if (regionNameDisplayInput) regionNameDisplayInput.value = ''; 
                    console.warn("Store data loaded, but regionId or regionName is missing/null in DTO. Store data:", store);
                }
                
                if(isActiveYesRadio) isActiveYesRadio.checked = store.isActive === true;
                if(isActiveNoRadio) isActiveNoRadio.checked = !(store.isActive === true);
                if(storeAddressInput) storeAddressInput.value = store.address || '';
                if(storePhoneInput) storePhoneInput.value = store.phoneNumber || '';
                if(erpCompanyDivisionField) { // Populate ERP company
                    erpCompanyDivisionField.value = store.erpCompanyDivision;
                }

                selectedStoreStaff = store.staffList ? store.staffList.filter(s => s.staffType === 'REGULAR_STAFF') : [];
                selectedSubstituteStaff = store.staffList ? store.staffList.filter(s => s.staffType === 'SUBSTITUTE_STAFF') : [];
                selectedStoreTaxIds = store.taxIdentifications || [];
                
                updateStaffTable('REGULAR_STAFF');
                updateStaffTable('SUBSTITUTE_STAFF');
                updateTaxIdsTable();
            } else {
                showFormError(result.message || '無法載入門市資料');
            }
        } catch (error) {
            console.error('Error loading store data:', error);
            showFormError('載入門市資料失敗: ' + error.message);
        }
    }

    // --- Region Management Modal Logic ---
    async function loadRegionsForManagementModal() {
        if (!existingRegionsList) return;
        existingRegionsList.innerHTML = '<li class="list-group-item">正在載入地區...</li>';
        try {
            const response = await window.fetchAuthenticated(API_URL_REGIONS_LIST + '?sort=sequenceOrder,asc');
            if (!response.ok) throw new Error('Failed to fetch regions for management');
            const apiResult = await response.json();

            existingRegionsList.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && Array.isArray(apiResult.data)) {
                if (apiResult.data.length === 0) {
                    existingRegionsList.innerHTML = '<li class="list-group-item">尚無地區資料。</li>';
                    return;
                }
                apiResult.data.forEach(region => {
                    const li = document.createElement('li');
                    li.className = 'list-group-item d-flex justify-content-between align-items-center';
                    li.textContent = `${region.regionName} (排序: ${region.sequenceOrder || 0})`;
                    
                    const buttonsDiv = document.createElement('div');
                    const editBtn = document.createElement('button');
                    editBtn.className = 'btn btn-sm btn-outline-primary me-2';
                    editBtn.innerHTML = '<i class="bi bi-pencil"></i> 編輯';
                    editBtn.onclick = () => {
                        editingRegionIdInput.value = region.regionId;
                        newRegionNameInput.value = region.regionName;
                        // TODO: Add sequence order to form if needed
                        newRegionForm.querySelector('button[type="submit"]').textContent = '更新地區';
                    };
                    buttonsDiv.appendChild(editBtn);

                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'btn btn-sm btn-outline-danger';
                    deleteBtn.innerHTML = '<i class="bi bi-trash"></i> 刪除';
                    deleteBtn.onclick = async () => {
                        if (confirm(`確定要刪除地區「${region.regionName}」嗎？`)) {
                            try {
                                const delResponse = await window.fetchAuthenticated(`/api/v1/regions/${region.regionId}`, { method: 'DELETE' });
                                const delResult = await delResponse.json();
                                if (delResponse.ok && delResult.code === 200) {
                                    alert(delResult.message || '地區已刪除');
                                    loadRegionsForManagementModal(); // Refresh list
                                    loadRegionsForSelectionModal(); // Also refresh selection modal if open/cached
                                } else {
                                    alert(`刪除失敗: ${delResult.message || '未知錯誤'}`);
                                }
                            } catch (err) {
                                alert('刪除地區時發生錯誤。'); console.error(err);
                            }
                        }
                    };
                    buttonsDiv.appendChild(deleteBtn);
                    li.appendChild(buttonsDiv);
                    existingRegionsList.appendChild(li);
                });
            } else {
                existingRegionsList.innerHTML = '<li class="list-group-item text-danger">無法載入地區列表。</li>';
            }
        } catch (error) {
            console.error('Error fetching regions for management modal:', error);
            existingRegionsList.innerHTML = `<li class="list-group-item text-danger">載入地區時發生錯誤: ${error.message}</li>`;
        }
    }

    if (manageRegionsBtn && regionManagementModalEl) {
        regionManagementModalEl.addEventListener('show.bs.modal', loadRegionsForManagementModal);
    }

    if (newRegionForm) {
        newRegionForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const regionName = newRegionNameInput.value.trim();
            const editingId = editingRegionIdInput.value;
            if (!regionName) {
                alert('地區名稱不得為空！'); return;
            }
            const payload = { regionName: regionName /* add sequenceOrder if form field exists */ };
            let url = '/api/v1/regions';
            let method = 'POST';
            if (editingId) {
                url = `/api/v1/regions/${editingId}`;
                method = 'PUT';
            }
            try {
                const response = await window.fetchAuthenticated(url, { method: method, body: JSON.stringify(payload) });
                const result = await response.json();
                if (response.ok && (result.code === 201 || result.code === 200)) {
                    alert(`地區已${editingId ? '更新' : '新增'}！`);
                    newRegionNameInput.value = '';
                    editingRegionIdInput.value = '';
                    newRegionForm.querySelector('button[type="submit"]').textContent = '新增地區';
                    loadRegionsForManagementModal();
                    loadRegionsForSelectionModal(); // Refresh selection modal as well
                } else {
                    alert(`操作失敗: ${result.message || '未知錯誤'}`);
                }
            } catch (error) {
                alert('操作地區時發生錯誤。'); console.error(error);
            }
        });
    }

    // --- Region Selection Modal Logic ---
    if (selectRegionBtn && regionSelectionModalEl) {
        regionSelectionModalEl.addEventListener('show.bs.modal', async function () {
            console.log("Region selection modal is about to be shown. Loading regions...");
            await loadRegionsForSelectionModal();
        });
    } 

    async function loadRegionsForSelectionModal() {
        if (!regionRadioGroupContainer) { console.error("regionRadioGroupContainer not found in modal."); return; }
        regionRadioGroupContainer.innerHTML = '<p>正在載入地區選項...</p>';
        try {
            const response = await window.fetchAuthenticated(API_URL_REGIONS_LIST + '?sort=sequenceOrder,asc');
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResult = await response.json();
            console.log("Regions for selection API result:", apiResult);
            regionRadioGroupContainer.innerHTML = ''; 
            if (apiResult.code === 200 && apiResult.data && Array.isArray(apiResult.data)) {
                if (apiResult.data.length === 0) {
                    regionRadioGroupContainer.innerHTML = '<p>沒有可選擇的地區。</p>'; return;
                }
                const currentSelectedRegionId = regionIdInput.value;
                apiResult.data.forEach(region => {
                    const div = document.createElement('div');
                    div.className = 'form-check';
                    const isChecked = region.regionId === currentSelectedRegionId ? 'checked' : '';
                    div.innerHTML = `
                        <input class="form-check-input region-radio-option" type="radio" name="selectedRegionOption" id="region-option-${region.regionId}" value="${region.regionId}" data-region-name="${region.regionName}" ${isChecked}>
                        <label class="form-check-label" for="region-option-${region.regionId}">${region.regionName}</label>
                    `;
                    regionRadioGroupContainer.appendChild(div);
                });
            } else {
                regionRadioGroupContainer.innerHTML = '<p class="text-danger">無法載入地區列表。</p>';
                console.error("Error or bad format in regions list API response:", apiResult.message || apiResult);
            }
        } catch (error) {
            console.error('Error fetching regions for selection modal:', error);
            regionRadioGroupContainer.innerHTML = `<p class="text-danger">載入地區時發生錯誤: ${error.message}</p>`;
        }
    }

    if (confirmRegionSelectionBtn) {
        confirmRegionSelectionBtn.addEventListener('click', function() {
            const selectedRadio = document.querySelector('.region-radio-option:checked');
            if (selectedRadio) {
                if(regionIdInput) regionIdInput.value = selectedRadio.value;
                if(regionNameDisplayInput) regionNameDisplayInput.value = selectedRadio.dataset.regionName;
                if (regionSelectionModal) regionSelectionModal.hide();
            } else {
                alert("請選擇一個地區。");
            }
        });
    }

    // --- Staff Management & Selection --- 
    document.getElementById('addStoreStaffBtn')?.addEventListener('click', () => {
        currentStaffSelectionType = 'REGULAR_STAFF'; 
        if(staffSelectionModalLabel) staffSelectionModalLabel.textContent = '選擇門市人員';
        loadAndShowAvailableStaff(); 
    });

    document.getElementById('addSubstituteStaffBtn')?.addEventListener('click', () => {
        currentStaffSelectionType = 'SUBSTITUTE_STAFF'; 
        if(staffSelectionModalLabel) staffSelectionModalLabel.textContent = '選擇代班人員'; 
        loadAndShowAvailableStaff();
    });

    async function fetchAllUsersOnce() {
        if (allAvailableUsers.length > 0) return; // Already fetched
        try {
            const response = await window.fetchAuthenticated(API_URL_STAFF_SELECTABLE);
            if (!response.ok) throw new Error('Failed to fetch users');
            const apiResult = await response.json();
            if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
                allAvailableUsers = apiResult.data;
            } else {
                console.error("Failed to fetch or parse user list:", apiResult.message);
                allAvailableUsers = [];
            }
        } catch (error) {
            console.error("Error fetching users:", error);
            allAvailableUsers = [];
        }
    }

    async function loadAndShowAvailableStaff() {
        await fetchAllUsersOnce(); // Ensure users are fetched
        if (!staffListContainer || !staffSelectionModal) return;

        staffListContainer.innerHTML = '<p>正在載入可選人員...</p>';
        const keyword = staffSearchKeywordInput ? staffSearchKeywordInput.value.toLowerCase() : '';
        
        const currentSelectedIds = (currentStaffSelectionType === 'REGULAR_STAFF' ? selectedStoreStaff : selectedSubstituteStaff)
            .map(s => s.userAccountId);

        const availableToDisplay = allAvailableUsers.filter(user => 
            !currentSelectedIds.includes(user.userAccountId) &&
            ( (user.userName && user.userName.toLowerCase().includes(keyword)) || 
              (user.employeeId && user.employeeId.toLowerCase().includes(keyword)) )
        );

        if (availableToDisplay.length === 0) {
            staffListContainer.innerHTML = '<p>沒有可選擇的人員或皆已加入。</p>';
        } else {
            staffListContainer.innerHTML = '';
            availableToDisplay.forEach(user => {
                const div = document.createElement('div');
                div.className = 'form-check';
                div.innerHTML = `
                    <input class="form-check-input staff-select-checkbox" type="checkbox" value="${user.userAccountId}" id="staff-select-${user.userAccountId}" 
                           data-user-name="${user.userName}" data-employee-id="${user.employeeId}">
                    <label class="form-check-label" for="staff-select-${user.userAccountId}">
                        ${user.userName} (${user.employeeId})
                    </label>
                `;
                staffListContainer.appendChild(div);
            });
        }
        if (staffSelectionModal) staffSelectionModal.show();
    }

    if(staffSearchKeywordInput) {
        staffSearchKeywordInput.addEventListener('input', loadAndShowAvailableStaff); // Re-filter on input
    }

    if (confirmStaffSelectionBtn) {
        confirmStaffSelectionBtn.addEventListener('click', function() {
            const selectedCheckboxes = staffListContainer.querySelectorAll('.staff-select-checkbox:checked');
            const targetList = currentStaffSelectionType === 'REGULAR_STAFF' ? selectedStoreStaff : selectedSubstituteStaff;
            
            selectedCheckboxes.forEach(cb => {
                const staffInfo = {
                    userAccountId: cb.value,
                    userName: cb.dataset.userName,
                    employeeId: cb.dataset.employeeId,
                    // staffType will be implicit by which list it's in, or set explicitly if DTO needs it
                };
                // Avoid duplicates if re-opening modal without saving main form
                if (!targetList.find(s => s.userAccountId === staffInfo.userAccountId)) {
                    targetList.push(staffInfo);
                }
            });
            updateStaffTable(currentStaffSelectionType);
            if (staffSelectionModal) staffSelectionModal.hide();
        });
    }

    function updateStaffTable(staffType) {
        const tableBody = staffType === 'REGULAR_STAFF' ? storeStaffTableBody : substituteStaffTableBody;
        const staffList = staffType === 'REGULAR_STAFF' ? selectedStoreStaff : selectedSubstituteStaff;
        const noDataMessage = staffType === 'REGULAR_STAFF' ? noStoreStaffMessage : noSubstituteStaffMessage;
        
        if (!tableBody || !noDataMessage) {
            console.error("Staff table elements not found for type:", staffType);
            return;
        }

        tableBody.innerHTML = '';
        if (staffList.length === 0) {
            noDataMessage.classList.remove('d-none');
            return;
        }
        noDataMessage.classList.add('d-none');
        staffList.forEach(staff => {
            const row = tableBody.insertRow();
            row.insertCell().textContent = staff.employeeId || 'N/A'; 
            row.insertCell().textContent = staff.userName || 'N/A';   
            const deleteBtnCell = row.insertCell();
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-sm btn-outline-danger';
            deleteBtn.innerHTML = '<i class="bi bi-trash-fill"></i>';
            deleteBtn.onclick = () => removeStaff(staffType, staff.userAccountId);
            deleteBtnCell.appendChild(deleteBtn);
        });
    }

    function removeStaff(staffType, userAccountIdToRemove) {
        if (staffType === 'REGULAR_STAFF') {
            selectedStoreStaff = selectedStoreStaff.filter(s => s.userAccountId !== userAccountIdToRemove);
        } else if (staffType === 'SUBSTITUTE_STAFF') {
            selectedSubstituteStaff = selectedSubstituteStaff.filter(s => s.userAccountId !== userAccountIdToRemove);
        }
        updateStaffTable(staffType);
    }

    // --- Tax ID Management & Selection ---
    function updateTaxIdsTable() {
        if (!storeTaxIdsTableBody || !noStoreTaxIdsMessage) return;
        storeTaxIdsTableBody.innerHTML = '';
        if (selectedStoreTaxIds.length === 0) {
            noStoreTaxIdsMessage.classList.remove('d-none');
            return;
        }
        noStoreTaxIdsMessage.classList.add('d-none');
        selectedStoreTaxIds.forEach(taxId => {
            const row = storeTaxIdsTableBody.insertRow();
            row.insertCell().textContent = taxId.taxIdNumber;
            row.insertCell().textContent = taxId.companyName;
            const deleteBtnCell = row.insertCell();
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-sm btn-outline-danger';
            deleteBtn.innerHTML = '<i class="bi bi-trash-fill"></i>';
            deleteBtn.onclick = () => removeTaxIdFromStore(taxId.taxIdentificationId);
            deleteBtnCell.appendChild(deleteBtn);
        });
    }

    function removeTaxIdFromStore(taxIdentificationIdToRemove) {
        selectedStoreTaxIds = selectedStoreTaxIds.filter(t => t.taxIdentificationId !== taxIdentificationIdToRemove);
        updateTaxIdsTable();
    }

    async function fetchAllGlobalTaxIdsOnce() {
        // if (allGlobalTaxIds.length > 0) return; // Simple cache, might need refresh if managed elsewhere
        try {
            const response = await window.fetchAuthenticated(API_URL_TAX_IDS_LIST);
            if (!response.ok) throw new Error('Failed to fetch global tax IDs');
            const apiResult = await response.json();
            if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
                allGlobalTaxIds = apiResult.data;
            } else {
                console.error("Failed to fetch or parse global tax ID list:", apiResult.message);
                allGlobalTaxIds = [];
            }
        } catch (error) {
            console.error("Error fetching global tax IDs:", error);
            allGlobalTaxIds = [];
        }
    }

    // --- Tax ID Management Modal Logic ---
    async function loadTaxIdsForManagementModal() {
        if (!existingTaxIdsTableBody || !taxIdManagementModalEl) return;
        await fetchAllGlobalTaxIdsOnce(); 
        existingTaxIdsTableBody.innerHTML = '';
        if (newTaxIdForm) newTaxIdForm.reset(); 
        if (editingTaxIdEntryIdInput) editingTaxIdEntryIdInput.value = '';
        if (newTaxIdForm) newTaxIdForm.querySelector('button[type="submit"]').textContent = '新增統編';

        if (allGlobalTaxIds.length === 0) {
            existingTaxIdsTableBody.innerHTML = '<tr><td colspan="3" class="text-center">尚無全域統編資料。</td></tr>';
            return;
        }
        allGlobalTaxIds.forEach(taxId => {
            const row = existingTaxIdsTableBody.insertRow();
            row.insertCell().textContent = taxId.taxIdNumber;
            row.insertCell().textContent = taxId.companyName;
            const actionsCell = row.insertCell();
            const editBtn = document.createElement('button');
            editBtn.className = 'btn btn-sm btn-outline-primary me-2';
            editBtn.innerHTML = '<i class="bi bi-pencil"></i>';
            editBtn.onclick = () => {
                if(editingTaxIdEntryIdInput) editingTaxIdEntryIdInput.value = taxId.taxIdentificationId;
                if(newTaxIdNumberInput) newTaxIdNumberInput.value = taxId.taxIdNumber;
                if(newCompanyNameInput) newCompanyNameInput.value = taxId.companyName;
                if(newTaxIdForm) newTaxIdForm.querySelector('button[type="submit"]').textContent = '更新統編';
            };
            actionsCell.appendChild(editBtn);

            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-sm btn-outline-danger';
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            deleteBtn.onclick = async () => {
                if (confirm(`確定要刪除全域統編「${taxId.taxIdNumber} - ${taxId.companyName}」嗎？`)) {
                    try {
                        const delResponse = await window.fetchAuthenticated(`/api/v1/tax-identifications/${taxId.taxIdentificationId}`, { method: 'DELETE' });
                        const delResult = await delResponse.json();
                        if (delResponse.ok && delResult.code === 200) {
                            alert(delResult.message || '統編已刪除');
                            await fetchAllGlobalTaxIdsOnce(); // Refresh global cache
                            loadTaxIdsForManagementModal(); 
                            // Also remove from current store's selection if it was there
                            removeTaxIdFromStore(taxId.taxIdentificationId);
                        } else {
                            alert(`刪除失敗: ${delResult.message || '未知錯誤'}`);
                        }
                    } catch (err) {
                        alert('刪除統編時發生錯誤。'); console.error(err);
                    }
                }
            };
            actionsCell.appendChild(deleteBtn);
        });
    }

    if (manageTaxIdsBtn && taxIdManagementModalEl) {
        taxIdManagementModalEl.addEventListener('show.bs.modal', loadTaxIdsForManagementModal);
    }

    if (newTaxIdForm) {
        newTaxIdForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const taxIdNumber = newTaxIdNumberInput.value.trim();
            const companyName = newCompanyNameInput.value.trim();
            const editingId = editingTaxIdEntryIdInput.value;

            if (!taxIdNumber || !companyName) {
                alert('統一編號和公司抬頭不得為空！'); return;
            }
            const payload = { taxIdNumber, companyName };
            let url = '/api/v1/tax-identifications';
            let method = 'POST';
            if (editingId) {
                // payload.taxIdentificationId = editingId; // ID is in URL for PUT
                url = `/api/v1/tax-identifications/${editingId}`;
                method = 'PUT';
            }
            try {
                const response = await window.fetchAuthenticated(url, { method: method, body: JSON.stringify(payload) });
                const result = await response.json();
                if (response.ok && (result.code === 201 || result.code === 200)) {
                    alert(`統編已${editingId ? '更新' : '新增'}！`);
                    newTaxIdNumberInput.value = '';
                    newCompanyNameInput.value = '';
                    editingTaxIdEntryIdInput.value = '';
                    if(newTaxIdForm) newTaxIdForm.querySelector('button[type="submit"]').textContent = '新增統編';
                    await fetchAllGlobalTaxIdsOnce();
                    loadTaxIdsForManagementModal();
                } else {
                    alert(`操作失敗: ${result.message || '未知錯誤'}`);
                }
            } catch (error) {
                alert('操作統編時發生錯誤。'); console.error(error);
            }
        });
    }

    // --- Tax ID Selection Modal Logic ---
    async function loadAvailableTaxIdsForSelection() {
        if (!taxIdListContainer) { console.error("taxIdListContainer for selection not found."); return; }
        await fetchAllGlobalTaxIdsOnce();
        taxIdListContainer.innerHTML = '<p>正在載入統編選項...</p>';
        const keyword = taxIdSearchKeywordInput ? taxIdSearchKeywordInput.value.toLowerCase() : '';
        
        const currentSelectedTaxIdsValues = selectedStoreTaxIds.map(t => t.taxIdentificationId);

        const availableToDisplay = allGlobalTaxIds.filter(taxId => 
            !currentSelectedTaxIdsValues.includes(taxId.taxIdentificationId) &&
            ( (taxId.taxIdNumber && taxId.taxIdNumber.toLowerCase().includes(keyword)) || 
              (taxId.companyName && taxId.companyName.toLowerCase().includes(keyword)) )
        );

        if (availableToDisplay.length === 0) {
            taxIdListContainer.innerHTML = '<p>沒有可選擇的統編或皆已加入。</p>';
        } else {
            taxIdListContainer.innerHTML = '';
            availableToDisplay.forEach(taxId => {
                const div = document.createElement('div');
                div.className = 'form-check';
                div.innerHTML = `
                    <input class="form-check-input taxid-select-checkbox" type="checkbox" value="${taxId.taxIdentificationId}" id="taxid-select-${taxId.taxIdentificationId}" 
                           data-tax-id-number="${taxId.taxIdNumber}" data-company-name="${taxId.companyName}">
                    <label class="form-check-label" for="taxid-select-${taxId.taxIdentificationId}">
                        ${taxId.companyName} (${taxId.taxIdNumber})
                    </label>
                `;
                taxIdListContainer.appendChild(div);
            });
        }
    }

    if (selectTaxIdsBtn && taxIdSelectionModalEl) {
        taxIdSelectionModalEl.addEventListener('show.bs.modal', loadAvailableTaxIdsForSelection);
    }
    if (taxIdSearchKeywordInput) {
        taxIdSearchKeywordInput.addEventListener('input', loadAvailableTaxIdsForSelection);
    }

    if (confirmSelectTaxIdModalBtn) {
        confirmSelectTaxIdModalBtn.addEventListener('click', function() {
            const selectedCheckboxes = taxIdListContainer.querySelectorAll('.taxid-select-checkbox:checked');
            selectedCheckboxes.forEach(cb => {
                const taxIdInfo = {
                    taxIdentificationId: cb.value,
                    taxIdNumber: cb.dataset.taxIdNumber,
                    companyName: cb.dataset.companyName
                };
                if (!selectedStoreTaxIds.find(t => t.taxIdentificationId === taxIdInfo.taxIdentificationId)) {
                    selectedStoreTaxIds.push(taxIdInfo);
                }
            });
            updateTaxIdsTable();
            if (taxIdSelectionModal) taxIdSelectionModal.hide();
        });
    } else {
        console.warn("Confirm button for Tax ID Selection Modal (id: confirmSelectTaxIdModalBtn) not found.");
    }

    // --- Main Form Submission ---
    storeForm.addEventListener('submit', async function(event) {
        event.preventDefault();
        clearFormError();
        const payload = {
            storeName: storeNameInput.value,
            storeCode: storeCodeInput.value || null,
            regionId: regionIdInput.value || null,
            isActive: isActiveYesRadio.checked,
            erpCompanyDivision: erpCompanyDivisionField.value ? Number(erpCompanyDivisionField.value) : null,
            address: storeAddressInput.value || null,
            phoneNumber: storePhoneInput.value || null,
            staffUserAccountIds: selectedStoreStaff.map(s => s.userAccountId), 
            substituteUserAccountIds: selectedSubstituteStaff.map(s => s.userAccountId),
            taxIdentificationIds: selectedStoreTaxIds.map(t => t.taxIdentificationId)
        };

        let url = API_URL_STORES;
        let method = 'POST';
        if (mode === 'edit' && currentStoreId) {
            payload.storeId = currentStoreId;
            url = `${API_URL_STORES}/${currentStoreId}`;
            method = 'PUT';
        }

        // Confirmation Dialog
        const confirmMessage = (mode === 'edit') ? "確定要更新此門市資訊嗎？" : "確定要新增此門市嗎？";
        if (!window.confirm(confirmMessage)) {
            return; // User cancelled
        }

        try {
            const response = await window.fetchAuthenticated(url, {
                method: method,
                body: JSON.stringify(payload)
            });
            const result = await response.json();
            if (response.ok && (result.code === (method === 'POST' ? 201 : 200))) {
                alert(`門市已${mode === 'add' ? '新增' : '更新'}！`);
                window.location.href = 'store_settings.html'; // Path updated
            } else {
                showFormError(result.message || '儲存失敗');
            }
        } catch (error) {
            console.error('Error saving store:', error);
            showFormError('儲存門市時發生錯誤: ' + error.message);
        }
    });

    document.getElementById('cancelStoreFormBtn').addEventListener('click', () => {
        window.location.href = 'store_settings.html'; // Path updated
    });

    initializeForm();

    // Function to fetch and populate ERP Company Divisions
    async function fetchErpCompanyDivisions() {
        if (!erpCompanyDivisionField) return;
        erpCompanyDivisionField.innerHTML = '<option value="">載入中...</option>';
        try {
            const response = await window.fetchAuthenticated(API_URL_ERP_COMPANIES);
            if (!response.ok) throw new Error('Failed to fetch ERP Company Divisions');
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                // The API returns EnumValueDto { id, description, code }
                // where 'code' is the short value we need.
                availableErpCompanyDivisions = apiResult.data; 
                populateErpCompanyDropdown();
            } else {
                throw new Error(apiResult.message || 'Failed to load ERP Company Divisions');
            }
        } catch (error) {
            console.error('Error fetching ERP Company Divisions:', error);
            erpCompanyDivisionField.innerHTML = '<option value="">公司別載入失敗</option>';
        }
    }

    function populateErpCompanyDropdown() {
        if (!erpCompanyDivisionField || availableErpCompanyDivisions.length === 0) return;
        const currentValue = erpCompanyDivisionField.value; // Preserve if already set (e.g. during edit load)
        erpCompanyDivisionField.innerHTML = '<option value="" disabled>請選擇公司別...</option>';
        availableErpCompanyDivisions.forEach(company => {
            const option = document.createElement('option');
            option.value = company.code; // Use 'code' from EnumValueDto
            option.textContent = company.label;
            erpCompanyDivisionField.appendChild(option);
        });

        // If a value was already set (e.g. from loadStoreData), try to restore it.
        // Otherwise, in 'add' mode, this will be handled by initializeForm.
        if (currentValue) {
            erpCompanyDivisionField.value = currentValue;
        }
    }
}); 