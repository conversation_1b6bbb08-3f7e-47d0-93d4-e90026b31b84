document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('id');
    const pageContent = document.querySelector('.page-content');

    if (!orderId) {
        pageContent.innerHTML = '<div class="alert alert-danger">錯誤：未提供領料單ID。</div>';
        return;
    }

    async function fetchAndRenderDetail(id) {
        try {
            const response = await window.fetchAuthenticated(`/api/v1/material-orders/${id}`);
            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || "查詢失敗");
            }
            const result = await response.json();
            if (result.code === 200 && result.data) {
                renderDetail(result.data);
            } else {
                throw new Error(result.message || "回傳資料格式錯誤");
            }
        } catch (error) {
            pageContent.innerHTML = `<div class="alert alert-danger">獲取詳情失敗: ${error.message}</div>`;
        }
    }

    function getStatusBadgeClass(statusCode) {
        switch (statusCode) {
            case 10: // 待揀料
                return 'bg-secondary';
            case 20: // 已揀料
                return 'bg-primary';
            case 30: // 領料完成
                return 'bg-success';
            case 40: // 已取消
                return 'bg-danger';
            default:
                return 'bg-info text-dark';
        }
    }

    function renderDetail(order) {
        const setText = (id, text) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = text || '-';
            }
        };

        setText('material-order-number', `#${order.materialOrderNumber}`);
        setText('technician-name', order.requestingTechnicianName);
        setText('warehouse-name', order.targetWarehouseName);
        
        const statusSpan = document.getElementById('status-description');
        if (statusSpan) {
            statusSpan.textContent = order.materialOrderStatusDescription || '-';
            statusSpan.className = `badge ${getStatusBadgeClass(order.materialOrderStatusCode)}`;
        }
        
        setText('create-time', order.createTime ? new Date(order.createTime).toLocaleString() : '-');
        setText('picker-name', order.pickerName);
        setText('picked-time', order.pickedTime ? new Date(order.pickedTime).toLocaleString() : '-');
        setText('collected-time', order.collectedTime ? new Date(order.collectedTime).toLocaleString() : '-');
        setText('remarks', order.remarks);

        const itemListBody = document.getElementById('item-list');
        if (itemListBody) {
            if (order.items && order.items.length > 0) {
                itemListBody.innerHTML = order.items.map(item => `
                    <tr>
                        <td>${item.dispatchRepairNumber || 'N/A'}</td>
                        <td>${item.productBarcode}</td>
                        <td>${item.productName}</td>
                        <td>${item.requestedQuantity}</td>
                        <td>${item.pickedQuantity || 0}</td>
                        <td>${item.collectedQuantity || 0}</td>
                    </tr>
                `).join('');
            } else {
                itemListBody.innerHTML = '<tr><td colspan="6" class="text-center">沒有品項資料</td></tr>';
            }
        }
    }

    fetchAndRenderDetail(orderId);
}); 