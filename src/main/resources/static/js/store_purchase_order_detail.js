// static/js/store_purchase_order_detail.js
document.addEventListener('DOMContentLoaded', function () {
    const loadingMessage = document.getElementById('loading-message');
    const errorMessage = document.getElementById('error-message');
    const orderDetailContent = document.getElementById('order-detail-content');
    
    const detailPurchaseOrderNumber = document.getElementById('detailPurchaseOrderNumber');
    const detailStatus = document.getElementById('detailStatus');
    const detailShipmentTime = document.getElementById('detailShipmentTime');
    const detailCreatedBy = document.getElementById('detailCreatedBy');
    const detailStoreName = document.getElementById('detailStoreName');
    const detailOriginalOrderId = document.getElementById('detailOriginalOrderId');
    const detailShippingMethod = document.getElementById('detailShippingMethod');
    const detailTrackingNumber = document.getElementById('detailTrackingNumber');
    const orderItemsTableBody = document.getElementById('order-items-table-body');
    const detailTotalAmount = document.getElementById('detailTotalAmount');
    const detailTaxAmount = document.getElementById('detailTaxAmount');
    const detailGrandTotalAmount = document.getElementById('detailGrandTotalAmount');
    const detailOrderNotes = document.getElementById('detailOrderNotes');

    const backToListBtn = document.getElementById('backToListBtn');
    const discrepancyBtn = document.getElementById('discrepancyBtn');
    const confirmReceiptBtn = document.getElementById('confirmReceiptBtn');

    const discrepancyModalEl = document.getElementById('discrepancyModal');
    const discrepancyModal = new bootstrap.Modal(discrepancyModalEl);
    const modalOrderNumber = document.getElementById('modalOrderNumber');
    const discrepancyForm = document.getElementById('discrepancyForm');
    const discrepancyItemsTableBody = document.getElementById('discrepancyItemsTableBody');

    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('id');
    const API_URL = '/api/v1/store-purchase-orders'; // Placeholder
    let currentOrderData = null;

    function showError(message) {
        loadingMessage.classList.add('d-none');
        orderDetailContent.classList.add('d-none');
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
    }

    async function fetchOrderDetail() {
        if (!orderId) {
            showError('未提供進貨單ID。');
            return;
        }
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/${orderId}`);
            if (!response.ok) {
                const errData = await response.json().catch(() => ({ message: `HTTP error! Status: ${response.status}` }));
                throw new Error(errData.message || `HTTP error! Status: ${response.status}`);
            }
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                currentOrderData = apiResult.data;
                renderOrderDetails(currentOrderData);
                loadingMessage.classList.add('d-none');
                orderDetailContent.classList.remove('d-none');
            } else {
                showError(apiResult.message || '無法載入進貨單詳情。');
            }
        } catch (error) {
            console.error('Error fetching order details:', error);
            showError(`載入進貨單詳情時發生錯誤: ${error.message}`);
        }
    }

    function renderOrderDetails(order) {
        detailPurchaseOrderNumber.textContent = order.purchaseOrderNumber;
        detailStatus.textContent = order.statusDescription || order.purchaseOrderStatus; // Assuming statusDescription exists
        detailShipmentTime.textContent = order.shipmentTime ? new Date(order.shipmentTime).toLocaleString() : '-';
        detailCreatedBy.textContent = order.createdByEmployeeName || order.createdByEmployeeId || '-';
        detailStoreName.textContent = order.storeName || '-'; // Assuming storeName is in DTO
        detailOriginalOrderId.textContent = order.originalWholesaleOrderId || '-';
        detailShippingMethod.textContent = order.shippingMethod || '-';
        detailTrackingNumber.textContent = order.trackingNumber || '-';
        
        detailTotalAmount.textContent = `NT$ ${order.totalAmount ? order.totalAmount.toLocaleString() : '0.00'}`;
        detailTaxAmount.textContent = `NT$ ${order.taxAmount ? order.taxAmount.toLocaleString() : '0.00'}`;
        detailGrandTotalAmount.textContent = `NT$ ${order.grandTotalAmount ? order.grandTotalAmount.toLocaleString() : '0.00'}`;
        detailOrderNotes.textContent = order.orderNotes || '無';

        orderItemsTableBody.innerHTML = '';
        if (order.items && order.items.length > 0) {
            order.items.forEach(item => {
                const row = orderItemsTableBody.insertRow();
                row.insertCell().textContent = item.productBarcode;
                row.insertCell().textContent = item.productName;
                row.insertCell().textContent = item.warehouseCode || '-';
                row.insertCell().textContent = item.orderedQuantity;
                row.insertCell().textContent = item.receivedQuantity !== null ? item.receivedQuantity : '-';
                row.insertCell().textContent = item.unitPrice ? item.unitPrice.toLocaleString() : '-';
                row.insertCell().textContent = item.itemSubtotal ? item.itemSubtotal.toLocaleString() : '-';
                row.insertCell().textContent = item.itemNotes || '-';
            });
        }
        // Update button states based on order status
        updateActionButtons(order.purchaseOrderStatus);
    }
    
    function updateActionButtons(status) {
        // Statuses: 0:配送中, 1:部分到貨, 2:已到貨(待確認), 3:數量異常, 4:已完成
        if (status === 4) { // 已完成
            if(discrepancyBtn) discrepancyBtn.style.display = 'none';
            if(confirmReceiptBtn) confirmReceiptBtn.style.display = 'none';
        } else if (status === 3) { // 數量異常
            if(discrepancyBtn) discrepancyBtn.textContent = '查看/編輯異常';
            if(confirmReceiptBtn) confirmReceiptBtn.style.display = 'block'; 
        } else { // 配送中, 部分到貨, 已到貨(待確認)
            if(discrepancyBtn) discrepancyBtn.textContent = '進貨數量異常';
            if(discrepancyBtn) discrepancyBtn.style.display = 'block';
            if(confirmReceiptBtn) confirmReceiptBtn.style.display = 'block';
        }
    }

    if (backToListBtn) {
        backToListBtn.addEventListener('click', () => window.location.href = 'store_purchase_orders.html');
    }

    if (discrepancyBtn) {
        discrepancyBtn.addEventListener('click', () => {
            if (!currentOrderData) return;
            if(modalOrderNumber) modalOrderNumber.textContent = currentOrderData.purchaseOrderNumber;
            if(discrepancyItemsTableBody) discrepancyItemsTableBody.innerHTML = '';
            if (currentOrderData.items && discrepancyItemsTableBody) {
                currentOrderData.items.forEach(item => {
                    const row = discrepancyItemsTableBody.insertRow();
                    row.insertCell().textContent = item.productBarcode;
                    row.insertCell().textContent = item.productName;
                    row.insertCell().textContent = item.orderedQuantity;
                    
                    const receivedCell = row.insertCell();
                    const receivedInput = document.createElement('input');
                    receivedInput.type = 'number';
                    receivedInput.className = 'form-control form-control-sm';
                    receivedInput.value = item.receivedQuantity !== null ? item.receivedQuantity : item.orderedQuantity;
                    receivedInput.min = "0";
                    receivedInput.dataset.itemId = item.storePurchaseOrderItemId;
                    receivedCell.appendChild(receivedInput);

                    const notesCell = row.insertCell();
                    const notesInput = document.createElement('input');
                    notesInput.type = 'text';
                    notesInput.className = 'form-control form-control-sm';
                    // TODO: Populate existing discrepancy notes if any
                    notesInput.placeholder = "差異原因";
                    notesCell.appendChild(notesInput);
                });
            }
            discrepancyModal.show();
        });
    }

    discrepancyForm.addEventListener('submit', async function(event) {
        event.preventDefault();
        const discrepancies = [];
        discrepancyItemsTableBody.querySelectorAll('tr').forEach(row => {
            const itemId = row.querySelector('input[type="number"]').dataset.itemId;
            const receivedQuantity = parseInt(row.querySelector('input[type="number"]').value);
            const reason = row.querySelector('input[type="text"]').value;
            const orderedQuantity = parseInt(row.cells[2].textContent); // Assuming this is where ordered qty is

            if (receivedQuantity !== orderedQuantity) {
                discrepancies.push({
                    storePurchaseOrderItemId: itemId,
                    actualQuantity: receivedQuantity,
                    reason: reason || null,
                    expectedQuantity: orderedQuantity
                });
            }
        });

        // API Call to submit discrepancies and update order status
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/${orderId}/report-discrepancy`, {
                method: 'POST',
                body: JSON.stringify({ items: discrepancies })
            });
            if (!response.ok) {
                const errData = await response.json().catch(()=>({message: '回報異常失敗'}));
                throw new Error(errData.message);
            }
            showToast('數量異常已回報。', 'success');
            discrepancyModal.hide();
            fetchOrderDetail(); // Refresh details
        } catch (error) {
            console.error('Error reporting discrepancies:', error);
            showToast(`回報異常失敗: ${error.message}`, 'error');
        }
    });

    if (confirmReceiptBtn) {
        confirmReceiptBtn.addEventListener('click', async () => {
            if (!currentOrderData) return;
            if (!confirm('確定所有商品已點收無誤，完成進貨嗎？')) return;

            try {
                const response = await window.fetchAuthenticated(`${API_URL}/${orderId}/confirm-receipt`, { method: 'POST' });
                if (!response.ok) {
                    const errData = await response.json().catch(()=>({message: '確認進貨失敗'}));
                    throw new Error(errData.message);
                }
                showToast('進貨已確認完成。', 'success');
                fetchOrderDetail(); // Refresh details
            } catch (error) {
                console.error('Error confirming receipt:', error);
                showToast(`確認進貨失敗: ${error.message}`, 'error');
            }
        });
    }

    async function init() {
        await loadSharedHTML();
        await fetchOrderDetail();
    }

    init();
}); 