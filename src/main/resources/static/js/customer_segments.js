document.addEventListener('DOMContentLoaded', function () {
    const activeSegmentsList = document.getElementById('activeSegmentsList');
    const inactiveSegmentsList = document.getElementById('inactiveSegmentsList');
    const noActiveMsg = document.getElementById('noActiveSegmentsMessage');
    const noInactiveMsg = document.getElementById('noInactiveSegmentsMessage');
    const addSegmentBtn = document.getElementById('addSegmentBtn');
    const saveOrderBtn = document.getElementById('saveOrderBtn');

    const segmentFormModalEl = document.getElementById('segmentFormModal');
    const segmentFormModal = new bootstrap.Modal(segmentFormModalEl);
    const segmentForm = document.getElementById('segmentForm');
    const segmentFormLabel = document.getElementById('segmentFormModalLabel');
    const segmentIdField = document.getElementById('segmentId');
    const segmentNameField = document.getElementById('segmentName');
    const saveSegmentBtn = document.getElementById('saveSegmentBtn');
    const segmentFormErrorMsg = document.getElementById('segmentFormErrorMessage');

    const deleteConfirmModalEl = document.getElementById('deleteConfirmModal');
    const deleteConfirmModal = new bootstrap.Modal(deleteConfirmModalEl);
    const deleteConfirmMessage = document.getElementById('deleteConfirmMessage');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    let segmentToDelete = null;
    let sortableActiveList = null;

    const API_URL = '/api/v1/customer-segments';

    function clearFormError() {
        if(segmentFormErrorMsg) {
            segmentFormErrorMsg.classList.add('d-none');
            segmentFormErrorMsg.textContent = '';
        }
    }
    function showFormError(message) {
        if(segmentFormErrorMsg) {
            segmentFormErrorMsg.textContent = message;
            segmentFormErrorMsg.classList.remove('d-none');
        }
    }

    async function loadSegments() {
        try {
            const [activeRes, inactiveRes] = await Promise.all([
                window.fetchAuthenticated(`${API_URL}/active`),
                window.fetchAuthenticated(`${API_URL}/inactive`)
            ]);

            if (!activeRes.ok || !inactiveRes.ok) {
                throw new Error('Failed to load segments from server.');
            }

            const activeApiResult = await activeRes.json();
            const inactiveApiResult = await inactiveRes.json();

            renderList(activeSegmentsList, activeApiResult.data || [], true, noActiveMsg);
            renderList(inactiveSegmentsList, inactiveApiResult.data || [], false, noInactiveMsg);
            
            if (sortableActiveList) {
                sortableActiveList.destroy();
            }
            if (activeSegmentsList) {
                 sortableActiveList = new Sortable(activeSegmentsList, {
                    animation: 150,
                    handle: '.handle',
                    ghostClass: 'ghost-class-segment',
                    chosenClass: 'chosen-class-segment',
                    // onEnd will be handled by saveOrderBtn now
                });
            }

        } catch (error) {
            console.error('Error loading segments:', error);
            if(activeSegmentsList) activeSegmentsList.innerHTML = '<li class="list-group-item text-danger">無法載入啟用管道</li>';
            if(inactiveSegmentsList) inactiveSegmentsList.innerHTML = '<li class="list-group-item text-danger">無法載入禁用管道</li>';
        }
    }

    function renderList(listElement, segments, isActiveList, noDataMsgEl) {
        if (!listElement || !noDataMsgEl) {
            console.error("Missing list or no-data message element for rendering segments.");
            return;
        }
        listElement.innerHTML = '';
        if (segments.length === 0) {
            noDataMsgEl.classList.remove('d-none');
            return;
        }
        noDataMsgEl.classList.add('d-none');

        segments.forEach(segment => {
            const li = document.createElement('li');
            li.className = 'list-group-item';
            li.dataset.segmentId = segment.segmentId;
            li.dataset.segmentName = segment.segmentName;

            let actionsHtml = `
                <button class="btn btn-sm btn-outline-secondary rename-btn" title="重新命名"><i class="bi bi-pencil"></i></button>
                <button class="btn btn-sm btn-outline-danger delete-btn" title="刪除"><i class="bi bi-trash"></i></button>
            `;

            if (isActiveList) {
                actionsHtml = `<i class="bi bi-grip-vertical handle me-2"></i> ` + actionsHtml;
                actionsHtml += `<button class="btn btn-sm btn-outline-warning ms-1 disable-btn" title="禁用"><i class="bi bi-toggle-off"></i></button>`;
            } else {
                actionsHtml += `<button class="btn btn-sm btn-outline-success ms-1 enable-btn" title="啟用"><i class="bi bi-toggle-on"></i></button>`;
            }

            li.innerHTML = `
                <span>${segment.segmentName}</span>
                <div class="actions">${actionsHtml}</div>
            `;
            listElement.appendChild(li);
        });
        addListActionListeners(listElement, isActiveList);
    }

    function addListActionListeners(listElement, isActiveList) {
        listElement.querySelectorAll('.rename-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const li = this.closest('.list-group-item');
                segmentIdField.value = li.dataset.segmentId;
                segmentNameField.value = li.dataset.segmentName;
                segmentFormModalLabel.textContent = '重新命名客群管道';
                saveSegmentBtn.textContent = '確認更新';
                clearFormError();
                segmentFormModal.show();
            });
        });

        listElement.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const li = this.closest('.list-group-item');
                segmentToDelete = { id: li.dataset.segmentId, name: li.dataset.segmentName };
                deleteConfirmMessage.textContent = `您確定要刪除客群管道「${segmentToDelete.name}」嗎？`;
                deleteConfirmModal.show();
            });
        });

        if (isActiveList) {
            listElement.querySelectorAll('.disable-btn').forEach(btn => {
                btn.addEventListener('click', async function() {
                    const li = this.closest('.list-group-item');
                    await setStatus(li.dataset.segmentId, false);
                });
            });
        } else {
            listElement.querySelectorAll('.enable-btn').forEach(btn => {
                btn.addEventListener('click', async function() {
                    const li = this.closest('.list-group-item');
                    await setStatus(li.dataset.segmentId, true);
                });
            });
        }
    }

    async function setStatus(segmentId, makeActive) {
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/${segmentId}/status?isActive=${makeActive}`, { method: 'PUT' });
            if (!response.ok) throw new Error('變更狀態失敗');
            const apiResult = await response.json();
            if (apiResult.code === 200) {
                showToast(`客群管道已${makeActive ? '啟用' : '禁用'}`, 'success');
                loadSegments();
            } else {
                showToast(apiResult.message || '變更狀態失敗', 'error');
            }
        } catch (error) {
            console.error('Error setting segment status:', error);
            showToast('變更狀態時發生錯誤', 'error');
        }
    }

    addSegmentBtn.addEventListener('click', () => {
        segmentForm.reset();
        segmentIdField.value = '';
        segmentFormModalLabel.textContent = '新增客群管道';
        saveSegmentBtn.textContent = '儲存';
        clearFormError();
    });

    segmentForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        if (!this.checkValidity()) {
            this.classList.add('was-validated');
            return;
        }
        clearFormError();
        const id = segmentIdField.value;
        const name = segmentNameField.value.trim();
        const payload = { segmentName: name };
        const method = id ? 'PUT' : 'POST';
        const url = id ? `${API_URL}/${id}` : API_URL;

        try {
            const response = await window.fetchAuthenticated(url, {
                method: method,
                body: JSON.stringify(payload)
            });
            const apiResult = await response.json();
            if (response.ok && (apiResult.code === (id ? 200 : 201) )) {
                showToast(`客群管道已成功${id ? '更新' : '新增'}`, 'success');
                segmentFormModal.hide();
                loadSegments();
            } else {
                showFormError(apiResult.message || '儲存失敗');
            }
        } catch (error) {
            console.error('Error saving segment:', error);
            showFormError('儲存時發生錯誤');
        }
    });

    confirmDeleteBtn.addEventListener('click', async function() {
        if (!segmentToDelete || !segmentToDelete.id) return;
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/${segmentToDelete.id}`, { method: 'DELETE' });
            const apiResult = await response.json();
            if (response.ok && apiResult.code === 200) {
                showToast(`客群管道「${segmentToDelete.name}」已刪除`, 'success');
                segmentToDelete = null;
                loadSegments();
            } else {
                showToast(apiResult.message || '刪除失敗', 'error');
            }
        } catch (error) {
            console.error('Error deleting segment:', error);
            showToast('刪除時發生錯誤', 'error');
        }
        deleteConfirmModal.hide();
    });

    saveOrderBtn.addEventListener('click', async function() {
        if (!sortableActiveList) return;
        const orderedItems = Array.from(activeSegmentsList.querySelectorAll('.list-group-item')).map(li => ({
            segmentId: li.dataset.segmentId,
            segmentName: li.dataset.segmentName,
            // sequenceOrder will be determined by backend based on array index
        }));
        
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/active/order`, {
                method: 'PUT',
                body: JSON.stringify(orderedItems)
            });
            const apiResult = await response.json();
            if (response.ok && apiResult.code === 200) {
                showToast('啟用管道排序已儲存', 'success');
                loadSegments(); // Refresh to confirm order and get potentially updated data
            } else {
                showToast(apiResult.message || '儲存排序失敗', 'error');
            }
        } catch (error) {
            console.error('Error saving order:', error);
            showToast('儲存排序時發生錯誤', 'error');
        }
    });

    loadSegments();
}); 