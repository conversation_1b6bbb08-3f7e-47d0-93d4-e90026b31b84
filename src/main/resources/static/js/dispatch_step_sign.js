document.addEventListener('DOMContentLoaded', function () {
    // ... (DOM elements)
    const canvas = document.getElementById('signature-pad');
    const signaturePad = new SignaturePad(canvas, {
        backgroundColor: 'rgb(255, 255, 255)'
    });

    // --- State ---
    const urlParams = new URLSearchParams(window.location.search);
    const dispatchRepairId = urlParams.get('id');
    let originalOrderData = null;

    // --- Initialization ---
    async function initializePage() {
        if (!dispatchRepairId) { /* ... */ return; }

        // Set breadcrumb link
        const detailBreadcrumbLink = document.getElementById('detail-breadcrumb-link');
        if(detailBreadcrumbLink) detailBreadcrumbLink.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;
        
        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}`);
            if (!response.ok) throw new Error('無法獲取派工單資料');
            
            originalOrderData = await response.json();
            
            window.renderDispatchOrderHeader(originalOrderData, document.getElementById('info-card-container'));
            renderCostSummary(originalOrderData);
            
            if (originalOrderData.statusCode !== 60) { // 60: 簽確中
                document.getElementById('complete-btn').disabled = true;
                showToast('此派工單狀態已變更，不可操作。', 'warning');
            }
            
            resizeCanvas();
            setupEventListeners();

        } catch (error) {
            console.error('頁面初始化失敗:', error);
            document.getElementById('info-card-container').innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
        }
    }
    
    function resizeCanvas() {
        const ratio =  Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        signaturePad.clear();
    }

    function renderCostSummary(order) {
        const costSummaryList = document.getElementById('cost-summary-list');
        if (!costSummaryList) return;

        const repairedParts = order.repairedParts || [];
        const techCollectionAmount = order.technicianCollectionAmount || 0;
        const otherDiscount = order.otherDiscount || 0;
        const openInvoice = order.originalOrderTaxAmount > 0;
        
        const repairCost = repairedParts.reduce((sum, item) => sum + (item.subtotalAmount || 0), 0);
        
        const subtotal = repairCost - otherDiscount;
        const tax = openInvoice ? Math.round(subtotal * 0.05) : 0;
        const totalDue = subtotal + tax + techCollectionAmount;
        
        let partsHtml = '';
        if (repairedParts.length > 0) {
            const groupedParts = repairedParts.reduce((acc, part) => {
                const mainProduct = part.mainProductName || '其他零件';
                if (!acc[mainProduct]) acc[mainProduct] = [];
                acc[mainProduct].push(part);
                return acc;
            }, {});

            for (const mainProduct in groupedParts) {
                partsHtml += `<h6 class="mt-3 text-primary">主商品：${mainProduct}</h6>`;
                groupedParts[mainProduct].forEach(part => {
                    partsHtml += `<li class="list-group-item d-flex justify-content-between"><span> - 零件: ${part.productName} (x${part.quantity})</span> <span>${part.subtotalAmount.toLocaleString()}</span></li>`;
                });
            }
            partsHtml += '<hr>';
        }

        costSummaryList.innerHTML = `
            <ul class="list-group list-group-flush">
                ${partsHtml}
                <li class="list-group-item d-flex justify-content-between"><span>裝修費總額</span> <span>${repairCost.toLocaleString()}</span></li>
                <li class="list-group-item d-flex justify-content-between"><span>其他折扣</span> <span class="text-danger">-${otherDiscount.toLocaleString()}</span></li>
                <li class="list-group-item d-flex justify-content-between ${openInvoice ? '' : 'd-none'}"><span>稅金 (5%)</span> <span>${tax.toLocaleString()}</span></li>
                <li class="list-group-item d-flex justify-content-between"><span>技師代收款</span> <span>${techCollectionAmount.toLocaleString()}</span></li>
                <li class="list-group-item d-flex justify-content-between fw-bold fs-5"><span>本次應收總額</span> <span>${totalDue.toLocaleString()}</span></li>
            </ul>`;
    }

    // --- Event Handling ---
    function setupEventListeners() {
        window.addEventListener("resize", resizeCanvas);
        document.getElementById('clear-signature-btn').addEventListener('click', () => signaturePad.clear());
        document.getElementById('complete-btn').addEventListener('click', handleFormSubmit);
        // ...
    }
    
    async function handleFormSubmit() {
        if (signaturePad.isEmpty()) {
            showToast('請客戶簽名', 'warning');
            return;
        }

        const signatureImage = signaturePad.toDataURL("image/png");
        const payload = { signatureImage: signatureImage };
        
        const completeBtn = document.getElementById('complete-btn');
        completeBtn.disabled = true;
        completeBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 處理中...';

        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}/complete-signature`, {
                method: 'POST',
                body: JSON.stringify(payload)
            });
            
            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '操作失敗');
            }

            showToast('簽名步驟已完成！', 'success');
            setTimeout(() => {
                window.location.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;
            }, 1500);

        } catch (error) {
            console.error('完成簽名步驟失敗:', error);
            showToast(`操作失敗: ${error.message}`, 'danger');
            completeBtn.disabled = false;
            completeBtn.innerHTML = '完成簽收';
        }
    }
    
    initializePage();
}); 