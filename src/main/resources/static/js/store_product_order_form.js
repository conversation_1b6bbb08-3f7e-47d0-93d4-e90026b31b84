/**
 * EastKing - Store Product Order Form Logic
 * This script handles the functionality for the store product order form.
 */
document.addEventListener('DOMContentLoaded', function () {

    // --- DOM Element Variables ---
    const orderForm = document.getElementById('store-order-form');
    const companyDivisionSelect = document.getElementById('companyDivision');
    const storeSelect = document.getElementById('store');
    const promotionSelect = document.getElementById('promotionSelect');
    const invoiceDetailsContainer = document.getElementById('invoice-details-container');
    const addPaymentButtons = document.getElementById('add-payment-buttons');
    const paymentContainer = document.getElementById('payment-methods-container');
    const formTitle = document.getElementById('form-title');
    const pageMainTitle = document.getElementById('page-main-title');

    // --- Global State ---
    let isEditMode = false;
    let currentOrderId = null;
    let currentOrderData = null;
    let currentPromotionsData = []; // To store full promotion data
    let eventListenersAttached = false; // Flag to prevent multiple bindings
    let isFromExchangeOrder = false; // 標記此訂單是否由換貨流程產生

    // =================================================================================
    //  FUNCTION DEFINITIONS
    // =================================================================================

    /**
     * Attaches all event listeners for the form.
     */
    function attachEventListeners() {
        if (eventListenersAttached) return; // Prevent re-binding

        if (storeSelect) {
            storeSelect.addEventListener('change', () => {
                loadPromotionsForStore(storeSelect.value).then(() => {
                    applyPromotionsToItems(); // Apply promotions after new ones are loaded
                });
            });
        }
        document.querySelectorAll('input[name="invoiceTypeBtn"]').forEach(radio => {
            radio.addEventListener('change', () => {
                handleInvoiceTypeChange();
                updateTotalAmountsUI(); // Also update totals when invoice type changes
            });
        });
        if (addPaymentButtons) {
            addPaymentButtons.addEventListener('click', handleAddPaymentClick);
        }
        if (paymentContainer) {
            paymentContainer.addEventListener('click', handlePaymentItemActions);
            paymentContainer.addEventListener('input', handlePaymentAmountChange);
            paymentContainer.addEventListener('change', function(event) {
                if (event.target.classList.contains('card-payment-type')) {
                    const row = event.target.closest('.payment-item');
                    const installmentsInput = row.querySelector('.card-installments');
                    if (event.target.value === '2') { // '2' for installments
                        installmentsInput.style.display = 'block';
                        installmentsInput.value = ''; // Clear to force user input
                        installmentsInput.placeholder = "請輸入期數";
                    } else { // '0' for single payment
                        installmentsInput.style.display = 'none';
                        installmentsInput.value = '0'; 
                    }
                }
            });
            paymentContainer.addEventListener('input', async function(event) {
                if (event.target.classList.contains('card-number')) {
                    const cardNumberInput = event.target;
                    const infoDisplay = cardNumberInput.closest('.position-relative').querySelector('.card-info-display');
                    const cardNumber = cardNumberInput.value.replace(/\s+/g, '');

                    infoDisplay.textContent = ''; // 清空舊的查詢結果

                    if (cardNumber.length === 16) {
                        const prefix = cardNumber.substring(0, 8); // 仍然使用前8位查詢
                        infoDisplay.textContent = '查詢中...';
                        try {
                            const response = await window.fetchAuthenticated(`/api/v1/utils/bin-lookup/${prefix}`);
                            if (response.ok) {
                                const result = await response.json();
                                if(result.data) {
                                    infoDisplay.textContent = `${result.data.brand || ''} - ${result.data.issuer || ''}`;
                                } else {
                                    infoDisplay.textContent = '查無卡片資訊';
                                }
                            } else {
                                infoDisplay.textContent = '查詢失敗';
                            }
                        } catch (error) {
                            console.error('BIN lookup error:', error);
                            infoDisplay.textContent = '查詢錯誤';
                        }
                    }
                }
            });
        }
        if (orderForm) {
            orderForm.addEventListener('submit', handleFormSubmit);
        }
        document.getElementById('delete-btn').addEventListener('click', handleDeleteOrder);
        document.getElementById('save-draft-btn').addEventListener('click', () => handleFormSubmit(true));
        document.getElementById('submit-order-btn').addEventListener('click', handleSubmitForPreview);
        document.getElementById('product-search-btn').addEventListener('click', () => handleProductSearch(false));
        document.getElementById('execProductSearchModalBtn').addEventListener('click', () => handleProductSearch(true));
        document.getElementById('productSearchResultsModal').addEventListener('click', handleProductSelection);
        document.getElementById('searchCustomerBtn').addEventListener('click', handleCustomerSearch);
        const customerSelectModalBody = document.getElementById('customerSelectModalBody');
        if (customerSelectModalBody) {
             customerSelectModalBody.addEventListener('click', handleCustomerSelectionFromModal);
        }
        document.querySelectorAll('input[name="taxTypeBtn"]').forEach(radio => {
            radio.addEventListener('change', updateTotalAmountsUI);
        });
        if (promotionSelect) {
            promotionSelect.addEventListener('change', applyPromotionsToItems);
        }

        eventListenersAttached = true; // Set the flag
    }

    /**
     * Populates the 'Company' dropdown based on user's login context and disables it.
     */
    async function populateCompanyDivision() {
        const companyContext = localStorage.getItem('selectedCompanyContext');
        if (companyContext) {
            const companyCode = window.getCompanyDivisionCode();
            const companyName = companyContext === 'QUEYOU' ? '雀友' : '東方不敗';
            companyDivisionSelect.innerHTML = `<option value="${companyCode}">${companyName}</option>`;
        } else {
            showToast('無法獲取公司資訊，請重新登入', 'danger');
            companyDivisionSelect.innerHTML = `<option value="">錯誤</option>`;
        }
    }

    /**
     * Fetches the user's operable stores and populates the 'Store' dropdown.
     */
    async function loadStores() {
        try {
            const response = await fetchAuthenticated('/api/v1/auth/operable-stores');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

        const apiResponse = await response.json();
            if (!apiResponse.data) throw new Error("API response did not contain store data.");

            const stores = apiResponse.data;
            storeSelect.innerHTML = '<option value="">請選擇門市</option>';
            if (stores && stores.length > 0) {
                stores.forEach(store => {
                const option = document.createElement('option');
                option.value = store.storeId;
                option.textContent = store.storeName;
                    storeSelect.appendChild(option);
            });
        } else {
                storeSelect.innerHTML = '<option value="">無可用門市</option>';
        }
    } catch (error) {
            console.error('無法載入門市列表:', error);
            showToast('無法載入門市列表', 'danger');
            storeSelect.innerHTML = '<option value="">載入失敗</option>';
        }
    }

    /**
     * Fetches and populates the 'Promotions' dropdown based on the selected store.
     */
    async function loadPromotionsForStore(storeId) {
        if (!storeId) {
            promotionSelect.innerHTML = '<option value="">請先選擇門市</option>';
            promotionSelect.disabled = true;
            return;
        }

        promotionSelect.innerHTML = '<option value="">載入中...</option>';
        promotionSelect.disabled = true;
        try {
            const url = `/api/v1/promotions/searchable?isActive=true&storeId=${storeId}`;
            const response = await fetchAuthenticated(url);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

            const apiResponse = await response.json();
            const promotions = apiResponse.data;
            currentPromotionsData = promotions; // Store full promotion data

            promotionSelect.innerHTML = '<option value="">請選擇優惠活動</option>';
            if (promotions && promotions.length > 0) {
                promotions.forEach(promo => {
                    const option = document.createElement('option');
                    option.value = promo.promotionId;
                    option.textContent = promo.promotionName;
                    promotionSelect.appendChild(option);
            });
        } else {
                promotionSelect.innerHTML = '<option value="">此門市無適用活動</option>';
        }
    } catch (error) {
            console.error(`無法載入門市 ${storeId} 的優惠活動:`, error);
            promotionSelect.innerHTML = '<option value="">載入失敗</option>';
        } finally {
            promotionSelect.disabled = false;
        }
    }

    /**
     * Handles the change event for invoice type radio buttons.
     */
    function handleInvoiceTypeChange() {
        const taxIdNumberInput = document.getElementById('taxIdNumber');
        const invoiceCompanyTitleInput = document.getElementById('invoiceCompanyTitle');
        const selectedTypeRadio = document.querySelector('input[name="invoiceTypeBtn"]:checked');

        if (!selectedTypeRadio) return; 
        const selectedType = selectedTypeRadio.value;

        const shouldShowDetails = selectedType === '2' || selectedType === '3';
        invoiceDetailsContainer.style.display = shouldShowDetails ? 'flex' : 'none';

        if (shouldShowDetails) {
            const isThreePart = selectedType === '3';
            taxIdNumberInput.disabled = !isThreePart;
            invoiceCompanyTitleInput.disabled = !isThreePart;
            if (!isThreePart) {
                taxIdNumberInput.value = '';
                invoiceCompanyTitleInput.value = '';
            }
        } else { 
            const allInvoiceInputs = invoiceDetailsContainer.querySelectorAll('input');
            allInvoiceInputs.forEach(input => input.value = '');
        }
    }

    /**
     * Handles clicks within the 'add payment' button group.
     */
    function handleAddPaymentClick(event) {
        console.log("Add Payment button clicked. Event target:", event.target);
        if (event.target.matches('button[data-payment-type]')) {
            const paymentType = event.target.dataset.paymentType;
            console.log("Payment type selected:", paymentType);
            addPaymentMethodRow(paymentType);
        }
    }

    /**
     * Dynamically adds a payment method row to the DOM.
     */
    function addPaymentMethodRow(type) {
        const paymentId = `payment-${Date.now()}`;
        let contentHtml = '';

        const baseRowStart = `<div class="payment-item row g-3 mb-2 align-items-center" id="${paymentId}" data-type="${type}">`;
        const removeButton = `<div class="col-md-1"><button type="button" class="btn btn-danger btn-sm remove-payment-btn">移除</button></div>`;
        const baseRowEnd = `</div>`;

        switch (type) {
            case 'cash':
                contentHtml = `<div class="col-md-3"><input type="text" class="form-control" value="現金" readonly></div><div class="col-md-4"><input type="number" class="form-control payment-amount" placeholder="金額"></div><div class="col-md-4"></div>`;
                break;
            case 'credit_card':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="信用卡" readonly></div>
                    <div class="col-md-3 position-relative">
                        <input type="text" class="form-control card-number" placeholder="輸入卡號自動帶入資訊">
                        <span class="card-info-display text-muted small position-absolute top-100 start-0"></span>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select card-payment-type">
                            <option value="0" selected>單筆</option>
                            <option value="2">分期</option>
                        </select>
                    </div>
                    <div class="col-md-2"><input type="number" class="form-control card-installments" placeholder="期數" value="0" style="display: none;"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
            case 'bank_transfer':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="匯款" readonly></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-name" placeholder="銀行名稱"></div>
                    <div class="col-md-2"><input type="text" class="form-control remitter-account" placeholder="匯出帳號後五碼"></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-account" placeholder="匯入帳號後五碼"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
        }

        const paymentHtml = `${baseRowStart}${contentHtml}${removeButton}${baseRowEnd}`;
        paymentContainer.insertAdjacentHTML('beforeend', paymentHtml);
        // Add listener to the new amount input
        paymentContainer.querySelector(`#${paymentId} .payment-amount`).addEventListener('input', updateTotalAmountsUI);
    }

    /**
     * Handles clicks on remove buttons for payment items.
     */
    function handlePaymentItemActions(event) {
        if (event.target.classList.contains('remove-payment-btn')) {
            event.target.closest('.payment-item').remove();
            updateTotalAmountsUI(); // Re-calculate totals after removing an item
        }
    }

    /**
     * Handles input changes on payment amount fields.
     */
    function handlePaymentAmountChange(event) {
         if (event.target.classList.contains('payment-amount')) {
            updateTotalAmountsUI();
        }
    }

    function applyPromotionsToItems() {
        const selectedPromotionId = promotionSelect.value;
        const selectedPromotion = currentPromotionsData.find(p => p.promotionId === selectedPromotionId);

        document.querySelectorAll('#order-items-table tbody tr').forEach(row => {
            const barcode = row.dataset.barcode;
            const originalPriceInput = row.querySelector('.original-price-input');
            const promoPriceInput = row.querySelector('.promo-price-input');
            const priceInput = row.querySelector('.price-input'); // The one used for calculation

            let originalPrice = parseFloat(originalPriceInput.dataset.originalPrice);
            let finalPrice = originalPrice;

            if (selectedPromotion && selectedPromotion.products) {
                const promoProduct = selectedPromotion.products.find(p => p.productBarcode === barcode);
                if (promoProduct) {
                    finalPrice = promoProduct.promoPrice;
                    promoPriceInput.value = finalPrice; // Use raw number for input value
                    promoPriceInput.classList.add('text-success', 'fw-bold');
                } else {
                    promoPriceInput.value = originalPrice; // Use raw number for input value
                    promoPriceInput.classList.remove('text-success', 'fw-bold');
                }
            } else {
                 promoPriceInput.value = originalPrice; // Use raw number for input value
                 promoPriceInput.classList.remove('text-success', 'fw-bold');
            }
            priceInput.value = finalPrice;
        });
        updateTotalAmountsUI();
    }

    /**
     * Handles the main form submission for both saving as draft and submitting.
     * @param {boolean} isDraft - True if saving as draft, false if submitting.
     */
    async function handleFormSubmit(isDraft, isSubmittingForPreview = false) {
        if(window.event && !isSubmittingForPreview) window.event.preventDefault(); // 在預覽模式下不阻止事件

        // --- START: Validation Logic ---
        let paymentValidationFailed = false;
        document.querySelectorAll('#payment-methods-container .payment-item').forEach(row => {
            const amountInput = row.querySelector('.payment-amount');
            const amount = parseFloat(amountInput.value);
            if (!amount || amount <= 0) {
                showToast('請填寫所有付款項目的金額，且金額必須大於 0。', 'warning');
                amountInput.classList.add('is-invalid'); // Highlight the invalid input
                paymentValidationFailed = true;
        } else {
                amountInput.classList.remove('is-invalid');
            }
        });

        if (paymentValidationFailed) {
            return; // Stop the submission
        }
        // --- END: Validation Logic ---

            const payload = buildPayload();

        // 只有存檔時，狀態才是草稿
        if (isDraft) {
            payload.orderStatusCode = 10; 
        }

        const url = isEditMode ? `/api/v1/orders/${currentOrderId}/correct` : '/api/v1/orders/store';
        const method = isEditMode ? 'PUT' : 'POST';

        try {
            const response = await window.fetchAuthenticated(url, {
                method: method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
        const result = await response.json();
            if (!response.ok) throw new Error(result.message || '儲存失敗');

            showToast('訂單已儲存', 'success');

            setTimeout(() => {
                if (!isEditMode && result.data && result.data.orderId) {
                    // This is a new order. Redirect to the edit page for this new order.
                    window.location.href = `store_product_order_form.html?orderId=${result.data.orderId}`;
                } else {
                    // This is an existing order. Reload to show the latest state.
                    location.reload();
                }
            }, 1000); // 1-second delay to allow toast to be seen

            return result.data; // 返回儲存後的訂單資料
            } catch (error) {
            console.error('儲存訂單失敗:', error);
            showToast(`儲存失敗: ${error.message}`, 'error');
            return null; // 返回 null 表示失敗
        }
    }

    async function handleSubmitForPreview(event) {
        event.preventDefault();
        // 步驟 1: 先呼叫存檔邏輯
        const savedOrder = await handleFormSubmit(true, true);

        // 步驟 2: 如果存檔成功，則跳轉到詳情頁
        if (savedOrder && savedOrder.orderId) {
            window.location.href = `order_detail.html?orderId=${savedOrder.orderId}`;
        }
    }

    /**
     * Handles deleting the order.
     */
    async function handleDeleteOrder() {
        if (!isEditMode || !currentOrderId) {
            showToast('只有已儲存的草稿訂單才能被刪除', 'warning');
        return;
    }

        if (confirm('您確定要刪除此訂單嗎？此操作無法復原。')) {
            try {
                const response = await fetchAuthenticated(`/api/v1/orders/${currentOrderId}`, {
                    method: 'DELETE'
                });
                if (!response.ok) {
                    const errData = await response.json();
                    throw new Error(errData.message || '刪除失敗');
                }
                showToast('訂單已成功刪除', 'success');
                window.location.href = 'store_product_orders_list.html';
    } catch (error) {
                console.error('刪除訂單失敗:', error);
                showToast(`刪除失敗: ${error.message}`, 'danger');
            }
        }
    }

    function buildPayload() {
        const items = [];
        document.querySelectorAll('#order-items-table tbody tr').forEach(row => {
            const item = {
                productName: row.querySelector('td:nth-child(1)').textContent,
                productBarcode: row.querySelector('td:nth-child(2)').textContent,
                quantity: parseInt(row.querySelector('.quantity-input').value),
                unitPrice: parseFloat(row.querySelector('.price-input').value),
                // Assuming default itemTypeCode for existing items if not specified
                itemTypeCode: 0 
            };
            items.push(item);
        });

        const paymentDetails = [];
        document.querySelectorAll('.payment-item').forEach(row => {
            const paymentMethodCode = row.dataset.type.toUpperCase();
            const amountInput = row.querySelector('.payment-amount');
            const amount = amountInput ? amountInput.value : null;

            if(amount && parseFloat(amount) > 0) {
                const cardInfoDisplay = row.querySelector('.card-info-display');
                let cardBrand = null;
                let cardIssuer = null;
                if (cardInfoDisplay && cardInfoDisplay.textContent && cardInfoDisplay.textContent.includes(' - ')) {
                    [cardBrand, cardIssuer] = cardInfoDisplay.textContent.split(' - ').map(s => s.trim());
                }
                const detail = {
                    paymentMethodCode: paymentMethodCode,
                    amount: parseFloat(amount),
                    cardNumber: row.querySelector('.card-number')?.value || null,
                    cardInstallments: row.querySelector('.card-installments')?.value ? parseInt(row.querySelector('.card-installments').value) : 1,
                    bankName: row.querySelector('.bank-name')?.value || null,
                    remitterAccountLastFive: row.querySelector('.remitter-account')?.value || null,
                    bankAccountLastFive: row.querySelector('.bank-account')?.value || null,
                    collectorName: null, // Add logic for this if needed
                    remarks: null, // Add logic for this if needed
                    cardBrand: cardBrand,
                    cardIssuer: cardIssuer,
                };
                paymentDetails.push(detail);
            }
        });

        const payload = {
            orderId: currentOrderId,
            companyDivisionCode: document.getElementById('companyDivision').value,
            storeId: document.getElementById('store').value,
            orderDate: document.getElementById('orderDate').value ? new Date(document.getElementById('orderDate').value).toISOString() : null,

            // --- START: Add Customer Info ---
            memberId: document.getElementById('customerId').value || null,
            customerName: document.getElementById('customerName').value,
            customerPhone: document.getElementById('customerPhoneDisplay').value,
            // --- END: Add Customer Info ---

            promotionId: document.getElementById('promotionSelect').value || null,

            items: items,
            paymentDetails: paymentDetails,

            // Invoice Information
            invoiceTypeCode: document.querySelector('input[name="invoiceTypeBtn"]:checked')?.value || null,
            taxIdNumber: document.getElementById('taxIdNumber')?.value || null,
            invoiceCompanyTitle: document.getElementById('invoiceCompanyTitle')?.value || null,
            invoiceDate: document.getElementById('invoiceDate')?.value || null,
            invoiceNumber: document.getElementById('invoiceNumber')?.value || null,

            remarks: document.getElementById('orderRemarks')?.value || null,

            // CRITICAL FIX: Always include the order type code for store orders.
            orderTypeCode: 1, // 1 corresponds to OrderTypeEnum.STORE_PRODUCT_ORDER

            // Include taxType parameter
            taxType: document.querySelector('input[name="taxTypeBtn"]:checked')?.value === 'inclusive' ? 2 : 1
        };
        return payload;
    }

    /**
     * Main initialization function, orchestrates the form setup.
     */
    async function initStoreOrderForm() {
        attachEventListeners();

        const urlParams = new URLSearchParams(window.location.search);
        currentOrderId = urlParams.get('orderId');
        isEditMode = !!currentOrderId;

        await populateCompanyDivision();
        await loadStores();
        attachEventListeners();

        handleInvoiceTypeChange();

        if (isEditMode) {
            formTitle.textContent = '修改門市商品訂單';
            pageMainTitle.textContent = '修改門市商品訂單';
            await loadOrderForEditing(currentOrderId);
        } else {
            // New order mode logic
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            const todayString = `${year}-${month}-${day}`;

            document.getElementById('orderDate').value = todayString;
            document.getElementById('invoiceDate').value = todayString; // Set invoice date as well

            storeSelect.dispatchEvent(new Event('change'));

            // For a new order, only "Save Draft" and "Submit" should be visible initially.
            document.getElementById('save-draft-btn').style.display = 'inline-block';
            document.getElementById('submit-order-btn').style.display = 'inline-block';
            document.getElementById('delete-btn').style.display = 'none'; // Explicitly hide delete
        }
    }

    async function loadOrderForEditing(orderId) {
        try {
            const response = await fetchAuthenticated(`/api/v1/orders/${orderId}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

            const apiResponse = await response.json();
            if (!apiResponse.data) throw new Error("API did not return order data.");

            currentOrderData = apiResponse.data;
            populateForm(currentOrderData);

            // After populating form, if we have a storeId, load its promotions
            if (currentOrderData.storeId) {
                await loadPromotionsForStore(currentOrderData.storeId);
                // After promotions are loaded, set the selected value if it exists in the order data
                if (currentOrderData.promotionId) {
                    promotionSelect.value = currentOrderData.promotionId;
                }
        }

    } catch (error) {
            console.error('Failed to load order for editing:', error);
            showToast('載入訂單資料失敗', 'danger');
        }
    }

    function populateForm(order) {
        // Populate simple fields
        document.getElementById('companyDivision').value = order.companyDivisionCode;
        document.getElementById('store').value = order.storeId;

        // Populate Order Number in the header
        const orderNumberDisplay = document.getElementById('orderNumberDisplay');
        if (orderNumberDisplay) {
            orderNumberDisplay.textContent = `#${order.orderNumber || ''}`;
        }

        // Correctly format the date for the input[type="date"]
        document.getElementById('orderDate').value = order.orderDate ? order.orderDate.substring(0, 10) : '';

        document.getElementById('orderStatus').value = order.orderStatusDescription || '草稿';

        // Populate customer info by calling the helper function
        populateCustomerInfo({
            customerId: order.memberId,
            customerName: order.customerName,
            phoneNumber: order.customerPhone
        });

        // Items - this requires dynamically creating rows
        const itemsContainer = document.getElementById('order-items-table').querySelector('tbody');
        itemsContainer.innerHTML = ''; // Clear existing
        order.items.forEach(item => addProductItemRow(item, true)); // Reuse or create a function to add rows

        // Totals
        updateTotalAmountsUI(); // A function to calculate and display totals based on items

        // Invoice
        const invoiceTypeRadio = document.querySelector(`input[name="invoiceTypeBtn"][value="${order.invoiceTypeCode}"]`);
        if (invoiceTypeRadio) invoiceTypeRadio.checked = true;
        handleInvoiceTypeChange(); // Trigger UI update based on selection
        document.getElementById('taxIdNumber').value = order.taxIdNumber || '';
        document.getElementById('invoiceCompanyTitle').value = order.invoiceCompanyTitle || '';
        document.getElementById('invoiceDate').value = order.invoiceDate || '';
        document.getElementById('invoiceNumber').value = order.invoiceNumber || '';

        // Payments - this now requires dynamic row creation based on detailed data
        paymentContainer.innerHTML = '';
        if (order.paymentDetails && order.paymentDetails.length > 0) {
            order.paymentDetails.forEach(p => {
                addPaymentMethodRowAndPopulate(p);
                // 檢查是否有換貨轉入的付款方式
                if (p.paymentMethodCode.toUpperCase() === 'EXCHANGE') {
                    isFromExchangeOrder = true;
                }
            });
        }
        updateTotalAmountsUI(); // To calculate paid amount and due amount

        // Remarks
        document.getElementById('orderRemarks').value = order.remarks || '';

        // --- Handle Button Visibility Based on Status ---
        const saveDraftBtn = document.getElementById('save-draft-btn');
        const submitBtn = document.getElementById('submit-order-btn');
        const deleteBtn = document.getElementById('delete-btn');

        if (order.orderStatusCode === 10) { // 10 is 'DRAFT' status
            saveDraftBtn.style.display = 'inline-block';
            submitBtn.style.display = 'inline-block';
            deleteBtn.style.display = 'inline-block';
        } else {
            saveDraftBtn.style.display = 'none';
            submitBtn.style.display = 'none';
            deleteBtn.style.display = 'none';
        }
    }

    // New helper function to add and populate a payment row
    function addPaymentMethodRowAndPopulate(paymentDetail) {
        const type = paymentDetail.paymentMethodCode.toLowerCase();
        const paymentId = `payment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        let contentHtml = '';
        let removeButtonHtml = `<div class="col-md-1"><button type="button" class="btn btn-danger btn-sm remove-payment-btn"><i class="bi bi-trash"></i></button></div>`;

        // 如果是換貨轉入，則不顯示移除按鈕並禁用欄位
        if (type === 'exchange') {
            removeButtonHtml = '<div class="col-md-1"></div>'; // Placeholder
            contentHtml = `
                <div class="col-md-3"><input type="text" class="form-control" value="換貨轉入" readonly></div>
                <div class="col-md-4"><input type="number" class="form-control payment-amount" value="${paymentDetail.amount}" readonly></div>
                <div class="col-md-4"></div>`;
        } else {
        switch (type) {
            case 'cash':
                contentHtml = `<div class="col-md-3"><input type="text" class="form-control" value="現金" readonly></div><div class="col-md-4"><input type="number" class="form-control payment-amount" placeholder="金額"></div><div class="col-md-4"></div>`;
                break;
            case 'credit_card':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="信用卡" readonly></div>
                        <div class="col-md-3 position-relative">
                            <input type="text" class="form-control card-number" placeholder="輸入卡號自動帶入資訊">
                            <span class="card-info-display text-muted small position-absolute top-100 start-0"></span>
                        </div>
                    <div class="col-md-2">
                        <select class="form-select card-payment-type">
                            <option value="0" selected>單筆</option>
                            <option value="2">分期</option>
                        </select>
                    </div>
                    <div class="col-md-2"><input type="number" class="form-control card-installments" placeholder="期數" value="0" style="display: none;"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
            case 'bank_transfer':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="匯款" readonly></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-name" placeholder="銀行名稱"></div>
                    <div class="col-md-2"><input type="text" class="form-control remitter-account" placeholder="匯出帳號後五碼"></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-account" placeholder="匯入帳號後五碼"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
        }
        }

        const paymentRowContainer = document.createElement('div');
        paymentRowContainer.className = 'payment-item row g-3 mb-2 align-items-center';
        paymentRowContainer.id = paymentId;
        paymentRowContainer.dataset.type = type;
        paymentContainer.appendChild(paymentRowContainer);

        paymentRowContainer.innerHTML = contentHtml + removeButtonHtml;

        // 4. Now, safely query within the specific container and populate data
        const newRow = document.getElementById(paymentId);
        if (!newRow) return;

        newRow.querySelector('.payment-amount').value = paymentDetail.amount;

        if (type === 'credit_card') {
            const cardNumberInput = newRow.querySelector('.card-number');
            if(cardNumberInput) cardNumberInput.value = paymentDetail.cardNumber || '';

            // 顯示已儲存的卡片資訊
            const infoDisplay = newRow.querySelector('.card-info-display');
            if (infoDisplay && paymentDetail.cardBrand && paymentDetail.cardIssuer) {
                infoDisplay.textContent = `${paymentDetail.cardBrand} - ${paymentDetail.cardIssuer}`;
            }

            const paymentTypeSelect = newRow.querySelector('.card-payment-type');
            const installmentsInput = newRow.querySelector('.card-installments');
            if (paymentTypeSelect && installmentsInput) {
            if (paymentDetail.cardInstallments > 1) {
                    paymentTypeSelect.value = '2'; // '2' for installments
                installmentsInput.style.display = 'block';
                installmentsInput.value = paymentDetail.cardInstallments;
            } else {
                    paymentTypeSelect.value = '0'; // '0' for single payment
                installmentsInput.style.display = 'none';
                    installmentsInput.value = '1';
                }
            }
        } else if (type === 'bank_transfer') {
            const bankNameInput = newRow.querySelector('.bank-name');
            const remitterInput = newRow.querySelector('.remitter-account');
            const bankAccountInput = newRow.querySelector('.bank-account');
            if(bankNameInput) bankNameInput.value = paymentDetail.bankName || '';
            if(remitterInput) remitterInput.value = paymentDetail.remitterAccountLastFive || '';
            if(bankAccountInput) bankAccountInput.value = paymentDetail.bankAccountLastFive || '';
        }
    }

    function addProductItemRow(item, isEditing = false) {
        const tableBody = document.getElementById('order-items-table').querySelector('tbody');
        const rowId = `item-${item.productBarcode || Date.now()}`;
        const newRow = document.createElement('tr');
        newRow.id = rowId;
        newRow.dataset.itemId = item.orderItemId || '';
        newRow.dataset.barcode = item.productBarcode;

        const originalPrice = item.unitPrice || 0;

        newRow.innerHTML = `
            <td>${item.productName || 'N/A'}</td>
            <td>${item.productBarcode || 'N/A'}</td>
            <td><input type="number" class="form-control form-control-sm original-price-input bg-light" value="${originalPrice}" readonly data-original-price="${originalPrice}"></td>
            <td><input type="number" class="form-control form-control-sm promo-price-input bg-light" value="${originalPrice}" readonly></td>
            <td><input type="number" class="form-control form-control-sm quantity-input" value="${item.quantity || 1}" min="1"></td>
            <td class="item-subtotal">${(originalPrice * (item.quantity || 1)).toLocaleString()}</td>
            <td>
                <input type="hidden" class="price-input" value="${originalPrice}">
                <button type="button" class="btn btn-danger btn-sm remove-item-btn"><i class="bi bi-trash"></i></button>
            </td>
        `;
        tableBody.appendChild(newRow);

        // Add listeners to the new row's inputs for real-time total calculation
        newRow.querySelector('.quantity-input').addEventListener('input', updateTotalAmountsUI);

        // 為新產生的刪除按鈕綁定事件
        newRow.querySelector('.remove-item-btn').addEventListener('click', function() {
            newRow.remove();
            updateTotalAmountsUI();
        });

        applyPromotionsToItems();
    }

    function updateTotalAmountsUI() {
        let netTotalFromItems = 0;
        document.querySelectorAll('#order-items-table tbody tr').forEach(row => {
            const price = parseFloat(row.querySelector('.price-input')?.value) || 0;
            const quantity = parseInt(row.querySelector('.quantity-input')?.value) || 0;
            const subtotal = price * quantity;
            row.querySelector('.item-subtotal').textContent = subtotal.toLocaleString();
            netTotalFromItems += subtotal;
        });

        const invoiceType = document.querySelector('input[name="invoiceTypeBtn"]:checked').value;
        const taxType = document.querySelector('input[name="taxTypeBtn"]:checked').value;

        let finalNetAmount = netTotalFromItems;
        let finalTaxAmount = 0;
        let finalGrandTotal = netTotalFromItems;

        if (invoiceType === '2' || invoiceType === '3') { // Taxable invoices
            if (taxType === 'inclusive') {
                // Reverse calculation: netTotalFromItems is considered the grand total
                finalGrandTotal = netTotalFromItems;
                finalNetAmount = Math.round(finalGrandTotal / 1.05);
                finalTaxAmount = finalGrandTotal - finalNetAmount;
            } else { // 'exclusive'
                finalTaxAmount = Math.round(netTotalFromItems * 0.05);
                finalGrandTotal = netTotalFromItems + finalTaxAmount;
            }
        } else { // No invoice, so no tax
            finalTaxAmount = 0;
            finalGrandTotal = netTotalFromItems;
        }

        document.getElementById('productsTotalAmount').textContent = `NT$ ${netTotalFromItems.toLocaleString()}`;
        document.getElementById('discountAmount').textContent = `NT$ 0`; // Placeholder
        document.getElementById('netAmount').textContent = `NT$ ${finalNetAmount.toLocaleString()}`;
        document.getElementById('taxAmount').textContent = `NT$ ${finalTaxAmount.toLocaleString()}`;
        document.getElementById('grandTotalAmount').textContent = `NT$ ${finalGrandTotal.toLocaleString()}`;

    let totalPaid = 0;
        document.querySelectorAll('#payment-methods-container .payment-amount').forEach(input => {
            totalPaid += parseFloat(input.value) || 0;
        });

        let amountDue = 0;
        if (isFromExchangeOrder) {
            // 如果是換貨訂單，只有當新訂單總額超過原訂單轉入的金額時，才產生待付差額
            if (finalGrandTotal > totalPaid) {
                amountDue = finalGrandTotal - totalPaid;
            } else {
                amountDue = 0; // 否則待付差額為 0
            }
        } else {
            // 一般訂單的邏輯保持不變
            amountDue = finalGrandTotal - totalPaid;
        }

        // Update the payment summary UI
        document.getElementById('totalPaidAmountDisplay').textContent = `NT$ ${totalPaid.toLocaleString()}`;
        document.getElementById('amountDueDisplay').textContent = `NT$ ${amountDue.toLocaleString()}`;

        // --- START: Enable/Disable Submit Button based on Amount Due ---
        const submitOrderBtn = document.getElementById('submit-order-btn');
        if (submitOrderBtn) {
            // 由於 Javascript 浮點數計算可能存在精度問題，將差額四捨五入到分來比較
            const roundedAmountDue = Math.round(amountDue * 100) / 100;
            // 只有當待付差額精確等於 0 且訂單總金額大於 0 時，才啟用按鈕
            if (roundedAmountDue === 0 && finalGrandTotal > 0) {
                submitOrderBtn.disabled = false;
            } else {
                submitOrderBtn.disabled = true;
            }
        }
        // --- END: Enable/Disable Submit Button based on Amount Due ---
    }

    /**
     * Handles the product search button click.
     */
    async function handleProductSearch(isModalSearch = false) {
        const storeId = document.getElementById('store').value;
        if (!storeId) {
            showToast('請先選擇一個門市', 'warning');
            return;
        }

        const keywordInput = isModalSearch ? document.getElementById('productSearchKeywordModal') : document.getElementById('productKeyword');
        const keyword = keywordInput.value.trim();
        if (!keyword) {
            showToast('請輸入商品關鍵字', 'warning');
            return;
        }

        const searchResultsContainer = document.getElementById('productSearchResultsModal');
        searchResultsContainer.innerHTML = '<div class="list-group-item">搜尋中...</div>';

        if (!isModalSearch) {
            const productSearchModal = new bootstrap.Modal(document.getElementById('productSearchModal'));
            productSearchModal.show();
        }

        try {
            const url = `/api/v1/product-settings/search-store-products?keyword=${encodeURIComponent(keyword)}&storeId=${storeId}`;
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) throw new Error('搜尋商品失敗');

            const apiResponse = await response.json();
            const products = apiResponse.data;

            searchResultsContainer.innerHTML = '';
            if (products && products.length > 0) {
                products.forEach(product => {
                    const price = product.salePrice !== null ? product.salePrice : 0;
                    const stock = product.stockQuantity !== null ? product.stockQuantity : 'N/A';
                    const productHtml = `
                        <a href="#" class="list-group-item list-group-item-action select-product-btn" 
                           data-product-barcode="${product.productBarcode}" 
                           data-product-name="${product.productName}" 
                           data-price="${price}">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${product.productName}</h6>
                                <small>庫存: ${stock}</small>
                            </div>
                            <p class="mb-1">${product.productBarcode}</p>
                            <small>價格: NT$ ${price.toLocaleString()}</small>
                        </a>
                    `;
                    searchResultsContainer.insertAdjacentHTML('beforeend', productHtml);
                });
            } else {
                searchResultsContainer.innerHTML = '<div class="list-group-item">在該門市找不到符合條件的商品。</div>';
            }
        } catch (error) {
            console.error('商品搜尋錯誤:', error);
            searchResultsContainer.innerHTML = '<div class="list-group-item text-danger">搜尋時發生錯誤。</div>';
        }
    }

    /**
     * Handles the selection of a product from the search modal.
     */
    function handleProductSelection(event) {
        event.preventDefault();
        const target = event.target.closest('.select-product-btn');
        if (!target) return;

        const product = {
            productBarcode: target.dataset.productBarcode,
            productName: target.dataset.productName,
            unitPrice: parseFloat(target.dataset.price),
            quantity: 1,
            itemTypeCode: 0 // Default to 0 (MAIN_PRODUCT)
        };

        addProductItemRow(product);
        updateTotalAmountsUI();

        const productSearchModal = bootstrap.Modal.getInstance(document.getElementById('productSearchModal'));
        productSearchModal.hide();
    }

    /**
     * Handles the customer search by phone number.
     */
    async function handleCustomerSearch() {
        const phoneInput = document.getElementById('customerPhoneSearch');
        const phoneNumber = phoneInput.value.trim();
        if (!phoneNumber) {
            showToast('請輸入會員電話號碼', 'warning');
        return;
    }

        try {
            const response = await fetchAuthenticated(`/api/v1/customers/by-phone?phoneNumber=${encodeURIComponent(phoneNumber)}`);
            if (!response.ok) throw new Error('會員查詢失敗');

            const apiResponse = await response.json();
            const customers = apiResponse.data;

            if (!customers || customers.length === 0) {
                showToast('查無此會員資料，您可以直接手動輸入客戶姓名與電話，或點擊按鈕快速新增會員。', 'info');
                document.getElementById('createNewCustomerBtn').style.display = 'inline-block';
            } else if (customers.length === 1) {
                populateCustomerInfo(customers[0]);
        } else {
                // Multiple customers found, show modal to select one
                populateCustomerSelectionModal(customers);
                const customerSelectModal = new bootstrap.Modal(document.getElementById('customerSelectModal'));
                customerSelectModal.show();
        }
    } catch (error) {
            console.error('Error searching customer:', error);
            showToast(`查詢會員時發生錯誤: ${error.message}`, 'danger');
        }
    }

    /**
     * Populates the customer selection modal with multiple results.
     */
    function populateCustomerSelectionModal(customers) {
        const modalBody = document.getElementById('customerSelectModalBody');
        modalBody.innerHTML = ''; // Clear previous results
        customers.forEach(customer => {
            const customerElement = document.createElement('a');
            customerElement.href = '#';
            customerElement.className = 'list-group-item list-group-item-action select-customer-option';
            customerElement.dataset.customerId = customer.customerId;
            customerElement.dataset.customerName = customer.customerName;
            customerElement.dataset.customerPhone = customer.phoneNumber;
            customerElement.innerHTML = `<strong>${customer.customerName}</strong><br><small>${customer.phoneNumber} - ${customer.companyDivisionDescription || ''}</small>`;
            modalBody.appendChild(customerElement);
        });
    }

    /**
     * Handles the click event when a customer is selected from the modal.
     */
    function handleCustomerSelectionFromModal(event) {
        event.preventDefault();
        const target = event.target.closest('.select-customer-option');
        if (!target) return;

        const customer = {
            customerId: target.dataset.customerId,
            customerName: target.dataset.customerName,
            phoneNumber: target.dataset.customerPhone
        };
        populateCustomerInfo(customer);

        const modalInstance = bootstrap.Modal.getInstance(document.getElementById('customerSelectModal'));
        modalInstance.hide();
    }

    /**
     * Populates the customer info fields on the main form.
     */
    function populateCustomerInfo(customer) {
        document.getElementById('customerId').value = customer.customerId || '';
        document.getElementById('customerName').value = customer.customerName || '';
        document.getElementById('customerPhoneDisplay').value = customer.phoneNumber || '';
        document.getElementById('customerPhoneSearch').value = customer.phoneNumber || '';

        const createBtn = document.getElementById('createNewCustomerBtn');
        if (createBtn) {
            createBtn.style.display = 'none';
        }
    }

    // --- EXECUTION ---
    initStoreOrderForm();
}); 
