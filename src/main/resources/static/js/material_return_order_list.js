document.addEventListener('DOMContentLoaded', function () {
    
    const materialReturnOrderList = document.getElementById('material-return-order-list');
    const statusFilter = document.getElementById('status-filter');
    const startDateFilter = document.getElementById('start-date-filter');
    const endDateFilter = document.getElementById('end-date-filter');
    const orderNumberFilter = document.getElementById('order-number-filter');
    const paginationControls = document.createElement('div');
    paginationControls.id = 'pagination-controls';
    materialReturnOrderList.after(paginationControls);

    async function loadStatusFilter() {
        if (!statusFilter) return;
        try {
            const response = await window.fetchAuthenticated('/api/v1/enums/material-return-order-statuses');
            if (!response.ok) throw new Error('無法獲取狀態列表');
            const result = await response.json();
            if (result.code === 200 && Array.isArray(result.data)) {
                populateDropdown(statusFilter, result.data, '所有狀態');
            }
        } catch (error) {
            console.error('載入退料單狀態失敗:', error);
        }
    }
    
    function populateDropdown(selectElement, options, defaultText) {
        selectElement.innerHTML = `<option value="">${defaultText}</option>`;
        if (options && Array.isArray(options)) {
            options.forEach(option => {
                selectElement.innerHTML += `<option value="${option.value}">${option.label}</option>`;
            });
        }
    }

    async function searchMaterialReturnOrders(page = 1) {
        const getFilterValue = (element) => {
            return element.value === element.options[0].text ? "" : element.value;
        };
        
        const filters = {
            statusCode: getFilterValue(statusFilter),
            startDate: startDateFilter.value,
            endDate: endDateFilter.value,
            orderNumber: orderNumberFilter.value
        };

        const params = new URLSearchParams({ page: page - 1, size: 15, sort: 'createTime,asc' });
        for (const key in filters) {
            if (filters[key]) {
                params.append(key, filters[key]);
            }
        }
        
        materialReturnOrderList.innerHTML = '<div class="text-center p-4"><span class="spinner-border spinner-border-sm"></span> 查詢中...</div>';

        try {
            const response = await window.fetchAuthenticated(`/api/v1/material-orders/refund?${params.toString()}`);
            if (!response.ok) throw new Error('無法獲取退料單列表');
            const result = await response.json();
            if (result.data) {
                renderMaterialReturnOrders(result.data.list || []);
                renderPagination(result.data);
            } else {
                renderMaterialReturnOrders([]);
                renderPagination(null);
            }
        } catch (error) {
            console.error('查詢退料單失敗:', error);
            materialReturnOrderList.innerHTML = '<div class="alert alert-danger">無法載入退料單。</div>';
        }
    }

    function renderMaterialReturnOrders(orders) {
        const listEl = document.getElementById('material-return-order-list');
        listEl.innerHTML = '';
        if (!orders || orders.length === 0) {
            listEl.innerHTML = '<div class="text-center p-4 text-muted">查無相關退料單。</div>';
            return;
        }
        orders.forEach((order, index) => {
            const card = document.createElement('div');
            card.className = 'card';
            card.innerHTML = `
                <div class="item-seq">${String((index + 1)).padStart(2, '0')}</div>
                <div class="item-info">
                    <p><strong>單號：</strong>${order.materialReturnOrderNumber}</p>
                    <p><strong>建單時間：</strong>${new Date(order.createTime).toLocaleString()}</p>
                    <p><strong>退料倉庫：</strong>${order.warehouseName || 'N/A'}</p>
                    <p><strong>退料單狀態：</strong><span class="badge ${getStatusBadgeClass(order.statusCode)}">${order.statusDescription}</span></p>
                </div>
                <div class="item-action">
                    <a href="material_return_order_detail.html?id=${order.materialReturnOrderId}" class="btn btn-outline-primary"><i class="bi bi-search"></i></a>
                </div>
            `;
            listEl.appendChild(card);
        });
    }

    function getStatusBadgeClass(statusCode) {
        // Define status colors for material return orders
        switch (statusCode) {
            case -10: // 待退料
                return 'bg-secondary';
            case -20: // 已驗料
                return 'bg-primary';
            case -30: // 退料完成
                return 'bg-success';
            case -40: // 取消退料
                return 'bg-danger';
            default:
                return 'bg-info text-dark';
        }
    }

    function renderPagination(pageData) {
        const paginationControls = document.getElementById('pagination-controls');
        if (!paginationControls) return;
        paginationControls.innerHTML = '';
        if (!pageData) return;
        
        const { number: page, totalPages } = pageData;

        if (totalPages <= 1) return;

        let paginationHtml = '<ul class="pagination justify-content-center">';

        // Previous button
        paginationHtml += `<li class="page-item ${page === 0 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page - 1}">‹</a></li>`;

        // Page numbers
        for (let i = 0; i < totalPages; i++) {
            paginationHtml += `<li class="page-item ${i === page ? 'active' : ''}"><a class="page-link" href="#" data-page="${i}">${i + 1}</a></li>`;
        }

        // Next button
        paginationHtml += `<li class="page-item ${page === totalPages - 1 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page + 1}">›</a></li>`;

        paginationHtml += '</ul>';
        paginationControls.innerHTML = paginationHtml;

        paginationControls.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const pageNum = this.dataset.page;
                if (!this.parentElement.classList.contains('disabled')) {
                    searchMaterialReturnOrders(parseInt(pageNum) + 1);
                }
            });
        });
    }
    
    [statusFilter, startDateFilter, endDateFilter, orderNumberFilter].forEach(el => {
        el.addEventListener('change', () => searchMaterialReturnOrders(1));
    });
    
    // Initial render
    loadStatusFilter();
    searchMaterialReturnOrders(1);
}); 