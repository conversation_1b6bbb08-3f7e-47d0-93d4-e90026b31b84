document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('id');

    if (!orderId) {
        document.body.innerHTML = '<div class="alert alert-danger">錯誤：未提供單據ID。</div>';
            return;
    }

    async function fetchAndRenderDetail(id) {
        try {
            // Use the unified detail API endpoint
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${id}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const order = await response.json();
            renderDetail(order);
        } catch (error) {
            console.error('獲取詳情失敗:', error);
            document.getElementById('detail-view').innerHTML = `<div class="alert alert-danger">獲取詳情失敗: ${error.message}</div>`;
        }
    }

    function getOrderTypeClass(typeCode) {
        const typeClassMap = {
            10: 'bg-primary',   // 派工
            20: 'bg-warning',   // 維修
            30: 'bg-secondary'  // 退機
        };
        return typeClassMap[typeCode] || 'bg-info';
    }

    function renderDetail(order) {
        // Helper to safely set text content
        const setText = (id, text) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = text || '-';
            }
        };

        const setHtml = (id, html) => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = html;
            }
        };

        const mainItem = order.items && order.items.length > 0 ? order.items[0] : {};

        // Populate basic info
        setText('repair-order-number', order.number);
        setText('report-date', order.createTime ? new Date(order.createTime).toLocaleDateString() : '-');
        const typeBadgeHtml = `<span class="badge ${getOrderTypeClass(order.typeCode)}">${order.typeDescription}</span>`;
        setHtml('repair-type', typeBadgeHtml);
        
        // This sets the status text in the (now hidden) basic info area
        setText('status-description', order.statusDescription);
        
        // This sets the main status badge at the top
        const statusBadge = document.getElementById('currentStatusBadge');
        if (statusBadge) {
            statusBadge.textContent = order.statusDescription || '未知';
            // You can add logic here to change badge color based on status if needed
            // e.g., statusBadge.className = 'badge rounded-pill fs-6 ms-2 ' + getStatusClass(order.statusCode);
        }

        // --- START: URGENT BADGE LOGIC ---
        const urgentBadge = document.getElementById('urgent-status-badge');
        if (urgentBadge) {
            urgentBadge.textContent = order.isUrgent === 1 ? '急單' : '';
        }
        // --- END: URGENT BADGE LOGIC ---

        // --- START: MODIFICATION FOR COLLABORATORS ---
        let techHtml = `<strong>${order.assignedTechnicianName || '未指派'}</strong>`;
        if (order.collaborators && order.collaborators.length > 0) {
            const collabText = order.collaborators.map(c => 
                `, ${c.technicianName} <span class="text-muted small">(${c.statusDescription || '未知'})</span>`
            ).join('');
            techHtml += collabText;
        }
        setHtml('technician-name', techHtml);
        // --- END: MODIFICATION FOR COLLABORATORS ---

        setText('repair-date', order.scheduledDate || '-');
        setText('remarks', order.remarks);
        
        // Populate customer and device info from the order and the main item
        setText('customer-name', order.customerName);
        setText('contact-name', order.customerName); 
        setText('contact-phone', order.customerPhone);
        setText('contact-address', order.installationAddress);
        setText('device-serial', mainItem.deviceSerialNumber);
        setText('product-model', mainItem.productModel);
        setText('warranty-status', mainItem.warrantyStatusDescription || '未定義');

        // Populate repair content and financials from order and main item
        setText('issue-description', mainItem.issueDescription);
        setText('handling-method', order.handlingMethod);
        setText('follow-up', order.followUpAction);
        setText('total-amount', order.totalAmount ? `NT$ ${order.totalAmount.toLocaleString()}` : '-');
        setText('paid-amount', order.paidAmount ? `NT$ ${order.paidAmount.toLocaleString()}` : '-');

        // Populate Amounts
        const formatCurrency = (amount) => amount != null ? `NT$ ${parseFloat(amount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}` : '-';
        setText('productsTotalAmount', formatCurrency(order.productsTotalAmount));
        setText('discountAmount', formatCurrency(order.discountAmount));
        setText('netAmount', formatCurrency(order.netAmount));
        setText('taxAmount', formatCurrency(order.taxAmount));
        setText('grandTotalAmount', formatCurrency(order.grandTotalAmount));
        setText('paidAmount', formatCurrency(order.paidAmount));

        // Populate payment details list
        const paymentDetailsList = document.getElementById('payment-details-list');
        if (paymentDetailsList) {
            paymentDetailsList.innerHTML = ''; // Clear previous
            if (order.paymentDetails && order.paymentDetails.length > 0) {
                order.paymentDetails.forEach(p => {
                    const paymentMethodName = p.paymentMethodCode ? (PaymentMethodEnum[p.paymentMethodCode]?.description || p.paymentMethodCode) : '未知';
                    const detailEl = document.createElement('p');
                    detailEl.className = 'text-muted small mb-1';
                    detailEl.innerHTML = ` - ${paymentMethodName}: <span class="fw-normal">${formatCurrency(p.amount)}</span>`;
                    paymentDetailsList.appendChild(detailEl);
                });
            } else {
                paymentDetailsList.innerHTML = '<p class="text-muted small mb-0">無付款紀錄</p>';
            }
        }

        // --- START: URGENT TOGGLE BUTTON LOGIC ---
        const urgentToggleBtn = document.getElementById('urgent-toggle-btn');
        if (urgentToggleBtn) {
            // Only show the button if status is editable
            if (order.statusCode <= 29) {
                urgentToggleBtn.style.display = 'inline-block';
                
                const isUrgent = order.isUrgent === 1;
                
                // Set initial text and style
                if (isUrgent) {
                    urgentToggleBtn.textContent = '取消急單';
                    urgentToggleBtn.className = 'btn btn-outline-danger';
                } else {
                    urgentToggleBtn.textContent = '設為急單';
                    urgentToggleBtn.className = 'btn btn-danger';
                }

                // Remove previous listener to avoid duplicates, then add a new one
                urgentToggleBtn.replaceWith(urgentToggleBtn.cloneNode(true));
                document.getElementById('urgent-toggle-btn').addEventListener('click', async () => {
                    try {
                        const newUrgentStatus = !isUrgent;
                        const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${order.id}/urgent-status`, {
                            method: 'PATCH',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ isUrgent: newUrgentStatus })
                        });

                        if (!response.ok) {
                            const errData = await response.json();
                            throw new Error(errData.message || '更新失敗');
                        }
                        
                        window.showToast('急單狀態已更新', 'success');
                        // Refresh the detail to show the new state
                        fetchAndRenderDetail(order.id);

                    } catch (error) {
                        console.error('更新急單狀態失敗:', error);
                        window.showToast(`更新失敗: ${error.message}`, 'danger');
                    }
                });

            } else {
                urgentToggleBtn.style.display = 'none';
            }
        }
        // --- END: URGENT TOGGLE BUTTON LOGIC ---

        // Populate items table
        const itemListBody = document.getElementById('item-list');
        if (itemListBody) {
            if (order.items && order.items.length > 0) {
                itemListBody.innerHTML = order.items.map(item => `
                    <tr>
                        <td>${item.itemTypeDescription || 'N/A'}</td>
                        <td>${item.productBarcode}</td>
                        <td>${item.productName}</td>
                        <td>${item.quantity}</td>
                        <td>${item.unitPrice !== null ? item.unitPrice.toLocaleString() : '-'}</td>
                        <td>${item.subtotalAmount !== null ? item.subtotalAmount.toLocaleString() : '-'}</td>
                    </tr>
                `).join('');
            } else {
                itemListBody.innerHTML = '<tr><td colspan="6" class="text-center">沒有品項資料</td></tr>';
            }
        }

        // Render Tech Records
        renderTechRecords(order.techRecords || []);
    }

    function renderTechRecords(records) {
        const container = document.getElementById('tech-records-container');
        if (!container) return;
        
        if (!records || records.length === 0) {
            container.innerHTML = '<p class="text-muted">無派工紀錄。</p>';
            return;
        }

        container.innerHTML = records.map(record => {
            let recordContent = '';
            if (record.recordType === 50) { // IMAGE_DATA
                recordContent = `<img src="${record.record3}" class="img-fluid rounded" alt="Record Image" style="max-height: 200px;">`;
            } else if (record.recordType === 70) { // SIGNED_INFO
                recordContent = `<div class="border p-2"><p class="mb-1">簽名:</p><img src="${record.record1}" class="bg-light" alt="Signature"></div>`;
            } else {
                recordContent = `<p class="mb-1">${record.record1 || ''}</p>
                                 <p class="mb-1">${record.record2 || ''}</p>
                                 <p class="mb-0">${record.record3 || ''}</p>`;
            }

            return `
                <div class="record-item mb-3 border-bottom pb-2">
                    <p class="mb-1">
                        <strong>${record.statusDescription} / ${record.recordTypeDescription}</strong>
                        <small class="text-muted ms-2">(${new Date(record.createTime).toLocaleString()})</small>
                    </p>
                    <div class="record-content ps-3">
                        ${recordContent}
                    </div>
                </div>
            `;
        }).join('');
    }

    // A simple Enum-like object for frontend use
    const PaymentMethodEnum = {
        CASH: { code: 0, description: '現金' },
        CREDIT_CARD: { code: 1, description: '信用卡' },
        BANK_TRANSFER: { code: 2, description: '匯款' },
        TECHNICIAN_COLLECTION: { code: 3, description: '技師代收款' },
        STAFF_COLLECTION: { code: 4, description: '人員代收款' },
        EXCHANGE: { code: 5, description: '換貨轉入' }
    };

    fetchAndRenderDetail(orderId);
}); 