console.log("TECHNICIAN_BONUS_SETTINGS.JS: Script start.");

document.addEventListener('DOMContentLoaded', function () {
    console.log("TECHNICIAN_BONUS_SETTINGS.JS: DOMContentLoaded event fired.");

    // Technician Roles Tab Elements
    const systemRoleSelect = document.getElementById('systemRoleSelect');
    const addTechnicianRoleBtn = document.getElementById('addTechnicianRoleBtn');
    const technicianRolesTableBody = document.getElementById('technicianRolesTableBody');
    // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Tech roles tab elements obtained.", {systemRoleSelect:!!systemRoleSelect, addTechnicianRoleBtn:!!addTechnicianRoleBtn, technicianRolesTableBody:!!technicianRolesTableBody});

    // Bonus Items Tab Elements
    const addNewBonusItemBtn = document.getElementById('addNewBonusItemBtn');
    const bonusItemsContainer = document.getElementById('bonusItemsContainer');
    const saveAllBonusSettingsBtn = document.getElementById('saveAllBonusSettingsBtn');
    // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Bonus items tab elements obtained.", {addNewBonusItemBtn:!!addNewBonusItemBtn, bonusItemsContainer:!!bonusItemsContainer, saveAllBonusSettingsBtn:!!saveAllBonusSettingsBtn});
    
    // Bonus Item Modal Elements
    const bonusItemModalEl = document.getElementById('bonusItemModal');
    let bonusItemModal = null;
    if (bonusItemModalEl) {
        try {
             bonusItemModal = new bootstrap.Modal(bonusItemModalEl);
            //  console.log("TECHNICIAN_BONUS_SETTINGS.JS: Bonus item modal initialized.");
        } catch(e) { console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error initializing bonusItemModal", e); }
    } else {
        console.warn("TECHNICIAN_BONUS_SETTINGS.JS: bonusItemModal element not found.");
    }
    const bonusItemForm = document.getElementById('bonusItemForm');
    const bonusItemModalLabel = document.getElementById('bonusItemModalLabel');
    const bonusItemIdInput = document.getElementById('bonusItemId');
    const bonusItemNameModalInput = document.getElementById('bonusItemNameModal'); 
    const bonusItemDescriptionModalInput = document.getElementById('bonusItemDescriptionModal'); 
    const bonusItemIsActiveModalCheckbox = document.getElementById('bonusItemIsActiveModal'); 
    const bonusItemSettingsInModalContainer = document.getElementById('bonusItemSettingsInModalContainer'); 
    // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Bonus item modal elements obtained.", {bonusItemModalEl:!!bonusItemModalEl, bonusItemForm:!!bonusItemForm, bonusItemNameModalInput:!!bonusItemNameModalInput });

    let allSystemRoles = []; 
    let configuredTechnicianRoles = []; 

    async function loadInitialData() {
        console.log("TECHNICIAN_BONUS_SETTINGS.JS: loadInitialData() called.");
        await loadSystemRolesForDropdown();
        await loadConfiguredTechnicianRoles(); 
        await loadBonusItemsAndSettings(); 
        console.log("TECHNICIAN_BONUS_SETTINGS.JS: Initial data loading sequence complete.");
    }

    async function loadSystemRolesForDropdown() {
        console.log("TECHNICIAN_BONUS_SETTINGS.JS: loadSystemRolesForDropdown() called.");
        if (!systemRoleSelect) {
            console.warn("TECHNICIAN_BONUS_SETTINGS.JS: systemRoleSelect element not found. Skipping role dropdown population.");
            return;
        }
        systemRoleSelect.innerHTML = '<option value="">載入中...</option>'; 
        try {
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Fetching system roles from /api/v1/role-permissions/list");
            const response = await window.fetchAuthenticated('/api/v1/role-permissions/list'); 
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: System roles fetch status:", response.status);
            if (!response.ok) {
                const errText = await response.text();
                console.error("TECHNICIAN_BONUS_SETTINGS.JS: Failed to load system roles. Status:", response.status, "Response:", errText);
                throw new Error('Failed to load system roles: ' + response.status);
            }
            const apiResp = await response.json();
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: System roles API response:", apiResp);
            allSystemRoles = apiResp.data || []; 
            
            systemRoleSelect.innerHTML = '<option value="">選擇一個系統角色</option>';
            allSystemRoles.forEach(role => {
                if (!configuredTechnicianRoles.find(ctr => ctr.roleId === role.roleId)) { 
                    const option = document.createElement('option');
                    option.value = role.roleId;
                    option.textContent = `${role.roleName} (${role.roleCode})`;
                    systemRoleSelect.appendChild(option);
                }
            });
             console.log("TECHNICIAN_BONUS_SETTINGS.JS: System roles dropdown populated.");
        } catch (error) {
            console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error loading system roles:", error);
            systemRoleSelect.innerHTML = '<option value="">載入角色失敗</option>';
            if(window.showToast) window.showToast('載入系統角色列表失敗: ' + error.message, 'error');
        }
    }

    async function loadConfiguredTechnicianRoles() {
        console.log("TECHNICIAN_BONUS_SETTINGS.JS: loadConfiguredTechnicianRoles() called.");
        if (!technicianRolesTableBody) return;
        technicianRolesTableBody.innerHTML = '<tr><td colspan="3" class="text-center">載入中...</td></tr>';
        try {
            const response = await window.fetchAuthenticated('/api/v1/technician-bonus/roles');
            if (!response.ok) throw new Error('Failed to load configured technician roles');
            const apiResp = await response.json();
            configuredTechnicianRoles = apiResp.data || [];
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Configured technician roles fetched:", JSON.parse(JSON.stringify(configuredTechnicianRoles)));

            technicianRolesTableBody.innerHTML = '';
            if (configuredTechnicianRoles.length === 0) {
                technicianRolesTableBody.innerHTML = '<tr><td colspan="3" class="text-center">尚未設定任何技師角色</td></tr>';
            } else {
                configuredTechnicianRoles.forEach(techRole => {
                    const row = technicianRolesTableBody.insertRow();
                    row.insertCell().textContent = techRole.roleName;
                    row.insertCell().textContent = techRole.roleCode;
                    const actionsCell = row.insertCell();
                    actionsCell.className = 'text-end';
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'btn btn-sm btn-danger remove-tech-role-btn';
                    deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
                    deleteBtn.dataset.configId = techRole.technicianRoleConfigId;
                    deleteBtn.dataset.roleName = techRole.roleName;
                    actionsCell.appendChild(deleteBtn);
                });
            }
            addRemoveTechRoleButtonListeners();
            await loadSystemRolesForDropdown(); 
            console.log("TECHNICIAN_BONUS_SETTINGS.JS: Configured technician roles table rendered.");
        } catch (error) {
            console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error loading configured technician roles:", error);
            technicianRolesTableBody.innerHTML = '<tr><td colspan="3" class="text-danger text-center">載入技師角色失敗</td></tr>';
            if(window.showToast) window.showToast('載入技師角色失敗: ' + error.message, 'error');
        }
    }
    
    if(addTechnicianRoleBtn) {
        addTechnicianRoleBtn.addEventListener('click', async function() {
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Add technician role button clicked.");
            const selectedRoleId = systemRoleSelect ? systemRoleSelect.value : null;
            if (!selectedRoleId) {
                if(window.showToast) window.showToast('請先選擇一個系統角色', 'warning');
                return;
            }
            try {
                const response = await window.fetchAuthenticated(`/api/v1/technician-bonus/roles/${selectedRoleId}`, { method: 'POST' });
                const apiResp = await response.json();
                if (!response.ok) throw new Error(apiResp.message || '新增技師角色失敗');
                if(window.showToast) window.showToast('技師角色新增成功!', 'success');
                await loadConfiguredTechnicianRoles(); 
                await loadBonusItemsAndSettings(); 
            } catch (error) {
                console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error adding technician role:", error);
                if(window.showToast) window.showToast('新增技師角色失敗: ' + error.message, 'error');
            }
        });
    }

    function addRemoveTechRoleButtonListeners() {
        document.querySelectorAll('.remove-tech-role-btn').forEach(button => {
            const newButton = button.cloneNode(true); 
            button.parentNode.replaceChild(newButton, button);
            newButton.addEventListener('click', async function() {
                const configId = this.dataset.configId;
                const roleName = this.dataset.roleName;
                // console.log(`TECHNICIAN_BONUS_SETTINGS.JS: Remove technician role button clicked for config ID: ${configId}`);
                if (confirm(`確定要從技師角色列表中移除 "${roleName}"? 此操作將影響獎金計算。`)) {
                    try {
                        const response = await window.fetchAuthenticated(`/api/v1/technician-bonus/roles/${configId}`, { method: 'DELETE' });
                        const apiResp = await response.json();
                         if (!response.ok) throw new Error(apiResp.message || '移除技師角色失敗');
                        if(window.showToast) window.showToast('技師角色移除成功!', 'success');
                        await loadConfiguredTechnicianRoles();
                        await loadBonusItemsAndSettings(); 
                    } catch (error) {
                        console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error removing technician role:", error);
                        if(window.showToast) window.showToast('移除技師角色失敗: ' + error.message, 'error');
                    }
                }
            });
        });
    }
    
    async function loadBonusItemsAndSettings() {
        // console.log("TECHNICIAN_BONUS_SETTINGS.JS: loadBonusItemsAndSettings() called. Current configuredTechnicianRoles:", JSON.parse(JSON.stringify(configuredTechnicianRoles)));
        if (!bonusItemsContainer) return;
        bonusItemsContainer.innerHTML = '<p class="text-center">載入獎金項目...</p>';
        
        if (configuredTechnicianRoles.length === 0) {
            // console.warn("TECHNICIAN_BONUS_SETTINGS.JS: No configured technician roles. Bonus items table might be empty or show no role columns.");
        }
        
        try {
            const response = await window.fetchAuthenticated('/api/v1/technician-bonus/items');
            if (!response.ok) throw new Error('Failed to load bonus items');
            const apiResp = await response.json();
            const bonusItems = apiResp.data || [];
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Bonus items fetched (raw API response):", JSON.parse(JSON.stringify(apiResp)));

            bonusItemsContainer.innerHTML = '';
            if (configuredTechnicianRoles.length === 0) {
                bonusItemsContainer.innerHTML = '<p class="text-center">請先在「技師角色設定」分頁中至少設定一個技師角色，才能設定獎金項目。</p>';
                return;
            }
            if (bonusItems.length === 0 ) {
                 bonusItemsContainer.innerHTML = '<p class="text-center">尚無獎金項目。請點擊上方按鈕新增。</p>';
                 return;
            }

            const table = document.createElement('table');
            table.className = 'table table-bordered table-sm align-middle app-table';
            const thead = table.createTHead();
            const tbody = table.createTBody();
            const headerRow = thead.insertRow();
            headerRow.insertCell().outerHTML = '<th scope="col">項目名稱</th>';
            
            configuredTechnicianRoles.forEach(techRole => {
                const th = document.createElement('th');
                th.scope = "col";
                th.className = "text-center";
                th.textContent = `${techRole.roleName} 獎金`;
                headerRow.appendChild(th);
            });
            headerRow.insertCell().outerHTML = '<th scope="col" class="text-center">啟用</th>';
            headerRow.insertCell().outerHTML = '<th scope="col" class="text-center">操作</th>';

            bonusItems.forEach(item => {
                // console.log(`TECHNICIAN_BONUS_SETTINGS.JS: Processing item: ${item.itemName}, ID: ${item.bonusItemId}, Settings:`, JSON.parse(JSON.stringify(item.bonusSettings)));
                const row = tbody.insertRow();
                row.insertCell().textContent = item.itemName;
                
                const settingsMap = new Map((item.bonusSettings || []).map(s => [s.roleId, s.bonusAmount]));
                // console.log(` - Item ${item.itemName} - built settingsMap:`, settingsMap);

                configuredTechnicianRoles.forEach(techRole => {
                    // console.log(`   - For Tech Role: ${techRole.roleName} (ID: ${techRole.roleId})`);
                    const rawAmountFromMap = settingsMap.get(techRole.roleId);
                    // console.log(`     - Raw amount from settingsMap for role ${techRole.roleId}:`, rawAmountFromMap, `(Type: ${typeof rawAmountFromMap})`);
                    
                    const amountToUse = rawAmountFromMap !== undefined ? rawAmountFromMap : 0.00;
                    // console.log(`     - Amount to use: ${amountToUse} (Type: ${typeof amountToUse})`);

                    let numericAmountValue = typeof amountToUse === 'string' ? parseFloat(amountToUse) : Number(amountToUse);
                    if (isNaN(numericAmountValue)) {
                        // console.warn(`     - Amount for role ${techRole.roleId} is NaN after parsing. Defaulting to 0.00 for input value.`);
                        numericAmountValue = 0;
                    }
                    
                    const inputValue = numericAmountValue.toFixed(2);
                    // console.log(`     - Value to be set in input: ${inputValue}`);

                    const cell = row.insertCell();
                    cell.className = 'text-center';
                    cell.innerHTML = `<input type="number" class="form-control form-control-sm bonus-amount-input" 
                                            style="max-width: 100px; margin: auto;" 
                                            value="${inputValue}" 
                                            step="0.01" min="0" 
                                            data-item-id="${item.bonusItemId}" 
                                            data-role-id="${techRole.roleId}">`;
                });
                
                const isActiveCell = row.insertCell();
                isActiveCell.className = 'text-center';
                isActiveCell.innerHTML = item.isActive ? '<i class="bi bi-check-circle-fill text-success"></i>' : '<i class="bi bi-x-circle-fill text-danger"></i>';
                
                const actionsCell = row.insertCell();
                actionsCell.className = 'text-center';
                const editBtn = document.createElement('button');
                editBtn.className = 'btn btn-sm btn-info me-1 edit-bonus-item-btn';
                editBtn.innerHTML = '<i class="bi bi-pencil"></i>';
                editBtn.dataset.itemId = item.bonusItemId;
                actionsCell.appendChild(editBtn);

                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'btn btn-sm btn-danger delete-bonus-item-btn';
                deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
                deleteBtn.dataset.itemId = item.bonusItemId;
                deleteBtn.dataset.itemName = item.itemName;
                actionsCell.appendChild(deleteBtn);
            });
            bonusItemsContainer.appendChild(table);
            addBonusItemActionListeners();
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Bonus items table rendered.");
        } catch (error) {
            console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error loading bonus items and settings:", error);
            bonusItemsContainer.innerHTML = '<p class="text-danger text-center">載入獎金項目失敗。</p>';
            if(window.showToast) window.showToast('載入獎金項目失敗: ' + error.message, 'error');
        }
    }
    if(addNewBonusItemBtn) {
        addNewBonusItemBtn.addEventListener('click', function() {
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Add new bonus item button clicked.");
            if(bonusItemForm) bonusItemForm.reset();
            if(bonusItemIdInput) bonusItemIdInput.value = '';
            if(bonusItemIsActiveModalCheckbox) bonusItemIsActiveModalCheckbox.checked = true;
            renderBonusSettingsInModal(null); 
            if(bonusItemModalLabel) bonusItemModalLabel.textContent = '新增獎金項目';
            if(bonusItemModal) bonusItemModal.show();
        });
    }
    
    function renderBonusSettingsInModal(currentItemSettings) {
        // console.log("TECHNICIAN_BONUS_SETTINGS.JS: renderBonusSettingsInModal called. currentItemSettings:", JSON.parse(JSON.stringify(currentItemSettings || [])));
        if (!bonusItemSettingsInModalContainer) {
            // console.error("TECHNICIAN_BONUS_SETTINGS.JS: bonusItemSettingsInModalContainer not found!");
            return;
        }

        bonusItemSettingsInModalContainer.innerHTML = '';
        if (configuredTechnicianRoles.length === 0) {
            bonusItemSettingsInModalContainer.innerHTML = '<p class="text-muted small">提醒：請先在「技師角色設定」頁籤中，將系統角色新增為技師角色，才能在此設定對應獎金。</p>';
            return;
        }
        // console.log("TECHNICIAN_BONUS_SETTINGS.JS: (Modal) Configured technician roles for modal inputs:", JSON.parse(JSON.stringify(configuredTechnicianRoles)));
        
        const settingsMap = new Map((currentItemSettings || []).map(s => [s.roleId, s.bonusAmount]));
        // console.log("TECHNICIAN_BONUS_SETTINGS.JS: (Modal) Built settingsMap:", settingsMap);

        configuredTechnicianRoles.forEach(techRole => {
            // console.log(`TECHNICIAN_BONUS_SETTINGS.JS: (Modal)   - For Tech Role: ${techRole.roleName} (ID: ${techRole.roleId})`);
            const rawAmountFromMap = settingsMap.get(techRole.roleId);
            // console.log(`TECHNICIAN_BONUS_SETTINGS.JS: (Modal)     - Raw amount from settingsMap for role ${techRole.roleId}:`, rawAmountFromMap, `(Type: ${typeof rawAmountFromMap})`);
            
            const amountToUse = rawAmountFromMap !== undefined ? rawAmountFromMap : 0.00;
            // console.log(`TECHNICIAN_BONUS_SETTINGS.JS: (Modal)     - Amount to use: ${amountToUse} (Type: ${typeof amountToUse})`);

            let numericAmountValue = typeof amountToUse === 'string' ? parseFloat(amountToUse) : Number(amountToUse);
            if (isNaN(numericAmountValue)) {
                // console.warn(`TECHNICIAN_BONUS_SETTINGS.JS: (Modal)     - Amount for role ${techRole.roleId} is NaN. Defaulting to 0.00.`);
                numericAmountValue = 0;
            }
            
            const inputValue = numericAmountValue.toFixed(2);
            // console.log(`TECHNICIAN_BONUS_SETTINGS.JS: (Modal)     - Value for input field: ${inputValue}`);

            const div = document.createElement('div');
            div.className = 'mb-2 row align-items-center';
            div.innerHTML = `
                <label for="bonus-modal-${techRole.roleId}" class="col-sm-4 col-form-label col-form-label-sm text-end">${techRole.roleName}:</label>
                <div class="col-sm-8">
                    <input type="number" class="form-control form-control-sm bonus-amount-modal-input" 
                           id="bonus-modal-${techRole.roleId}"
                           value="${inputValue}" 
                           step="0.01" min="0" data-role-id="${techRole.roleId}">
                </div>
            `;
            bonusItemSettingsInModalContainer.appendChild(div);
        });
        // console.log("TECHNICIAN_BONUS_SETTINGS.JS: (Modal) Finished rendering bonus settings in modal.");
    }

    if(bonusItemForm) {
        bonusItemForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Bonus item form submitted.");
            const itemId = bonusItemIdInput ? bonusItemIdInput.value : null;
            const payload = {
                bonusItemId: itemId ? itemId : null,
                itemName: bonusItemNameModalInput ? bonusItemNameModalInput.value : '',
                itemDescription: bonusItemDescriptionModalInput ? bonusItemDescriptionModalInput.value : '',
                isActive: bonusItemIsActiveModalCheckbox ? bonusItemIsActiveModalCheckbox.checked : false,
                bonusSettings: []
            };

            document.querySelectorAll('.bonus-amount-modal-input').forEach(input => {
                payload.bonusSettings.push({
                    roleId: input.dataset.roleId,
                    bonusAmount: parseFloat(input.value) || 0
                });
            });
            console.log("TECHNICIAN_BONUS_SETTINGS.JS: Save bonus item payload:", payload);
            
            // Confirmation Dialog
            const confirmMessage = payload.bonusItemId ? "確定要更新此獎金項目設定嗎？" : "確定要新增此獎金項目嗎？";
            if (!window.confirm(confirmMessage)) {
                return; // User cancelled
            }

            try {
                const response = await window.fetchAuthenticated('/api/v1/technician-bonus/items', {
                    method: 'POST', 
                    body: JSON.stringify(payload)
                });
                const apiResp = await response.json();
                if (!response.ok) throw new Error(apiResp.message || '儲存獎金項目失敗');
                
                if(window.showToast) window.showToast('獎金項目儲存成功!', 'success');
                if(bonusItemModal) bonusItemModal.hide();
                await loadBonusItemsAndSettings(); 
            } catch (error) {
                console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error saving bonus item:", error);
                if(window.showToast) window.showToast('儲存獎金項目失敗: ' + error.message, 'error');
            }
        });
    }
    
    if(saveAllBonusSettingsBtn) {
        saveAllBonusSettingsBtn.addEventListener('click', async function() {
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Save All Bonus Settings button clicked.");
            const bonusItemsToSave = [];
            const itemRows = bonusItemsContainer.querySelectorAll('table tbody tr');

            itemRows.forEach(row => {
                const editBtnInRow = row.querySelector('.edit-bonus-item-btn');
                if (!editBtnInRow || !editBtnInRow.dataset.itemId) return; 

                const currentItemId = editBtnInRow.dataset.itemId;
                const currentItemName = row.cells[0].textContent; 
                const isActiveCell = row.cells[configuredTechnicianRoles.length + 1]; 
                const currentIsActive = isActiveCell.querySelector('i.bi-check-circle-fill') !== null;
                
                const itemPayload = {
                    bonusItemId: currentItemId,
                    itemName: currentItemName, 
                    isActive: currentIsActive, 
                    bonusSettings: []
                };

                const amountInputs = row.querySelectorAll('.bonus-amount-input');
                amountInputs.forEach(input => {
                    itemPayload.bonusSettings.push({
                        roleId: input.dataset.roleId,
                        bonusAmount: parseFloat(input.value) || 0
                    });
                });
                bonusItemsToSave.push(itemPayload);
            });

            if (bonusItemsToSave.length === 0) {
                if(window.showToast) window.showToast('沒有獎金項目設定可以儲存。', 'info');
                return;
            }
            const setupPayload = { bonusItems: bonusItemsToSave };
            // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Save all payload:", setupPayload);

            // Confirmation Dialog
            if (!window.confirm("確定要儲存所有顯示的獎金項目設定嗎？此操作將會更新列表中的所有項目。")) {
                return; // User cancelled
            }

            try {
                const response = await window.fetchAuthenticated('/api/v1/technician-bonus/setup', {
                    method: 'POST',
                    body: JSON.stringify(setupPayload)
                });
                const apiResp = await response.json();
                if (!response.ok) throw new Error(apiResp.message || '儲存全部設定失敗');
                if(window.showToast) window.showToast('所有技師獎金設定已儲存!', 'success');
                await loadBonusItemsAndSettings(); 
            } catch (error) {
                console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error saving all bonus settings:", error);
                if(window.showToast) window.showToast('儲存全部設定失敗: ' + error.message, 'error');
            }
        });
    }
    
    function addBonusItemActionListeners() {
         document.querySelectorAll('.edit-bonus-item-btn').forEach(button => {
            const newButton = button.cloneNode(true); button.parentNode.replaceChild(newButton, button);
            newButton.addEventListener('click', async function() {
                const itemId = this.dataset.itemId;
                // console.log(`TECHNICIAN_BONUS_SETTINGS.JS: Edit bonus item button clicked for ID: ${itemId}`);
                try {
                    const response = await window.fetchAuthenticated(`/api/v1/technician-bonus/items/${itemId}`);
                    if(!response.ok) throw new Error('無法載入項目資料');
                    const apiResp = await response.json();
                    const itemData = apiResp.data;
                    // console.log("TECHNICIAN_BONUS_SETTINGS.JS: Fetched item data for modal:", itemData);

                    if(bonusItemIdInput) bonusItemIdInput.value = itemData.bonusItemId;
                    if(bonusItemNameModalInput) bonusItemNameModalInput.value = itemData.itemName;
                    if(bonusItemDescriptionModalInput) bonusItemDescriptionModalInput.value = itemData.itemDescription || '';
                    if(bonusItemIsActiveModalCheckbox) bonusItemIsActiveModalCheckbox.checked = itemData.isActive;
                    renderBonusSettingsInModal(itemData.bonusSettings);
                    if(bonusItemModalLabel) bonusItemModalLabel.textContent = '編輯獎金項目';
                    if(bonusItemModal) bonusItemModal.show();
                } catch (error) {
                     console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error fetching item for edit modal:", error);
                    if(window.showToast) window.showToast('載入項目資料失敗: ' + error.message, 'error');
                }
            });
        });
        document.querySelectorAll('.delete-bonus-item-btn').forEach(button => {
            const newButton = button.cloneNode(true); button.parentNode.replaceChild(newButton, button);
            newButton.addEventListener('click', async function() {
                 const itemId = this.dataset.itemId;
                 const itemName = this.dataset.itemName;
                //  console.log(`TECHNICIAN_BONUS_SETTINGS.JS: Delete bonus item button clicked for ID: ${itemId}`);
                 if (confirm(`確定要刪除獎金項目 "${itemName}" 及其所有獎金設定?`)) {
                    try {
                        const response = await window.fetchAuthenticated(`/api/v1/technician-bonus/items/${itemId}`, {method: 'DELETE'});
                        const apiResp = await response.json();
                        if (!response.ok) throw new Error(apiResp.message || '刪除項目失敗');
                        if(window.showToast) window.showToast('獎金項目刪除成功!', 'success');
                        await loadBonusItemsAndSettings();
                    } catch (error) {
                        console.error("TECHNICIAN_BONUS_SETTINGS.JS: Error deleting bonus item:", error);
                        if(window.showToast) window.showToast('刪除項目失敗: ' + error.message, 'error');
                    }
                 }
            });
        });
    }

    async function initializePage() {
        console.log("TECHNICIAN_BONUS_SETTINGS.JS: Initializing page...");
        await loadSystemRolesForDropdown();
        await loadConfiguredTechnicianRoles(); 
        await loadBonusItemsAndSettings(); 
        console.log("TECHNICIAN_BONUS_SETTINGS.JS: Page initialization complete.");
    }

    initializePage();
}); 