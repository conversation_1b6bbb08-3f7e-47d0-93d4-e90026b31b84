document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('return-order-form');
    const searchDeviceBtn = document.getElementById('search-device-btn');
    const deviceSearchInput = document.getElementById('device-search');
    const customerNameP = document.getElementById('customer-name');
    const customerDeviceIdInput = document.getElementById('customer-device-id');
    const itemList = document.getElementById('item-list');
    const technicianSelect = document.getElementById('technician-select');

    async function initializeForm() {
        // Load technicians for the dropdown
        await loadTechnicians();
    }
    
    async function loadTechnicians() {
        if (!technicianSelect) return;
        technicianSelect.innerHTML = '<option value="">稍後指派</option>';
        try {
            const response = await window.fetchAuthenticated('/api/v1/users/technicians');
            if (!response.ok) throw new Error('無法載入技師列表');
            const result = await response.json();
            if(result.data) {
                result.data.forEach(tech => {
                    technicianSelect.innerHTML += `<option value="${tech.userAccountId}">${tech.userName}</option>`;
                });
            }
        } catch (error) {
            console.error('Failed to load technicians', error);
            if(window.showToast) window.showToast('無法載入技師列表', 'danger');
        }
    }

    searchDeviceBtn.addEventListener('click', async () => {
        const serialNumber = deviceSearchInput.value.trim();
        if (!serialNumber) {
            if(window.showToast) window.showToast('請輸入機身序號', 'warning');
            return;
        }
        
        // TODO: Replace with actual API call to search device
        // Mocking the response for now
        const mockDevice = {
            customerDeviceId: 'device-uuid-1',
            customerName: '模擬客戶',
            productName: '斜行高手3.3代',
            productBarcode: '*********'
        };

        customerNameP.textContent = mockDevice.customerName;
        customerDeviceIdInput.value = mockDevice.customerDeviceId;
        
        itemList.innerHTML = `
            <tr>
                <td>${mockDevice.productName}</td>
                <td><input type="number" class="form-control form-control-sm" value="1" min="1" data-barcode="${mockDevice.productBarcode}"></td>
            </tr>
        `;
    });

    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        const items = [];
        itemList.querySelectorAll('tr').forEach(row => {
            const input = row.querySelector('input[type="number"]');
            items.push({
                productBarcode: input.dataset.barcode,
                quantity: parseInt(input.value)
            });
        });
        
        const payload = {
            customerDeviceId: customerDeviceIdInput.value,
            contactName: document.getElementById('customer-name').textContent, // Simplified
            reportDate: document.getElementById('report-date').value,
            scheduledDate: document.getElementById('scheduled-date').value,
            assignedTechnicianId: technicianSelect.value || null,
            remarks: document.getElementById('remarks').value,
            items: items
        };

        if(!payload.customerDeviceId || items.length === 0) {
            if(window.showToast) window.showToast('請先搜尋有效的設備並確認品項。', 'warning');
            return;
        }

        try {
            const response = await window.fetchAuthenticated('/api/v1/repairs/returns', {
                method: 'POST',
                body: JSON.stringify(payload),
            });

            if (!response.ok) {
                 const errData = await response.json();
                 throw new Error(errData.message || "建立退機單失敗");
            }
            if(window.showToast) window.showToast('退機單建立成功！', 'success');
            setTimeout(() => { window.location.href = 'return_order_list.html'; }, 1000);
        } catch (error) {
            console.error(error);
            if(window.showToast) window.showToast(`錯誤: ${error.message}`, 'danger');
        }
    });

    initializeForm();
}); 