document.addEventListener('DOMContentLoaded', function () {
    const urlParams = new URLSearchParams(window.location.search);
    const returnOrderId = urlParams.get('id');

    async function initPage() {
        if (!returnOrderId) {
            document.getElementById('detail-view').innerHTML = '<div class="alert alert-danger">未提供退機單ID。</div>';
            return;
        }
        await fetchAndRenderDetails(returnOrderId);
    }

    async function fetchAndRenderDetails(id) {
        try {
            const response = await window.fetchAuthenticated(`/api/v1/repairs/returns/${id}`);
            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '無法獲取退機單詳情');
            }
            const result = await response.json();
            renderDetails(result.data);
        } catch (error) {
            console.error('獲取詳情失敗:', error);
            document.getElementById('detail-view').innerHTML = `<div class="alert alert-danger">載入失敗: ${error.message}</div>`;
        }
    }

    function renderDetails(order) {
        if (!order) return;
        
        const setText = (id, text) => {
            const el = document.getElementById(id);
            if (el) el.textContent = text || '-';
        };
        const formatDate = (date) => date ? new Date(date).toLocaleDateString() : '-';

        setText('return-order-number', order.returnOrderNumber);
        setText('status-description', order.returnStatusDescription);
        setText('original-order-number', order.originalOrderNumber);
        setText('dispatch-order-number', order.dispatchOrderNumber);
        setText('customer-name', order.customerName);
        setText('customer-phone', order.customerPhone);
        setText('pickup-address', order.pickupAddress);
        setText('device-serial-number', order.deviceSerialNumber);
        setText('product-model', order.productModel);
        setText('refund-method', order.refundMethodDescription);
        setText('total-refund', order.totalRefundAmount ? `NT$ ${order.totalRefundAmount.toLocaleString()}`: '-');
        setText('report-date', formatDate(order.reportDate));
        setText('scheduled-date', formatDate(order.scheduledDate));
        setText('technician-name', order.assignedTechnicianName);
        setText('remarks', order.remarks);

        const itemList = document.getElementById('item-list');
        itemList.innerHTML = '';
        if (order.items && order.items.length > 0) {
            order.items.forEach(item => {
                const row = `
                    <tr>
                        <td>${item.productBarcode}</td>
                        <td>${item.productName}</td>
                        <td>${item.quantity}</td>
                    </tr>
                `;
                itemList.innerHTML += row;
            });
        } else {
            itemList.innerHTML = '<tr><td colspan="3" class="text-center">此單無退機品項</td></tr>';
        }
    }

    initPage();
}); 