document.addEventListener('DOMContentLoaded', function () {
    const infoCardContainer = document.getElementById('info-card');
    const scheduleForm = document.getElementById('schedule-form');
    const backBtn = document.getElementById('back-btn');
    const completeBtn = document.getElementById('complete-btn');
    const detailBreadcrumbLink = document.getElementById('detail-breadcrumb-link');

    const urlParams = new URLSearchParams(window.location.search);
    const dispatchRepairId = urlParams.get('id');

    if (!dispatchRepairId) {
        infoCardContainer.innerHTML = '<div class="alert alert-danger">錯誤：找不到派工單ID。</div>';
        return;
    }
    
    detailBreadcrumbLink.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;

    async function initializePage() {
        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}`);
            if (!response.ok) throw new Error('無法獲取派工單資料');
            
            const order = await response.json();
            
            if (!order) {
                throw new Error('從API獲取的訂單資料為空');
            }

            window.renderDispatchOrderHeader(order, infoCardContainer);

            const scheduledDateInput = document.getElementById('scheduledDate');
            if (order.scheduledDate) {
                scheduledDateInput.value = order.scheduledDate;
            }
            
            if (order.statusCode !== 29) { // 29: 待排單
                scheduleForm.querySelectorAll('input, button[type="submit"]').forEach(el => {
                    el.disabled = true;
                });
                if(window.showToast) window.showToast('此派工單狀態已變更，不可編輯。', 'warning');
            }

        } catch (error) {
            console.error('頁面初始化失敗:', error);
            infoCardContainer.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
        }
    }
    
    async function handleFormSubmit(event) {
        event.preventDefault();
        const scheduledDate = document.getElementById('scheduledDate').value;
        if (!scheduledDate) {
            if(window.showToast) window.showToast('請選擇預約日期。', 'warning');
            return;
        }

        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}/update-schedule`, {
                method: 'POST',
                body: JSON.stringify({ scheduledDate: scheduledDate })
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '操作失敗');
            }
            
            if(window.showToast) window.showToast('排單步驟已完成！', 'success');
            setTimeout(() => {
                window.location.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;
            }, 1500);

        } catch (error) {
            console.error('完成排單步驟失敗:', error);
            if(window.showToast) window.showToast(`操作失敗: ${error.message}`, 'danger');
        }
    }

    backBtn.addEventListener('click', () => window.history.back());
    scheduleForm.addEventListener('submit', handleFormSubmit);

    initializePage();
}); 