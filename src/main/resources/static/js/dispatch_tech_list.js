const API_BASE_URL = "/api/v1";

document.addEventListener('DOMContentLoaded', async function () {
    const acceptOrderBtn = document.getElementById('accept-order-btn');
    const confirmAcceptBtn = document.getElementById('confirm-accept-btn');
    const dispatchOrderList = document.getElementById('dispatch-order-list');
    const pendingOrdersList = document.getElementById('pending-orders-list');
    const dispatchTypeFilter = document.getElementById('dispatch-type-filter');
    const dispatchStatusFilter = document.getElementById('dispatch-status-filter');
    const startDateFilter = document.getElementById('start-date-filter');
    const endDateFilter = document.getElementById('end-date-filter');
    const sortFilter = document.getElementById('sort-filter');

    // Wait for both dropdowns to be populated before any further action
    await Promise.all([
        populateEnumSelect('dispatch-status-filter', `${API_BASE_URL}/enums/dispatch-statuses`, '所有狀態'),
        populateEnumSelect('dispatch-type-filter', `${API_BASE_URL}/enums/dispatch-order-types`, '所有類型')
    ]);

    async function fetchAndRenderDispatchOrders(page = 0, size = 10) {
        const tableBody = document.getElementById("dispatch-order-list"); // Assuming this is where you show content/loading
        const filterForm = document.getElementById('search-orders-form');
        const params = new URLSearchParams();
        
        // Build query parameters from form elements
        params.append('page', page);
        params.append('size', size);
        params.append('sortOrder', sortFilter.value || 'newest');

        // Corrected logic: Only add the parameter if the value is truthy (i.e., not an empty string)
        if (dispatchTypeFilter.value) {
            params.append('dispatchTypeCode', dispatchTypeFilter.value);
        }
        if (dispatchStatusFilter.value) {
            params.append('dispatchStatusCode', dispatchStatusFilter.value);
        }
        if (startDateFilter.value) {
            params.append('startDate', startDateFilter.value);
        }
        if (endDateFilter.value) {
            params.append('endDate', endDateFilter.value);
        }
        
        try {
            // Re-using showLoading and showError logic from other pages
            if(window.showLoading) window.showLoading(tableBody, 5);
            else tableBody.innerHTML = '<tr><td colspan="5">載入中...</td></tr>';
            
            const url = `${API_BASE_URL}/dispatch-orders/technician-view?${params.toString()}`;
            const response = await window.fetchAuthenticated(url);

            if (!response.ok) {
                throw new Error(`無法獲取派工單列表: HTTP ${response.status}`);
            }
            const result = await response.json();
            
            if (result.code === 200 && result.data) {
                renderDispatchOrders(result.data.list || []);
                // TODO: renderPagination(result.data.page);
            } else {
                renderDispatchOrders([]);
                // TODO: renderPagination(null);
            }
        } catch (error) {
            console.error("獲取派工單失敗:", error);
             if(window.showError) window.showError(tableBody, 5, `獲取派工單失敗: ${error.message}`);
             else tableBody.innerHTML = `<tr><td colspan="5">獲取失敗: ${error.message}</td></tr>`;
        }
    }
    
    function getOrderTypeClass(typeCode) {
        const typeClassMap = {
            10: 'bg-primary',   // 派工
            20: 'bg-warning',   // 維修
            30: 'bg-secondary'  // 退機
        };
        return typeClassMap[typeCode] || 'bg-info';
    }

    function getCustomerInfoBackgroundColorClass(typeCode) {
        const colorClassMap = {
            10: 'bg-primary-subtle',   // 派工 (淺藍)
            20: 'bg-warning-subtle',   // 維修 (淺黃)
            30: 'bg-secondary-subtle'  // 退機 (淺灰)
        };
        return colorClassMap[typeCode] || 'bg-light'; // Default color
    }

    function renderDispatchOrders(orders) {
        if (!orders || orders.length === 0) {
            dispatchOrderList.innerHTML = '<div class="alert alert-info">目前沒有派工單。</div>';
            return;
        }

        dispatchOrderList.innerHTML = orders.map(order => {
            const typeClass = getOrderTypeClass(order.dispatchTypeCode);
            const statusClass = order.dispatchStatusCode === 100 ? 'status-completed' : 'status-ongoing';
            let backgroundClass = getCustomerInfoBackgroundColorClass(order.dispatchTypeCode);
            
            const isCollab = order.isCollaboration;
            const technicianInfo = isCollab 
                ? `<span class="text-info">(協同)</span> ${order.assignedTechnicianName || '未指派'}` 
                : (order.assignedTechnicianName || '未指派');
            const detailButtonClass = isCollab ? 'btn-outline-info' : 'btn-outline-primary';
            const detailHref = order.dispatchStatusCode < 0 
                ? `dispatch_tech_refund_detail.html?id=${order.dispatchOrderId}` 
                : `dispatch_tech_detail.html?id=${order.dispatchOrderId}`;

            if (order.isUrgent === 1) {
                backgroundClass = 'bg-urgent';
            }

            return `
                <div class="card order-card mb-3" data-order-id="${order.dispatchOrderId}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <span>單號: ${order.dispatchOrderNumber}</span>
                            <span class="badge ${typeClass} ms-2">${order.dispatchTypeDescription}</span>
                        </div>
                        <span class="badge rounded-pill ${statusClass}">${order.dispatchStatusDescription}</span>
                    </div>
                    <div class="card-body ${backgroundClass}">
                        <p class="card-text mb-1"><strong>客戶:</strong> ${order.customerName}</p>
                        <p class="card-text mb-1"><strong>技師:</strong> ${technicianInfo}</p>
                        <p class="card-text"><strong>預約時間:</strong> ${order.scheduledDate ? new Date(order.scheduledDate).toLocaleString() : '未定'}</p>
                        <a href="${detailHref}" class="btn btn-sm ${detailButtonClass} mt-2">
                            <i class="bi bi-search me-1"></i>查看詳情
                        </a>
                    </div>
                </div>
            `;
        }).join('');
    }

    async function fetchPendingOrders() {
        try {
            const response = await window.fetchAuthenticated(`${API_BASE_URL}/dispatch-orders/pending`);
            if (!response.ok) {
                throw new Error('無法獲取待接單列表');
            }
            const apiResponse = await response.json();
            renderPendingOrders(apiResponse.data || []);
        } catch (error) {
            console.error('獲取待接單失敗:', error);
            pendingOrdersList.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
        }
    }
    
    function renderPendingOrders(orders) {
        if (orders.length === 0) {
            pendingOrdersList.innerHTML = '<p>目前沒有待接的派工單。</p>';
            return;
        }

        pendingOrdersList.innerHTML = orders.map(order => {
            console.log("Pending Order Data:", order); // DEBUG: Log the received order object
            // --- START: MODIFICATION FOR COLLABORATION ---
            const isCollab = order.collaboration;
            const collabText = isCollab ? ` <span class="text-info">(與 ${order.assignedTechnicianName} 協同)</span>` : '';
            // --- END: MODIFICATION FOR COLLABORATION ---
            return `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="${order.dispatchRepairId}" id="order-${order.dispatchRepairId}" data-is-collaboration="${isCollab}">
                <label class="form-check-label" for="order-${order.dispatchRepairId}">
                    ${order.dispatchRepairNumber} - ${order.customerName} (${order.scheduledDate})${collabText}
                </label>
            </div>
        `}).join('');
    }

    // Event Listeners
    if (acceptOrderBtn) {
        acceptOrderBtn.addEventListener('click', () => {
            fetchPendingOrders();
            const acceptModal = new bootstrap.Modal(document.getElementById('acceptOrderModal'));
            acceptModal.show();
        });
    }

    if (confirmAcceptBtn) {
    confirmAcceptBtn.addEventListener('click', async () => {
            const selectedCheckboxes = Array.from(pendingOrdersList.querySelectorAll('input[type=checkbox]:checked'));

            if (selectedCheckboxes.length === 0) {
                if(window.showToast) window.showToast('請至少選擇一個派工單。', 'warning');
            return;
        }
        
            // --- START: MODIFICATION FOR COLLABORATION ---
            const mainOrderIds = [];
            const collaborationOrderIds = [];

            selectedCheckboxes.forEach(checkbox => {
                if (checkbox.dataset.isCollaboration === 'true') {
                    collaborationOrderIds.push(checkbox.value);
                } else {
                    mainOrderIds.push(checkbox.value);
                }
            });

            const promises = [];
            
            // Promise for main orders
            if (mainOrderIds.length > 0) {
                promises.push(window.fetchAuthenticated(`${API_BASE_URL}/dispatch-orders/accept`, {
                method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(mainOrderIds)
                }));
            }

            // Promises for collaboration orders
            collaborationOrderIds.forEach(id => {
                promises.push(window.fetchAuthenticated(`${API_BASE_URL}/dispatch-orders/${id}/collaborations/accept`, {
                    method: 'POST'
                }));
            });

            try {
                const responses = await Promise.all(promises);

                const allOk = responses.every(res => res.ok);

                if (allOk) {
            if(window.showToast) window.showToast('接單成功！', 'success');
                    const acceptModal = bootstrap.Modal.getInstance(document.getElementById('acceptOrderModal'));
                    acceptModal.hide();
                    fetchAndRenderDispatchOrders(); // Refresh the list
                } else {
                    // Handle potential partial failure
                    const errorMessages = await Promise.all(responses
                        .filter(res => !res.ok)
                        .map(res => res.json().then(err => err.message || `HTTP ${res.status}`))
                    );
                    throw new Error(errorMessages.join('; '));
                }

        } catch (error) {
            console.error('接單失敗:', error);
            if(window.showToast) window.showToast(`接單失敗: ${error.message}`, 'danger');
        }
            // --- END: MODIFICATION FOR COLLABORATION ---
    });
    }

    [dispatchTypeFilter, dispatchStatusFilter, startDateFilter, endDateFilter, sortFilter].forEach(el => {
        if (el) {
            el.addEventListener('change', () => {
                fetchAndRenderDispatchOrders();
            });
        }
    });
    
    // Initial load - now happens only after dropdowns are populated
    fetchAndRenderDispatchOrders();
}); 