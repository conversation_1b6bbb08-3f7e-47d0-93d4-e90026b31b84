document.addEventListener('DOMContentLoaded', function () {
    const API_URL = '/api/v1/customers';
    const searchInput = document.getElementById('keyword-search');
    const tableBody = document.getElementById('member-list-body');
    const paginationContainer = document.getElementById('pagination-container');
    let currentPage = 0;

    async function fetchMembers(page = 0, keyword = '') {
        console.log(`Fetching members for page: ${page}, keyword: ${keyword}`);
        tableBody.innerHTML = `<tr><td colspan="6" class="text-center">載入中...</td></tr>`;
        try {
            let url = `${API_URL}?page=${page}&size=100&sort=createTime,desc`;
            if (keyword) {
                url += `&keyword=${encodeURIComponent(keyword)}`;
            }
            const response = await fetchAuthenticated(url);
            if (!response.ok) throw new Error('無法獲取會員列表');
            const apiResponse = await response.json();
            
            console.log("API Response:", apiResponse); // Log the full API response

            if (apiResponse && apiResponse.data && apiResponse.data.list) {
                populateTable(apiResponse.data.list);
                setupPagination(apiResponse.data.page);
            } else {
                 console.error("Unexpected API response structure:", apiResponse);
                 populateTable([]); // Clear table and show no results
                 setupPagination(null);
            }
        } catch (error) {
            console.error('獲取會員列表失敗:', error);
            tableBody.innerHTML = `<tr><td colspan="6" class="text-center text-danger">載入失敗: ${error.message}</td></tr>`;
        }
    }

    function populateTable(members) {
        console.log("Populating table with members:", members);
        tableBody.innerHTML = '';
        if (!members || members.length === 0) {
            tableBody.innerHTML = `<tr><td colspan="6" class="text-center">找不到任何會員</td></tr>`;
            return;
        }
        members.forEach(member => {
            const row = `
                <tr>
                    <td>${member.memberLevelName || 'N/A'}</td>
                    <td><a href="member_detail.html?customerId=${member.customerId}">${member.customerName}</a></td>
                    <td>${member.erpCustomerCode || 'N/A'}</td>
                    <td>${member.phoneNumber || ''}</td>
                    <td>${member.fullAddress || ''}</td>
                    <td><a href="member_detail.html?customerId=${member.customerId}" class="btn btn-sm btn-outline-dark">查看</a></td>
                </tr>
            `;
            tableBody.insertAdjacentHTML('beforeend', row);
        });
    }

    function setupPagination(pageData) {
        // ... (standard pagination logic here)
    }
    
    searchInput.addEventListener('input', () => fetchMembers(0, searchInput.value));

    // Initial fetch
    fetchMembers();
}); 