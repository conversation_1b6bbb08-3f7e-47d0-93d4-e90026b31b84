// js/member_benefits.js // Path comment updated
document.addEventListener('DOMContentLoaded', function () {
    const benefitsForm = document.getElementById('member-benefits-form');
    const levelsContainer = document.getElementById('member-levels-container');
    console.log('Member benefits JS loaded. Levels container:', levelsContainer);

    async function fetchMemberLevels() {
        console.log('Fetching member levels...');
        if (!levelsContainer) { 
            console.error("levelsContainer is null in fetchMemberLevels. Aborting."); 
            return; 
        }
        try {
            const response = await window.fetchAuthenticated('/api/v1/member-levels');
            console.log('Fetch member levels response status:', response.status);
            if (!response.ok) throw new Error('Failed to fetch member levels. Status: ' + response.status);
            const apiResult = await response.json();
            console.log('API Result for member levels:', JSON.stringify(apiResult, null, 2));

            if (apiResult.code === 200 && apiResult.data) {
                console.log('API success, data received for member levels:', apiResult.data);
                renderMemberLevels(apiResult.data);
            } else {
                levelsContainer.innerHTML = '<p class="text-danger">無法載入會員等級設定 (API Code: ' + (apiResult ? apiResult.code : 'N/A') + ').</p>';
                console.error('Error fetching member levels (API level):', apiResult ? apiResult.message : 'No API result');
            }
        } catch (error) {
            levelsContainer.innerHTML = '<p class="text-danger">載入會員等級設定時發生嚴重錯誤。</p>';
            console.error('Catch Error fetching member levels:', error);
        }
    }

    function renderMemberLevels(levels) {
        console.log('Rendering member levels, data received:', levels);
        if (!levelsContainer) { 
            console.error("levelsContainer is null in renderMemberLevels. Aborting."); 
            return; 
        }
        levelsContainer.innerHTML = ''; 
        if (!Array.isArray(levels)) { 
            console.error('renderMemberLevels called with non-array:', levels);
            levelsContainer.innerHTML = '<p class="text-danger">會員等級資料格式錯誤。</p>';
            return;
        }
        if (levels.length === 0) {
            levelsContainer.innerHTML = '<p>尚無會員等級資料可設定。</p>';
            return;
        }
        console.log(`Rendering ${levels.length} levels.`);
        
        levels.forEach((level, index) => {
            console.log(`Rendering level ${index + 1}:`, level);
            const levelId = level.memberLevelId;
            const collapseId = `collapse-${levelId}`;
            const headingId = `heading-${levelId}`;
            const isFirst = index === 0;

            const discountCouponEnabledChecked = level.discountCouponEnabled === true || String(level.discountCouponEnabled).toLowerCase() === 'true' || level.discountCouponEnabled === 1 || String(level.discountCouponEnabled) === '1';
            const birthdayGiftEnabledChecked = level.birthdayGiftEnabled === true || String(level.birthdayGiftEnabled).toLowerCase() === 'true' || level.birthdayGiftEnabled === 1 || String(level.birthdayGiftEnabled) === '1';
            const additionalPerkEnabledChecked = level.additionalPerkEnabled === true || String(level.additionalPerkEnabled).toLowerCase() === 'true' || level.additionalPerkEnabled === 1 || String(level.additionalPerkEnabled) === '1';

            const itemHtml = `
                <div class="accordion-item">
                    <h2 class="accordion-header" id="${headingId}">
                        <button class="accordion-button ${isFirst ? '' : 'collapsed'}" type="button" data-bs-toggle="collapse" data-bs-target="#${collapseId}" aria-expanded="${isFirst}" aria-controls="${collapseId}">
                            ${level.levelName || '未命名等級'} ${level.levelNickname ? '('+level.levelNickname+')' : ''}
                        </button>
                    </h2>
                    <div id="${collapseId}" class="accordion-collapse collapse ${isFirst ? 'show' : ''}" aria-labelledby="${headingId}">
                        <div class="accordion-body" data-level-id="${levelId}">
                            <input type="hidden" class="member-level-id-field" value="${levelId}">
                            <input type="hidden" class="level-name-hidden" value="${level.levelName || ''}">
                            <h5>維持條件設定</h5>
                            <div class="row mb-3">
                                <div class="col-md-6 mb-2">
                                    <label class="form-label">維持條件-年</label>
                                    <input type="number" class="form-control maintenance-period-years" value="${level.maintenancePeriodYears || ''}">
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label class="form-label">維持條件-金額(元)</label>
                                    <input type="number" step="0.01" class="form-control maintenance-amount" value="${level.maintenanceAmount || ''}">
                                </div>
                            </div>
                            
                            <h5>折扣券發放</h5>
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input discount-coupon-enabled" type="checkbox" role="switch" id="discountCouponEnabled-${levelId}" ${discountCouponEnabledChecked ? 'checked' : ''}>
                                <label class="form-check-label" for="discountCouponEnabled-${levelId}">啟用折扣券發放</label>
                            </div>
                            <div class="row mb-3 discount-coupon-details" style="display: ${discountCouponEnabledChecked ? 'flex' : 'none'};">
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">消費金額(元)</label>
                                    <input type="number" step="0.01" class="form-control discount-coupon-threshold" value="${level.discountCouponThresholdAmount || ''}">
                                </div>
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">折扣金額(元)</label>
                                    <input type="number" step="0.01" class="form-control discount-coupon-amount" value="${level.discountCouponDiscountAmount || ''}">
                                </div>
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">使用期限(天)</label>
                                    <input type="number" class="form-control discount-coupon-validity" value="${level.discountCouponValidityDays || ''}">
                                </div>
                            </div>

                            <h5>生日禮設定</h5>
                             <div class="form-check form-switch mb-2">
                                <input class="form-check-input birthday-gift-enabled" type="checkbox" role="switch" id="birthdayGiftEnabled-${levelId}" ${birthdayGiftEnabledChecked ? 'checked' : ''}>
                                <label class="form-check-label" for="birthdayGiftEnabled-${levelId}">啟用生日禮</label>
                            </div>
                            <div class="row mb-3 birthday-gift-details" style="display: ${birthdayGiftEnabledChecked ? 'flex' : 'none'};">
                                <div class="col-md-6 mb-2">
                                    <label class="form-label">生日禮商品/內容</label>
                                    <input type="text" class="form-control birthday-gift-description" value="${level.birthdayGiftItemDescription || ''}" placeholder="例如: 生日獨享8折優惠券">
                                </div>
                            </div>

                            <h5>額外福利</h5>
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input additional-perk-enabled" type="checkbox" role="switch" id="additionalPerkEnabled-${levelId}" ${additionalPerkEnabledChecked ? 'checked' : ''}>
                                <label class="form-check-label" for="additionalPerkEnabled-${levelId}">啟用額外福利</label>
                            </div>
                            <div class="row mb-3 additional-perk-details" style="display: ${additionalPerkEnabledChecked ? 'flex' : 'none'};">
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">福利名稱</label>
                                    <input type="text" class="form-control additional-perk-name" value="${level.additionalPerkName || ''}">
                                </div>
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">消費金額(元)</label>
                                    <input type="number" step="0.01" class="form-control additional-perk-threshold" value="${level.additionalPerkThresholdAmount || ''}">
                                </div>
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">折扣金額(元)</label>
                                    <input type="number" step="0.01" class="form-control additional-perk-amount" value="${level.additionalPerkDiscountAmount || ''}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            levelsContainer.insertAdjacentHTML('beforeend', itemHtml);
        });
        addSwitchToggleListeners();
    }

    function addSwitchToggleListeners() {
        levelsContainer.querySelectorAll('.form-check-input[type="checkbox"][role="switch"]').forEach(switchEl => {
            switchEl.addEventListener('change', function() {
                let detailsDiv = this.closest('.form-check').nextElementSibling;
                if (!detailsDiv || !detailsDiv.classList.contains('row')) {
                     detailsDiv = this.closest('.accordion-body').querySelector('.' + this.id.split('-')[0] + '-details');
                }
                
                if (detailsDiv && detailsDiv.classList.contains('row')) { 
                     detailsDiv.style.display = this.checked ? 'flex' : 'none';
                }
            });
        });
    }

    benefitsForm.addEventListener('submit', async function(event) {
        event.preventDefault();
        const updatedLevels = [];
        levelsContainer.querySelectorAll('.accordion-body').forEach(body => {
            const levelDto = {
                memberLevelId: body.querySelector('.member-level-id-field').value,
                levelName: body.querySelector('.level-name-hidden').value, // Include levelName for error messages
                maintenancePeriodYears: parseInt(body.querySelector('.maintenance-period-years').value) || null,
                maintenanceAmount: parseFloat(body.querySelector('.maintenance-amount').value) || null,
                discountCouponEnabled: body.querySelector('.discount-coupon-enabled').checked,
                discountCouponThresholdAmount: parseFloat(body.querySelector('.discount-coupon-threshold').value) || null,
                discountCouponDiscountAmount: parseFloat(body.querySelector('.discount-coupon-amount').value) || null,
                discountCouponValidityDays: parseInt(body.querySelector('.discount-coupon-validity').value) || null,
                birthdayGiftEnabled: body.querySelector('.birthday-gift-enabled').checked,
                birthdayGiftItemDescription: body.querySelector('.birthday-gift-description').value || null,
                additionalPerkEnabled: body.querySelector('.additional-perk-enabled').checked,
                additionalPerkName: body.querySelector('.additional-perk-name').value || null,
                additionalPerkThresholdAmount: parseFloat(body.querySelector('.additional-perk-threshold').value) || null,
                additionalPerkDiscountAmount: parseFloat(body.querySelector('.additional-perk-amount').value) || null
            };
            updatedLevels.push(levelDto);
        });

        let allSuccessful = true;
        for (const level of updatedLevels) {
            try {
                const response = await window.fetchAuthenticated(`/api/v1/member-levels/${level.memberLevelId}`, {
                    method: 'PUT',
                    body: JSON.stringify(level)
                });
                if (!response.ok) {
                    allSuccessful = false;
                    const errorResult = await response.json().catch(() => ({ message: 'Unknown error' }));
                    console.error(`Error updating level ${level.levelName || level.memberLevelId}:`, errorResult.message);
                    alert(`更新等級 ${level.levelName || level.memberLevelId} 失敗: ${errorResult.message}`);
                    break; 
                }
            } catch (error) {
                allSuccessful = false;
                console.error(`Error updating level ${level.levelName || level.memberLevelId}:`, error);
                alert(`更新等級 ${level.levelName || level.memberLevelId} 時發生錯誤。`);
                break;
            }
        }

        if (allSuccessful) {
            alert('會員福利設定已全部儲存！');
            fetchMemberLevels();
        } else {
            alert('部分會員福利設定儲存失敗，請檢查控制台錯誤訊息。');
        }
    });

    document.getElementById('cancelMemberBenefitsBtn').addEventListener('click', () => {
        if(confirm("尚未儲存的變更將會遺失，確定要返回嗎？")) {
             window.location.href = 'home.html'; // Corrected path to root home.html
        }
    });

    fetchMemberLevels();
}); 