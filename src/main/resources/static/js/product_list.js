document.addEventListener('DOMContentLoaded', async function () {
    const API_URL = '/api/v1/external-products/paged';
    const productTableBody = document.getElementById('product-table-body');
    const noDataMessage = document.getElementById('no-data-message');
    const paginationControls = document.getElementById('pagination-controls');
    const filterForm = document.getElementById('filter-form');
    const clearFiltersBtn = document.getElementById('clear-filters-btn');

    let currentPage = 0;
    const defaultPageSize = 15; 

    async function fetchProducts(page = 0, queryParams = {}) {
        const pageSize = queryParams.size || defaultPageSize;
        let url = `${API_URL}?page=${page}&size=${pageSize}&sort=productName,asc`; // Default sort

        if (queryParams.productId) url += `&productId=${encodeURIComponent(queryParams.productId)}`;
        if (queryParams.category) url += `&category=${encodeURIComponent(queryParams.category)}`;
        if (queryParams.subjectCategory) url += `&subjectCategory=${encodeURIComponent(queryParams.subjectCategory)}`;
        if (queryParams.keyword) url += `&keyword=${encodeURIComponent(queryParams.keyword)}`;
        // Note: The backend ExternalProductController /paged only explicitly takes 'keyword' and Pageable.
        // Other filter params (productId, category, subjectCategory) would need to be added to its method signature
        // and to the ExternalProductService if they are to be used in backend filtering.
        // For now, they will be passed but might be ignored by the current mock backend service.

        try {
            // const response = await fetch(url, { // Using window.fetchAuthenticated from main.js
            const response = await window.fetchAuthenticated(url, {
                headers: { 'Authorization': `Bearer ${localStorage.getItem('jwtToken')}` }
            });
            
            // No need to check for 401/403 here, fetchAuthenticated handles it.
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const apiResponse = await response.json();

            if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.list) {
                renderTable(apiResponse.data.list);
                renderPagination(apiResponse.data.page);
            } else {
                showToast(apiResponse.message || '無法載入商品列表', 'error');
                renderTable([]);
                renderPagination(null);
            }
        } catch (error) {
            console.error('Fetch error:', error);
            if (!error.message || !error.message.includes('Authentication failed')) { // Avoid double toast if fetchAuthenticated handled it
                 showToast('載入商品列表失敗，請稍後再試', 'error');
            }
            renderTable([]);
            renderPagination(null);
        }
    }

    function renderTable(products) {
        productTableBody.innerHTML = ''; 
        if (!products || products.length === 0) {
            noDataMessage.classList.remove('d-none');
            return;
        }
        noDataMessage.classList.add('d-none');

        products.forEach((product, index) => {
            const row = productTableBody.insertRow();
            row.insertCell().textContent = (currentPage * defaultPageSize) + index + 1; // Simple row number
            row.insertCell().textContent = product.productId || '-'; // Assuming productId exists in DTO
            row.insertCell().textContent = product.productCategory || '-'; // Assuming productCategory exists
            row.insertCell().textContent = product.subjectCategory || '-'; // Assuming subjectCategory exists
            row.insertCell().textContent = product.productName || '-';
            row.insertCell().textContent = product.productBarcode || '-';
            
            const priceCell = row.insertCell();
            const price = parseFloat(product.price); // Assuming price exists
            priceCell.textContent = !isNaN(price) ? price.toLocaleString('en-US', { style: 'currency', currency: 'TWD' }) : '-';
        });
    }

    function renderPagination(pageData) {
        paginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 1) return;

        const createPageItem = (text, pageNum, isDisabled = false, isActive = false) => {
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = text;
            if (!isDisabled) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    currentPage = pageNum;
                    fetchProducts(currentPage, getCurrentFilters());
                });
            }
            li.appendChild(link);
            return li;
        };

        paginationControls.appendChild(createPageItem('上一頁', pageData.page - 1, pageData.page === 0));

        // Simplified pagination: show a few pages around current
        const maxPagesToShow = 5;
        let startPage = Math.max(0, pageData.page - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(pageData.totalPages - 1, startPage + maxPagesToShow - 1);
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(0, endPage - maxPagesToShow + 1);
        }

        if (startPage > 0) {
            paginationControls.appendChild(createPageItem('1', 0));
            if (startPage > 1) paginationControls.appendChild(createPageItem('...', -1, true));
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i + 1, i, false, i === pageData.page));
        }

        if (endPage < pageData.totalPages - 1) {
            if (endPage < pageData.totalPages - 2) paginationControls.appendChild(createPageItem('...', -1, true));
            paginationControls.appendChild(createPageItem(pageData.totalPages, pageData.totalPages - 1));
        }

        paginationControls.appendChild(createPageItem('下一頁', pageData.page + 1, pageData.page === pageData.totalPages - 1));
    }
    
    function getCurrentFilters() {
        return {
            productId: document.getElementById('filterProductId').value || null,
            category: document.getElementById('filterCategory').value || null,
            subjectCategory: document.getElementById('filterSubjectCategory').value || null,
            keyword: document.getElementById('filterKeyword').value || null
        };
    }

    filterForm.addEventListener('submit', function(event) {
        event.preventDefault();
        currentPage = 0; 
        fetchProducts(currentPage, getCurrentFilters());
    });

    clearFiltersBtn.addEventListener('click', function() {
        filterForm.reset();
        currentPage = 0;
        fetchProducts(currentPage);
    });

    // Initial Load
    await loadSharedHTML(); // Load header/sidebar
    fetchProducts(currentPage, getCurrentFilters()); // Fetch initial data
}); 