// Fallback for fetchWithAuth if main.js is slow to define it
if (typeof fetchWithAuth === 'undefined') {
    console.warn('fetchWithAuth not defined, creating a local fallback.');
    function fetchWithAuth(url, options = {}) {
        const token = localStorage.getItem('jwtToken');
        const headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            ...options.headers,
        };
        const newOptions = { ...options, headers };
        return fetch(url, newOptions);
    }
}

// Fallback for formatDateTime if main.js is slow to define it
if (typeof formatDateTime === 'undefined') {
    console.warn('formatDateTime not defined, creating a local fallback.');
    function formatDateTime(isoString) {
        if (!isoString) return '';
        try {
            const date = new Date(isoString);
            return date.toLocaleString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        } catch (e) {
            console.error('Invalid date string for formatting:', isoString, e);
            return isoString;
        }
    }
}

document.addEventListener('DOMContentLoaded', function () {
    const API_BASE_URL = '/api/v1';
    let currentPage = 0;

    const filterForm = document.getElementById('filter-form');
    const tableBody = document.getElementById('picking-list-table-body');
    const paginationContainer = document.getElementById('pagination-container');
    const warehouseFilter = document.getElementById('warehouse-filter');
    const technicianFilter = document.getElementById('technician-filter');
    const statusFilter = document.getElementById('status-filter');
    const resetBtn = document.getElementById('reset-btn');

    // --- Initialization ---
    
    async function initializePage() {
        await Promise.all([
            populateWarehouses(),
            populateTechnicians(),
            populateStatuses()
        ]);
        fetchPickingLists(currentPage);
    }

    async function populateWarehouses() {
        try {
            const response = await fetchWithAuth(`${API_BASE_URL}/warehouses`);
            const result = await response.json();
            if (result.code === 200 && Array.isArray(result.data)) {
                warehouseFilter.innerHTML = '<option value="">全部倉庫</option>';
                result.data.forEach(warehouse => {
                    const option = new Option(warehouse.warehouseName, warehouse.warehouseId);
                    warehouseFilter.add(option);
                });
            }
        } catch (error) {
            console.error('Failed to load warehouses:', error);
        }
    }

    async function populateTechnicians() {
        try {
            const response = await fetchWithAuth(`${API_BASE_URL}/users/technicians`);
            const result = await response.json();
            if (result.code === 200 && Array.isArray(result.data)) {
                technicianFilter.innerHTML = '<option value="">全部技師</option>';
                result.data.forEach(tech => {
                    const option = document.createElement('option');
                    option.value = tech.userAccountId;
                    option.textContent = tech.userName;
                    technicianFilter.add(option);
                });
            }
        } catch (error) {
            console.error('Failed to load technicians:', error);
        }
    }

    async function populateStatuses() {
         try {
            const response = await fetchWithAuth(`${API_BASE_URL}/enums/material-order-statuses`);
            const result = await response.json();
            if (result.code === 200 && Array.isArray(result.data)) {
                statusFilter.innerHTML = '<option value="">全部狀態</option>';
                result.data.forEach(status => {
                    const option = new Option(status.label, status.value);
                    statusFilter.add(option);
                });
            }
        } catch (error) {
            console.error('Failed to load statuses:', error);
        }
    }

    // --- Fetch and Render ---

    async function fetchPickingLists(page = 0) {
        const queryParams = new URLSearchParams({
            page: page,
            size: 15, // or your preferred page size
            sort: 'createTime,desc'
        });

        const warehouseId = warehouseFilter.value;
        const technicianId = technicianFilter.value;
        const status = statusFilter.value;
        const keyword = document.getElementById('keyword-filter').value;
        const dateFrom = document.getElementById('date-from-filter').value;
        const dateTo = document.getElementById('date-to-filter').value;

        if (warehouseId) queryParams.append('warehouseId', warehouseId);
        if (technicianId) queryParams.append('technicianId', technicianId);
        if (status) queryParams.append('status', status);
        if (keyword) queryParams.append('keyword', keyword);
        if (dateFrom) queryParams.append('dateFrom', new Date(dateFrom).toISOString());
        if (dateTo) queryParams.append('dateTo', new Date(dateTo).toISOString());

        try {
            const response = await fetchWithAuth(`${API_BASE_URL}/picking-lists?${queryParams}`);
            const result = await response.json();

            if (result.code === 200 && result.data) {
                renderTable(result.data.list);
                renderPagination(result.data.page, page);
            } else {
                showToast(result.message || '無法解析資料', 'error');
                renderTable([]); // Clear table
                renderPagination(null, 0); // Clear pagination
            }
        } catch (error) {
            console.error('Error fetching picking lists:', error);
            showToast('無法載入揀料單列表', 'error');
        }
    }

    function renderTable(data) {
        tableBody.innerHTML = '';
        if (!data || data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center">查無資料</td></tr>';
            return;
        }

        data.forEach(order => {
            const row = document.createElement('tr');
            row.dataset.id = order.id;
            row.innerHTML = `
                <td>${order.pickingOrderNumber || ''}</td>
                <td>${formatDateTime(order.requestDate)}</td>
                <td>${order.requesterName || ''}</td>
                <td>${order.technicianName || ''}</td>
                <td>${order.totalItemTypes || 0}</td>
                <td>${order.totalItemQuantity || 0}</td>
                <td><span class="badge ${getStatusBadgeClass(order.statusCode)}">${order.statusDescription || ''}</span></td>
            `;
            row.addEventListener('click', () => {
                window.location.href = `picking_list_detail.html?id=${order.id}`;
            });
            tableBody.appendChild(row);
        });
    }
    
    function getStatusBadgeClass(statusCode) {
        switch (statusCode) {
            case 10: return 'bg-secondary'; // PENDING_PICKING
            case 20: return 'bg-primary';   // PICKING_COMPLETED
            case 30: return 'bg-success';   // COLLECTED
            case 40: return 'bg-danger';    // CANCELLED
            default: return 'bg-light text-dark';
        }
    }

    function renderPagination(pageData, currentPage) {
        paginationContainer.innerHTML = '';
        if (!pageData) return;
        
        const { totalPages, total: totalElements, pageSize, page: pageNumber } = pageData;
        
        if (totalPages <= 1) return;

        const ul = document.createElement('ul');
        ul.className = 'pagination justify-content-center';
        
        const first = pageNumber === 0;
        const last = pageNumber === totalPages - 1;

        // First and Previous
        ul.innerHTML += `
            <li class="page-item ${first ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="0">«</a>
            </li>
            <li class="page-item ${first ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${pageNumber - 1}">‹</a>
            </li>
        `;

        // Page numbers
        for (let i = 0; i < totalPages; i++) {
            ul.innerHTML += `
                <li class="page-item ${i === pageNumber ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i + 1}</a>
                </li>
            `;
        }

        // Next and Last
        ul.innerHTML += `
            <li class="page-item ${last ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${pageNumber + 1}">›</a>
            </li>
            <li class="page-item ${last ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${totalPages - 1}">»</a>
            </li>
        `;
        
        paginationContainer.appendChild(ul);
    }
    
    // --- Event Listeners ---
    
    filterForm.addEventListener('submit', function (event) {
        event.preventDefault();
        currentPage = 0;
        fetchPickingLists(currentPage);
    });
    
    resetBtn.addEventListener('click', function() {
        filterForm.reset();
        currentPage = 0;
        fetchPickingLists(currentPage);
    });

    paginationContainer.addEventListener('click', function (e) {
        e.preventDefault();
        if (e.target.tagName === 'A' && e.target.dataset.page) {
            const page = parseInt(e.target.dataset.page, 10);
            if (!isNaN(page) && page !== currentPage) {
                currentPage = page;
                fetchPickingLists(page);
            }
        }
    });

    initializePage();
}); 