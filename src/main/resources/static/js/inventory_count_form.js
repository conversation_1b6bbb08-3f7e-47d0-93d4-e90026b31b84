document.addEventListener('DOMContentLoaded', function () {
    const storeSelect = document.getElementById('storeId');
    const addItemBtn = document.getElementById('add-item-btn');
    const itemsTableBody = document.getElementById('items-table-body');
    const form = document.getElementById('inventory-count-form');
    const importStockBtn = document.getElementById('import-system-stock-btn');
    
    // Set default date and month
    const today = new Date();
    document.getElementById('countDate').valueAsDate = today;
    document.getElementById('countMonth').value = today.getFullYear() + '-' + ('0' + (today.getMonth() + 1)).slice(-2);

    let itemCounter = 0;

    async function populateStoreSelect() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/auth/operable-stores');
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                storeSelect.innerHTML = '<option value="" disabled selected>請選擇門市</option>';
                apiResponse.data.forEach(store => {
                    storeSelect.add(new Option(store.storeName, store.storeId));
                });
            }
        } catch (error) {
            console.error('Failed to load stores:', error);
        }
    }
    
    function addEmptyItemRow() {
        itemCounter++;
        const row = itemsTableBody.insertRow();
        row.id = `item-row-${itemCounter}`;
        row.innerHTML = `
            <td>
                <input type="text" class="form-control form-control-sm product-search" placeholder="輸入條碼或名稱搜尋..." list="product-list-${itemCounter}">
                <datalist id="product-list-${itemCounter}"></datalist>
                <input type="hidden" class="product-barcode">
                <input type="hidden" class="product-name">
            </td>
            <td><input type="number" class="form-control form-control-sm system-quantity" readonly></td>
            <td><input type="number" class="form-control form-control-sm counted-quantity" required min="0"></td>
            <td><input type="text" class="form-control form-control-sm remarks"></td>
            <td><button type="button" class="btn btn-sm btn-danger remove-item-btn"><i class="bi bi-trash"></i></button></td>
        `;
    }
    
    // Event delegation for remove buttons
    itemsTableBody.addEventListener('click', function(e) {
        if (e.target.closest('.remove-item-btn')) {
            e.target.closest('tr').remove();
        }
    });

    // Event listener for "Import System Stock" button
    importStockBtn.addEventListener('click', async function() {
        const selectedStoreId = storeSelect.value;
        if (!selectedStoreId) {
            alert('請先選擇一個盤點門市。');
            return;
        }
        
        this.disabled = true;
        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 載入中...';

        try {
            const response = await window.fetchAuthenticated(`/api/v1/store-inventory/${selectedStoreId}/all-products`);
            const apiResponse = await response.json();

            if (apiResponse.code === 200 && apiResponse.data) {
                if (confirm('這將會清除目前的品項列表並帶入所選門市的系統庫存，確定要繼續嗎？')) {
                    itemsTableBody.innerHTML = ''; // Clear existing items
                    apiResponse.data.forEach(stockItem => {
                        addStockItemRow(stockItem);
                    });
                }
            } else {
                alert('載入系統庫存失敗: ' + (apiResponse.message || '未知錯誤'));
            }
        } catch (error) {
            console.error('Error fetching system stock:', error);
            alert('載入系統庫存時發生錯誤。');
        } finally {
            this.disabled = false;
            this.innerHTML = '帶入系統庫存';
        }
    });

    // Event listener for product search inputs
    itemsTableBody.addEventListener('keydown', async function(e) {
        if (e.target.classList.contains('product-search') && e.key === 'Enter') {
            e.preventDefault();
            
            const input = e.target;
            const keyword = input.value.trim();
            const selectedStoreId = storeSelect.value;
            
            if (!keyword) {
                alert('請輸入商品條碼或名稱');
                return;
            }
            
            if (!selectedStoreId) {
                alert('請先選擇盤點門市');
                return;
            }
            
            // 查詢商品資訊
            try {
                const response = await window.fetchAuthenticated(`/api/v1/store-inventory?storeId=${selectedStoreId}&keyword=${encodeURIComponent(keyword)}`);
                const apiResponse = await response.json();
                
                if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.list && apiResponse.data.list.length > 0) {
                    const products = apiResponse.data.list;
                    
                    if (products.length === 1) {
                        // 只有一個結果，直接填入
                        const product = products[0];
                        const row = input.closest('tr');
                        fillProductData(row, product);
                    } else {
                        // 多個結果，顯示選擇列表
                        showProductSelection(input, products);
                    }
                } else {
                    alert('找不到符合的商品');
                }
            } catch (error) {
                console.error('Error searching products:', error);
                alert('搜尋商品時發生錯誤');
            }
        }
    });
    
    // 填入商品資料的函數
    function fillProductData(row, product) {
        const productInput = row.querySelector('.product-search');
        const barcodeInput = row.querySelector('.product-barcode');
        const nameInput = row.querySelector('.product-name');
        const systemQtyInput = row.querySelector('.system-quantity');
        const countedQtyInput = row.querySelector('.counted-quantity');
        
        productInput.value = `${product.productName} (${product.productBarcode})`;
        barcodeInput.value = product.productBarcode;
        nameInput.value = product.productName;
        systemQtyInput.value = product.quantityOnHand || 0;
        
        // 設定唯讀，避免修改
        productInput.setAttribute('readonly', true);
        
        // 聚焦到實盤數量欄位
        countedQtyInput.focus();
    }
    
    // 顯示商品選擇列表
    function showProductSelection(input, products) {
        // 移除舊的選擇列表
        const existingDropdown = document.querySelector('.product-selection-dropdown');
        if (existingDropdown) {
            existingDropdown.remove();
        }
        
        // 建立下拉選單
        const dropdown = document.createElement('div');
        dropdown.className = 'product-selection-dropdown position-absolute bg-white border rounded shadow-sm';
        dropdown.style.zIndex = '1000';
        dropdown.style.maxHeight = '200px';
        dropdown.style.overflowY = 'auto';
        dropdown.style.width = input.offsetWidth + 'px';
        
        products.forEach(product => {
            const item = document.createElement('div');
            item.className = 'dropdown-item p-2';
            item.style.cursor = 'pointer';
            item.innerHTML = `
                <div>${product.productName}</div>
                <small class="text-muted">條碼: ${product.productBarcode} | 庫存: ${product.quantityOnHand || 0}</small>
            `;
            
            item.addEventListener('click', function() {
                const row = input.closest('tr');
                fillProductData(row, product);
                dropdown.remove();
            });
            
            item.addEventListener('mouseenter', function() {
                this.classList.add('bg-light');
            });
            
            item.addEventListener('mouseleave', function() {
                this.classList.remove('bg-light');
            });
            
            dropdown.appendChild(item);
        });
        
        // 定位下拉選單
        const rect = input.getBoundingClientRect();
        dropdown.style.position = 'fixed';
        dropdown.style.left = rect.left + 'px';
        dropdown.style.top = rect.bottom + 'px';
        
        document.body.appendChild(dropdown);
        
        // 點擊其他地方時關閉下拉選單
        document.addEventListener('click', function closeDropdown(e) {
            if (!dropdown.contains(e.target) && e.target !== input) {
                dropdown.remove();
                document.removeEventListener('click', closeDropdown);
            }
        });
    }
    
    addItemBtn.addEventListener('click', addEmptyItemRow);

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        const clickedButton = e.submitter;
        
        const payload = {
            storeId: storeSelect.value,
            countMonth: document.getElementById('countMonth').value,
            countDate: new Date(document.getElementById('countDate').value).toISOString(),
            remarks: document.getElementById('remarks').value,
            items: [],
            // sheetStatus will be set based on which button was clicked
        };
        
        const itemsRows = itemsTableBody.querySelectorAll('tr');
        for(const row of itemsRows) {
            const productBarcode = row.querySelector('.product-barcode').value;
            const countedQuantity = row.querySelector('.counted-quantity').value;
            if(!productBarcode || countedQuantity === '') {
                alert('所有品項都必須選擇商品並填寫實盤數量。');
                return;
            }
            payload.items.push({
                productBarcode: productBarcode,
                productName: row.querySelector('.product-name').value,
                countedQuantity: parseInt(countedQuantity, 10),
                remarks: row.querySelector('.remarks').value
            });
        }
        
        if(payload.items.length === 0){
            alert('盤點單至少需要一個品項。');
            return;
        }

        if(clickedButton.id === 'save-draft-btn') {
            payload.isSubmitted = false; // Or use a status code
            console.log("Saving as draft...");
        } else { // submit-btn
            payload.isSubmitted = true;
            console.log("Submitting for approval...");
        }
        
        console.log("Submitting payload:", JSON.stringify(payload, null, 2));

        try {
            const response = await window.fetchAuthenticated('/api/v1/inventory-counts', {
                method: 'POST',
                body: JSON.stringify(payload)
            });
            const result = await response.json();
            if(response.ok && result.code === 201) {
                alert('盤點單已成功儲存！');
                window.location.href = 'inventory_count_list.html';
            } else {
                 document.getElementById('form-error-message').textContent = result.message || '儲存失敗';
                 document.getElementById('form-error-message').classList.remove('d-none');
            }
        } catch (error) {
             document.getElementById('form-error-message').textContent = '儲存時發生錯誤: ' + error.message;
             document.getElementById('form-error-message').classList.remove('d-none');
        }
    });

    // Function to add a row with pre-filled stock data
    function addStockItemRow(stockItem) {
        itemCounter++;
        const row = itemsTableBody.insertRow();
        row.id = `item-row-${itemCounter}`;
        row.innerHTML = `
            <td>
                <input type="text" class="form-control form-control-sm product-search" value="${stockItem.productName} (${stockItem.productBarcode})" readonly>
                <input type="hidden" class="product-barcode" value="${stockItem.productBarcode}">
                <input type="hidden" class="product-name" value="${stockItem.productName}">
            </td>
            <td><input type="number" class="form-control form-control-sm system-quantity" value="${stockItem.quantityOnHand || 0}" readonly></td>
            <td><input type="number" class="form-control form-control-sm counted-quantity" required min="0"></td>
            <td><input type="text" class="form-control form-control-sm remarks"></td>
            <td><button type="button" class="btn btn-sm btn-danger remove-item-btn"><i class="bi bi-trash"></i></button></td>
        `;
    }

    // Initial state
    populateStoreSelect();
    addEmptyItemRow(); // Start with one empty row
}); 