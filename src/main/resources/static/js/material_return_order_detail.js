document.addEventListener('DOMContentLoaded', async () => {

    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('id');
    const actionButtons = document.getElementById('action-buttons');
    const completeReturnBtn = document.getElementById('complete-return-btn');

    if (!orderId) {
        document.getElementById('detail-view').innerHTML = '<p class="text-danger">未提供退料單ID。</p>';
        return;
    }
        
    try {
        const response = await window.fetchAuthenticated(`/api/v1/material-return-orders/${orderId}`);
        const result = await response.json();

        if (result.code === 200) {
            const order = result.data;
            
            const setText = (id, text) => {
                const el = document.getElementById(id);
                if (el) el.textContent = text || '';
            };

            setText('order-number', order.materialReturnOrderNumber);
            setText('create-time', new Date(order.createTime).toLocaleString());
            setText('technician-name', order.requestingTechnicianName);
            setText('warehouse-name', order.targetWarehouseName);
            setText('remarks', order.remarks);

            const statusBadge = document.getElementById('status-description');
            statusBadge.textContent = order.statusDescription;
            statusBadge.className = `badge ${getStatusBadgeClass(order.statusCode)}`;

            const itemList = document.getElementById('item-list');
            if(itemList) {
                itemList.innerHTML = '';
                order.items.forEach(item => {
                    const row = `<tr>
                            <td>${item.dispatchRepairNumber || '-'}</td>
                            <td>${item.productBarcode}</td>
                            <td>${item.productName}</td>
                            <td>${item.requestedQuantity}</td>
                            <td>${item.pickedQuantity || 0}</td>
                            <td>${item.collectedQuantity || 0}</td>
                    </tr>`;
                    itemList.innerHTML += row;
                });
            }
            
            // Display logic for the button based on return order status
            if (order.statusCode === -20) { // -20: 已驗料
                actionButtons.style.display = 'block'; 
            }

        } else {
            document.getElementById('detail-view').innerHTML = `<p class="text-danger">載入失敗: ${result.message}</p>`;
        }
    } catch (error) {
        console.error('Error fetching details:', error);
        document.getElementById('detail-view').innerHTML = '<p class="text-danger">載入退料單詳情時發生錯誤。</p>';
    }

    function getStatusBadgeClass(statusCode) {
        switch (statusCode) {
            case -10: // 待退料
                return 'bg-secondary';
            case -20: // 已驗料
                return 'bg-primary';
            case -30: // 退料完成
                return 'bg-success';
            case -40: // 取消退料
                return 'bg-danger';
            default:
                return 'bg-info text-dark';
        }
    }
    
    if (completeReturnBtn) {
        completeReturnBtn.addEventListener('click', async () => {
            try {
                const response = await window.fetchAuthenticated(`/api/v1/material-orders/${orderId}/refund-complete-collection`, {
                    method: 'POST'
                });
                const result = await response.json();

                if (result.code === 200) {
                    showToast('退料完成！', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showToast(result.message || '操作失敗', 'error');
                }
            } catch (error) {
                console.error('Error completing return:', error);
                showToast('操作時發生錯誤', 'error');
            }
        });
    }
}); 