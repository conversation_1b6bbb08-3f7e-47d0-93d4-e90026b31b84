// js/main.js - Shared JavaScript for authenticated pages

const SecurityUtil = {
    getUserContext: async function() {
        console.log("SecurityUtil: getUserContext called");
        return new Promise((resolve, reject) => {
            try {
                const storeString = localStorage.getItem('selectedStore');
                const company = localStorage.getItem('selectedCompanyContext');

                if (!company) { // The primary check should be for company context
                    console.error("SecurityUtil: Missing 'selectedCompanyContext' in localStorage.");
                }

                const storeId = storeString ? JSON.parse(storeString).storeId : null;
                const context = {
                    storeId: storeId,
                    companyDivisionCode: company
                };
                
                console.log("SecurityUtil: Successfully retrieved context:", context);
                resolve(context);

            } catch (error) {
                console.error("SecurityUtil: Error processing context from localStorage.", error);
                reject(error);
            }
        });
    }
};

// Define fetchAuthenticated in the global scope so it's available immediately
window.fetchAuthenticated = async (url, options = {}) => {
    console.log("MAIN.JS: fetchAuthenticated called for URL:", url); 
    const token = localStorage.getItem('jwtToken');
    const companyContext = localStorage.getItem('selectedCompanyContext'); 

    const headers = {
        'Content-Type': 'application/json',
        ...(options.headers || {}),
    };

    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    if (companyContext) {
        headers['X-Company-Context'] = companyContext;
    }

    const response = await fetch(url, { ...options, headers });
    console.log("MAIN.JS: fetchAuthenticated - Response status for URL:", url, "is:", response.status);
    
    // 檢查所有 response headers
    // console.log("MAIN.JS: fetchAuthenticated - All response headers:");
    // for (let [key, value] of response.headers.entries()) {
    //     console.log(`  ${key}: ${value}`);
    // }

    if (response.ok) {
        const authHeader = response.headers.get('Authorization');
        //console.log("MAIN.JS: fetchAuthenticated - Authorization header from response:", authHeader);
        
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const newToken = authHeader.substring(7); // Remove "Bearer " prefix
            const oldToken = localStorage.getItem('jwtToken');
            
            if (newToken && newToken !== oldToken) {
                console.log('MAIN.JS: New JWT token received in Authorization header, updating localStorage.');
                //console.log('MAIN.JS: Old token (first 20 chars):', oldToken ? oldToken.substring(0, 20) + '...' : 'NULL');
                //console.log('MAIN.JS: New token (first 20 chars):', newToken.substring(0, 20) + '...');
                localStorage.setItem('jwtToken', newToken);
                // 重設過期計時器
                resetTokenExpirationTimer(newToken);
            } else if (newToken === oldToken) {
                console.log('MAIN.JS: JWT token in Authorization header is same as current token, not updating.');
            } else {
                console.log('MAIN.JS: No new JWT token in Authorization header.');
            }
        } else {
            console.log('MAIN.JS: No Authorization header in response or invalid format.');
        }
    }

    if (response.status === 401) { 
        console.warn('MAIN.JS: fetchAuthenticated - Received 401 Unauthorized. Clearing token and redirecting to login for URL:', url);
        localStorage.removeItem('jwtToken');
        localStorage.removeItem('sessionUuid');
        localStorage.removeItem('userName');
        localStorage.removeItem('userRoles');
        localStorage.removeItem('selectedStore');
        localStorage.removeItem('availableStores');
        localStorage.removeItem('selectedCompanyContext'); // Clear company context on logout/auth failure
        window.location.href = '/login.html'; 
        throw new Error('Authentication failed: Unauthorized'); 
    } else if (response.status === 403) {
        console.warn('MAIN.JS: fetchAuthenticated - Received 403 Forbidden for URL:', url);
    }
    return response;
};

async function loadUserSidebarMenu() {
    console.log("MAIN.JS: loadUserSidebarMenu() called.");
    const sidebarPlaceholder = document.getElementById('sidebar-placeholder');
    if (!sidebarPlaceholder) {
        console.error("MAIN.JS: Sidebar placeholder not found. Cannot load user menu.");
        return;
    }
    sidebarPlaceholder.innerHTML = '<div class="text-light p-3">正在載入選單...</div>';

    // === START: Sidebar Collapse Logic ===
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (isCollapsed) {
        sidebarPlaceholder.classList.add('collapsed');
    }
    // === END: Sidebar Collapse Logic ===

    // Apply company theme to sidebar
    const companyContext = localStorage.getItem('selectedCompanyContext');
    const sidebarNavElement = document.querySelector('.sidebar-nav'); // Assuming sidebar-nav is the main container class after HTML is set
    
    // It's better to apply to the placeholder that will contain the .sidebar-nav or the .main-container's child directly
    // Let's assume sidebarPlaceholder will get the .sidebar-nav inside it.
    // Or, if .sidebar-nav is directly inside .main-container, target that.
    // For now, if sidebarPlaceholder IS the element that gets the .sidebar-nav class eventually, this is okay.
    // If not, this logic might need to be moved to after the sidebar HTML is fully rendered by buildMenuHtml.

    if (companyContext === 'QUEYOU') {
        // sidebarPlaceholder.classList.add('sidebar-theme-queyou'); // Apply to placeholder
        // Or, if sidebarNavElement is reliably found *before* content load by fetchAuthenticated:
        // if (sidebarNavElement) sidebarNavElement.classList.add('sidebar-theme-queyou');
    } else {
        // sidebarPlaceholder.classList.remove('sidebar-theme-queyou'); // Ensure default for EASTKING/others
        // if (sidebarNavElement) sidebarNavElement.classList.remove('sidebar-theme-queyou');
    }

    try {
        const response = await window.fetchAuthenticated('/api/v1/menu/user-sidebar');
        if (!response.ok) {
            const errData = await response.json().catch(() => ({ message: "無法載入選單" }));
            throw new Error(errData.message || `HTTP Error ${response.status}`);
        }
        const apiResult = await response.json();
        if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
            const menuData = apiResult.data;
            console.log("MAIN.JS: User sidebar menu data received:", menuData);

            // === START: Add Toggle Button HTML ===
            let sidebarHtml = `
                <div class="sidebar-header">
                    <button type="button" id="sidebar-toggle-btn" class="btn btn-sm" title="收合選單">
                        <i class="bi ${isCollapsed ? 'bi-arrow-right-square-fill' : 'bi-arrow-left-square-fill'}"></i>
                    </button>
                </div>
                <nav class="sidebar-nav"><ul class="nav flex-column">`;
            // === END: Add Toggle Button HTML ===
            
            function buildMenuHtml(items, isSubmenu = false) {
                let html = '';
                items.forEach(item => {
                    const hasChildren = item.children && item.children.length > 0;
                    const linkClass = isSubmenu ? "nav-link sub-link" : "nav-link";
                    const iconHtml = item.icon ? `<i class="${item.icon} me-2"></i>` : '<i class="bi bi-circle me-2"></i>'; // Default icon

                    if (item.type === 'GROUP') {
                        const collapseId = `submenu-${item.code.replace(/[^a-zA-Z0-9]/g, '-')}`;
                        html += `
                            <li class="nav-item">
                                <a class="${linkClass}" href="#" data-bs-toggle="collapse" data-bs-target="#${collapseId}" aria-expanded="false">
                                    ${iconHtml}<span class="sidebar-text">${item.name}</span>
                                    ${hasChildren ? '<i class="bi bi-chevron-down float-end sidebar-text"></i>' : ''}
                                </a>`;
                        if (hasChildren) {
                            html += `<ul class="collapse list-unstyled ps-3" id="${collapseId}">`;
                            html += buildMenuHtml(item.children, true);
                            html += '</ul>';
                        }
                        html += '</li>';
                    } else { // OPERATION type
                        html += `
                            <li class="nav-item">
                                <a class="${linkClass}" href="${item.path || '#'}">
                                    ${iconHtml}<span class="sidebar-text">${item.name}</span>
                                </a>
                            </li>`;
                    }
                });
                return html;
            }

            sidebarHtml += buildMenuHtml(menuData);
            sidebarHtml += '</ul></nav>';
                sidebarPlaceholder.innerHTML = sidebarHtml;
            console.log("MAIN.JS: User sidebar menu dynamically rendered.");

            // === START: Add Toggle Button Event Listener ===
            const toggleBtn = document.getElementById('sidebar-toggle-btn');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', () => {
                    const placeholder = document.getElementById('sidebar-placeholder');
                    const icon = toggleBtn.querySelector('i');
                    placeholder.classList.toggle('collapsed');
                    const isNowCollapsed = placeholder.classList.contains('collapsed');
                    localStorage.setItem('sidebarCollapsed', isNowCollapsed);
                    
                    if (isNowCollapsed) {
                        icon.classList.remove('bi-arrow-left-square-fill');
                        icon.classList.add('bi-arrow-right-square-fill');
                        toggleBtn.setAttribute('title', '展開選單');
                    } else {
                        icon.classList.remove('bi-arrow-right-square-fill');
                        icon.classList.add('bi-arrow-left-square-fill');
                        toggleBtn.setAttribute('title', '收合選單');
                    }
                });
            }
            // === END: Add Toggle Button Event Listener ===

            // Apply theme AFTER sidebar HTML is set
            const renderedSidebarNav = sidebarPlaceholder.querySelector('.sidebar-nav');
            if (renderedSidebarNav) {
                if (companyContext === 'QUEYOU') {
                    renderedSidebarNav.classList.add('sidebar-theme-queyou');
                } else {
                    renderedSidebarNav.classList.remove('sidebar-theme-queyou'); // Ensure default for EASTKING/others
                }
            } else {
                // Fallback if .sidebar-nav is not found within placeholder, try placeholder itself
                // This depends on how loadUserSidebarMenu sets the class on sidebarPlaceholder
                 if (companyContext === 'QUEYOU') {
                    sidebarPlaceholder.classList.add('sidebar-theme-queyou');
                } else {
                    sidebarPlaceholder.classList.remove('sidebar-theme-queyou');
                }
            }

            initializePageLayoutLogic(); // Re-initialize after dynamic content is loaded
            } else {
            console.error("MAIN.JS: Error or unexpected format in user menu API response:", apiResult.message || apiResult);
            sidebarPlaceholder.innerHTML = '<p class="text-danger p-3">載入選單失敗: ' + (apiResult.message || "回應格式錯誤") + '</p>';
            }
        } catch (error) {
        console.error("MAIN.JS: Error fetching user sidebar menu:", error);
        sidebarPlaceholder.innerHTML = '<p class="text-danger p-3">載入選單時發生錯誤。</p>';
    }
}

async function loadSharedHTML() {
    console.log("MAIN.JS: loadSharedHTML() called.");
    
    // Load User Sidebar Menu dynamically
    // The loadUserSidebarMenu function will now handle populating the sidebar-placeholder
    await loadUserSidebarMenu();

    // Load Header
    const headerPlaceholder = document.getElementById('header-placeholder');
    if (headerPlaceholder) {
        try {
            console.log("MAIN.JS: Fetching /_header.html");
            const response = await fetch('/_header.html'); 
            console.log("MAIN.JS: /_header.html fetch status:", response.status);
            if (response.ok) {
                const headerHtml = await response.text();
                headerPlaceholder.innerHTML = headerHtml;
                console.log("MAIN.JS: Header HTML loaded. Initializing header-dependent logic.");
                initializeHeaderDependentLogic(); 
            } else {
                console.error('MAIN.JS: Error loading header. Status:', response.status, response.statusText);
                headerPlaceholder.innerHTML = '<p class="text-danger">Error loading header.</p>';
            }
        } catch (error) {
            console.error('MAIN.JS: Failed to load header:', error);
            headerPlaceholder.innerHTML = '<p class="text-danger">Failed to load header.</p>';
        }
    }

    initializePageLayoutLogic(); 
    console.log("MAIN.JS: DOMContentLoaded - Page layout logic (sidebar highlighting, etc.) initialized.");
}

function initializeHeaderDependentLogic() { 
    console.log("MAIN.JS: initializeHeaderDependentLogic() called.");
    const userName = localStorage.getItem('userName');
    const selectedStoreString = localStorage.getItem('selectedStore');
    const selectedCompanyContext = localStorage.getItem('selectedCompanyContext'); // Get company context

    const logoImg = document.querySelector('.app-header .logo-img'); // More specific selector
    const userNameDisplay = document.getElementById('userNameDisplay'); 
    const logoutButton = document.getElementById('logoutButton'); 
    const selectedStoreDisplay = document.getElementById('selectedStoreDisplay'); 

    // Update logo based on company context
    if (logoImg) {
        if (selectedCompanyContext === 'QUEYOU') {
            logoImg.src = 'img/logo1_placeholder.png';
            logoImg.alt = '雀友 Logo'; // Update alt text as well
            console.log("MAIN.JS: Company context is QUEYOU, setting logo to logo1_placeholder.png");
        } else {
            logoImg.src = 'img/logo_placeholder.png';
            logoImg.alt = '東方不敗 Logo';
            console.log("MAIN.JS: Company context is not QUEYOU (or not set), setting logo to logo_placeholder.png");
        }
    }

    if (userNameDisplay && userName) {
        userNameDisplay.textContent = userName;
    }
    
    if (selectedStoreDisplay && selectedStoreString) {
        try {
            const selectedStore = JSON.parse(selectedStoreString);
            selectedStoreDisplay.textContent = selectedStore.storeName || '未指定門市';
            if (userNameDisplay && selectedStore.storeName && userName) { 
                 userNameDisplay.textContent = `${userName} (${selectedStore.storeName})`;
            }
        } catch (e) {
            console.error("MAIN.JS: Error parsing selected store for display:", e);
            if(selectedStoreDisplay) selectedStoreDisplay.textContent = '門市錯誤';
        }
    } else if (selectedStoreDisplay) {
         selectedStoreDisplay.textContent = '總公司';
    }

    if (logoutButton) {
        logoutButton.addEventListener('click', function () {
            console.log('MAIN.JS: Logout button clicked. Clearing localStorage and redirecting.');
            localStorage.removeItem('jwtToken');
            localStorage.removeItem('sessionUuid');
            localStorage.removeItem('userName');
            localStorage.removeItem('userRoles');
            localStorage.removeItem('availableStores');
            localStorage.removeItem('selectedStore');
            localStorage.removeItem('selectedCompanyContext'); // Clear company context on logout
            window.location.href = '/login.html'; 
        });
    } else {
        console.warn("MAIN.JS: Logout button not found in initializeHeaderDependentLogic.");
    }
}

function initializePageLayoutLogic() { 
    console.log("MAIN.JS: initializePageLayoutLogic() called.");
    const currentPath = window.location.pathname.split("/").pop() || "home.html"; // Default to home.html if path is empty
    const sidebarNav = document.querySelector('.sidebar-nav');

    if (sidebarNav) {
        const navLinks = sidebarNav.querySelectorAll('.nav-link');
        let activeParentCollapseId = null;

        // First pass: Find the active link and its parent collapse ID
        navLinks.forEach(link => {
            const linkPath = (link.getAttribute('href') || "").split("/").pop();
            if (linkPath === currentPath) {
                link.classList.add('active');
                const parentCollapse = link.closest('.collapse');
                if (parentCollapse) {
                    activeParentCollapseId = parentCollapse.id;
                }
            } else {
                link.classList.remove('active');
            }
        });

        // Second pass: Manage collapse states based on the active link
        const allCollapsableSubmenus = sidebarNav.querySelectorAll('ul.collapse');
        allCollapsableSubmenus.forEach(submenu => {
            const trigger = document.querySelector(`[data-bs-target="#${submenu.id}"]`);
            if (submenu.id === activeParentCollapseId) { // This is the parent of the active sub-link
                if (!submenu.classList.contains('show')) {
                    submenu.classList.add('show');
                }
                if (trigger && trigger.classList.contains('collapsed')) {
                    trigger.classList.remove('collapsed');
                    trigger.setAttribute('aria-expanded', 'true');
                }
            } else {
                // For other submenus (not parents of the currently active link)
                // Original request: "子選單應不必收合，等點主選單再收合即可"
                // This means this script should NOT actively collapse them on page load.
                // Bootstrap default click behavior will handle user-initiated collapse/expand.
                // So, we don't explicitly add/remove 'show' or change trigger state here for non-active parents.
                // Their state will persist from previous user interaction or Bootstrap defaults.
            }
        });

        // Special handling for the "總覽 (首頁)" link itself if it's the active one
        // and ensure it's marked active if currentPath is home.html and no submenu is active.
        const homeLink = sidebarNav.querySelector('a.nav-link[href="home.html"]');
        if (homeLink && currentPath === "home.html") {
            homeLink.classList.add('active');
        }

        // Ensure parent of an active link is also marked as "active" if it's a dropdown trigger
        if(activeParentCollapseId){
            const activeParentTrigger = document.querySelector(`[data-bs-target="#${activeParentCollapseId}"]`);
            if(activeParentTrigger) {
                activeParentTrigger.classList.add('active'); // Or some other class to indicate it's open
            }
        }
    }

    const userNameContent = document.getElementById('userNameContent');
    const userName = localStorage.getItem('userName');
    if (userNameContent && userName) {
        userNameContent.textContent = userName;
    }
}

function checkAuthAndRedirectIfNeeded() {
    const jwtToken = localStorage.getItem('jwtToken');
    const currentPath = window.location.pathname;
    // Consider root as login redirect or a public page that might not need this strict check
    const isOnLoginPage = currentPath.endsWith('login.html') || currentPath === '/' || currentPath === '/index.html';
    const isOnSelectStorePage = currentPath.endsWith('select_store.html');

    console.log("MAIN.JS: checkAuthAndRedirectIfNeeded - Token:", jwtToken ? "Present" : "Absent", "Path:", currentPath, "IsLogin:", isOnLoginPage, "IsSelectStore:", isOnSelectStorePage);

    if (!jwtToken && !isOnLoginPage && !isOnSelectStorePage) {
        console.log("MAIN.JS: Auth check failed. Redirecting to login.html from " + currentPath);
        window.location.replace('/login.html'); // Use replace to avoid this redirect showing in history
    }
}

document.addEventListener('DOMContentLoaded', async function () {
    console.log("MAIN.JS: DOMContentLoaded event fired for path:", window.location.pathname);
    
    checkAuthAndRedirectIfNeeded(); // Initial check

    // Proceed with loading shared components and page logic only if not redirected
    // (i.e., if token exists or on a public page where checkAuthAndRedirectIfNeeded doesn't redirect)
    const currentToken = localStorage.getItem('jwtToken'); // Re-check token after potential redirect
    const currentPath = window.location.pathname;
    const onLoginPage = currentPath.endsWith('login.html') || currentPath === '/' || currentPath === '/index.html';
    const onSelectStorePage = currentPath.endsWith('select_store.html');

    if (currentToken || onLoginPage || onSelectStorePage) {
        console.log("MAIN.JS: DOMContentLoaded - Token check passed OR on a public page. Proceeding to load shared HTML.");
        await loadSharedHTML(); 
        console.log("MAIN.JS: DOMContentLoaded - Shared HTML loaded (sidebar and header attempt complete).");
        initializePageLayoutLogic(); 
        console.log("MAIN.JS: DOMContentLoaded - Page layout logic (sidebar highlighting, etc.) initialized.");
    } else {
        console.log("MAIN.JS: DOMContentLoaded - Redirect should have occurred. Not loading further page elements.");
    }
    
    console.log("!!! MAIN.JS: DOMContentLoaded - END OF LISTENER EXECUTION !!!");
}); 

window.addEventListener('pageshow', function(event) {
    console.log("MAIN.JS: pageshow event fired. Persisted:", event.persisted, "Path:", window.location.pathname);
    // event.persisted is true if the page was loaded from bfcache
    // Always check auth on pageshow, especially if from bfcache
    checkAuthAndRedirectIfNeeded();
});

// Global Toast Notification Function
window.showToast = function(message, type = 'info') {
    const toastId = 'toast-' + Date.now();
    const toastBgColor = type === 'error' ? 'bg-danger' :
                       type === 'warning' ? 'bg-warning' :
                       type === 'success' ? 'bg-success' :
                       'bg-info';

    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white ${toastBgColor} border-0 position-fixed top-0 end-0 m-3" 
             role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="5000">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', toastHtml);
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    
    toastElement.addEventListener('hidden.bs.toast', function () {
        toastElement.remove();
    });

    toast.show();
};

/**
 * Gets the company division code from localStorage.
 * @returns {Short | null} 0 for 'EASTKING', 1 for 'QUEYOU', or null if not set.
 */
window.getCompanyDivisionCode = function() {
    const companyContext = localStorage.getItem('selectedCompanyContext');
    if (companyContext === 'EASTKING') {
        return 0;
    } else if (companyContext === 'QUEYOU') {
        return 1;
    }
    console.warn('Company context not found or invalid in localStorage.');
    return null; // Or a default value if appropriate
}; 

let eventListenersAttached = false; // Flag to prevent multiple bindings
let tokenExpirationTimer = null; // 全域變數，用於存放過期計時器

// =================================================================================
//  Helper Functions
// =================================================================================
/**
 * 解碼 JWT Token 以獲取其 payload，不驗證簽名。
 * @param {string} token - JWT Token.
 * @returns {object|null} - The decoded payload object or null on failure.
 */
function decodeJwtPayload(token) {
    try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        return JSON.parse(jsonPayload);
    } catch (e) {
        console.error("Failed to decode JWT payload:", e);
        return null;
    }
}

/**
 * 根據 JWT Token 的過期時間，重設自動登出計時器。
 * @param {string} token - The new JWT token.
 */
function resetTokenExpirationTimer(token) {
    // 1. 清除任何現有的計時器
    if (tokenExpirationTimer) {
        clearTimeout(tokenExpirationTimer);
        console.log("MAIN.JS: Previous token expiration timer cleared.");
    }

    // 2. 解碼新的 token 以獲取過期時間
    const payload = decodeJwtPayload(token);
    if (!payload || !payload.exp) {
        console.error("MAIN.JS: Could not decode token or find 'exp' claim. Auto-logout timer not set.");
        return;
    }

    // 3. 計算距離過期的時間
    const expiresAtMs = payload.exp * 1000;
    const nowMs = Date.now();
    const timeUntilExpiryMs = expiresAtMs - nowMs;

    // 我們在過期前 10 秒觸發登出，給予緩衝時間
    const timeoutDuration = timeUntilExpiryMs - 10000; 

    if (timeoutDuration > 0) {
        console.log(`MAIN.JS: New token expires at ${new Date(expiresAtMs).toLocaleString()}. Setting auto-logout timer in ${(timeoutDuration / 1000 / 60).toFixed(2)} minutes.`);
        tokenExpirationTimer = setTimeout(() => {
            console.warn("MAIN.JS: Token expiration timer fired. Logging out user.");
            window.showToast('連線逾時，您已被自動登出。', 'warning');
            // 執行登出邏輯
            localStorage.removeItem('jwtToken');
            localStorage.removeItem('sessionUuid');
            localStorage.removeItem('userName');
            localStorage.removeItem('userRoles');
            localStorage.removeItem('selectedStore');
            localStorage.removeItem('availableStores');
            localStorage.removeItem('selectedCompanyContext');
            window.location.href = '/login.html';
        }, timeoutDuration);
    } else {
        console.warn("MAIN.JS: Token is already expired or will expire in less than 10 seconds. Not setting a new timer.");
    }
} 

/**
 * Populates a <select> element with options from an enum API endpoint.
 * @param {string} selectId - The ID of the <select> element.
 * @param {string} apiUrl - The URL of the API endpoint to fetch enum values from.
 * @param {string} [placeholder='請選擇...'] - The placeholder text for the first option.
 * @param {string|number} [selectedValue=null] - The value to be pre-selected.
 */
async function populateEnumSelect(selectId, apiUrl, placeholder = '請選擇...', selectedValue = null) {
    const selectElement = document.getElementById(selectId);
    if (!selectElement) {
        console.error(`Element with ID "${selectId}" not found for populating enum.`);
        return;
    }

    try {
        const response = await window.fetchAuthenticated(apiUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch enum from ${apiUrl}`);
        }
        const apiResult = await response.json();
        
        if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
            selectElement.innerHTML = `<option value="">${placeholder}</option>`; 
            apiResult.data.forEach(enumValue => {
                const option = document.createElement('option');
                
                // Robustly get code and description
                const code = enumValue.code !== undefined && enumValue.code !== null ? enumValue.code : enumValue.value;
                const description = enumValue.description !== undefined && enumValue.description !== null ? enumValue.description : enumValue.label;

                // Ensure value is not null, which can become "null" string
                option.value = (code === null || code === undefined) ? "" : code;
                option.textContent = description;
                
                if (selectedValue && String(option.value) === String(selectedValue)) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            });
        } else {
            console.error(`API response for ${apiUrl} is not in the expected format.`, apiResult);
        }
    } catch (error) {
        console.error(`Error populating select ${selectId}:`, error);
        selectElement.innerHTML = `<option value="">載入失敗</option>`;
    }
} 