document.addEventListener('DOMContentLoaded', function () {
    const formatCurrency = (amount) => amount != null ? new Intl.NumberFormat('zh-TW').format(amount) : '0';
    const settlementDateEl = document.getElementById('settlementDate');
    const storeSelectEl = document.getElementById('storeSelect');
    const queryBtn = document.getElementById('queryBtn');
    const settlementDetailsEl = document.getElementById('settlementDetails');
    const submitSettlementBtn = document.getElementById('submitSettlementBtn');
    
    // Unsettled Modal
    const unsettledModal = new bootstrap.Modal(document.getElementById('unsettledModal'));
    const unsettledDateEl = document.getElementById('unsettledDate');
    const confirmUnsettledBtn = document.getElementById('confirmUnsettledBtn');

    // Initialize date to today
    settlementDateEl.valueAsDate = new Date();

    // Fetch operable stores
    fetchAuthenticated('/api/v1/auth/operable-stores')
        .then(response => response.json())
        .then(apiResponse => {
            if (apiResponse.data) {
                apiResponse.data.forEach(store => {
                    const option = document.createElement('option');
                    option.value = store.storeId;
                    option.textContent = store.storeName;
                    storeSelectEl.appendChild(option);
                });
            }
        });

    queryBtn.addEventListener('click', () => {
        const storeId = storeSelectEl.value;
        const date = settlementDateEl.value;
        if (!storeId || !date) {
            alert('請選擇門市和日期');
            return;
        }

        fetchAuthenticated(`/api/v1/store-daily-settlements/data-for-settlement?storeId=${storeId}&date=${date}`)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => { throw new Error(err.message || '查詢失敗') });
                }
                return response.json();
            })
            .then(apiResponse => {
                if (apiResponse.code === 200) {
                    populateSettlementData(apiResponse.data);
                    settlementDetailsEl.classList.remove('d-none');
                } else {
                    throw new Error(apiResponse.message);
                }
            })
            .catch(error => {
                if (error.message.includes('尚有未完成的日結')) {
                    const dateMatch = error.message.match(/：(\d{4}-\d{2}-\d{2})/);
                    unsettledDateEl.textContent = dateMatch ? dateMatch[1] : '較早的日期';
                    unsettledModal.show();
                } else {
                    alert(`錯誤: ${error.message}`);
                }
            });
    });

    confirmUnsettledBtn.addEventListener('click', () => {
        const unsettledDate = unsettledDateEl.textContent;
        if (unsettledDate) {
            settlementDateEl.value = unsettledDate;
            unsettledModal.hide();
            queryBtn.click();
        }
    });

    function populateSettlementData(data) {


        document.getElementById('salesTotal').textContent = formatCurrency(data.totalSalesAmount);
        document.getElementById('returnsTotal').textContent = formatCurrency(data.totalReturnsAmount);
        
        document.getElementById('cashIncome').value = formatCurrency(data.cashIncomeAmount);
        document.getElementById('creditCardIncome').value = formatCurrency(data.creditCardIncomeAmount);
        document.getElementById('remittanceIncome').value = formatCurrency(data.remittanceIncomeAmount);
        document.getElementById('unpaidBalance').value = formatCurrency(data.accountsReceivableAmount);
        document.getElementById('repaymentAmount').value = formatCurrency(data.receivableRepaymentAmount);

        document.getElementById('totalRevenue').value = formatCurrency(data.totalRevenueAmount);
        
        const salesAccordion = document.getElementById('salesAccordion');
        const returnsAccordion = document.getElementById('returnsAccordion');
        salesAccordion.innerHTML = createDetailsAccordion('sales', data.salesDetails);
        returnsAccordion.innerHTML = createDetailsAccordion('returns', data.returnDetails);

        updateSummary();
    }
    
    function createDetailsAccordion(idPrefix, details) {
        if (!details || details.length === 0) return '<p class="text-muted">無資料</p>';
        return details.map((item, index) => `
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading-${idPrefix}-${index}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-${idPrefix}-${index}">
                        ${item.sourceDocumentNumber} - ${item.customerName || ''} - ${formatCurrency(item.amount)}
                    </button>
                </h2>
                <div id="collapse-${idPrefix}-${index}" class="accordion-collapse collapse" data-bs-parent="#${idPrefix}Accordion">
                    <div class="accordion-body">
                        <strong>品項:</strong> ${item.productName || 'N/A'}<br>
                        <strong>類型:</strong> ${item.transactionTypeName}
                    </div>
                </div>
            </div>
        `).join('');
    }

    // Expenditure Logic
    const expenditureListEl = document.getElementById('expenditureList');
    const addExpenditureBtn = document.getElementById('addExpenditureBtn');

    addExpenditureBtn.addEventListener('click', () => {
        const expenditureItem = `
            <div class="row g-3 mb-2 expenditure-item">
                <div class="col-md-4">
                    <input type="text" class="form-control expenditure-name" placeholder="支出項目">
                </div>
                <div class="col-md-3">
                    <input type="number" class="form-control expenditure-amount" placeholder="金額">
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control expenditure-remarks" placeholder="備註">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm remove-expenditure-btn">移除</button>
                </div>
            </div>
        `;
        expenditureListEl.insertAdjacentHTML('beforeend', expenditureItem);
    });

    expenditureListEl.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('remove-expenditure-btn')) {
            e.target.closest('.expenditure-item').remove();
            updateSummary();
        }
    });
    
    expenditureListEl.addEventListener('input', function(e) {
        if (e.target && e.target.classList.contains('expenditure-amount')) {
            updateSummary();
        }
    });

    function updateSummary() {
        let totalExpenditure = 0;
        document.querySelectorAll('.expenditure-amount').forEach(input => {
            totalExpenditure += Number(input.value) || 0;
        });

        const totalExpenditureEl = document.getElementById('totalExpenditure');
        totalExpenditureEl.value = new Intl.NumberFormat('zh-TW').format(totalExpenditure);

        const cashIncome = parseFloat(document.getElementById('cashIncome').value.replace(/,/g, '')) || 0;
        const repayment = parseFloat(document.getElementById('repaymentAmount').value.replace(/,/g, '')) || 0;
        const expectedRepayment = (cashIncome + repayment) - totalExpenditure;
        document.getElementById('expectedRepayment').value = new Intl.NumberFormat('zh-TW').format(expectedRepayment);
    }
    
    // Submit Logic
    submitSettlementBtn.addEventListener('click', () => {
        const expenditureDetails = [];
        document.querySelectorAll('.expenditure-item').forEach(item => {
            const name = item.querySelector('.expenditure-name').value;
            const amount = item.querySelector('.expenditure-amount').value;
            const remarks = item.querySelector('.expenditure-remarks').value;
            if (name && amount) {
                expenditureDetails.push({ expenditureItemName: name, amount, remarks });
            }
        });

        const actualCashInput = document.getElementById('actualCashIncome').value;
        
        const requestBody = {
            storeId: storeSelectEl.value,
            settlementDate: settlementDateEl.value,
            actualCashIncomeAmount: actualCashInput ? parseFloat(actualCashInput) : null,
            remarks: document.getElementById('remarks').value,
            expenditureDetails: expenditureDetails
        };
        
        console.log("Submitting:", requestBody);

        fetchAuthenticated('/api/v1/store-daily-settlements', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody)
        })
        .then(response => response.json())
        .then(apiResponse => {
            if (apiResponse.code === 201) {
                alert('日結成功！');
                window.location.reload();
            } else {
                alert(`日結失敗: ${apiResponse.message}`);
            }
        })
        .catch(error => alert(`錯誤: ${error.message}`));
    });
}); 