document.addEventListener('DOMContentLoaded', function () {
    // --- DOM Elements ---
    const warehouseSelect = document.getElementById('warehouse-select');
    const productSearchInput = document.getElementById('product-search');
    const searchProductBtn = document.getElementById('search-product-btn');
    const dispatchItemsContainer = document.getElementById('dispatch-items-container');
    const materialItemsByWarehouseContainer = document.getElementById('material-items-by-warehouse');
    const submitBtn = document.getElementById('submit-btn');
    const accessorySearchModal = new bootstrap.Modal(document.getElementById('accessorySearchModal'));
    const accessorySearchResultsContainer = document.getElementById('accessory-search-results');

    // --- State ---
    let manualItems = []; // To store manually added items for return

    // --- Initialization ---
    async function initializePage() {
        await loadWarehousesForSelect();
        await loadAndRenderAllPendingItems();
        attachEventListeners();
    }

    // --- Data Fetching & Rendering ---
    async function loadWarehousesForSelect() {
        try {
            // For material return, we are returning to a specific warehouse.
            // This might need adjustment based on business logic (e.g., only show technician's own warehouse)
            const response = await window.fetchAuthenticated('/api/v1/warehouses?isTechnicianWarehouse=true');
            if (!response.ok) throw new Error('無法載入倉庫列表');
            const result = await response.json();
            populateSelect(warehouseSelect, result.data, 'warehouseId', 'warehouseName', '請選擇退料倉庫');
        } catch (error) {
            console.error(error);
            showToast(error.message, 'danger');
        }
    }

    async function loadAndRenderAllPendingItems() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/dispatch-orders/refund-pending-items');
            if (!response.ok) throw new Error('無法載入待退料品項');
            const result = await response.json();
            allPendingItems = result.data || [];
            renderDispatchItemRows(allPendingItems);
        } catch (error) {
            console.error(error);
            showToast(error.message, 'danger');
            dispatchItemsContainer.innerHTML = `<tr><td colspan="9" class="text-center text-danger">${error.message}</td></tr>`;
        }
    }

    function populateSelect(element, data, valueField, textField, defaultOptionText) {
        element.innerHTML = `<option value="">${defaultOptionText}</option>`;
        if (data && Array.isArray(data)) {
            data.forEach(item => {
                element.innerHTML += `<option value="${item[valueField]}">${item[textField]}</option>`;
            });
        }
    }

    function renderDispatchItemRows(items) {
        dispatchItemsContainer.innerHTML = '';
        if (!items || items.length === 0) {
            dispatchItemsContainer.innerHTML = '<p class="text-center text-muted">無待退料品項</p>';
            return;
        }

        // Add a header row
        const header = document.createElement('div');
        header.className = 'dispatch-item-row dispatch-item-header d-none d-lg-flex row gx-2';
        header.innerHTML = `
            <div class="col-2"><strong>派工單號</strong></div>
            <div class="col-3"><strong>商品名稱</strong></div>
            <div class="col-1 text-center"><strong>退料</strong></div>
            <div class="col-1 text-center"><strong>預退</strong></div>
            <div class="col-1 text-center"><strong>已退</strong></div>
            <div class="col-2"><strong>倉庫</strong></div>
            <div class="col-2"><strong>退料數量</strong></div>
        `;
        dispatchItemsContainer.appendChild(header);

        items.forEach(item => {
            const totalRequested = item.alreadyRequestedQuantity || 0;
            const dispatchQuantity = item.quantity || 0;
            const remainingQty = dispatchQuantity - totalRequested;
            const canPick = remainingQty > 0;

            const row = document.createElement('div');
            row.className = 'dispatch-item-row card card-body mb-2'; // Use card for styling
            row.dataset.itemId = item.dispatchRepairItemId;
            row.dataset.dispatchQuantity = dispatchQuantity;
            row.dataset.alreadyRequested = totalRequested;

            row.innerHTML = `
                <div class="row gx-2 align-items-center">
                    <div class="col-12 col-lg-2 mb-2 mb-lg-0">
                        <strong class="d-lg-none">退機單: </strong>
                        ${item.dispatchRepairNumber}
                        <span class="text-muted small">${item.scheduledDate}</span>
                    </div>
                    <div class="col-12 col-lg-3 mb-2 mb-lg-0">
                        <strong class="d-lg-none">商品: </strong>
                        ${item.productName}
                        <span class="text-muted small">(${item.productBarcode})</span>
                    </div>
                    <div class="col-4 col-lg-1 text-lg-center"><strong class="d-lg-none">退料: </strong>${dispatchQuantity}</div>
                    <div class="col-4 col-lg-1 text-lg-center"><strong class="d-lg-none">預退: </strong>${totalRequested}</div>
                    <div class="col-4 col-lg-1 text-lg-center"><strong class="d-lg-none">已退: </strong>${item.pickedQuantity || 0}</div>
                    <div class="col-12 col-lg-2 mb-2 mb-lg-0"><strong class="d-lg-none">倉庫: </strong>${item.warehouseName}</div>
                    <div class="col-12 col-lg-2">
                        <div class="input-group input-group-sm">
                            <input type="number" class="form-control quantity-to-pick" value="${remainingQty}" min="0" max="${remainingQty}" ${!canPick ? 'disabled' : ''} aria-label="退料數量">
                            <div class="input-group-text">
                                <input class="form-check-input mt-0 add-to-list-checkbox" type="checkbox" title="加入退料" ${!canPick ? 'disabled' : ''} aria-label="加入退料">
                            </div>
                        </div>
                    </div>
                </div>
            `;
            dispatchItemsContainer.appendChild(row);
        });
    }
    
    function renderProductSearchResults(items) {
        accessorySearchResultsContainer.innerHTML = '';
        if (!items || items.length === 0) {
            accessorySearchResultsContainer.innerHTML = '<p class="text-muted">查無相關品項</p>';
            return;
        }
        items.forEach(item => {
            const itemEl = document.createElement('div');
            itemEl.className = 'list-group-item d-flex justify-content-between align-items-center';
            itemEl.innerHTML = `
                <div>
                    <h6 class="mb-1">${item.productName}</h6>
                    <small class="text-muted">${item.productBarcode}</small>
                    <small class="text-primary"> (庫存: ${item.quantityOnHand})</small>
                </div>
                <div class="input-group" style="width: 200px;">
                    <input type="number" class="form-control accessory-quantity" placeholder="數量" min="1" max="${item.quantityOnHand}">
                    <button class="btn btn-primary select-accessory-btn" data-barcode="${item.productBarcode}" data-name="${item.productName}">選定</button>
                </div>
            `;
            accessorySearchResultsContainer.appendChild(itemEl);
        });
    }

    function updateMaterialItemsDisplay() {
        materialItemsByWarehouseContainer.innerHTML = '';
        const itemsToSubmit = manualItems;
        const itemsByWarehouse = groupBy(itemsToSubmit, 'warehouseName');

        for (const warehouseName in itemsByWarehouse) {
            const warehouseDiv = document.createElement('div');
            warehouseDiv.innerHTML = `<h5>退料倉庫: ${warehouseName}</h5>`;
            const table = document.createElement('table');
            table.className = 'table table-striped';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>條碼</th>
                        <th>商品名稱</th>
                        <th>退料數量</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemsByWarehouse[warehouseName].map(item => `
                        <tr data-barcode="${item.productBarcode}">
                            <td>${item.productBarcode}</td>
                            <td>${item.productName}</td>
                            <td>${item.requestedQuantity}</td>
                            <td><button class="btn btn-sm btn-danger delete-item-btn"><i class="bi bi-trash"></i></button></td>
                        </tr>
                    `).join('')}
                </tbody>
            `;
            warehouseDiv.appendChild(table);
            materialItemsByWarehouseContainer.appendChild(warehouseDiv);
        }
        
        submitBtn.disabled = itemsToSubmit.length === 0;
    }

    // --- Event Listeners ---
    function attachEventListeners() {
        searchProductBtn.addEventListener('click', handleProductSearch);
        accessorySearchResultsContainer.addEventListener('click', handleProductSelect);
        materialItemsByWarehouseContainer.addEventListener('click', handleDeleteItem);
        submitBtn.addEventListener('click', handleSubmit);
    }

    async function handleProductSearch() {
        const selectedWarehouseId = warehouseSelect.value;
        const keyword = productSearchInput.value.trim();

        if (!selectedWarehouseId) {
            showToast('請先選擇退料倉庫', 'warning');
            return;
        }
        if (!keyword) {
            showToast('請輸入品項關鍵字', 'warning');
            return;
        }
        
        try {
            // Search technician's own inventory for items to return
            const response = await window.fetchAuthenticated(`/api/v1/inventories/technician-stock?warehouseId=${selectedWarehouseId}&keyword=${keyword}`);
            if (!response.ok) throw new Error('搜尋庫存失敗');
            const result = await response.json();
            renderProductSearchResults(result.data);
            accessorySearchModal.show();
        } catch (error) {
            showToast(error.message, 'danger');
        }
    }

    function handleProductSelect(event) {
        const target = event.target;
        if (!target.classList.contains('select-accessory-btn')) return;

        const itemRow = target.closest('.list-group-item');
        const quantityInput = itemRow.querySelector('.accessory-quantity');
        const quantity = parseInt(quantityInput.value, 10);
        const maxQuantity = parseInt(quantityInput.max, 10);

        if (isNaN(quantity) || quantity <= 0) {
            showToast('請輸入有效的退料數量', 'warning');
            return;
        }
        if (quantity > maxQuantity) {
            showToast(`退料數量不可超過庫存量 (${maxQuantity})`, 'warning');
            return;
        }

        const selectedWarehouseText = warehouseSelect.options[warehouseSelect.selectedIndex].text;
        
        manualItems.push({
            productBarcode: target.dataset.barcode,
            productName: target.dataset.name,
            requestedQuantity: quantity,
            warehouseName: selectedWarehouseText
        });

        updateMaterialItemsDisplay();
        accessorySearchModal.hide();
    }
    
    async function handleSubmit() {
        if (manualItems.length === 0) {
            showToast('請至少選擇一個退料品項', 'warning');
            return;
        }

        const itemsByWarehouse = groupBy(manualItems, 'warehouseName');
        const submissionPromises = [];

        for (const warehouseName in itemsByWarehouse) {
            const warehouseId = warehouseSelect.options[Array.from(warehouseSelect.options).findIndex(opt => opt.text === warehouseName)].value;
            
            const payload = {
                targetWarehouseId: warehouseId,
                remarks: `退料單 for ${warehouseName}`,
                items: itemsByWarehouse[warehouseName].map(item => ({
                    productBarcode: item.productBarcode,
                    requestedQuantity: item.requestedQuantity,
                }))
            };
            
            const promise = window.fetchAuthenticated('/api/v1/material-orders/refund', {
                method: 'POST',
                body: JSON.stringify(payload)
            }).then(response => {
                if (!response.ok) {
                    return response.json().then(err => Promise.reject(err));
                }
                return response.json();
            });
            submissionPromises.push(promise);
        }

        try {
            await Promise.all(submissionPromises);
            showToast('所有退料單已成功建立！', 'success');
            setTimeout(() => { window.location.href = 'material_return_order_list.html'; }, 1500);
        } catch (error) {
            console.error('建立退料單失敗:', error);
            showToast(`建立退料單失敗: ${error.message || '未知錯誤'}`, 'danger');
        }
    }

    function handleDeleteItem(event) {
        const target = event.target;
        if (!target.closest('.delete-item-btn')) return;

        const rowToDelete = target.closest('tr');
        const productBarcode = rowToDelete.dataset.barcode;
        
        manualItems = manualItems.filter(item => item.productBarcode !== productBarcode);

        updateMaterialItemsDisplay();
    }

    // --- Helpers ---
    function groupBy(array, key) {
        return array.reduce((result, currentValue) => {
            (result[currentValue[key]] = result[currentValue[key]] || []).push(currentValue);
            return result;
        }, {});
    }
    
    function showToast(message, type = 'info') {
        if(window.showToast) {
            window.showToast(message, type);
        } else {
            alert(message);
        }
    }
    
    // --- Start ---
    initializePage();
}); 