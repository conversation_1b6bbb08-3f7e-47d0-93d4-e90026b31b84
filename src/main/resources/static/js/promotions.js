document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.getElementById('promotions-table-body');
    const paginationControls = document.getElementById('pagination-controls');
    const filterForm = document.getElementById('filter-form');
    const noDataMessage = document.getElementById('no-data-message');
    const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const deleteConfirmMessage = document.getElementById('deleteConfirmMessage');
    let promotionToDeleteId = null;

    let currentPage = 0;
    const pageSize = 10;
    let currentSort = { key: 'updateTime', direction: 'desc' };

    async function fetchPromotions(page = 0, sortKey = currentSort.key, sortDirection = currentSort.direction) {
        currentPage = page;
        currentSort.key = sortKey;
        currentSort.direction = sortDirection;

        let url = `/api/v1/promotions?page=${page}&size=${pageSize}&sort=${sortKey},${sortDirection}`;
        const keyword = document.getElementById('filterKeyword').value;
        const activityDateFrom = document.getElementById('filterActivityDateFrom').value;
        const activityDateTo = document.getElementById('filterActivityDateTo').value;
        const isActive = document.getElementById('filterIsActive').value;

        if (keyword) url += `&keyword=${encodeURIComponent(keyword)}`;
        if (activityDateFrom) url += `&activityDateFrom=${new Date(activityDateFrom).toISOString()}`;
        if (activityDateTo) url += `&activityDateTo=${new Date(activityDateTo).toISOString()}`;
        if (isActive !== "") url += `&isActive=${isActive}`;

        try {
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
            const apiResult = await response.json();

            tableBody.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && apiResult.data.list && apiResult.data.list.length > 0) {
                noDataMessage.classList.add('d-none');
                apiResult.data.list.forEach((promo, index) => {
                    const row = tableBody.insertRow();
                    row.insertCell().textContent = (currentPage * pageSize) + index + 1;
                    row.insertCell().textContent = promo.promotionName;
                    row.insertCell().textContent = promo.startTime ? new Date(promo.startTime).toLocaleString() : '-';
                    row.insertCell().textContent = promo.endTime ? new Date(promo.endTime).toLocaleString() : '-';
                    row.insertCell().innerHTML = promo.isActive ? '<span class="badge bg-success">啟用中</span>' : '<span class="badge bg-secondary">未啟用</span>';
                    row.insertCell().textContent = promo.updateByName || '-';
                    row.insertCell().textContent = promo.updateTime ? new Date(promo.updateTime).toLocaleString() : '-';
                    
                    const actionsCell = row.insertCell();
                    actionsCell.innerHTML = `
                        <a href="promotion_form.html?id=${promo.promotionId}&mode=edit" class="btn btn-sm btn-outline-primary me-1" title="編輯"><i class="bi bi-pencil"></i></a>
                        <button class="btn btn-sm btn-outline-danger delete-btn" data-id="${promo.promotionId}" data-name="${promo.promotionName}" title="刪除"><i class="bi bi-trash"></i></button>
                    `;
                });
                renderPagination(apiResult.data.page);
                updateSortIcons();
            } else {
                noDataMessage.classList.remove('d-none');
                paginationControls.innerHTML = '';
            }
        } catch (error) {
            console.error('Error fetching promotions:', error);
            tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">無法載入促銷活動: ${error.message}</td></tr>`;
            noDataMessage.classList.remove('d-none');
        }
    }
    
    function updateSortIcons() {
        document.querySelectorAll('.sortable-header').forEach(header => {
            header.classList.remove('asc', 'desc');
            if (header.dataset.sortKey === currentSort.key) {
                header.classList.add(currentSort.direction);
            }
        });
    }

    document.querySelectorAll('.sortable-header').forEach(header => {
        header.addEventListener('click', function() {
            const sortKey = this.dataset.sortKey;
            let newDirection = 'desc';
            if (currentSort.key === sortKey && currentSort.direction === 'desc') {
                newDirection = 'asc';
            }
            fetchPromotions(currentPage, sortKey, newDirection);
        });
    });

    function renderPagination(pageData) {
        // Similar pagination logic as in other list JS files (e.g., announcements.js)
        // Needs to be adapted to use pageData.page (0-indexed), pageData.totalPages, etc.
        paginationControls.innerHTML = ''; 
        if (!pageData || pageData.totalPages <= 1) return;

        const createPageItem = (pageNum, text, isDisabled = false, isActive = false) => {
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = text;
            if (!isDisabled) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    fetchPromotions(pageNum, currentSort.key, currentSort.direction);
                });
            }
            li.appendChild(link);
            return li;
        };

        paginationControls.appendChild(createPageItem(pageData.page - 1, '上一頁', pageData.page === 0));

        const maxPagesToShow = 5;
        let startPage = Math.max(0, pageData.page - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(pageData.totalPages - 1, startPage + maxPagesToShow - 1);
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(0, endPage - maxPagesToShow + 1);
        }

        if (startPage > 0) {
            paginationControls.appendChild(createPageItem(0, '1'));
            if (startPage > 1) paginationControls.appendChild(createPageItem(-1, '...', true));
        }
        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i, i + 1, false, i === pageData.page));
        }
        if (endPage < pageData.totalPages - 1) {
            if (endPage < pageData.totalPages - 2) paginationControls.appendChild(createPageItem(-1, '...', true));
            paginationControls.appendChild(createPageItem(pageData.totalPages - 1, pageData.totalPages));
        }
        paginationControls.appendChild(createPageItem(pageData.page + 1, '下一頁', pageData.page >= pageData.totalPages - 1));
    }

    tableBody.addEventListener('click', function(event) {
        if (event.target.closest('.delete-btn')) {
            const button = event.target.closest('.delete-btn');
            promotionToDeleteId = button.dataset.id;
            deleteConfirmMessage.textContent = `您確定要刪除促銷活動「${button.dataset.name}」嗎？`;
            deleteConfirmModal.show();
        }
    });

    confirmDeleteBtn.addEventListener('click', async function() {
        if (!promotionToDeleteId) return;
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/${promotionToDeleteId}`, { method: 'DELETE' });
            const result = await response.json();
            if (response.ok && result.code === 200) {
                showToast(result.message || '促銷活動已刪除', 'success');
                fetchPromotions(currentPage, currentSort.key, currentSort.direction);
            } else {
                showToast(result.message || '刪除失敗', 'error');
            }
        } catch (error) {
            console.error('Error deleting promotion:', error);
            showToast('刪除促銷活動時發生錯誤', 'error');
        }
        deleteConfirmModal.hide();
        promotionToDeleteId = null;
    });

    filterForm.addEventListener('submit', function(event) {
        event.preventDefault();
        fetchPromotions(0, currentSort.key, currentSort.direction);
    });

    // Initial load
    fetchPromotions();
}); 