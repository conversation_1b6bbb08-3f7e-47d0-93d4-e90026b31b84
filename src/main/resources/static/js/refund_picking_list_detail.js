document.addEventListener('DOMContentLoaded', function() {
    const API_BASE_URL = '/api/v1';
    const urlParams = new URLSearchParams(window.location.search);
    const pickingListId = urlParams.get('id');

    const itemListBody = document.getElementById('item-list-table-body');
    const checkAllBtn = document.getElementById('check-all-btn');
    const uncheckAllBtn = document.getElementById('uncheck-all-btn');
    const completePickingBtn = document.getElementById('complete-picking-btn');
    
    // --- Initialization ---

    async function initializePage() {
        if (!pickingListId) {
            showToast('未提供驗料單ID', 'error');
            return;
        }
        fetchRefundPickingListDetails();
    }

    // --- Fetch and Render ---

    async function fetchRefundPickingListDetails() {
        try {
            const response = await window.fetchAuthenticated(`${API_BASE_URL}/picking-lists/${pickingListId}`);
            const result = await response.json();

            if (result.code === 200 && result.data) {
                renderDetails(result.data);
            } else {
                showToast(result.message || '無法解析資料', 'error');
            }
        } catch (error) {
            console.error('Error fetching refund picking list details:', error);
            showToast('無法載入驗料單詳情', 'error');
        }
    }

    function renderDetails(data) {
        document.getElementById('picking-order-number').textContent = data.pickingOrderNumber || '';
        document.getElementById('request-date').textContent = formatDateTime(data.requestDate);
        document.getElementById('status-description').innerHTML = `<span class="badge ${getStatusBadgeClass(data.statusCode)}">${data.statusDescription || ''}</span>`;
        document.getElementById('requester-name').textContent = data.requesterName || '';
        document.getElementById('technician-name').textContent = data.technicianName || '';
        document.getElementById('warehouse-name').textContent = data.warehouseName || '';
        document.getElementById('notes').textContent = data.notes || '';

        // Display logic based on status code
        if (data.statusCode > -10) {
            checkAllBtn.style.display = 'none';
            uncheckAllBtn.style.display = 'none';
            completePickingBtn.style.display = 'none';
        } else {
            checkAllBtn.style.display = 'inline-block';
            uncheckAllBtn.style.display = 'inline-block';
            completePickingBtn.style.display = 'inline-block';
        }

        renderItems(data.items, data.statusCode);
    }

    function renderItems(items, statusCode) {
        itemListBody.innerHTML = '';
        if (!items || items.length === 0) {
            itemListBody.innerHTML = '<tr><td colspan="4" class="text-center">此驗料單無品項</td></tr>';
            return;
        }

        const isReadOnly = statusCode > -10;

        items.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.productBarcode || ''}</td>
                <td>${item.productName || ''}</td>
                <td>${item.requestedQuantity || 0}</td>
                <td>
                    <div class="form-check d-flex justify-content-center">
                        <input class="form-check-input pick-status-checkbox" type="checkbox" 
                               data-item-id="${item.itemId}" 
                               data-quantity="${item.requestedQuantity}" 
                               ${(item.isPicked || isReadOnly) ? 'checked' : ''} 
                               ${isReadOnly ? 'disabled' : ''}>
                    </div>
                </td>
            `;
            itemListBody.appendChild(row);
        });
    }

    function getStatusBadgeClass(statusCode) {
        switch (statusCode) {
            case -10: return 'bg-secondary';
            case -20: return 'bg-primary';
            case -30: return 'bg-success';
            case -40: return 'bg-danger';
            default: return 'bg-light text-dark';
        }
    }

    // --- Event Listeners ---

    itemListBody.addEventListener('change', function(event) {
        const target = event.target;
        if (target.classList.contains('pick-status-checkbox')) {
            // UI change only, no API call here.
        }
    });
    
    checkAllBtn.addEventListener('click', () => {
        document.querySelectorAll('.pick-status-checkbox:not(:checked)').forEach(cb => {
            if (!cb.disabled) cb.checked = true;
        });
    });

    uncheckAllBtn.addEventListener('click', () => {
        document.querySelectorAll('.pick-status-checkbox:checked').forEach(cb => {
            if (!cb.disabled) cb.checked = false;
        });
    });

    completePickingBtn.addEventListener('click', async function() {
        const allCheckboxes = document.querySelectorAll('.pick-status-checkbox');
        const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);

        if (!allChecked) {
            showToast('尚有未驗料的品項，請全部勾選後再提交。', 'warning');
            return;
        }
        
        const itemsToSubmit = Array.from(allCheckboxes).map(cb => ({
            itemId: cb.dataset.itemId,
            pickedQuantity: parseInt(cb.dataset.quantity, 10)
        }));

        const requestDto = {
            items: itemsToSubmit
        };

        try {
            const response = await window.fetchAuthenticated(`${API_BASE_URL}/picking-lists/${pickingListId}/complete-refund-picking`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestDto)
            });

            const result = await response.json();

            if (result.code === 200) {
                showToast('驗料完成！', 'success');
                setTimeout(() => {
                    window.location.href = 'refund_picking_list.html';
                }, 1500);
            } else {
                showToast(result.message || '提交失敗，請重試。', 'error');
            }
        } catch (error) {
            console.error('Failed to complete refund picking:', error);
            showToast('提交時發生錯誤。', 'error');
        }
    });

    initializePage();
}); 