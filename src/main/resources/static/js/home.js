// js/home.js - Home page specific JavaScript
document.addEventListener('DOMContentLoaded', function () {
    const announcementModalElement = document.getElementById('announcementModal');
    const announcementsContainer = document.getElementById('announcementsContainer');
    let announcementModalInstance = null;

    // Ensure user is authenticated (main.js should handle redirect if not)
    if (!localStorage.getItem('jwtToken')) {
        console.warn("No JWT token found on home.js, main.js should have redirected.");
        // main.js should redirect, but as a fallback, uncomment if needed:
        // window.location.href = 'login.html'; 
        return; 
    }
    
    if (announcementModalElement) {
        announcementModalInstance = new bootstrap.Modal(announcementModalElement);
        fetchPublicAnnouncements(); // Changed from fetchAnnouncements
    }

    async function fetchPublicAnnouncements() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/announcements/public');
            if (!response.ok) {
                 let errorMsg = `讀取公告失敗: ${response.status}`;
                 try {
                    const errData = await response.json();
                    errorMsg = errData.message || errorMsg;
                 } catch(e){ /* ignore */}
                 throw new Error(errorMsg);
            }
            const apiResult = await response.json(); // Changed variable name
            if (apiResult.code === 200 && apiResult.data) {
                const announcements = apiResult.data; // Actual list is in apiResult.data
                renderAnnouncements(announcements);
                if (announcements && announcements.length > 0 && announcementModalInstance) {
                    announcementModalInstance.show();
                }
            } else {
                // Handle cases where API returns OK but with an error code in the body
                const errorMessage = apiResult.message || '無法正確讀取公告內容。';
                console.error('Error in public announcements response:', errorMessage);
                announcementsContainer.innerHTML = `<p class="text-danger">${errorMessage}</p>`;
                if (announcementModalInstance) announcementModalInstance.show(); // Show modal to inform user
            }
        } catch (error) {
            console.error('Error fetching public announcements:', error);
            announcementsContainer.innerHTML = `<p class="text-danger">無法載入公告：${error.message}</p>`;
            if (announcementModalInstance) announcementModalInstance.show(); // Show modal even with error to inform user
        }
    }

    function renderAnnouncements(announcementsArray) {
        if (!announcementsContainer) return; // Safety check

        if (!Array.isArray(announcementsArray)) { // Explicitly check if it's an array
            console.error('renderAnnouncements was called with non-array:', announcementsArray);
            announcementsContainer.innerHTML = '<p>公告資料格式錯誤。</p>';
            return;
        }

        if (announcementsArray.length === 0) {
            announcementsContainer.innerHTML = '<p>目前沒有最新公告。</p>';
            return;
        }

        let html = announcementsArray.map(ann => {
            // Use categoryDescription if available, otherwise map code
            const categoryText = ann.categoryDescription || (categories[ann.categoryCode] || '一般');
            // Format dates nicely
            const startTimeFormatted = ann.startTime ? new Date(ann.startTime).toLocaleDateString() : 'N/A';
            const titleWithDate = `${ann.title} (${startTimeFormatted})`;

            return `
            <div class="announcement-item ${ann.isImportant ? 'announcement-important' : ''}">
                <div class="header">
                    <span class="tag">${categoryText}</span>
                    <strong class="title">${titleWithDate}</strong>
                </div>
                <div class="content-text">${ann.content}</div>
            </div>
        `}).join('');
        announcementsContainer.innerHTML = html;
    }

    // Define categories map for fallback rendering if categoryDescription is not in response
    const categories = {
        1: "人事公告", 2: "時事公告", 3: "公司規範", 4: "公司福利",
        5: "新品上市", 6: "缺貨通知", 7: "促銷通知", 8: "內部公告",
        null: "一般" // Fallback for null or unknown category code
    };
}); 