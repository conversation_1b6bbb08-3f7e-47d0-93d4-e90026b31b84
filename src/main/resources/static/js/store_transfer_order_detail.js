document.addEventListener('DOMContentLoaded', function () {
    const loadingMessage = document.getElementById('loading-message');
    const errorMessage = document.getElementById('error-message');
    const detailContent = document.getElementById('transfer-order-detail-content');
    const detailTitle = document.getElementById('detail-title');

    const detailTransferOrderNumber = document.getElementById('detailTransferOrderNumber');
    const detailTransferStatus = document.getElementById('detailTransferStatus');
    const detailRequestDate = document.getElementById('detailRequestDate');
    const detailRequestingStore = document.getElementById('detailRequestingStore');
    const detailRequestingUser = document.getElementById('detailRequestingUser');
    const detailSupplyingStore = document.getElementById('detailSupplyingStore');
    const detailDispatchDate = document.getElementById('detailDispatchDate');
    const detailDispatchingUser = document.getElementById('detailDispatchingUser');
    const detailReceivedDate = document.getElementById('detailReceivedDate');
    const detailReceivingUser = document.getElementById('detailReceivingUser');
    const detailNotes = document.getElementById('detailNotes');
    const detailRejectionReason = document.getElementById('detailRejectionReason');
    const itemsTableBody = document.getElementById('transfer-order-items-table-body');

    const backToListBtn = document.getElementById('backToListBtn');
    const confirmDispatchBtn = document.getElementById('confirmDispatchBtn');
    const confirmReceiveBtn = document.getElementById('confirmReceiveBtn');
    const approveTransferBtn = document.getElementById('approveTransferBtn');
    const rejectTransferBtn = document.getElementById('rejectTransferBtn');
    const cancelTransferBtn = document.getElementById('cancelTransferBtn');

    const actionModalEl = document.getElementById('transferActionModal');
    let actionModal = null;
    if (actionModalEl) {
        actionModal = new bootstrap.Modal(actionModalEl);
    }
    const modalActionOrderNumber = document.getElementById('modalActionOrderNumber');
    const modalActionTypeSpan = document.getElementById('modalActionTypeSpan');
    const actionForm = document.getElementById('transferActionForm');
    const actionItemsTableBody = document.getElementById('transferActionItemsTableBody');
    const actionFormErrorMessage = document.getElementById('actionFormErrorMessage');
    const confirmTransferActionBtn = document.getElementById('confirmTransferActionBtn');

    const rejectionReasonModalEl = document.getElementById('rejectionReasonModal');
    let rejectionReasonModal = null;
    if (rejectionReasonModalEl) {
        rejectionReasonModal = new bootstrap.Modal(rejectionReasonModalEl);
    }
    const rejectionReasonForm = document.getElementById('rejectionReasonForm');
    const rejectionReasonInput = document.getElementById('rejectionReasonInput');
    const confirmRejectBtn = document.getElementById('confirmRejectBtn');

    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('id');
    const API_BASE_URL = '/api/v1/store-transfer-orders'; 
    let currentOrderData = null;
    let currentActionType = ''; // 'dispatch' or 'receive'

    function showError(message) {
        if(loadingMessage) loadingMessage.classList.add('d-none');
        if(detailContent) detailContent.classList.add('d-none');
        if(errorMessage) {
            errorMessage.textContent = message;
            errorMessage.classList.remove('d-none');
        }
        console.error("Displaying error:", message);
    }

    function renderOrderDetail(order) {
        if (!order) return;
        currentOrderData = order; // Store for later actions

        if(detailTitle) detailTitle.textContent = `調撥單詳情: ${order.transferOrderNumber || 'N/A'}`;
        if(detailTransferOrderNumber) detailTransferOrderNumber.textContent = order.transferOrderNumber || '-';
        if(detailTransferStatus) detailTransferStatus.textContent = order.statusDescription || order.transferStatus?.toString() || '-';
        if(detailRequestDate) detailRequestDate.textContent = order.requestDate ? new Date(order.requestDate).toLocaleString() : '-';
        if(detailRequestingStore) detailRequestingStore.textContent = order.requestingStoreName || '-';
        if(detailRequestingUser) detailRequestingUser.textContent = order.requestingUserName || '-';
        if(detailSupplyingStore) detailSupplyingStore.textContent = order.supplyingStoreName || '-';
        if(detailDispatchDate) detailDispatchDate.textContent = order.dispatchDate ? new Date(order.dispatchDate).toLocaleString() : '-';
        if(detailDispatchingUser) detailDispatchingUser.textContent = order.dispatchingUserName || '-';
        if(detailReceivedDate) detailReceivedDate.textContent = order.receivedDate ? new Date(order.receivedDate).toLocaleString() : '-';
        if(detailReceivingUser) detailReceivingUser.textContent = order.receivingUserName || '-';
        if(detailNotes) detailNotes.textContent = order.notes || '-';
        if(detailRejectionReason) detailRejectionReason.textContent = order.rejectionReason || '-';

        if (itemsTableBody) {
            itemsTableBody.innerHTML = ''; 
            if (order.items && order.items.length > 0) {
                order.items.forEach(item => {
                    const row = itemsTableBody.insertRow();
                    row.insertCell().textContent = item.productBarcode || '-';
                    row.insertCell().textContent = item.productName || '-';
                    
                    const requestedQtyCell = row.insertCell();
                    requestedQtyCell.textContent = item.requestedQuantity || 0;
                    requestedQtyCell.classList.add('text-end');

                    const dispatchedQtyCell = row.insertCell();
                    dispatchedQtyCell.textContent = item.dispatchedQuantity != null ? item.dispatchedQuantity : '-';
                    dispatchedQtyCell.classList.add('text-end');

                    const receivedQtyCell = row.insertCell();
                    receivedQtyCell.textContent = item.receivedQuantity != null ? item.receivedQuantity : '-';
                    receivedQtyCell.classList.add('text-end');
                });
            } else {
                itemsTableBody.innerHTML = '<tr><td colspan="5" class="text-center">此調撥單無品項資料</td></tr>';
            }
        }
        updateActionButtonsVisibility(order.transferStatus); // Update buttons based on status code
    }

    function updateActionButtonsVisibility(status) {
        // Status codes are from StoreTransferOrderStatusEnum
        // 0:PENDING_APPROVAL, 1:APPROVED(待出庫), 2:DISPATCHED, 
        // 3:RECEIVED_COMPLETE, 4:RECEIVED_DISCREPANCY, 5:CANCELLED, 6:REJECTED
        console.log("[DEBUG] updateActionButtonsVisibility - Input status:", status); // DEBUG

        let userPermissions = [];
        try {
            const permissionsString = localStorage.getItem('userFunctionPermissions');
            if (permissionsString) {
                userPermissions = JSON.parse(permissionsString);
            }
        } catch (e) {
            console.error("Error parsing userFunctionPermissions from localStorage:", e);
        }
        console.log("[DEBUG] updateActionButtonsVisibility - Parsed userPermissions:", userPermissions); // DEBUG
        
        const hasApprovalPermission = userPermissions.includes('F0021_APPROVE');
        console.log("[DEBUG] updateActionButtonsVisibility - User has F0021_APPROVE permission:", hasApprovalPermission); // DEBUG

        const canApproveReject = status === 0 && hasApprovalPermission; // PENDING_APPROVAL (0)
        const canDispatch = status === 1; // APPROVED (1) (means PENDING_DISPATCH)
        const canReceive = status === 2;  // DISPATCHED (2)
        const canCancel = (status === 0 || status === 1); // PENDING_APPROVAL (0) or APPROVED (1)

        console.log("[DEBUG] updateActionButtonsVisibility - Calculated visibility: canApproveReject:", canApproveReject, ", canDispatch:", canDispatch, ", canReceive:", canReceive, ", canCancel:", canCancel); // DEBUG

        // For Approve Button
        if (approveTransferBtn) {
            if (canApproveReject) {
                approveTransferBtn.classList.remove('d-none');
                approveTransferBtn.style.display = 'inline-block'; 
            } else {
                approveTransferBtn.classList.add('d-none');
                approveTransferBtn.style.display = 'none'; 
            }
        }

        // For Reject Button
        if (rejectTransferBtn) {
            if (canApproveReject) {
                rejectTransferBtn.classList.remove('d-none');
                rejectTransferBtn.style.display = 'inline-block';
            } else {
                rejectTransferBtn.classList.add('d-none');
                rejectTransferBtn.style.display = 'none';
            }
        }

        // For Confirm Dispatch Button
        if (confirmDispatchBtn) {
            if (canDispatch) {
                confirmDispatchBtn.classList.remove('d-none');
                confirmDispatchBtn.style.display = 'inline-block';
            } else {
                confirmDispatchBtn.classList.add('d-none');
                confirmDispatchBtn.style.display = 'none';
            }
        }
        
        // For Confirm Receive Button
        if (confirmReceiveBtn) {
            if (canReceive) {
                confirmReceiveBtn.classList.remove('d-none');
                confirmReceiveBtn.style.display = 'inline-block';
            } else {
                confirmReceiveBtn.classList.add('d-none');
                confirmReceiveBtn.style.display = 'none';
            }
        }

        // For Cancel Transfer Button
        if (cancelTransferBtn) {
            if (canCancel) {
                cancelTransferBtn.classList.remove('d-none');
                cancelTransferBtn.style.display = 'inline-block';
            } else {
                cancelTransferBtn.classList.add('d-none');
                cancelTransferBtn.style.display = 'none';
            }
        }
    }

    async function fetchTransferOrderDetail() {
        if (!orderId) {
            showError('未提供調撥單ID。');
            return;
        }
        if(loadingMessage) loadingMessage.classList.remove('d-none');
        if(errorMessage) errorMessage.classList.add('d-none');
        if(detailContent) detailContent.classList.add('d-none');

        try {
            const response = await window.fetchAuthenticated(`${API_BASE_URL}/${orderId}`);
            if (!response.ok) {
                const errData = await response.json().catch(() => ({ message: `HTTP error! Status: ${response.status}` }));
                throw new Error(errData.message);
            }
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                renderOrderDetail(apiResponse.data);
                if(loadingMessage) loadingMessage.classList.add('d-none');
                if(detailContent) detailContent.classList.remove('d-none');
            } else {
                showError(apiResponse.message || '無法載入調撥單詳情。');
            }
        } catch (error) {
            console.error('Error fetching transfer order detail:', error);
            showError(`載入調撥單失敗: ${error.message}`);
        }
    }
    
    // Placeholder for action modal preparation
    function prepareActionModal(actionType) {
        currentActionType = actionType;
        if (!currentOrderData || !actionModalEl || !modalActionOrderNumber || !modalActionTypeSpan || !actionItemsTableBody) {
            showToast('無法準備操作視窗: 頁面元件遺失', 'error');
            return;
        }
        modalActionOrderNumber.textContent = currentOrderData.transferOrderNumber;
        modalActionTypeSpan.textContent = actionType === 'dispatch' ? '出庫' : '入庫';
        actionItemsTableBody.innerHTML = '';
        currentOrderData.items.forEach(item => {
            const row = actionItemsTableBody.insertRow();
            row.insertCell().textContent = item.productBarcode;
            row.insertCell().textContent = item.productName;
            row.insertCell().textContent = item.requestedQuantity;
            const quantityCell = row.insertCell();
            const input = document.createElement('input');
            input.type = 'number';
            input.className = 'form-control form-control-sm action-quantity-input';
            input.name = `items[${item.storeTransferOrderItemId}].quantity`;
            input.value = actionType === 'dispatch' ? (item.dispatchedQuantity || item.requestedQuantity) : (item.receivedQuantity || item.dispatchedQuantity || item.requestedQuantity) ;
            input.min = 0;
            input.max = item.requestedQuantity; // Basic validation
             if(actionType === 'receive' && item.dispatchedQuantity !== null) {
                 input.max = item.dispatchedQuantity; // Cannot receive more than dispatched
             }
            input.required = true;
            input.dataset.itemId = item.storeTransferOrderItemId;
            quantityCell.appendChild(input);
        });
        if(actionFormErrorMessage) actionFormErrorMessage.classList.add('d-none');
        actionModal.show();
    }

    // Placeholder for submitting the action form
    async function handleActionFormSubmit(event) {
        event.preventDefault();
        if (!currentOrderData || !currentActionType) return;

        const itemsPayload = [];
        const inputs = actionItemsTableBody.querySelectorAll('.action-quantity-input');
        let formValid = true;
        inputs.forEach(input => {
            const quantity = parseInt(input.value, 10);
            if (isNaN(quantity) || quantity < 0) {
                formValid = false;
            }
            itemsPayload.push({
                storeTransferOrderItemId: input.dataset.itemId,
                actualQuantity: quantity
            });
        });

        if (!formValid) {
            if(actionFormErrorMessage) {
                actionFormErrorMessage.textContent = '請輸入有效的數量。';
                actionFormErrorMessage.classList.remove('d-none');
            }
            return;
        }

        const requestBody = {
            items: itemsPayload,
            notes: ""
        };

        const actionUrl = `${API_BASE_URL}/${currentOrderData.storeTransferOrderId}/${currentActionType}`;
        try {
            const response = await fetchAuthenticated(actionUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });
            const apiResponse = await response.json();
            if (response.ok && apiResponse.code === 200) {
                showToast(`調撥單已成功${modalActionTypeSpan.textContent}。`, 'success');
                actionModal.hide();
                fetchTransferOrderDetail(); // Refresh detail view
            } else {
                if(actionFormErrorMessage) {
                    actionFormErrorMessage.textContent = apiResponse.message || `操作失敗 (${response.status})`;
                    actionFormErrorMessage.classList.remove('d-none');
                }
                showToast(apiResponse.message || `操作失敗`, 'error');
            }
        } catch (error) {
            console.error(`Error performing ${currentActionType}:`, error);
            if(actionFormErrorMessage) {
                actionFormErrorMessage.textContent = `操作時發生錯誤: ${error.message}`;
                actionFormErrorMessage.classList.remove('d-none');
            }
            showToast(`操作時發生錯誤`, 'error');
        }
    }

    // Event Listeners
    if(confirmDispatchBtn) confirmDispatchBtn.addEventListener('click', () => prepareActionModal('dispatch'));
    if(confirmReceiveBtn) confirmReceiveBtn.addEventListener('click', () => prepareActionModal('receive'));
    
    if(approveTransferBtn) {
        approveTransferBtn.addEventListener('click', async () => {
            if (!currentOrderData) return;
            if (confirm(`您確定要核准調撥單 ${currentOrderData.transferOrderNumber} 嗎?`)) {
                try {
                    const response = await fetchAuthenticated(`${API_BASE_URL}/${currentOrderData.storeTransferOrderId}/approve`, { method: 'POST' });
                    const apiResponse = await response.json();
                    if (response.ok && apiResponse.code === 200) {
                        showToast('調撥單已成功核准。', 'success');
                        fetchTransferOrderDetail(); // Refresh
                    } else {
                        showToast(apiResponse.message || '核准失敗', 'error');
                    }
                } catch (err) {
                    showToast(`核准操作失敗: ${err.message}`, 'error');
                }
            }
        });
    }

    if(rejectTransferBtn && rejectionReasonModal) {
        rejectTransferBtn.addEventListener('click', () => {
            if (!currentOrderData) return;
            if(rejectionReasonInput) rejectionReasonInput.value = ''; // Clear previous reason
            if(rejectionReasonForm) rejectionReasonForm.classList.remove('was-validated');
            rejectionReasonModal.show();
        });
    }

    if(rejectionReasonForm && confirmRejectBtn) {
        rejectionReasonForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            if (!rejectionReasonForm.checkValidity()) {
                rejectionReasonForm.classList.add('was-validated');
                return;
            }
            const reason = rejectionReasonInput.value;
            if (!currentOrderData) return;

            try {
                const response = await fetchAuthenticated(`${API_BASE_URL}/${currentOrderData.storeTransferOrderId}/reject`, {
                    method: 'POST',
                    body: JSON.stringify({ reason: reason })
                });
                const apiResponse = await response.json();
                if (response.ok && apiResponse.code === 200) {
                    showToast('調撥單已成功駁回。', 'success');
                    rejectionReasonModal.hide();
                    fetchTransferOrderDetail(); // Refresh
                } else {
                    showToast(apiResponse.message || '駁回失敗', 'error');
                }
            } catch (err) {
                showToast(`駁回操作失敗: ${err.message}`, 'error');
            }
        });
    }

    if(cancelTransferBtn) {
        cancelTransferBtn.addEventListener('click', async () => {
            if (!currentOrderData) return;
            if (confirm(`您確定要取消調撥單 ${currentOrderData.transferOrderNumber} 嗎?`)) {
                try {
                    const response = await fetchAuthenticated(`${API_BASE_URL}/${currentOrderData.storeTransferOrderId}/cancel`, { method: 'POST' });
                    const apiResponse = await response.json();
                    if (response.ok && apiResponse.code === 200) {
                        showToast('調撥單已成功取消。', 'success');
                        fetchTransferOrderDetail(); // Refresh
                    } else {
                        showToast(apiResponse.message || '取消失敗', 'error');
                    }
                } catch (err) {
                    showToast(`取消操作失敗: ${err.message}`, 'error');
                }
            }
        });
    }
    if(actionForm) actionForm.addEventListener('submit', handleActionFormSubmit);

    // Initial fetch
    fetchTransferOrderDetail();
}); 