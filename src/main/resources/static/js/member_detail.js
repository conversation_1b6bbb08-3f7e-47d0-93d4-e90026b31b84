document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const customerId = urlParams.get('customerId');

    if (!customerId) {
        // Handle error: no customer ID provided
        return;
    }

    const API_BASE_URL = `/api/v1/customers/${customerId}`;

    async function fetchData() {
        try {
            const response = await fetchAuthenticated(API_BASE_URL);
            if (!response.ok) throw new Error('無法獲取會員資料');
            
            const apiResponse = await response.json();
            populateData(apiResponse.data);
        } catch (error) {
            console.error("獲取會員詳情失敗:", error);
        }
    }

    function populateData(customer) {
        // Populate Customer Info
        const customerInfoBody = document.getElementById('customer-info-body');
        customerInfoBody.innerHTML = `
            <p><strong>會員等級:</strong> ${customer.memberLevelName || ''}</p>
            <p><strong>會員姓名:</strong> ${customer.customerName || ''}</p>
            <!-- Add all other customer fields here -->
        `;
        document.getElementById('edit-member-btn').href = `member_form.html?customerId=${customerId}`;
        
        // Populate Store Orders, Dispatch Orders, and Devices tables
        // This requires dedicated functions to build the table rows
    }

    fetchData();
}); 