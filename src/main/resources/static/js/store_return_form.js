document.addEventListener('DOMContentLoaded', function () {
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('orderId');
    const actionType = urlParams.get('actionType'); // 'RETURN' or 'CHANGE'

    if (!orderId || !actionType) {
        alert('無效的請求，缺少訂單ID或操作類型。');
        window.location.href = 'store_product_orders_list.html';
        return;
    }

    // 更新頁面標題和按鈕文字
    const isReturn = actionType === 'RETURN';
    document.getElementById('page-title').textContent = isReturn ? '訂單退貨' : '訂單換貨';
    document.getElementById('form-title').textContent = isReturn ? '訂單退貨' : '訂單換貨';
    document.getElementById('page-main-title').textContent = isReturn ? '訂單退貨' : '訂單換貨';
    document.getElementById('submit-return-btn').textContent = isReturn ? '確認退貨' : '確認換貨';
    document.getElementById('source-order-detail-link').href = `order_detail.html?orderId=${orderId}`;
    document.getElementById('cancel-btn').href = `order_detail.html?orderId=${orderId}`;

    // 載入原訂單資訊
    async function loadOriginalOrder() {
        try {
            const response = await window.fetchAuthenticated(`/api/v1/orders/${orderId}`);
            if (!response.ok) throw new Error('無法載入原訂單資訊');
            const result = await response.json();
            if (result.code === 200 && result.data) {
                displayOriginalOrder(result.data);
            } else {
                throw new Error(result.message || '獲取訂單資料格式錯誤');
            }
        } catch (error) {
            console.error(error);
            document.getElementById('original-order-info').innerHTML = `<p class="text-danger">${error.message}</p>`;
        }
    }

    function displayOriginalOrder(order) {
        document.getElementById('original-net_amount').textContent = `NT$ ${order.netAmount.toLocaleString()}`;
        document.getElementById('original-grand-total').textContent = `NT$ ${order.grandTotalAmount.toLocaleString()}`;
        const refundAmountInput = document.getElementById('refundAmount');
        
        if (isReturn) {
            refundAmountInput.value = order.grandTotalAmount; // 退貨時預設全額
            refundAmountInput.readOnly = false;
        } else { // 換貨時
            refundAmountInput.value = 0; // 換貨時退款金額為 0
            refundAmountInput.readOnly = true; // 不可編輯
        }

        const infoDiv = document.getElementById('original-order-info');
        infoDiv.innerHTML = `
            <p><strong>訂單號碼:</strong> ${order.orderNumber}</p>
            <p><strong>訂單日期:</strong> ${new Date(order.orderDate).toLocaleDateString()}</p>
            <p><strong>客戶姓名:</strong> ${order.customerName} (${order.customerPhone})</p>
            <p><strong>商品列表:</strong></p>
            <ul>
                ${order.items.map(item => `<li>${item.productName} x ${item.quantity}</li>`).join('')}
            </ul>
        `;
    }

    // 表單提交
    document.getElementById('return-form').addEventListener('submit', async function(event) {
        event.preventDefault();
        
        const refundAmount = document.getElementById('refundAmount').value;
        const reason = document.getElementById('refundReason').value;

        if (!reason) { // 金額在換貨時可以是0，但原因必填
            alert('請填寫退貨/換貨原因。');
            return;
        }

        const confirmMessage = isReturn ? 
            `確定要發起退貨申請嗎？` : 
            `確定要發起換貨申請嗎？`;

        if (!confirm(confirmMessage)) {
            return;
        }

        try {
            // 這個表單現在只負責"發起"流程
            const response = await window.fetchAuthenticated(`/api/v1/order-refunds/initiate?orderId=${orderId}&actionType=${actionType}`, {
                method: 'POST'
            });
            const result = await response.json();
            if (!response.ok) throw new Error(result.message || '發起退/換貨失敗');
            
            const refundId = result.data.refundId;

            // 步驟 2: 呼叫後端 API，完成退款/換貨結案流程
            const completePayload = {
                refundAmount: refundAmount,
                reason: reason
            };
            const completeResponse = await window.fetchAuthenticated(`/api/v1/order-refunds/${refundId}/complete-store-return`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(completePayload)
            });
            const completeResult = await completeResponse.json();
            if (!completeResponse.ok) throw new Error(completeResult.message || '完成退/換貨失敗');

            // 步驟 3: 根據結果進行跳轉
            if (isReturn) {
                alert('訂單退貨成功！');
                window.location.href = `order_detail.html?orderId=${orderId}`;
            } else { // isChange
                // 假設後端返回的 data 中有 newOrderId
                const newOrderId = completeResult.data.newOrderId; 
                if (newOrderId) {
                    alert('訂單換貨成功！已為您產生新訂單，正在跳轉至編輯畫面...');
                    window.location.href = `store_product_order_form.html?orderId=${newOrderId}`;
                } else {
                    // 如果後端沒有返回 newOrderId，則只提示成功並跳回原訂單詳情
                    alert('訂單換貨流程已完成！');
                    window.location.href = `order_detail.html?orderId=${orderId}`;
                }
            }

        } catch (error) {
            console.error('退/換貨流程失敗:', error);
            alert(`操作失敗: ${error.message}`);
        }
    });

    loadOriginalOrder();
}); 