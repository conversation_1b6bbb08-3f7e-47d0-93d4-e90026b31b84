document.addEventListener('DOMContentLoaded', function () {
    const storesTableBody = document.getElementById('stores-table-body');
    const paginationControls = document.getElementById('stores-pagination-controls');
    const filterForm = document.getElementById('store-filter-form');
    const filterRegion = document.getElementById('filterRegion');
    const filterKeyword = document.getElementById('filterKeyword');
    const noStoresMessage = document.getElementById('no-stores-message');
    const clearFiltersBtn = document.getElementById('clearStoreFiltersBtn');

    let currentPage = 1; // For UI state, 1-indexed
    const pageSize = 10;

    async function fetchRegions() {
        try {
            // Assuming /api/v1/regions/list returns ApiResponse<List<RegionDto>>
            const response = await window.fetchAuthenticated('/api/v1/regions/list'); 
            if (!response.ok) throw new Error('Failed to fetch regions');
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data && Array.isArray(apiResult.data)) {
                filterRegion.innerHTML = '<option value="">全部地區</option>';
                apiResult.data.forEach(region => {
                    const option = document.createElement('option');
                    option.value = region.regionId;
                    option.textContent = region.regionName;
                    filterRegion.appendChild(option);
                });
            } else {
                console.error('Error fetching regions or unexpected format:', apiResult.message || apiResult);
                filterRegion.innerHTML = '<option value="">無法載入地區</option>';
            }
        } catch (error) {
            console.error('Error fetching regions:', error);
            filterRegion.innerHTML = '<option value="">載入地區失敗</option>';
        }
    }

    async function fetchStores(page = 1) { // Expects 1-indexed page for consistency with UI
        currentPage = page; 
        const regionId = filterRegion.value;
        const keyword = filterKeyword.value.trim();

        let queryParams = `page=${page - 1}&size=${pageSize}&sort=storeName,asc`; // API is 0-indexed
        if (regionId) queryParams += `&regionId=${regionId}`;
        if (keyword) queryParams += `&storeName=${encodeURIComponent(keyword)}`;

        try {
            const response = await window.fetchAuthenticated(`/api/v1/stores?${queryParams}`);
            if (!response.ok) {
                if (response.status === 401 || response.status === 403) return; 
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const apiResult = await response.json();

            storesTableBody.innerHTML = ''; 
            if (apiResult.code === 200 && apiResult.data && apiResult.data.list && Array.isArray(apiResult.data.list)) {
                if (apiResult.data.list.length > 0) {
                    noStoresMessage.classList.add('d-none');
                    apiResult.data.list.forEach((store, index) => {
                        const row = storesTableBody.insertRow();
                        row.insertCell().textContent = (currentPage - 1) * pageSize + index + 1;
                        row.insertCell().textContent = store.regionName || 'N/A'; 
                        row.insertCell().textContent = store.storeName;
                        row.insertCell().textContent = store.erpCompanyDivisionDescription || 'N/A';
                        
                        const statusCell = row.insertCell();
                        const statusBadge = document.createElement('span');
                        statusBadge.classList.add('badge');
                        if (store.isActive) {
                            statusBadge.classList.add('bg-success');
                            statusBadge.textContent = '啟用';
                        } else {
                            statusBadge.classList.add('bg-danger');
                            statusBadge.textContent = '停用';
                        }
                        statusCell.appendChild(statusBadge);

                        const actionsCell = row.insertCell();
                        actionsCell.innerHTML = `
                            <a href="store_form.html?mode=edit&id=${store.storeId}" class="btn btn-sm btn-outline-primary me-1" title="編輯">
                                <i class="bi bi-pencil-fill"></i> 編輯
                            </a>
                            <button class="btn btn-sm btn-outline-danger delete-store-btn" data-store-id="${store.storeId}" data-store-name="${store.storeName}" title="刪除">
                                <i class="bi bi-trash-fill"></i> 刪除
                            </button>
                        `;
                    });
                    renderPagination(apiResult.data.page.total, apiResult.data.page.page, apiResult.data.page.pageSize);
                    addDeleteEventListeners();
                } else {
                     storesTableBody.innerHTML = '<tr><td colspan="6" class="text-center">查無門市資料</td></tr>';
                     noStoresMessage.classList.remove('d-none'); 
                     paginationControls.innerHTML = '';
                }
            } else {
                storesTableBody.innerHTML = '<tr><td colspan="6" class="text-center">查無門市資料或回應錯誤</td></tr>';
                noStoresMessage.classList.remove('d-none');
                paginationControls.innerHTML = '';
                console.error("Error or unexpected format in fetchStores response:", apiResult.message || apiResult);
            }
        } catch (error) {
            console.error('Error fetching stores:', error);
            storesTableBody.innerHTML = `<tr><td colspan="6" class="text-center">無法載入門市資料: ${error.message}</td></tr>`;
            noStoresMessage.classList.remove('d-none');
        }
    }

    function renderPagination(totalItems, currentPageBackend, itemsPerPage) { 
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        const uiCurrentPage = currentPageBackend + 1; 
        paginationControls.innerHTML = '';

        if (totalPages <= 1) return;

        const createPageItem = (pageToFetch, text, isDisabled, isActive) => { 
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = text;
            if (!isDisabled) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    fetchStores(pageToFetch); 
                });
            }
            li.appendChild(link);
            return li;
        };

        paginationControls.appendChild(createPageItem(uiCurrentPage - 1, '上一頁', uiCurrentPage <= 1, false));

        let startPage = Math.max(1, uiCurrentPage - 2);
        let endPage = Math.min(totalPages, uiCurrentPage + 2);
        if (uiCurrentPage <= 3) endPage = Math.min(totalPages, 5);
        if (uiCurrentPage > totalPages - 2) startPage = Math.max(1, totalPages - 4);
        
        if (startPage > 1) {
            paginationControls.appendChild(createPageItem(1, '1', false, 1 === uiCurrentPage));
            if (startPage > 2) {
                 const ellipsis = document.createElement('li');
                 ellipsis.className = 'page-item disabled';
                 ellipsis.innerHTML = '<a class="page-link" href="#">...</a>';
                 paginationControls.appendChild(ellipsis);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i, i, false, i === uiCurrentPage));
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('li');
                ellipsis.className = 'page-item disabled';
                ellipsis.innerHTML = '<a class="page-link" href="#">...</a>';
                paginationControls.appendChild(ellipsis);
            }
            paginationControls.appendChild(createPageItem(totalPages, totalPages, false, totalPages === uiCurrentPage));
        }
        
        paginationControls.appendChild(createPageItem(uiCurrentPage + 1, '下一頁', uiCurrentPage >= totalPages, false));
    }

    function addDeleteEventListeners() {
        document.querySelectorAll('.delete-store-btn').forEach(button => {
            button.addEventListener('click', async function () {
                const storeId = this.dataset.storeId;
                const storeName = this.dataset.storeName;
                if (confirm(`確定要刪除門市「${storeName}」嗎？此操作無法復原。`)) {
                    try {
                        const response = await window.fetchAuthenticated(`/api/v1/stores/${storeId}`, { method: 'DELETE' });
                        const result = await response.json(); 
                        if (response.ok && result.code === 200) {
                            alert(result.message || '門市已刪除');
                            fetchStores(currentPage); 
                        } else {
                            alert(`刪除失敗: ${result.message || response.statusText}`);
                        }
                    } catch (error) {
                        console.error('Error deleting store:', error);
                        alert('刪除門市時發生錯誤。');
                    }
                }
            });
        });
    }

    filterForm.addEventListener('submit', function (event) {
        event.preventDefault();
        fetchStores(1); 
    });

    clearFiltersBtn.addEventListener('click', function() {
        filterRegion.value = '';
        filterKeyword.value = '';
        fetchStores(1);
    });

    fetchRegions(); 
    fetchStores(); 
});
