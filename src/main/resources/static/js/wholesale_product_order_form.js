/**
 * EastKing - Wholesale Product Order Form Logic
 * This script handles the functionality for the wholesale product order form.
 */
document.addEventListener('DOMContentLoaded', function () {

    // --- DOM Element Variables ---
    const orderForm = document.getElementById('wholesale-order-form');
    const companyDivisionSelect = document.getElementById('companyDivision');
    const storeSelect = document.getElementById('store');
    const distributorSelect = document.getElementById('distributorSelect');
    const salespersonSelect = document.getElementById('salespersonSelect');
    const promotionSelect = document.getElementById('promotionSelect');
    const dispatchPromotionSelect = document.getElementById('dispatchPromotionSelect');
    const invoiceDetailsContainer = document.getElementById('invoice-details-container');
    const addPaymentButtons = document.getElementById('add-payment-buttons');
    const paymentContainer = document.getElementById('payment-methods-container');
    const formTitle = document.getElementById('form-title');
    const pageMainTitle = document.getElementById('page-main-title');
    
    // 商品類型切換相關元素
    const productTypeStoreRadio = document.getElementById('productTypeStore');
    const productTypeDispatchRadio = document.getElementById('productTypeDispatch');
    const storeProductSection = document.getElementById('store-product-section');
    const dispatchProductSection = document.getElementById('dispatch-product-section');
    
    // 門市商品相關元素
    const productKeywordInput = document.getElementById('productKeyword');
    const productSearchBtn = document.getElementById('product-search-btn');
    
    // 派工商品相關元素
    const dispatchProductKeywordInput = document.getElementById('dispatchProductKeyword');
    const dispatchProductSearchBtn = document.getElementById('dispatch-product-search-btn');
    const dispatchGroupTabs = document.getElementById('dispatch-group-tabs');
    const dispatchGroupTabContent = document.getElementById('dispatch-group-tab-content');

    // --- Global State ---
    let isEditMode = false;
    let currentOrderId = null;
    let currentOrderData = null;
    let currentPromotionsData = []; // To store full promotion data
    let eventListenersAttached = false; // Flag to prevent multiple bindings
    let tabCounter = 0; // 用於生成唯一的 TAB ID
    let currentProductType = 'store'; // 'store' or 'dispatch'

    // =================================================================================
    //  FUNCTION DEFINITIONS
    // =================================================================================

    /**
     * Populates a dropdown with options.
     */
    function populateDropdown(selectElement, items, defaultOptionText, valueField, textField) {
        if (!selectElement) return;
        selectElement.innerHTML = `<option value="">${defaultOptionText}</option>`;
        if (items && items.length > 0) {
            items.forEach(item => {
                const option = document.createElement('option');
                option.value = item[valueField];
                option.textContent = item[textField];
                selectElement.appendChild(option);
            });
        }
    }

    /**
     * Attaches all event listeners for the form.
     */
    function attachEventListeners() {
        if (eventListenersAttached) return; // Prevent re-binding

        // 商品類型切換監聽器
        if (productTypeStoreRadio) {
            productTypeStoreRadio.addEventListener('change', handleProductTypeChange);
        }
        if (productTypeDispatchRadio) {
            productTypeDispatchRadio.addEventListener('change', handleProductTypeChange);
        }

        if (storeSelect) {
            storeSelect.addEventListener('change', handleStoreOrDistributorChange);
        }
        if (distributorSelect) {
            distributorSelect.addEventListener('change', handleStoreOrDistributorChange);
        }

        document.querySelectorAll('input[name="invoiceTypeBtn"]').forEach(radio => {
            radio.addEventListener('change', () => {
                handleInvoiceTypeChange();
                updateTotalAmountsUI(); // Also update totals when invoice type changes
            });
        });

        if (addPaymentButtons) {
            addPaymentButtons.addEventListener('click', handleAddPaymentClick);
        }

        if (paymentContainer) {
            paymentContainer.addEventListener('click', handlePaymentItemActions);
            paymentContainer.addEventListener('input', handlePaymentAmountChange);
            paymentContainer.addEventListener('change', function(event) {
                if (event.target.classList.contains('card-payment-type')) {
                    const row = event.target.closest('.payment-item');
                    const installmentsInput = row.querySelector('.card-installments');
                    if (event.target.value === '2') { // '2' for installments
                        installmentsInput.style.display = 'block';
                        installmentsInput.value = ''; // Clear to force user input
                        installmentsInput.placeholder = "請輸入期數";
                    } else { // '0' for single payment
                        installmentsInput.style.display = 'none';
                        installmentsInput.value = '0'; 
                    }
                }
            });
            paymentContainer.addEventListener('input', async function(event) {
                if (event.target.classList.contains('card-number')) {
                    const cardNumberInput = event.target;
                    const infoDisplay = cardNumberInput.closest('.position-relative').querySelector('.card-info-display');
                    const cardNumber = cardNumberInput.value.replace(/\s+/g, '');

                    infoDisplay.textContent = ''; // 清空舊的查詢結果

                    if (cardNumber.length === 16) {
                        const prefix = cardNumber.substring(0, 8); // 仍然使用前8位查詢
                        infoDisplay.textContent = '查詢中...';
                        try {
                            const response = await window.fetchAuthenticated(`/api/v1/utils/bin-lookup/${prefix}`);
                            if (response.ok) {
                                const result = await response.json();
                                if(result.data) {
                                    infoDisplay.textContent = `${result.data.brand || ''} - ${result.data.issuer || ''}`;
                                } else {
                                    infoDisplay.textContent = '查無卡片資訊';
                                }
                            } else {
                                infoDisplay.textContent = '查詢失敗';
                            }
                        } catch (error) {
                            console.error('BIN lookup error:', error);
                            infoDisplay.textContent = '查詢錯誤';
                        }
                    }
                }
            });
        }

        if (orderForm) {
            orderForm.addEventListener('submit', handleFormSubmit);
        }

        document.getElementById('delete-btn').addEventListener('click', handleDeleteOrder);
        document.getElementById('save-draft-btn').addEventListener('click', () => handleFormSubmit(true));
        document.getElementById('submit-order-btn').addEventListener('click', handleSubmitForPreview);

        // 門市商品搜尋
        if (productSearchBtn) {
            productSearchBtn.addEventListener('click', () => handleProductSearch(false));
        }

        // 派工商品搜尋
        if (dispatchProductSearchBtn) {
            dispatchProductSearchBtn.addEventListener('click', () => handleDispatchProductSearch(false));
        }

        document.getElementById('execProductSearchModalBtn').addEventListener('click', () => {
            if (currentProductType === 'store') {
                handleProductSearch(true);
            } else {
                handleDispatchProductSearch(true);
            }
        });

        document.getElementById('productSearchResultsModal').addEventListener('click', handleProductSelection);
        document.getElementById('searchCustomerBtn').addEventListener('click', handleCustomerSearch);
        
        const customerSelectModalBody = document.getElementById('customerSelectModalBody');
        if (customerSelectModalBody) {
             customerSelectModalBody.addEventListener('click', handleCustomerSelectionFromModal);
        }

        document.querySelectorAll('input[name="taxTypeBtn"]').forEach(radio => {
            radio.addEventListener('change', updateTotalAmountsUI);
        });

        if (promotionSelect) {
            promotionSelect.addEventListener('change', applyPromotionsToItems);
        }

        if (dispatchPromotionSelect) {
            dispatchPromotionSelect.addEventListener('change', applyPromotionsToItems);
        }

        // 使用事件代理處理 TAB 頁籤的關閉按鈕
        const tabsContainer = document.getElementById('dispatch-group-tabs');
        if (tabsContainer) {
            tabsContainer.addEventListener('click', (event) => {
                // 確保點擊的是關閉按鈕
                if (event.target.classList.contains('btn-close')) {
                    event.preventDefault();
                    event.stopPropagation();

                    // 從關閉按鈕向上找到 nav-link 按鈕，並獲取其 ID
                    const tabLink = event.target.closest('.nav-link');
                    if (tabLink && tabLink.id) {
                        removeTab(tabLink.id);
                    }
                }
            });
        }

        eventListenersAttached = true; // Set the flag
    }

    /**
     * 處理商品類型切換
     */
    function handleProductTypeChange() {
        const selectedType = document.querySelector('input[name="productType"]:checked').value;
        currentProductType = selectedType;

        if (selectedType === 'store') {
            storeProductSection.style.display = 'block';
            dispatchProductSection.style.display = 'none';
        } else {
            storeProductSection.style.display = 'none';
            dispatchProductSection.style.display = 'block';
            
            // 切換到派工商品模式時，如果還沒有任何 tab，建立預設的主商品 tab
            const existingTabs = document.querySelectorAll('#dispatch-group-tabs .nav-item');
            if (existingTabs.length === 0) {
                createMainProductTab();
            }
        }

        // 只清空門市商品列表，不清空派工商品的 tabs
        if (selectedType === 'store') {
            clearStoreProductItems();
        } else {
            clearStoreProductItems(); // 切換到派工商品時清空門市商品
        }
        updateTotalAmountsUI();
    }

    /**
     * 清空門市商品項目
     */
    function clearStoreProductItems() {
        const orderItemsTable = document.getElementById('order-items-table');
        if (orderItemsTable) {
            const tbody = orderItemsTable.querySelector('tbody');
            if (tbody) {
                tbody.innerHTML = '';
            }
        }
    }

    /**
     * 清空所有商品項目 (包含派工商品的 tabs)
     */
    function clearProductItems() {
        clearStoreProductItems();

        // 清空派工商品的 tabs
        if (dispatchGroupTabs) {
            dispatchGroupTabs.innerHTML = '';
        }
        if (dispatchGroupTabContent) {
            dispatchGroupTabContent.innerHTML = '';
        }
        tabCounter = 0;
    }

    /**
     * Populates the 'Company' dropdown based on user's login context and disables it.
     */
    async function populateCompanyDivision() {
        const companyContext = localStorage.getItem('selectedCompanyContext');
        if (companyContext) {
            const companyCode = window.getCompanyDivisionCode();
            const companyName = companyContext === 'QUEYOU' ? '雀友' : '東方不敗';
            companyDivisionSelect.innerHTML = `<option value="${companyCode}">${companyName}</option>`;
        } else {
            showToast('無法獲取公司資訊，請重新登入', 'danger');
            companyDivisionSelect.innerHTML = `<option value="">錯誤</option>`;
        }
    }

    /**
     * Fetches the user's operable stores and populates the 'Store' dropdown.
     */
    async function loadStores() {
        try {
            const response = await fetchAuthenticated('/api/v1/auth/operable-stores');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

        const apiResponse = await response.json();
            if (!apiResponse.data) throw new Error("API response did not contain store data.");

            const stores = apiResponse.data;
            storeSelect.innerHTML = '<option value="">請選擇門市</option>';
            if (stores && stores.length > 0) {
                stores.forEach(store => {
                const option = document.createElement('option');
                option.value = store.storeId;
                option.textContent = store.storeName;
                    storeSelect.appendChild(option);
            });
        } else {
                storeSelect.innerHTML = '<option value="">無可用門市</option>';
        }
    } catch (error) {
            console.error('無法載入門市列表:', error);
            showToast('無法載入門市列表', 'danger');
            storeSelect.innerHTML = '<option value="">載入失敗</option>';
        }
    }

    /**
     * Fetches and populates the distributors dropdown.
     */
    async function loadDistributors() {
        if (!distributorSelect) return;
        const companyCode = window.getCompanyDivisionCode();
        try {
            const response = await fetchAuthenticated(`/api/v1/distributors?companyDivisionCode=${companyCode}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResponse = await response.json();
            // The distributor API returns data directly in an array within the 'data' property.
            // It is not a paginated response like others.
            if (!apiResponse.data) throw new Error("API response did not contain distributor data.");
            populateDropdown(distributorSelect, apiResponse.data, '請選擇銷售據點', 'distributorId', 'distributorName');
        } catch (error) {
            console.error('無法載入經銷商列表:', error);
            window.showToast?.('無法載入經銷商列表', 'danger');
            distributorSelect.innerHTML = '<option value="">載入失敗</option>';
        }
    }

    /**
     * Fetches and populates salespersons based on the selected store or distributor.
     */
    async function loadSalespersons(storeId = null, distributorId = null) {
        let url = '/api/v1/users/salespersons?';
        if (storeId) {
            url += `storeId=${storeId}`;
        } else if (distributorId) {
            url += `distributorId=${distributorId}`;
        } else {
             populateDropdown(salespersonSelect, [], '請先選擇門市或據點', 'userAccountId', 'userName');
            return;
        }

        try {
            const response = await fetchAuthenticated(url);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResponse = await response.json();
            if (!apiResponse.data) throw new Error("API did not return salesperson data.");
            populateDropdown(salespersonSelect, apiResponse.data, '請選擇銷售人員', 'userAccountId', 'userName');
        } catch (error) {
            console.error('無法載入銷售人員列表:', error);
            if(window.showToast) showToast('無法載入銷售人員列表', 'danger');
            salespersonSelect.innerHTML = '<option value="">載入失敗</option>';
        }
    }

    /**
     * Handles store or distributor selection change.
     */
    async function handleStoreOrDistributorChange() {
        const storeId = storeSelect.value;
        const distributorId = distributorSelect.value;
        // When store changes, update salespersons.
        if(storeId) {
            await loadSalespersons(storeId, null);
        } else {
             await loadSalespersons(null, distributorId);
        }
        await loadPromotionsForStore(storeId);
        applyPromotionsToItems();
    }

    /**
     * Fetches and populates the 'Promotions' dropdown based on the selected store.
     */
    async function loadPromotionsForStore(storeId) {
        if (!storeId) {
            promotionSelect.innerHTML = '<option value="">請先選擇門市</option>';
            promotionSelect.disabled = true;
            dispatchPromotionSelect.innerHTML = '<option value="">請先選擇門市</option>';
            dispatchPromotionSelect.disabled = true;
            return;
        }

        promotionSelect.innerHTML = '<option value="">載入中...</option>';
        promotionSelect.disabled = true;
        dispatchPromotionSelect.innerHTML = '<option value="">載入中...</option>';
        dispatchPromotionSelect.disabled = true;
        
        try {
            const url = `/api/v1/promotions/searchable?isActive=true&storeId=${storeId}`;
            const response = await fetchAuthenticated(url);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

            const apiResponse = await response.json();
            const promotions = apiResponse.data;
            currentPromotionsData = promotions; // Store full promotion data

            // 更新兩個優惠活動下拉選單
            const promotionOptions = '<option value="">請選擇優惠活動</option>' + 
                (promotions && promotions.length > 0 ? 
                    promotions.map(promo => `<option value="${promo.promotionId}">${promo.promotionName}</option>`).join('') :
                    '<option value="">此門市無適用活動</option>');
            
            promotionSelect.innerHTML = promotionOptions;
            dispatchPromotionSelect.innerHTML = promotionOptions;
            
    } catch (error) {
            console.error(`無法載入門市 ${storeId} 的優惠活動:`, error);
            promotionSelect.innerHTML = '<option value="">載入失敗</option>';
            dispatchPromotionSelect.innerHTML = '<option value="">載入失敗</option>';
        } finally {
            promotionSelect.disabled = false;
            dispatchPromotionSelect.disabled = false;
        }
    }

    /**
     * Handles the change event for invoice type radio buttons.
     */
    function handleInvoiceTypeChange() {
        const taxIdNumberInput = document.getElementById('taxIdNumber');
        const invoiceCompanyTitleInput = document.getElementById('invoiceCompanyTitle');
        const selectedTypeRadio = document.querySelector('input[name="invoiceTypeBtn"]:checked');

        if (!selectedTypeRadio) return; 
        const selectedType = selectedTypeRadio.value;

        const shouldShowDetails = selectedType === '2' || selectedType === '3';
        invoiceDetailsContainer.style.display = shouldShowDetails ? 'flex' : 'none';

        if (shouldShowDetails) {
            const isThreePart = selectedType === '3';
            taxIdNumberInput.disabled = !isThreePart;
            invoiceCompanyTitleInput.disabled = !isThreePart;
            if (!isThreePart) {
                taxIdNumberInput.value = '';
                invoiceCompanyTitleInput.value = '';
            }
        } else { 
            const allInvoiceInputs = invoiceDetailsContainer.querySelectorAll('input');
            allInvoiceInputs.forEach(input => input.value = '');
        }
    }

    /**
     * Handles clicks within the 'add payment' button group.
     */
    function handleAddPaymentClick(event) {
        console.log("Add Payment button clicked. Event target:", event.target);
        if (event.target.matches('button[data-payment-type]')) {
            const paymentType = event.target.dataset.paymentType;
            console.log("Payment type selected:", paymentType);
            addPaymentMethodRow(paymentType);
        }
    }

    /**
     * Dynamically adds a payment method row to the DOM.
     */
    function addPaymentMethodRow(type) {
        const paymentId = `payment-${Date.now()}`;
        let contentHtml = '';

        const baseRowStart = `<div class="payment-item row g-3 mb-2 align-items-center" id="${paymentId}" data-type="${type}">`;
        const removeButton = `<div class="col-md-1"><button type="button" class="btn btn-danger btn-sm remove-payment-btn">移除</button></div>`;
        const baseRowEnd = `</div>`;

        switch (type) {
            case 'cash':
                contentHtml = `<div class="col-md-3"><input type="text" class="form-control" value="現金" readonly></div><div class="col-md-4"><input type="number" class="form-control payment-amount" placeholder="金額"></div><div class="col-md-4"></div>`;
                break;
            case 'credit_card':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="信用卡" readonly></div>
                    <div class="col-md-3 position-relative">
                        <input type="text" class="form-control card-number" placeholder="輸入卡號自動帶入資訊">
                        <span class="card-info-display text-muted small position-absolute top-100 start-0"></span>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select card-payment-type">
                            <option value="0" selected>單筆</option>
                            <option value="2">分期</option>
                        </select>
                    </div>
                    <div class="col-md-2"><input type="number" class="form-control card-installments" placeholder="期數" value="0" style="display: none;"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
            case 'bank_transfer':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="匯款" readonly></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-name" placeholder="銀行名稱"></div>
                    <div class="col-md-2"><input type="text" class="form-control remitter-account" placeholder="匯出帳號後五碼"></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-account" placeholder="匯入帳號後五碼"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-amount" placeholder="金額"></div>`;
                break;
        }

        const paymentHtml = `${baseRowStart}${contentHtml}${removeButton}${baseRowEnd}`;
        paymentContainer.insertAdjacentHTML('beforeend', paymentHtml);
        // Add listener to the new amount input
        paymentContainer.querySelector(`#${paymentId} .payment-amount`).addEventListener('input', updateTotalAmountsUI);
    }

    /**
     * Handles clicks on remove buttons for payment items.
     */
    function handlePaymentItemActions(event) {
        if (event.target.classList.contains('remove-payment-btn')) {
            event.target.closest('.payment-item').remove();
            updateTotalAmountsUI(); // Re-calculate totals after removing an item
        }
    }

    /**
     * Handles input changes on payment amount fields.
     */
    function handlePaymentAmountChange(event) {
         if (event.target.classList.contains('payment-amount')) {
            updateTotalAmountsUI();
        }
    }

    function applyPromotionsToItems() {
        const activePromotionSelect = currentProductType === 'store' ? promotionSelect : dispatchPromotionSelect;
        const selectedPromotionId = activePromotionSelect.value;
        const selectedPromotion = currentPromotionsData.find(p => p.promotionId === selectedPromotionId);

        // 處理門市商品的促銷
        if (currentProductType === 'store') {
            document.querySelectorAll('#order-items-table tbody tr').forEach(row => {
                applyPromotionToRow(row, selectedPromotion);
            });
        } else {
            // 處理派工商品的促銷 (針對所有 tab 中的商品)
            document.querySelectorAll('.tab-pane .table tbody tr').forEach(row => {
                applyPromotionToRow(row, selectedPromotion);
            });
        }
        
        updateTotalAmountsUI();
    }

    function applyPromotionToRow(row, selectedPromotion) {
        const barcode = row.dataset.barcode;
        const originalPriceInput = row.querySelector('.original-price-input');
        const promoPriceInput = row.querySelector('.promo-price-input');
        const priceInput = row.querySelector('.price-input'); // The one used for calculation

        let originalPrice = parseFloat(originalPriceInput.dataset.originalPrice);
        let finalPrice = originalPrice;

        if (selectedPromotion && selectedPromotion.products) {
            const promoProduct = selectedPromotion.products.find(p => p.productBarcode === barcode);
            if (promoProduct) {
                finalPrice = promoProduct.promoPrice;
                promoPriceInput.value = finalPrice; // Use raw number for input value
                promoPriceInput.classList.add('text-success', 'fw-bold');
            } else {
                promoPriceInput.value = originalPrice; // Use raw number for input value
                promoPriceInput.classList.remove('text-success', 'fw-bold');
            }
        } else {
             promoPriceInput.value = originalPrice; // Use raw number for input value
             promoPriceInput.classList.remove('text-success', 'fw-bold');
        }
        priceInput.value = finalPrice;
    }

    /**
     * Handles the main form submission for both saving as draft and submitting.
     * @param {boolean} isDraft - True if saving as draft, false if submitting.
     */
    async function handleFormSubmit(isDraft, isSubmittingForPreview = false) {
        if(window.event && !isSubmittingForPreview) window.event.preventDefault(); // 在預覽模式下不阻止事件

        // --- START: Validation Logic ---
        let paymentValidationFailed = false;
        document.querySelectorAll('#payment-methods-container .payment-item').forEach(row => {
            const amountInput = row.querySelector('.payment-amount');
            const amount = parseFloat(amountInput.value);
            if (!amount || amount <= 0) {
                showToast('請填寫所有付款項目的金額，且金額必須大於 0。', 'warning');
                amountInput.classList.add('is-invalid'); // Highlight the invalid input
                paymentValidationFailed = true;
        } else {
                amountInput.classList.remove('is-invalid');
            }
        });

        if (paymentValidationFailed) {
            return; // Stop the submission
        }
        // --- END: Validation Logic ---

            const payload = buildPayload();

        // 只有存檔時，狀態才是草稿
        if (isDraft) {
            payload.orderStatusCode = 10; 
        }

        const url = isEditMode ? `/api/v1/orders/${currentOrderId}/correct` : '/api/v1/orders/wholesale';
        const method = isEditMode ? 'PUT' : 'POST';

        try {
            const response = await window.fetchAuthenticated(url, {
                method: method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
        const result = await response.json();
            if (!response.ok) throw new Error(result.message || '儲存失敗');

            showToast('訂單已儲存', 'success');

            setTimeout(() => {
                if (!isEditMode && result.data && result.data.orderId) {
                    // This is a new order. Redirect to the edit page for this new order.
                    window.location.href = `wholesale_product_order_form.html?orderId=${result.data.orderId}`;
                } else {
                    // This is an existing order. Reload to show the latest state.
                    location.reload();
                }
            }, 1000); // 1-second delay to allow toast to be seen

            return result.data; // 返回儲存後的訂單資料
            } catch (error) {
            console.error('儲存訂單失敗:', error);
            showToast(`儲存失敗: ${error.message}`, 'error');
            return null; // 返回 null 表示失敗
        }
    }

    async function handleSubmitForPreview(event) {
        event.preventDefault();
        // 步驟 1: 先呼叫存檔邏輯
        const savedOrder = await handleFormSubmit(true, true);

        // 步驟 2: 如果存檔成功，則跳轉到詳情頁
        if (savedOrder && savedOrder.orderId) {
            window.location.href = `order_detail.html?orderId=${savedOrder.orderId}`;
        }
    }

    /**
     * Handles deleting the order.
     */
    async function handleDeleteOrder() {
        if (!isEditMode || !currentOrderId) {
            showToast('只有已儲存的草稿訂單才能被刪除', 'warning');
        return;
    }

        if (confirm('您確定要刪除此訂單嗎？此操作無法復原。')) {
            try {
                const response = await fetchAuthenticated(`/api/v1/orders/${currentOrderId}`, {
                    method: 'DELETE'
                });
                if (!response.ok) {
                    const errData = await response.json();
                    throw new Error(errData.message || '刪除失敗');
                }
                showToast('訂單已成功刪除', 'success');
                window.location.href = 'wholesale_product_orders_list.html';
    } catch (error) {
                console.error('刪除訂單失敗:', error);
                showToast(`刪除失敗: ${error.message}`, 'danger');
            }
        }
    }

    function buildPayload() {
        const items = [];
        
        if (currentProductType === 'store') {
            // 收集門市商品項目
            document.querySelectorAll('#order-items-table tbody tr').forEach(row => {
                const item = {
                    productName: row.querySelector('td:nth-child(1)').textContent,
                    productBarcode: row.querySelector('td:nth-child(2)').textContent,
                    quantity: parseInt(row.querySelector('.quantity-input').value),
                    unitPrice: parseFloat(row.querySelector('.price-input').value),
                    itemTypeCode: 0 
                };
                items.push(item);
            });
        } else {
            // 收集派工商品項目 (來自各個 tab)
            document.querySelectorAll('.tab-pane .table tbody tr').forEach(row => {
                const item = {
                    productName: row.querySelector('td:nth-child(1)').textContent,
                    productBarcode: row.querySelector('td:nth-child(2)').textContent,
                    quantity: parseInt(row.querySelector('.quantity-input').value) || 1,
                    unitPrice: parseFloat(row.querySelector('.price-input').value) || 0,
                    itemTypeCode: parseInt(row.dataset.itemType) || 0,
                    warehouseId: row.querySelector('.warehouse-select')?.value || null
                };
                items.push(item);
            });
        }

        const paymentDetails = [];
        document.querySelectorAll('.payment-item').forEach(row => {
            const paymentMethodCode = row.dataset.type.toUpperCase();
            const amountInput = row.querySelector('.payment-amount');
            const amount = amountInput ? amountInput.value : null;

            if(amount && parseFloat(amount) > 0) {
                const cardInfoDisplay = row.querySelector('.card-info-display');
                let cardBrand = null;
                let cardIssuer = null;
                if (cardInfoDisplay && cardInfoDisplay.textContent && cardInfoDisplay.textContent.includes(' - ')) {
                    [cardBrand, cardIssuer] = cardInfoDisplay.textContent.split(' - ').map(s => s.trim());
                }
                const detail = {
                    paymentMethodCode: paymentMethodCode,
                    amount: parseFloat(amount),
                    cardNumber: row.querySelector('.card-number')?.value || null,
                    cardInstallments: row.querySelector('.card-installments')?.value ? parseInt(row.querySelector('.card-installments').value) : 1,
                    bankName: row.querySelector('.bank-name')?.value || null,
                    remitterAccountLastFive: row.querySelector('.remitter-account')?.value || null,
                    bankAccountLastFive: row.querySelector('.bank-account')?.value || null,
                    collectorName: null, // Add logic for this if needed
                    remarks: null, // Add logic for this if needed
                    cardBrand: cardBrand,
                    cardIssuer: cardIssuer,
                };
                paymentDetails.push(detail);
            }
        });

        const activePromotionSelect = currentProductType === 'store' ? promotionSelect : dispatchPromotionSelect;

        const payload = {
            orderId: currentOrderId,
            companyDivisionCode: document.getElementById('companyDivision').value,
            storeId: document.getElementById('store').value,
            distributorId: document.getElementById('distributorSelect').value || null,
            salespersonId: document.getElementById('salespersonSelect').value || null,
            orderDate: document.getElementById('orderDate').value ? new Date(document.getElementById('orderDate').value).toISOString() : null,

            // --- START: Add Customer Info ---
            memberId: document.getElementById('customerId').value || null,
            customerName: document.getElementById('customerName').value,
            customerPhone: document.getElementById('customerPhoneDisplay').value,
            // --- END: Add Customer Info ---

            promotionId: activePromotionSelect.value || null,

            items: items,
            paymentDetails: paymentDetails,

            // Invoice Information
            invoiceTypeCode: document.querySelector('input[name="invoiceTypeBtn"]:checked')?.value || null,
            taxIdNumber: document.getElementById('taxIdNumber')?.value || null,
            invoiceCompanyTitle: document.getElementById('invoiceCompanyTitle')?.value || null,
            invoiceDate: document.getElementById('invoiceDate')?.value || null,
            invoiceNumber: document.getElementById('invoiceNumber')?.value || null,

            remarks: document.getElementById('orderRemarks')?.value || null,

            // CRITICAL FIX: 設定正確的訂單類型代碼
            orderTypeCode: 2, // 2 corresponds to OrderTypeEnum.WHOLESALE_PRODUCT_ORDER

            // Include taxType parameter
            taxType: document.querySelector('input[name="taxTypeBtn"]:checked')?.value === 'inclusive' ? 2 : 1
        };
        return payload;
    }

    /**
     * Handles the product search button click for store products.
     */
    async function handleProductSearch(isModalSearch = false) {
        const storeId = document.getElementById('store').value;
        if (!storeId) {
            showToast('請先選擇一個門市', 'warning');
            return;
        }

        const keywordInput = isModalSearch ? document.getElementById('productSearchKeywordModal') : productKeywordInput;
        const keyword = keywordInput.value.trim();
        if (!keyword) {
            showToast('請輸入商品關鍵字', 'warning');
            return;
        }

        const searchResultsContainer = document.getElementById('productSearchResultsModal');
        searchResultsContainer.innerHTML = '<div class="list-group-item">搜尋中...</div>';

        if (!isModalSearch) {
            const productSearchModal = new bootstrap.Modal(document.getElementById('productSearchModal'));
            productSearchModal.show();
        }

        try {
            const url = `/api/v1/product-settings/search-store-products?keyword=${encodeURIComponent(keyword)}&storeId=${storeId}`;
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) throw new Error('搜尋商品失敗');

            const apiResponse = await response.json();
            const products = apiResponse.data;

            searchResultsContainer.innerHTML = '';
            if (products && products.length > 0) {
                products.forEach(product => {
                    const price = product.salePrice !== null ? product.salePrice : 0;
                    const stock = product.stockQuantity !== null ? product.stockQuantity : 'N/A';
                    const productHtml = `
                        <a href="#" class="list-group-item list-group-item-action select-product-btn" 
                           data-product-barcode="${product.productBarcode}" 
                           data-product-name="${product.productName}" 
                           data-price="${price}"
                           data-search-type="store">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${product.productName}</h6>
                                <small>庫存: ${stock}</small>
                            </div>
                            <p class="mb-1">${product.productBarcode}</p>
                            <small>價格: NT$ ${price.toLocaleString()}</small>
                        </a>
                    `;
                    searchResultsContainer.insertAdjacentHTML('beforeend', productHtml);
                });
            } else {
                searchResultsContainer.innerHTML = '<div class="list-group-item">在該門市找不到符合條件的商品。</div>';
            }
        } catch (error) {
            console.error('商品搜尋錯誤:', error);
            searchResultsContainer.innerHTML = '<div class="list-group-item text-danger">搜尋時發生錯誤。</div>';
        }
    }

    /**
     * Handles the product search button click for dispatch products.
     */
    async function handleDispatchProductSearch(isModalSearch = false) {
        const keywordInput = isModalSearch ? document.getElementById('productSearchKeywordModal') : dispatchProductKeywordInput;
        const keyword = keywordInput.value.trim();
        if (!keyword) {
            showToast('請輸入商品關鍵字', 'warning');
            return;
        }

        const searchResultsContainer = document.getElementById('productSearchResultsModal');
        searchResultsContainer.innerHTML = '<div class="list-group-item">搜尋中...</div>';

        if (!isModalSearch) {
            const productSearchModal = new bootstrap.Modal(document.getElementById('productSearchModal'));
            productSearchModal.show();
        }

        try {
            const url = `/api/v1/product-settings/search-dispatch-products?keyword=${encodeURIComponent(keyword)}`;
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) throw new Error('搜尋商品失敗');

            const apiResponse = await response.json();
            const products = apiResponse.data;

            searchResultsContainer.innerHTML = '';
            if (products && products.length > 0) {
                products.forEach(product => {
                    const price = product.salePrice !== null ? product.salePrice : 0;
                    const productHtml = `
                        <a href="#" class="list-group-item list-group-item-action select-product-btn" 
                           data-product-barcode="${product.productBarcode}" 
                           data-product-name="${product.productName}" 
                           data-price="${price}"
                           data-search-type="dispatch">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${product.productName}</h6>
                                <small>類型: ${product.productTypeName || 'N/A'}</small>
                            </div>
                            <p class="mb-1">${product.productBarcode}</p>
                            <small>價格: NT$ ${price.toLocaleString()}</small>
                        </a>
                    `;
                    searchResultsContainer.insertAdjacentHTML('beforeend', productHtml);
                });
            } else {
                searchResultsContainer.innerHTML = '<div class="list-group-item">找不到符合條件的商品。</div>';
            }
        } catch (error) {
            console.error('商品搜尋錯誤:', error);
            searchResultsContainer.innerHTML = '<div class="list-group-item text-danger">搜尋時發生錯誤。</div>';
        }
    }

    /**
     * Handles the selection of a product from the search modal.
     */
    function handleProductSelection(event) {
        event.preventDefault();
        const target = event.target.closest('.select-product-btn');
        if (!target) return;

        const searchType = target.dataset.searchType;
        const product = {
            productBarcode: target.dataset.productBarcode,
            productName: target.dataset.productName,
            unitPrice: parseFloat(target.dataset.price),
            listPrice: parseFloat(target.dataset.listPrice || target.dataset.price),
            salePrice: parseFloat(target.dataset.price),
            quantity: 1,
            itemTypeCode: 0 // Default to 0 (MAIN_PRODUCT)
        };

        const productSearchModal = bootstrap.Modal.getInstance(document.getElementById('productSearchModal'));
        if(productSearchModal) productSearchModal.hide();

        if (searchType === 'store') {
            addStoreProductItemRow(product);
        } else {
            // 派工商品處理邏輯
            if (isSearchingForMainProduct) {
                const placeholderPane = document.querySelector('.tab-pane[data-is-placeholder="true"]');

                if (placeholderPane) {
                    fillPlaceholderTab(placeholderPane, product);
                } else {
                    addMainProductTab(product);
                }
                isSearchingForMainProduct = false;
            } else {
                addDispatchProductToTab(product);
            }
        }
        
        updateTotalAmountsUI();
    }

    /**
     * 填入 placeholder tab 的主商品資訊
     */
    function fillPlaceholderTab(pane, product) {
        if (!pane || !product) return;

        // 更新 pane 的 data 屬性
        pane.dataset.isPlaceholder = 'false';
        pane.dataset.mainBarcode = product.productBarcode;
        pane.dataset.mainProductName = product.productName;

        // 更新顯示欄位
        const nameDisplay = pane.querySelector('.main-product-name-display');
        if (nameDisplay) nameDisplay.value = product.productName;

        const listPriceDisplay = pane.querySelector('.list-price-display');
        if (listPriceDisplay) listPriceDisplay.value = (product.listPrice || 0).toLocaleString();

        const salePriceDisplay = pane.querySelector('.sale-price-display');
        if (salePriceDisplay) salePriceDisplay.value = (product.salePrice || 0).toLocaleString();

        const amountDisplay = pane.querySelector('.amount-display');
        if (amountDisplay) amountDisplay.value = (product.salePrice || 0).toLocaleString();

        // 更新 tab 標題
        const tabId = pane.getAttribute('aria-labelledby');
        const tabLink = document.getElementById(tabId);
        if (tabLink) {
            const tabTitle = tabLink.querySelector('span');
            if (tabTitle) {
                tabTitle.textContent = product.productName;
            }
        }

        // 隱藏選擇主商品按鈕
        const chooseBtn = pane.querySelector('.choose-main-product-btn');
        if (chooseBtn) {
            chooseBtn.style.display = 'none';
        }
    }

    /**
     * 新增門市商品項目到表格
     */
    function addStoreProductItemRow(item) {
        const tableBody = document.getElementById('order-items-table').querySelector('tbody');
        const rowId = `item-${item.productBarcode || Date.now()}`;
        const newRow = document.createElement('tr');
        newRow.id = rowId;
        newRow.dataset.itemId = item.orderItemId || '';
        newRow.dataset.barcode = item.productBarcode;

        const originalPrice = item.unitPrice || 0;

        newRow.innerHTML = `
            <td>${item.productName || 'N/A'}</td>
            <td>${item.productBarcode || 'N/A'}</td>
            <td><input type="number" class="form-control form-control-sm original-price-input bg-light" value="${originalPrice}" readonly data-original-price="${originalPrice}"></td>
            <td><input type="number" class="form-control form-control-sm promo-price-input bg-light" value="${originalPrice}" readonly></td>
            <td><input type="number" class="form-control form-control-sm quantity-input" value="${item.quantity || 1}" min="1"></td>
            <td class="item-subtotal">${(originalPrice * (item.quantity || 1)).toLocaleString()}</td>
            <td>
                <input type="hidden" class="price-input" value="${originalPrice}">
                <button type="button" class="btn btn-danger btn-sm remove-item-btn"><i class="bi bi-trash"></i></button>
            </td>
        `;
        tableBody.appendChild(newRow);

        // Add listeners to the new row's inputs for real-time total calculation
        newRow.querySelector('.quantity-input').addEventListener('input', updateTotalAmountsUI);

        // 為新產生的刪除按鈕綁定事件
        newRow.querySelector('.remove-item-btn').addEventListener('click', function() {
            newRow.remove();
            updateTotalAmountsUI();
        });

        applyPromotionsToItems();
    }

    /**
     * 新增派工商品到對應的 tab
     */
    function addDispatchProductToTab(item) {
        // 檢查是否已有主商品 tab，如果沒有則建立
        let mainProductTab = document.querySelector('#dispatch-group-tabs .nav-link[data-item-type="0"]');
        if (!mainProductTab) {
            createMainProductTab();
        }

        // 將商品添加到主商品 tab
        const mainTabContent = document.querySelector('#main-product-pane .table tbody');
        if (mainTabContent) {
            const newRow = createDispatchProductRow(item, 0);
            mainTabContent.appendChild(newRow);
        }
    }

    /**
     * 新增一個主商品 TAB 頁籤和對應的內容面板
     */
    function addMainProductTab(mainProduct = null) {
        tabCounter++;
        const tabId = `group-tab-${tabCounter}`;
        const paneId = `group-pane-${tabCounter}`;
        const isPlaceholder = mainProduct === null;

        const tabTitle = isPlaceholder ? `主商品 ${tabCounter}` : mainProduct.productName;
        const mainProductBarcode = isPlaceholder ? '' : mainProduct.productBarcode;
        const mainProductName = isPlaceholder ? '待選擇...' : mainProduct.productName;
        const listPrice = isPlaceholder ? 0 : (mainProduct.listPrice || 0);
        const salePrice = isPlaceholder ? 0 : (mainProduct.salePrice || 0);

        // 1. 建立新的 TAB 頁籤按鈕
        const newTabHtml = `
            <li class="nav-item" role="presentation">
                <a class="nav-link" id="${tabId}" data-bs-toggle="tab" data-bs-target="#${paneId}" href="#" role="tab" aria-controls="${paneId}" aria-selected="false">
                    <span>${tabTitle}</span>
                    <button type="button" class="btn-close btn-sm ms-2" aria-label="Close"></button>
                </a>
            </li>`;
        document.getElementById('dispatch-group-tabs').insertAdjacentHTML('beforeend', newTabHtml);

        // 2. 建立新的 TAB 內容面板
        const newPaneHtml = `
            <div class="tab-pane fade" id="${paneId}" role="tabpanel" aria-labelledby="${tabId}" 
                 data-main-barcode="${mainProductBarcode}" 
                 data-main-product-name="${mainProductName}" 
                 data-is-placeholder="${isPlaceholder}"
                 data-main-order-item-id="${isPlaceholder ? '' : (mainProduct.orderItemId || '')}">
                <div class="card mb-3 border-top-0">
                    <div class="card-body">
                        <h5>主商品資訊</h5>
                        <div class="row g-3">
                            <div class="col-md-3"><label class="form-label">商品名稱</label><input type="text" class="form-control main-product-name-display" value="${mainProductName}" readonly></div>
                            <div class="col-md-3"><label class="form-label">出貨方式</label><select class="form-select dispatch-type-select"></select></div>
                            <div class="col-md-3"><label class="form-label">原價</label><input type="text" class="form-control list-price-display" value="${listPrice.toLocaleString()}" readonly></div>
                            <div class="col-md-3"><label class="form-label">活動價</label><input type="text" class="form-control sale-price-display" value="${salePrice.toLocaleString()}" readonly></div>
                            <div class="col-md-3"><label class="form-label">數量</label><input type="text" class="form-control" value="1" readonly></div>
                            <div class="col-md-3"><label class="form-label">金額</label><input type="text" class="form-control amount-display" value="${salePrice.toLocaleString()}" readonly></div>
                            <div class="col-md-3"><label class="form-label">電動桌機身號碼</label><input type="text" class="form-control serial-number" placeholder="機身號碼"></div>
                            <div class="col-md-3"><label class="form-label">出貨倉庫</label><select class="form-select warehouse-select"></select></div>
                        </div>
                        ${isPlaceholder ? `
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-primary choose-main-product-btn">選擇主商品</button>
                        </div>` : ''}
                        <hr>
                        <h5>贈品</h5>
                        <button type="button" class="btn btn-sm btn-outline-info add-sub-item-btn mb-2" data-item-type="GIFT">新增贈品</button>
                        <div class="table-responsive">
                            <table class="table table-sm sub-items-table gifts-table">
                                <thead>
                                    <tr>
                                        <th>商品名稱</th>
                                        <th>出貨方式</th>
                                        <th>原價</th>
                                        <th>活動價</th>
                                        <th>數量</th>
                                        <th>小計</th>
                                        <th>出貨倉庫</th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <div class="text-end mt-2 gift-summary">
                            <span>原贈品金額總計：<span class="original-gift-total fw-bold">0</span>；</span>
                            <span>換贈品金額總計：<span class="exchanged-gift-total fw-bold">0</span></span>
                        </div>

                        <!-- 加購品區塊 -->
                        <hr>
                        <h5>加購品</h5>
                        <button type="button" class="btn btn-sm btn-outline-primary add-sub-item-btn mb-2" data-item-type="ADDON">新增加購品</button>
                        <div class="table-responsive">
                            <table class="table table-sm sub-items-table addons-table">
                                <thead>
                                    <tr>
                                        <th>商品名稱</th>
                                        <th>出貨方式</th>
                                        <th>原價</th>
                                        <th>活動價</th>
                                        <th>數量</th>
                                        <th>小計</th>
                                        <th>出貨倉庫</th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>`;
        document.getElementById('dispatch-group-tab-content').insertAdjacentHTML('beforeend', newPaneHtml);

        // 為新面板中的倉庫下拉選單填充選項
        const newWarehouseSelect = document.querySelector(`#${paneId} .warehouse-select`);
        if (newWarehouseSelect) {
            populateWarehouseDropdown(newWarehouseSelect);
        }

        // 為新的派工類型下拉選單填充選項
        const newDispatchTypeSelect = document.querySelector(`#${paneId} .dispatch-type-select`);
        if (newDispatchTypeSelect) {
            populateRequireDispatchTypes(newDispatchTypeSelect).then(() => {
                newDispatchTypeSelect.value = '1'; // 預設為派工
            });
        }

        // 3. 啟用並顯示新的 TAB
        const newTabTrigger = document.getElementById(tabId);
        if (newTabTrigger) {
            const tab = new bootstrap.Tab(newTabTrigger);
            tab.show();
        }

        // 4. 綁定選擇主商品按鈕事件
        if (isPlaceholder) {
            const chooseBtn = document.querySelector(`#${paneId} .choose-main-product-btn`);
            if (chooseBtn) {
                chooseBtn.addEventListener('click', () => {
                    isSearchingForMainProduct = true;
                    handleDispatchProductSearch(false);
                });
            }
        }
    }

    /**
     * 建立主商品 tab (舊函數名稱，保持相容性)
     */
    function createMainProductTab() {
        addMainProductTab(null); // 建立 placeholder tab
    }

    /**
     * 移除指定的 TAB
     */
    function removeTab(tabId) {
        const tabLink = document.getElementById(tabId);
        if (!tabLink) return;

        const tabListItem = tabLink.closest('li');
        const paneId = tabLink.getAttribute('data-bs-target');
        const pane = document.querySelector(paneId);

        const allTabs = document.querySelectorAll('#dispatch-group-tabs .nav-item');

        if (allTabs.length > 1) {
            // 先移除 DOM 元素
            tabListItem.remove();
            if (pane) {
                pane.remove();
            }

            // 然後將剩餘的第一個 TAB 設為 active
            const remainingTabsContainer = document.getElementById('dispatch-group-tabs');
            const firstTabLink = remainingTabsContainer.querySelector('.nav-link');
            if (firstTabLink) {
                const tabToActivate = new bootstrap.Tab(firstTabLink);
                tabToActivate.show();
            }
        } else {
            // 只剩下最後一個TAB，則重置為placeholder
            resetTabToPlaceholder(pane);
        }
        updateTotalAmountsUI();
    }

    /**
     * 重置 tab 為 placeholder 狀態
     */
    function resetTabToPlaceholder(pane) {
        if (!pane) return;
        
        pane.dataset.isPlaceholder = 'true';
        pane.dataset.mainBarcode = '';
        pane.dataset.mainProductName = '待選擇...';
        pane.dataset.mainOrderItemId = '';

        // 重置主商品顯示
        const nameDisplay = pane.querySelector('.main-product-name-display');
        if (nameDisplay) nameDisplay.value = '待選擇...';

        const listPriceDisplay = pane.querySelector('.list-price-display');
        if (listPriceDisplay) listPriceDisplay.value = '0';

        const salePriceDisplay = pane.querySelector('.sale-price-display');
        if (salePriceDisplay) salePriceDisplay.value = '0';

        const amountDisplay = pane.querySelector('.amount-display');
        if (amountDisplay) amountDisplay.value = '0';

        // 清空子商品表格
        const giftTableBody = pane.querySelector('.gifts-table tbody');
        if (giftTableBody) giftTableBody.innerHTML = '';

        const addonsTableBody = pane.querySelector('.addons-table tbody');
        if (addonsTableBody) addonsTableBody.innerHTML = '';

        // 重置總計
        updateGiftSectionState(pane);

        // 顯示選擇主商品按鈕
        let chooseBtn = pane.querySelector('.choose-main-product-btn');
        if (!chooseBtn) {
            const targetContainer = pane.querySelector('.row.g-3');
            if (targetContainer) {
                const buttonHtml = `
                    <div class="col-12 text-center mt-3">
                        <button type="button" class="btn btn-primary choose-main-product-btn">選擇主商品</button>
                    </div>`;
                targetContainer.insertAdjacentHTML('afterend', buttonHtml);
                chooseBtn = pane.querySelector('.choose-main-product-btn');
            }
        }

        if (chooseBtn) {
            chooseBtn.style.display = 'block';
            chooseBtn.addEventListener('click', () => {
                isSearchingForMainProduct = true;
                handleDispatchProductSearch(false);
            });
        }
    }

    /**
     * 更新贈品區塊狀態
     */
    function updateGiftSectionState(pane) {
        const originalGiftTotal = pane.querySelector('.original-gift-total');
        const exchangedGiftTotal = pane.querySelector('.exchanged-gift-total');
        
        if (originalGiftTotal) originalGiftTotal.textContent = '0';
        if (exchangedGiftTotal) exchangedGiftTotal.textContent = '0';
    }

    /**
     * 填充倉庫下拉選單 (placeholder 函數)
     */
    function populateWarehouseDropdown(selectElement) {
        if (!selectElement) return;
        selectElement.innerHTML = '<option value="">請選擇倉庫</option>';
        // TODO: 實作載入倉庫列表的邏輯
    }

    /**
     * 填充派工類型下拉選單 (placeholder 函數)
     */
    async function populateRequireDispatchTypes(selectElement) {
        if (!selectElement) return;
        selectElement.innerHTML = `
            <option value="1">派工</option>
            <option value="2">自取</option>
            <option value="3">運送</option>
        `;
    }

    /**
     * 建立派工商品的表格行
     */
    function createDispatchProductRow(item, itemType) {
        const rowId = `item-${item.productBarcode || Date.now()}-${Date.now()}`;
        const newRow = document.createElement('tr');
        newRow.id = rowId;
        newRow.dataset.itemId = item.orderItemId || '';
        newRow.dataset.barcode = item.productBarcode;
        newRow.dataset.itemType = itemType;

        const originalPrice = item.unitPrice || 0;

        newRow.innerHTML = `
            <td>${item.productName || 'N/A'}</td>
            <td>${item.productBarcode || 'N/A'}</td>
            <td><input type="number" class="form-control form-control-sm original-price-input bg-light" value="${originalPrice}" readonly data-original-price="${originalPrice}"></td>
            <td><input type="number" class="form-control form-control-sm promo-price-input bg-light" value="${originalPrice}" readonly></td>
            <td><input type="number" class="form-control form-control-sm quantity-input" value="${item.quantity || 1}" min="1"></td>
            <td>
                <select class="form-select form-select-sm warehouse-select">
                    <option value="">請選擇倉庫</option>
                    <!-- 倉庫選項將由其他函數填充 -->
                </select>
            </td>
            <td class="item-subtotal">${(originalPrice * (item.quantity || 1)).toLocaleString()}</td>
            <td>
                <input type="hidden" class="price-input" value="${originalPrice}">
                <button type="button" class="btn btn-danger btn-sm remove-item-btn"><i class="bi bi-trash"></i></button>
            </td>
        `;

        // 為數量輸入框綁定事件
        newRow.querySelector('.quantity-input').addEventListener('input', updateTotalAmountsUI);

        // 為刪除按鈕綁定事件
        newRow.querySelector('.remove-item-btn').addEventListener('click', function() {
            newRow.remove();
            updateTotalAmountsUI();
        });

        return newRow;
    }

    function updateTotalAmountsUI() {
        let netTotalFromItems = 0;
        
        // 根據當前的商品類型計算總額
        if (currentProductType === 'store') {
            document.querySelectorAll('#order-items-table tbody tr').forEach(row => {
                const price = parseFloat(row.querySelector('.price-input')?.value) || 0;
                const quantity = parseInt(row.querySelector('.quantity-input')?.value) || 0;
                const subtotal = price * quantity;
                row.querySelector('.item-subtotal').textContent = subtotal.toLocaleString();
                netTotalFromItems += subtotal;
            });
        } else {
            // 派工商品模式 - 計算所有 tab 中的主商品和子商品
            document.querySelectorAll('.tab-pane').forEach(pane => {
                if (pane.dataset.isPlaceholder === 'true') return; // 跳過 placeholder tab

                // 計算主商品金額
                const amountDisplay = pane.querySelector('.amount-display');
                if (amountDisplay) {
                    const mainProductAmount = parseFloat(amountDisplay.value.replace(/,/g, '')) || 0;
                    netTotalFromItems += mainProductAmount;
                }

                // 計算子商品金額 (贈品和加購品)
                const subItemTables = pane.querySelectorAll('.sub-items-table tbody tr');
                subItemTables.forEach(row => {
                    const subtotalCell = row.querySelector('td:nth-child(6)'); // 小計欄位
                    if (subtotalCell) {
                        const subtotal = parseFloat(subtotalCell.textContent.replace(/[^\d.-]/g, '')) || 0;
                        netTotalFromItems += subtotal;
                    }
                });
            });
        }

        const invoiceType = document.querySelector('input[name="invoiceTypeBtn"]:checked').value;
        const taxType = document.querySelector('input[name="taxTypeBtn"]:checked').value;

        let finalNetAmount = netTotalFromItems;
        let finalTaxAmount = 0;
        let finalGrandTotal = netTotalFromItems;

        if (invoiceType === '2' || invoiceType === '3') { // Taxable invoices
            if (taxType === 'inclusive') {
                // Reverse calculation: netTotalFromItems is considered the grand total
                finalGrandTotal = netTotalFromItems;
                finalNetAmount = Math.round(finalGrandTotal / 1.05);
                finalTaxAmount = finalGrandTotal - finalNetAmount;
            } else { // 'exclusive'
                finalTaxAmount = Math.round(netTotalFromItems * 0.05);
                finalGrandTotal = netTotalFromItems + finalTaxAmount;
            }
        } else { // No invoice, so no tax
            finalTaxAmount = 0;
            finalGrandTotal = netTotalFromItems;
        }

        document.getElementById('productsTotalAmount').textContent = `NT$ ${netTotalFromItems.toLocaleString()}`;
        document.getElementById('discountAmount').textContent = `NT$ 0`; // Placeholder
        document.getElementById('netAmount').textContent = `NT$ ${finalNetAmount.toLocaleString()}`;
        document.getElementById('taxAmount').textContent = `NT$ ${finalTaxAmount.toLocaleString()}`;
        document.getElementById('grandTotalAmount').textContent = `NT$ ${finalGrandTotal.toLocaleString()}`;

    let totalPaid = 0;
        document.querySelectorAll('#payment-methods-container .payment-amount').forEach(input => {
            totalPaid += parseFloat(input.value) || 0;
        });

        const amountDue = finalGrandTotal - totalPaid;

        // Update the payment summary UI
        document.getElementById('totalPaidAmountDisplay').textContent = `NT$ ${totalPaid.toLocaleString()}`;
        document.getElementById('amountDueDisplay').textContent = `NT$ ${amountDue.toLocaleString()}`;

        // --- START: Enable/Disable Submit Button based on Amount Due ---
        const submitOrderBtn = document.getElementById('submit-order-btn');
        if (submitOrderBtn) {
            // 由於 Javascript 浮點數計算可能存在精度問題，將差額四捨五入到分來比較
            const roundedAmountDue = Math.round(amountDue * 100) / 100;
            // 只有當待付差額精確等於 0 且訂單總金額大於 0 時，才啟用按鈕
            if (roundedAmountDue === 0 && finalGrandTotal > 0) {
                submitOrderBtn.disabled = false;
            } else {
                submitOrderBtn.disabled = true;
            }
        }
        // --- END: Enable/Disable Submit Button based on Amount Due ---
    }

    /**
     * Handles the customer search by phone number.
     */
    async function handleCustomerSearch() {
        const phoneInput = document.getElementById('customerPhoneSearch');
        const phoneNumber = phoneInput.value.trim();
        if (!phoneNumber) {
            showToast('請輸入會員電話號碼', 'warning');
        return;
    }

        try {
            const response = await fetchAuthenticated(`/api/v1/customers/by-phone?phoneNumber=${encodeURIComponent(phoneNumber)}`);
            if (!response.ok) throw new Error('會員查詢失敗');

            const apiResponse = await response.json();
            const customers = apiResponse.data;

            if (!customers || customers.length === 0) {
                showToast('查無此會員資料，您可以直接手動輸入客戶姓名與電話，或點擊按鈕快速新增會員。', 'info');
                document.getElementById('createNewCustomerBtn').style.display = 'inline-block';
            } else if (customers.length === 1) {
                populateCustomerInfo(customers[0]);
        } else {
                // Multiple customers found, show modal to select one
                populateCustomerSelectionModal(customers);
                const customerSelectModal = new bootstrap.Modal(document.getElementById('customerSelectModal'));
                customerSelectModal.show();
        }
    } catch (error) {
            console.error('Error searching customer:', error);
            showToast(`查詢會員時發生錯誤: ${error.message}`, 'danger');
        }
    }

    /**
     * Populates the customer selection modal with multiple results.
     */
    function populateCustomerSelectionModal(customers) {
        const modalBody = document.getElementById('customerSelectModalBody');
        modalBody.innerHTML = ''; // Clear previous results
        customers.forEach(customer => {
            const customerElement = document.createElement('a');
            customerElement.href = '#';
            customerElement.className = 'list-group-item list-group-item-action select-customer-option';
            customerElement.dataset.customerId = customer.customerId;
            customerElement.dataset.customerName = customer.customerName;
            customerElement.dataset.customerPhone = customer.phoneNumber;
            customerElement.innerHTML = `<strong>${customer.customerName}</strong><br><small>${customer.phoneNumber} - ${customer.companyDivisionDescription || ''}</small>`;
            modalBody.appendChild(customerElement);
        });
    }

    /**
     * Handles the click event when a customer is selected from the modal.
     */
    function handleCustomerSelectionFromModal(event) {
        event.preventDefault();
        const target = event.target.closest('.select-customer-option');
        if (!target) return;

        const customer = {
            customerId: target.dataset.customerId,
            customerName: target.dataset.customerName,
            phoneNumber: target.dataset.customerPhone
        };
        populateCustomerInfo(customer);

        const modalInstance = bootstrap.Modal.getInstance(document.getElementById('customerSelectModal'));
        modalInstance.hide();
    }

    /**
     * Populates the customer info fields on the main form.
     */
    function populateCustomerInfo(customer) {
        document.getElementById('customerId').value = customer.customerId || '';
        document.getElementById('customerName').value = customer.customerName || '';
        document.getElementById('customerPhoneDisplay').value = customer.phoneNumber || '';
        document.getElementById('customerPhoneSearch').value = customer.phoneNumber || '';

        const createBtn = document.getElementById('createNewCustomerBtn');
        if (createBtn) {
            createBtn.style.display = 'none';
        }
    }

    /**
     * Main initialization function, orchestrates the form setup.
     */
    async function initWholesaleOrderForm() {
        attachEventListeners();

        const urlParams = new URLSearchParams(window.location.search);
        currentOrderId = urlParams.get('orderId');
        isEditMode = !!currentOrderId;

        await populateCompanyDivision();
        await loadStores();
        await loadDistributors();
        await loadSalespersons();

        handleInvoiceTypeChange();
        handleProductTypeChange(); // 初始化商品類型顯示

        if (isEditMode) {
            formTitle.textContent = '修改批發商品訂單';
            pageMainTitle.textContent = '修改批發商品訂單';
            await loadOrderForEditing(currentOrderId);
        } else {
            // New order mode logic
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            const todayString = `${year}-${month}-${day}`;

            document.getElementById('orderDate').value = todayString;
            document.getElementById('invoiceDate').value = todayString; // Set invoice date as well

            storeSelect.dispatchEvent(new Event('change'));

            // For a new order, only "Save Draft" and "Submit" should be visible initially.
            document.getElementById('save-draft-btn').style.display = 'inline-block';
            document.getElementById('submit-order-btn').style.display = 'inline-block';
            document.getElementById('delete-btn').style.display = 'none'; // Explicitly hide delete
        }
    }

    async function loadOrderForEditing(orderId) {
        try {
            const response = await fetchAuthenticated(`/api/v1/orders/${orderId}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

            const apiResponse = await response.json();
            if (!apiResponse.data) throw new Error("API did not return order data.");

            currentOrderData = apiResponse.data;
            populateForm(currentOrderData);

            // After populating form, if we have a storeId, load its promotions
            if (currentOrderData.storeId) {
                await loadPromotionsForStore(currentOrderData.storeId);
                // After promotions are loaded, set the selected value if it exists in the order data
                if (currentOrderData.promotionId) {
                    const activePromotionSelect = currentProductType === 'store' ? promotionSelect : dispatchPromotionSelect;
                    activePromotionSelect.value = currentOrderData.promotionId;
                }
        }

    } catch (error) {
            console.error('Failed to load order for editing:', error);
            showToast('載入訂單資料失敗', 'danger');
        }
    }

    function populateForm(order) {
        // 這裡需要根據訂單的具體數據來填充表單
        // 由於這是示例，暫時保留空實現
        console.log('populateForm called with order:', order);
        // TODO: 實現表單填充邏輯
    }

    // --- EXECUTION ---
    initWholesaleOrderForm();
});
