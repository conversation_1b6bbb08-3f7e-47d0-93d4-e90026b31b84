// role_permissions_list.js
document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.getElementById('roles-table-body');
    const paginationControls = document.getElementById('pagination-controls');
    const noRolesMessage = document.getElementById('no-roles-message');

    const API_URL = '/api/v1/role-permissions';
    let currentPage = 0;
    const pageSize = 10; // Default page size

    async function fetchRoles(page = 0) {
        currentPage = page;
        const queryParams = `?page=${page}&size=${pageSize}&sort=roleName,asc`; // Default sort

        try {
            const response = await window.fetchAuthenticated(API_URL + queryParams);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const apiResult = await response.json();

            tableBody.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && apiResult.data.list) {
                if (apiResult.data.list.length > 0) {
                    noRolesMessage.classList.add('d-none');
                    const roles = apiResult.data.list;
                    roles.forEach((role, index) => {
                        const row = tableBody.insertRow();
                        // 計算序號：考慮分頁偏移量
                        const rowNumber = (currentPage * pageSize) + index + 1;
                        row.insertCell().textContent = rowNumber;
                        row.insertCell().textContent = role.roleCode;
                        row.insertCell().textContent = role.roleName;
                        row.insertCell().textContent = role.roleDescription || '-';
                        row.insertCell().textContent = role.updateTime || '-';
                        row.insertCell().textContent = role.updateByName || '-';
                        
                        const actionsCell = row.insertCell();
                        actionsCell.classList.add('text-nowrap');
                        
                        const editBtn = document.createElement('a');
                        editBtn.href = `role_permission_form.html?id=${role.roleId}&mode=edit`;
                        editBtn.className = 'btn btn-sm btn-outline-primary me-1';
                        editBtn.innerHTML = '<i class="bi bi-pencil-fill"></i> 編輯權限';
                        actionsCell.appendChild(editBtn);

                        const deleteBtn = document.createElement('button');
                        deleteBtn.className = 'btn btn-sm btn-outline-danger delete-role-btn';
                        deleteBtn.innerHTML = '<i class="bi bi-trash"></i> 刪除';
                        deleteBtn.dataset.roleId = role.roleId;
                        deleteBtn.dataset.roleName = role.roleName;
                        actionsCell.appendChild(deleteBtn);
                    });
                    renderPagination(apiResult.data.page.total, apiResult.data.page.page, apiResult.data.page.pageSize);
                    addDeleteEventListeners();
                } else {
                    noRolesMessage.classList.remove('d-none');
                    paginationControls.innerHTML = '';
                }
            } else {
                noRolesMessage.classList.remove('d-none');
                tableBody.innerHTML = `<tr><td colspan="7">${apiResult.message || '無法載入角色資料'}</td></tr>`;
                paginationControls.innerHTML = '';
            }
        } catch (error) {
            console.error('Error fetching roles:', error);
            tableBody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">無法載入角色資料: ${error.message}</td></tr>`;
            noRolesMessage.classList.add('d-none');
        }
    }

    function renderPagination(totalItems, currentPageBackend, itemsPerPage) {
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        let localCurrentPage = currentPageBackend + 1; // Convert 0-indexed to 1-indexed for UI logic
        paginationControls.innerHTML = '';

        if (totalPages <= 1) return;

        const createPageItem = (pageNumberForFetch, text, isDisabled, isActive) => {
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = text;
            if (!isDisabled) {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    fetchRoles(pageNumberForFetch); // pageNumberForFetch should be 0-indexed for API
                });
            }
            li.appendChild(link);
            return li;
        };

        paginationControls.appendChild(createPageItem(localCurrentPage - 2, '上一頁', localCurrentPage <= 1, false));

        let startPage = Math.max(1, localCurrentPage - 2);
        let endPage = Math.min(totalPages, localCurrentPage + 2);
        if (localCurrentPage <= 3) endPage = Math.min(totalPages, 5);
        if (localCurrentPage > totalPages - 2) startPage = Math.max(1, totalPages - 4);

        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i - 1, i, false, i === localCurrentPage));
        }

        paginationControls.appendChild(createPageItem(localCurrentPage, '下一頁', localCurrentPage >= totalPages, false));
    }

    function addDeleteEventListeners() {
        document.querySelectorAll('.delete-role-btn').forEach(button => {
            button.addEventListener('click', async function() {
                const roleId = this.dataset.roleId;
                const roleName = this.dataset.roleName;
                if (confirm(`確定要刪除角色「${roleName}」嗎？`)) {
                    try {
                        const response = await window.fetchAuthenticated(`/api/v1/role-permissions/${roleId}`, { method: 'DELETE' });
                        const result = await response.json();
                        if (response.ok && result.code === 200) {
                            showGlobalSuccess(result.message || '角色已刪除');
                            fetchRoles(); // Refresh page, ideally current page
                        } else {
                            showGlobalError(`刪除失敗: ${result.message || '未知錯誤'}`);
                        }
                    } catch (err) {
                        showGlobalError('刪除角色時發生錯誤');
                        console.error('Delete error:', err);
                    }
                }
            });
        });
    }

    // Initial load
    fetchRoles();
}); 