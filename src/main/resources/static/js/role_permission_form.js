// role_permission_form.js
document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('role-permission-form');
    const roleIdField = document.getElementById('roleId');
    const roleCodeField = document.getElementById('roleCode');
    const roleNameField = document.getElementById('roleName');
    const roleDescriptionField = document.getElementById('roleDescription');
    const functionPermissionsContainer = document.getElementById('function-permissions-container');
    // const fieldPermissionsContainer = document.getElementById('field-permissions-container'); // TODO: Implement field permissions UI
    const regionCheckboxes = document.querySelectorAll('.region-checkbox');

    const submitBtn = document.getElementById('submit-btn');
    const formModeBreadcrumb = document.getElementById('form-mode-breadcrumb');
    const formTitle = document.getElementById('form-title');
    const formErrorMessage = document.getElementById('form-error-message');

    const API_URL = '/api/v1/role-permissions';
    const urlParams = new URLSearchParams(window.location.search);
    const currentRoleId = urlParams.get('id');
    const mode = urlParams.get('mode') || (currentRoleId ? 'edit' : 'add'); // Default to edit if ID exists

    let allFunctions = [];
    const permissionLabels = {
        "CREATE": "新增", "READ": "讀取", "UPDATE": "更新", "DELETE": "刪除",
        "APPROVE": "審核", "CHANGE_PRICE": "改價", "PRINT": "列印",
        "STORE_APPROVE": "門市審核", "DISPATCH_APPROVE": "派工審核"
    };

    function showFormError(message) {
        if (formErrorMessage) {
            formErrorMessage.textContent = message;
            formErrorMessage.classList.remove('d-none');
        }
    }

    function clearError() {
        if (formErrorMessage) {
            formErrorMessage.classList.add('d-none');
            formErrorMessage.textContent = '';
        }
    }

    async function fetchSystemFunctions() {
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/system-functions`);
            if (!response.ok) {
                let errorMsg = `讀取系統功能失敗: ${response.status}`;
                try {
                    const errData = await response.json(); // Attempt to get more info from body
                    errorMsg = errData.message || errorMsg;
                } catch (e) { /* ignore if response body is not json or empty */}
                throw new Error(errorMsg);
            }
            const apiResult = await response.json();
            if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
                return apiResult.data; // This should be the List<SystemFunctionResponseDto>
            } else {
                console.error("Unexpected response structure for system functions:", apiResult);
                throw new Error(apiResult.message || '讀取系統功能回應格式錯誤');
            }
        } catch (error) {
            console.error('Failed to fetch or parse system functions:', error);
            // Propagate error to be caught by initializeForm
            throw error; // Re-throw to be caught by initializeForm's catch block
        }
    }

    async function initializeForm() {
        formErrorMessage.classList.add('d-none');
        try {
            console.log("Initializing form, fetching system functions...");
            allFunctions = await fetchSystemFunctions();
            console.log("Fetched System Functions for rendering:", allFunctions); 
            await loadAndRenderPermissions();
            if (mode === 'edit' || mode === 'view') {
                await loadRolePermissionData();
            }
        } catch (error) {
            formErrorMessage.textContent = `無法載入系統功能: ${error.message}`;
            formErrorMessage.classList.remove('d-none');
            console.error("Error during form initialization (system functions):", error);
            return; // Stop further processing if functions can't be loaded
        }

        if (mode === 'add') {
            formModeBreadcrumb.textContent = '新增角色權限';
            formTitle.textContent = '新增角色權限';
            submitBtn.textContent = '儲存設定';
        } else if (mode === 'edit') {
            formModeBreadcrumb.textContent = '編輯角色權限';
            formTitle.textContent = '編輯角色權限';
            submitBtn.textContent = '確認編輯';
        } else { // Should not happen for a form, but good to handle
            formModeBreadcrumb.textContent = '查看角色權限';
            formTitle.textContent = '查看角色權限';
            submitBtn.style.display = 'none';
            disableAllFormFields(true);
        }

        attachPermissionToggleListeners();
    }
    
    function disableAllFormFields(disable) {
        form.querySelectorAll('input, select, textarea, button').forEach(el => {
            if(el.id !== 'form-mode-breadcrumb' && !el.classList.contains('btn-outline-secondary')) { // don't disable breadcrumb or back button
                 el.disabled = disable;
            }
        });
    }

    async function loadAndRenderPermissions() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/role-permissions/system-functions');
            const result = await response.json();
            if (result.code === 200 && Array.isArray(result.data)) {
                allFunctions = result.data;
                const container = document.getElementById('function-permissions-container');
                container.innerHTML = buildPermissionTree(allFunctions);
            }
        } catch (error) {
            console.error('Failed to load system functions:', error);
            throw error;
        }
    }

    async function loadAndApplyRolePermissions(roleId) {
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/${roleId}`);
            if (!response.ok) throw new Error('無法讀取角色資料');
            const result = await response.json();
            if (result.code === 200 && result.data) {
                const roleData = result.data;
                // 填充功能權限
                if (roleData.functionPermissions && Array.isArray(roleData.functionPermissions)) {
                    roleData.functionPermissions.forEach(perm => {
                        Object.keys(perm).forEach(key => {
                            if (key.startsWith('can') && perm[key] === 1) {
                                const permKey = key.substring(3).toUpperCase().replace(/([A-Z])/g, '_$1').slice(1);
                                const checkbox = document.getElementById(`perm-${perm.systemFunctionId}-${permKey}`);
                                if (checkbox) checkbox.checked = true;
                            }
                        });
                    });
                }
                // 填充其他資料...
            }
        } catch (error) {
            console.error('Failed to apply role permissions:', error);
            showFormError(`套用角色權限失敗: ${error.message}`);
        }
    }

    function buildPermissionTree(nodes) {
        if (!nodes || nodes.length === 0) {
            return '';
        }
        let html = '<ul class="list-unstyled ps-3">';
        
        nodes.forEach(node => {
            // 規則 1: is_show == 0, 則完全不顯示
            if (node.isShow === 0) {
                return; 
            }

            html += `<li class="mb-3 p-2 border-bottom" data-function-id="${node.id}">`; // Add data-function-id for event delegation
            html += `<div class="fw-bold">${node.name} (${node.code})</div>`;
            
            // 規則 2: funtion_type != 0 (非群組) 且有可用權限，才顯示勾選框
            if (node.functionType !== 0 && node.availablePermissions && node.availablePermissions.length > 0) {
                // 加入全選/全不選按鈕
                html += `
                    <div class="my-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm permission-toggle-btn" data-action="select-all">全選</button>
                        <button type="button" class="btn btn-outline-secondary btn-sm permission-toggle-btn" data-action="deselect-all">全不選</button>
                    </div>
                `;
                html += '<div class="d-flex flex-wrap gap-3 ps-3 mt-2">';
                node.availablePermissions.forEach(perm => {
                    const label = permissionLabels[perm] || perm;
                    html += `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="${perm}" id="perm-${node.id}-${perm}" data-function-id="${node.id}" data-permission-key="${perm}">
                            <label class="form-check-label" for="perm-${node.id}-${perm}">${label}</label>
                        </div>
                    `;
                });
                html += '</div>';
            }

            // 遞歸渲染子節點
            if (node.children && node.children.length > 0) {
                html += buildPermissionTree(node.children);
            }
            html += '</li>';
        });

        html += '</ul>';
        return html;
    }

    function applyPermissionsToTree(permissions) {
        if (!permissions || !Array.isArray(permissions)) return;

        // 先清除所有勾選
        document.querySelectorAll('#function-permissions-container .form-check-input').forEach(cb => cb.checked = false);

        const keyToPermMap = {
            canCreate: 'CREATE', canRead: 'READ', canUpdate: 'UPDATE', canDelete: 'DELETE',
            canApprove: 'APPROVE', canChangePrice: 'CHANGE_PRICE', canPrint: 'PRINT',
            canStoreApprove: 'STORE_APPROVE', canDispatchApprove: 'DISPATCH_APPROVE'
        };

        // 根據數據勾選
        permissions.forEach(perm => {
            Object.keys(perm).forEach(key => {
                if (key.startsWith('can') && perm[key] === 1) {
                    const permKey = keyToPermMap[key];
                    if (!permKey) return; // 如果找不到對應的權限鍵，則跳過

                    const checkbox = document.getElementById(`perm-${perm.systemFunctionId}-${permKey}`);
                    if (checkbox) {
                        checkbox.checked = true;
                    } else {
                        console.warn(`Checkbox not found for id: perm-${perm.systemFunctionId}-${permKey}`);
                    }
                }
            });
        });
    }

    function collectPermissions() {
        const permissionsMap = new Map();
        const allCheckboxes = document.querySelectorAll('#function-permissions-container .form-check-input');

        allCheckboxes.forEach(checkbox => {
            const functionId = checkbox.dataset.functionId;
            if (!functionId) return;

            // 初始化 DTO
            if (!permissionsMap.has(functionId)) {
                permissionsMap.set(functionId, {
                    systemFunctionId: functionId,
                    canCreate: 0, canRead: 0, canUpdate: 0, canDelete: 0,
                    canApprove: 0, canChangePrice: 0, canPrint: 0,
                    canStoreApprove: 0, canDispatchApprove: 0
                });
            }

            // 如果被勾選，則更新對應的權限值
            if (checkbox.checked) {
                const permissionKey = checkbox.dataset.permissionKey;
                const dtoKey = `can${permissionKey.charAt(0).toUpperCase() + permissionKey.slice(1).toLowerCase().replace(/_([a-z])/g, g => g[1].toUpperCase())}`;
                
                const permDto = permissionsMap.get(functionId);
                if (permDto.hasOwnProperty(dtoKey)) {
                    permDto[dtoKey] = 1;
                }
            }
        });

        return Array.from(permissionsMap.values());
    }

    async function loadRolePermissionData() {
        if (!currentRoleId) return;
        clearError();
        console.log(`Loading role data for ID: ${currentRoleId}`);
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/${currentRoleId}`);
            if (!response.ok) {
                let errorMsg = `讀取角色資料失敗: ${response.status}`;
                try { const errData = await response.json(); errorMsg = errData.message || errorMsg; } catch (e) { /* ignore */ }
                throw new Error(errorMsg);
            }
            const apiResult = await response.json();

            if (apiResult.code === 200 && apiResult.data) {
                const roleData = apiResult.data;
                console.log("Loaded Role Data for edit:", JSON.stringify(roleData, null, 2));

                roleIdField.value = roleData.roleId || '';
                roleCodeField.value = roleData.roleCode || '';
                roleNameField.value = roleData.roleName || '';
                roleDescriptionField.value = roleData.roleDescription || '';

                // 使用新方法應用權限
                applyPermissionsToTree(roleData.functionPermissions);

                regionCheckboxes.forEach(cb => cb.checked = false);
                if (roleData.regionCodes && Array.isArray(roleData.regionCodes)) {
                    roleData.regionCodes.forEach(code => {
                        const checkbox = document.querySelector(`.region-checkbox[value="${code}"]`);
                        if (checkbox) checkbox.checked = true;
                    });
                }
                
                if (mode === 'view') {
                    disableAllFormFields(true);
                }

            } else {
                showFormError(apiResult.message || '無法載入角色權限資料');
                console.error("Error in API response for role data:", apiResult);
            }
        } catch (error) {
            console.error('Error loading role data:', error);
            showFormError(`無法載入角色權限資料: ${error.message}`);
        }
    }

    form.addEventListener('submit', async function(event) {
        event.preventDefault();
        formErrorMessage.classList.add('d-none');

        const functionPermissions = collectPermissions();
        
        // TODO: Collect field permissions from UI once implemented
        const fieldPermissions = []; 

        const selectedRegionCodes = [];
        regionCheckboxes.forEach(cb => {
            if (cb.checked) selectedRegionCodes.push(cb.value);
        });

        const requestBody = {
            roleCode: roleCodeField.value,
            roleName: roleNameField.value,
            roleDescription: roleDescriptionField.value,
            functionPermissions: functionPermissions,
            fieldPermissions: fieldPermissions, // Will be empty for now
            regionCodes: selectedRegionCodes
        };

        // Confirmation Dialog
        const confirmMessage = currentRoleId ? "確定要儲存對此角色權限的變更嗎？" : "確定要新增此角色權限嗎？";
        if (!window.confirm(confirmMessage)) {
            return; // User cancelled
        }

        try {
            let response;
            let alertMessage = '';

            if (currentRoleId) { // Edit mode
                response = await window.fetchAuthenticated(`${API_URL}/${currentRoleId}`, {
                    method: 'PUT',
                    body: JSON.stringify(requestBody)
                });
                alertMessage = '角色權限已更新！';
            } else { // Add mode
                response = await window.fetchAuthenticated(API_URL, {
                    method: 'POST',
                    body: JSON.stringify(requestBody)
                });
                alertMessage = '角色權限已新增！';
            }

            const apiResult = await response.json();
            if (response.ok && (apiResult.code === 200 || apiResult.code === 201)) {
                // showGlobalSuccess(alertMessage); // Replaced with standard alert
                alert(alertMessage);
                setTimeout(() => { window.location.href = 'role_permissions_list.html'; }, 1000);
            } else {
                showFormError(apiResult.message || '儲存失敗');
            }
        } catch (error) {
            console.error('Error saving role permissions:', error);
            formErrorMessage.textContent = `儲存角色權限時發生錯誤: ${error.message}`;
            formErrorMessage.classList.remove('d-none');
        }
    });

    function attachPermissionToggleListeners() {
        const container = document.getElementById('function-permissions-container');
        container.addEventListener('click', function(event) {
            const target = event.target;
            if (target.classList.contains('permission-toggle-btn')) {
                const action = target.dataset.action;
                const parentLi = target.closest('li[data-function-id]');
                if (!parentLi) return;

                const checkboxes = parentLi.querySelectorAll('.form-check-input');
                const shouldBeChecked = action === 'select-all';
                
                checkboxes.forEach(cb => {
                    cb.checked = shouldBeChecked;
                });
            }
        });
    }

    initializeForm();
}); 