// announcement_form.js
document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('announcement-form');
    const announcementIdField = document.getElementById('announcementId');
    const categoryCodeField = document.getElementById('categoryCode');
    const titleField = document.getElementById('title');
    const contentField = document.getElementById('content');
    const startTimeField = document.getElementById('startTime');
    const endTimeField = document.getElementById('endTime');
    const isImportantField = document.getElementById('isImportant');
    const isEnabledField = document.getElementById('isEnabled');
    const targetsContainer = document.getElementById('targets-container');
    const addTargetBtn = document.getElementById('add-target-btn');
    const submitBtn = document.getElementById('submit-btn');
    const formModeBreadcrumb = document.getElementById('form-mode-breadcrumb');
    const formTitle = document.getElementById('form-title');
    const formErrorMessage = document.getElementById('form-error-message');

    const API_URL = '/api/v1/announcements';
    const CATEGORIES_API_URL = '/api/v1/enums/announcement-categories';
    const TARGET_TYPES_API_URL = '/api/v1/enums/announcement-target-types';

    const urlParams = new URLSearchParams(window.location.search);
    const currentId = urlParams.get('id');
    const mode = urlParams.get('mode') || (currentId ? 'view' : 'add');

    let categoriesData = [];
    let targetTypesData = [];

    async function fetchCategories() {
        console.log("ANNOUNCEMENT_FORM.JS: Fetching categories from new endpoint:", CATEGORIES_API_URL);
        if (!categoryCodeField) { console.warn("ANNOUNCEMENT_FORM.JS: categoryCodeField not found."); return; }
        try {
            categoryCodeField.innerHTML = '<option value="">載入中...</option>';
            const response = await window.fetchAuthenticated(CATEGORIES_API_URL);
            if (!response.ok)  {
                const errText = await response.text();
                throw new Error(`HTTP error! status: ${response.status} - ${errText}`);
            }
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                categoriesData = apiResult.data;
                console.log("ANNOUNCEMENT_FORM.JS: Categories fetched:", categoriesData);
                populateCategoryDropdown();
            } else {
                throw new Error(apiResult.message || '無法載入公告類別資料');
            }
        } catch (error) {
            console.error('Error fetching categories:', error);
            categoryCodeField.innerHTML = '<option value="">類別載入失敗</option>';
            if(formErrorMessage) {
                formErrorMessage.textContent = `無法載入公告類別: ${error.message}`;
                formErrorMessage.classList.remove('d-none');
            }
            if(window.showToast) window.showToast('載入公告類別失敗: ' + error.message, 'error');
        }
    }

    async function fetchTargetTypes() {
        console.log("ANNOUNCEMENT_FORM.JS: Fetching target types from new endpoint:", TARGET_TYPES_API_URL);
        try {
            const response = await window.fetchAuthenticated(TARGET_TYPES_API_URL);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                targetTypesData = apiResult.data;
                console.log("ANNOUNCEMENT_FORM.JS: Target types fetched:", targetTypesData);
            } else {
                throw new Error(apiResult.message || '無法載入目標對象類型資料');
            }
        } catch (error) {
            console.error('Error fetching target types:', error);
            if(formErrorMessage) {
                formErrorMessage.textContent = `無法載入目標對象類型: ${error.message}`;
                formErrorMessage.classList.remove('d-none');
            }
             if(window.showToast) window.showToast('載入目標對象類型失敗: ' + error.message, 'error');
        }
    }

    function populateCategoryDropdown() {
        if (!categoryCodeField || !categoriesData) return;
        console.log("ANNOUNCEMENT_FORM.JS: Populating category dropdown with data:", categoriesData);
        const currentValue = categoryCodeField.value;
        categoryCodeField.innerHTML = '<option value="">選擇類別</option>';
        categoriesData.forEach(cat => {
            const option = document.createElement('option');
            option.value = cat.value; 
            option.textContent = cat.label;
            categoryCodeField.appendChild(option);
        });
        if (currentValue) categoryCodeField.value = currentValue;
    }

    function setupFormForMode() {
        if(formErrorMessage) formErrorMessage.classList.add('d-none');
        if (mode === 'add') {
            if(formModeBreadcrumb) formModeBreadcrumb.textContent = '新增公告';
            if(formTitle) formTitle.textContent = '新增系統公告';
            if(submitBtn) submitBtn.textContent = '儲存公告';
            if(addTargetBtn) addTargetBtn.style.display = '';
            if(targetsContainer && targetsContainer.children.length === 0) addTargetRow();
        } else if (mode === 'edit') {
            if(formModeBreadcrumb) formModeBreadcrumb.textContent = '編輯公告';
            if(formTitle) formTitle.textContent = '編輯系統公告';
            if(submitBtn) submitBtn.textContent = '確認編輯';
            if(addTargetBtn) addTargetBtn.style.display = '';
            loadAnnouncementData();
        } else { // view mode
            if(formModeBreadcrumb) formModeBreadcrumb.textContent = '查看公告';
            if(formTitle) formTitle.textContent = '查看系統公告';
            if(submitBtn) submitBtn.style.display = 'none';
            if(addTargetBtn) addTargetBtn.style.display = 'none';
            disableFormFields(true);
            loadAnnouncementData();
        }
    }
    
    function disableFormFields(disable) {
        const fields = [categoryCodeField, titleField, contentField, startTimeField, endTimeField, isImportantField, isEnabledField];
        fields.forEach(field => { if(field) field.disabled = disable; });
        document.querySelectorAll('.target-row select, .target-row input, .target-row button').forEach(el => el.disabled = disable);
    }

    async function loadAnnouncementData() {
        if (!currentId) return;
        console.log("ANNOUNCEMENT_FORM.JS: Loading announcement data for ID:", currentId);
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/${currentId}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResult = await response.json();

            if (apiResult.code === 200 && apiResult.data) {
                const data = apiResult.data;
                console.log("ANNOUNCEMENT_FORM.JS: Announcement data fetched:", data);
                if(announcementIdField) announcementIdField.value = data.announcementId;
                if (categoriesData.length > 0) {
                    if(categoryCodeField) categoryCodeField.value = data.categoryCode;
                } else {
                    if(categoryCodeField) categoryCodeField.dataset.pendingValue = data.categoryCode;
                }
                if(titleField) titleField.value = data.title;
                if(contentField) contentField.value = data.content;
                if (startTimeField && data.startTime) startTimeField.value = data.startTime.slice(0, 16);
                if (endTimeField && data.endTime) endTimeField.value = data.endTime.slice(0, 16);
                if(isImportantField) isImportantField.checked = data.isImportant;
                if(isEnabledField) isEnabledField.checked = data.isEnabled;

                if(targetsContainer) targetsContainer.innerHTML = '';
                if (data.targets && data.targets.length > 0) {
                    data.targets.forEach(target => addTargetRow(target.targetType, target.targetIdentifier, mode ==='view'));
                } else if (mode !== 'view') {
                }
            } else {
                throw new Error(apiResult.message || '無法載入公告資料');
            }

        } catch (error) {
            console.error('Error loading announcement data:', error);
            if(formErrorMessage) {
                formErrorMessage.textContent = `無法載入公告資料: ${error.message}`;
                formErrorMessage.classList.remove('d-none');
            }
            if(window.showToast) window.showToast('無法載入公告資料: ' + error.message, 'error');
        }
    }

    function addTargetRow(typeCode = null, identifier = null, isViewMode = false) {
        if (!targetsContainer || !targetTypesData) return;
        console.log("ANNOUNCEMENT_FORM.JS: Adding target row. TypeCode:", typeCode, "Identifier:", identifier, "isViewMode:", isViewMode);
        const rowDiv = document.createElement('div');
        rowDiv.className = 'target-row input-group input-group-sm mb-2';

        const typeSelect = document.createElement('select');
        typeSelect.className = 'form-select target-type';
        typeSelect.innerHTML = '<option value="">選擇類型</option>';
        targetTypesData.forEach(tt => {
            const option = document.createElement('option');
            option.value = tt.value;       
            option.textContent = tt.label; 
            if (typeCode && String(tt.code) === String(typeCode)) {
                 option.selected = true;
            }
            typeSelect.appendChild(option);
        });

        const identifierInput = document.createElement('input');
        identifierInput.type = 'text';
        identifierInput.className = 'form-control target-identifier';
        identifierInput.placeholder = '部門ID/員工ID';
        if (identifier) identifierInput.value = identifier;

        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'btn btn-outline-danger remove-target-btn';
        removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
        removeBtn.onclick = () => rowDiv.remove();
        
        if(isViewMode) {
            typeSelect.disabled = true;
            identifierInput.disabled = true;
            removeBtn.style.display = 'none';
        }

        rowDiv.appendChild(typeSelect);
        rowDiv.appendChild(identifierInput);
        if (!isViewMode) {
            rowDiv.appendChild(removeBtn);
        }
        targetsContainer.appendChild(rowDiv);
    }

    if(addTargetBtn) {
        addTargetBtn.addEventListener('click', () => addTargetRow(null,null, mode === 'view'));
    }

    if(form) {
        form.addEventListener('submit', async function(event) {
            event.preventDefault();
            if(formErrorMessage) formErrorMessage.classList.add('d-none');
            console.log("ANNOUNCEMENT_FORM.JS: Form submitted.");

            const targets = [];
            document.querySelectorAll('.target-row').forEach(row => {
                const typeEl = row.querySelector('.target-type');
                const identifierEl = row.querySelector('.target-identifier');
                if (typeEl && identifierEl) {
                    const type = typeEl.value;
                    const identifier = identifierEl.value;
                    if (type && identifier) {
                        targets.push({ targetType: type, targetIdentifier: identifier });
                    }
                }
            });

            const requestBody = {
                categoryCode: categoryCodeField ? categoryCodeField.value : null,
                title: titleField ? titleField.value : null,
                content: contentField ? contentField.value : null,
                startTime: startTimeField && startTimeField.value ? new Date(startTimeField.value).toISOString() : null,
                endTime: endTimeField && endTimeField.value ? new Date(endTimeField.value).toISOString() : null,
                isImportant: isImportantField ? isImportantField.checked : false,
                isEnabled: isEnabledField ? isEnabledField.checked : true,
                targets: targets
            };
            console.log("ANNOUNCEMENT_FORM.JS: Submit payload:", requestBody);

            if (requestBody.startTime && requestBody.endTime && requestBody.endTime <= requestBody.startTime) {
                if(formErrorMessage) {
                    formErrorMessage.textContent = '結束時間必須晚於開始時間。';
                    formErrorMessage.classList.remove('d-none');
                }
                if(window.showToast) window.showToast('結束時間必須晚於開始時間。','error');
                return;
            }

            // Confirmation Dialog
            const confirmMessage = mode === 'add' ? "確定要新增此公告嗎？" : "確定要儲存對此公告的變更嗎？";
            if (!window.confirm(confirmMessage)) {
                return; // User cancelled
            }

            try {
                let response;
                let alertMessage = '';
                const fetchOptions = {
                    method: mode === 'add' ? 'POST' : 'PUT',
                    body: JSON.stringify(requestBody)
                };
                const url = mode === 'add' ? API_URL : `${API_URL}/${currentId}`;

                console.log(`ANNOUNCEMENT_FORM.JS: Submitting to ${url} with method ${fetchOptions.method}`);
                response = await window.fetchAuthenticated(url, fetchOptions);
                alertMessage = mode === 'add' ? '公告已成功新增！' : '公告已成功更新！';
                
                const apiResult = await response.json();
                console.log("ANNOUNCEMENT_FORM.JS: Submit response:", apiResult);

                if (response.ok && (apiResult.code === 201 || apiResult.code === 200)) {
                    if(window.showToast) window.showToast(alertMessage, 'success');
                    else alert(alertMessage);
                    window.location.href = 'announcements.html'; 
                } else {
                     throw new Error(apiResult.message || `儲存失敗: ${response.status}`);
                }
            } catch (error) {
                console.error('Error saving announcement:', error);
                if(formErrorMessage) {
                    formErrorMessage.textContent = `儲存公告時發生錯誤: ${error.message}`;
                    formErrorMessage.classList.remove('d-none');
                }
                 if(window.showToast) window.showToast(`儲存公告時發生錯誤: ${error.message}`, 'error');
            }
        });
    }

    async function initializeForm() {
        console.log("ANNOUNCEMENT_FORM.JS: initializeForm() called. Mode:", mode);
        await Promise.all([fetchCategories(), fetchTargetTypes()]);
        setupFormForMode(); 
        if (categoryCodeField && categoryCodeField.dataset.pendingValue && categoriesData.length > 0) {
            console.log("ANNOUNCEMENT_FORM.JS: Applying pending category value:", categoryCodeField.dataset.pendingValue);
            categoryCodeField.value = categoryCodeField.dataset.pendingValue;
            delete categoryCodeField.dataset.pendingValue;
        }
        console.log("ANNOUNCEMENT_FORM.JS: Form initialization complete.");
    }

    initializeForm();
}); 