/**
 * 可共用的地址輸入元件
 * 負責處理縣市、鄉鎮市區、街路三級連動下拉選單
 */
class AddressComponent {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.error(`AddressComponent: Container with id '${containerId}' not found.`);
            return;
        }
        this.options = {
            showLabels: true,
            ...options
        };
        this.render();
        this.initEventListeners();
        this.loadCities();
    }

    render() {
        this.container.innerHTML = `
            <div class="row g-2">
                <div class="col-md-3">
                    ${this.options.showLabels ? '<label for="address-city" class="form-label">縣市</label>' : ''}
                    <select id="address-city" class="form-select" required>
                        <option value="" selected disabled>請選擇縣市</option>
                    </select>
                </div>
                <div class="col-md-3">
                    ${this.options.showLabels ? '<label for="address-district" class="form-label">鄉鎮市區</label>' : ''}
                    <select id="address-district" class="form-select" required>
                        <option value="" selected disabled>請先選擇縣市</option>
                    </select>
                </div>
                <div class="col-md-6">
                    ${this.options.showLabels ? '<label for="address-street" class="form-label">街路</label>' : ''}
                    <select id="address-street" class="form-select">
                         <option value="" selected disabled>請先選擇鄉鎮市區</option>
                    </select>
                </div>
            </div>
            <div class="row g-2 mt-1">
                <div class="col">
                    <div class="input-group">
                        <input type="text" id="address-lane" class="form-control">
                        <span class="input-group-text">巷</span>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <input type="text" id="address-alley" class="form-control">
                        <span class="input-group-text">弄</span>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <input type="text" id="address-number" class="form-control">
                        <span class="input-group-text">號</span>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <input type="text" id="address-floor" class="form-control">
                        <span class="input-group-text">樓</span>
                    </div>
                </div>
                <div class="col">
                    <div class="input-group">
                        <span class="input-group-text">之</span>
                        <input type="text" id="address-unit" class="form-control">
                    </div>
                </div>
            </div>
        `;
        this.elements = {
            city: this.container.querySelector('#address-city'),
            district: this.container.querySelector('#address-district'),
            street: this.container.querySelector('#address-street'),
            lane: this.container.querySelector('#address-lane'),
            alley: this.container.querySelector('#address-alley'),
            number: this.container.querySelector('#address-number'),
            floor: this.container.querySelector('#address-floor'),
            unit: this.container.querySelector('#address-unit'),
        };
    }

    initEventListeners() {
        this.elements.city.addEventListener('change', () => this.onCityChange());
        this.elements.district.addEventListener('change', () => this.onDistrictChange());
    }

    async loadCities() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/address/cities');
            const result = await response.json();
            if (result.code === 200) {
                this.populateSelect(this.elements.city, result.data, 'cityName', 'cityId');
                this.elements.city.dispatchEvent(new Event('citiesLoaded'));
            }
        } catch (error) {
            console.error('Failed to load cities:', error);
        }
    }

    async onCityChange() {
        const cityId = this.elements.city.value;
        this.resetSelect(this.elements.district, '請選擇鄉鎮市區');
        this.resetSelect(this.elements.street, '請先選擇鄉鎮市區');
        if (!cityId) return;

        try {
            const response = await window.fetchAuthenticated(`/api/v1/address/districts/${cityId}`);
            const result = await response.json();
            if (result.code === 200) {
                this.populateSelect(this.elements.district, result.data, 'districtName', 'districtId');
            }
        } catch (error) {
            console.error('Failed to load districts:', error);
        }
    }

    async onDistrictChange() {
        const districtSelect = this.elements.district;
        const districtName = districtSelect.options[districtSelect.selectedIndex]?.text;
        const citySelect = this.elements.city;
        const cityName = citySelect.options[citySelect.selectedIndex]?.text;
        
        this.resetSelect(this.elements.street, '請選擇街路');
        if (!districtName || districtName === '請選擇鄉鎮市區' || !cityName) return;

        try {
            const response = await window.fetchAuthenticated(`/api/v1/address/streets?cityName=${cityName}&districtName=${districtName}`);
            const result = await response.json();
            if (result.code === 200) {
                const streetData = result.data.map(item => ({ name: item.streetName, id: item.streetName }));
                this.populateSelect(this.elements.street, streetData, 'name', 'id');
            }
        } catch (error) {
            console.error('Failed to load streets:', error);
        }
    }

    populateSelect(selectElement, items, textKey, valueKey) {
        // Keep the first disabled option
        selectElement.innerHTML = `<option value="" selected disabled>${selectElement.options[0].text}</option>`;
        items.forEach(item => {
            const option = document.createElement('option');
            option.textContent = item[textKey];
            option.value = item[valueKey];
            selectElement.appendChild(option);
        });
    }

    resetSelect(selectElement, defaultText) {
        selectElement.innerHTML = `<option value="" selected disabled>${defaultText}</option>`;
    }
    
    getData() {
        const getSelectedText = (select) => select.selectedIndex > 0 ? select.options[select.selectedIndex].text : '';
        const city = getSelectedText(this.elements.city);
        const district = getSelectedText(this.elements.district);
        const street = getSelectedText(this.elements.street);
        const lane = this.elements.lane.value;
        const alley = this.elements.alley.value;
        const number = this.elements.number.value;
        const floor = this.elements.floor.value;
        const unit = this.elements.unit.value;

        const fullAddress = `${city}${district}${street}` +
                            (lane ? `${lane}巷` : '') +
                            (alley ? `${alley}弄` : '') +
                            (number ? `${number}號` : '') +
                            (floor ? `${floor}樓` : '') +
                            (unit ? `之${unit}` : '');

        return {
            addressCityName: city,
            addressDistrictName: district,
            addressStreetName: street,
            addressLane: lane,
            addressAlley: alley,
            addressNumber: number,
            addressFloor: floor,
            addressUnit: unit,
            fullAddress: fullAddress
        };
    }
    
    setData(data) {
        if (!data) return;
        
        const waitForCities = new Promise(resolve => {
            if (this.elements.city.options.length > 1) resolve();
            else this.elements.city.addEventListener('citiesLoaded', resolve, { once: true });
        });

        waitForCities.then(async () => {
            this.elements.city.value = Array.from(this.elements.city.options).find(opt => opt.text === data.addressCityName)?.value || '';
            await this.onCityChange();

            this.elements.district.value = Array.from(this.elements.district.options).find(opt => opt.text === data.addressDistrictName)?.value || '';
            await this.onDistrictChange();

            this.elements.street.value = data.addressStreetName || '';
            this.elements.lane.value = data.addressLane || '';
            this.elements.alley.value = data.addressAlley || '';
            this.elements.number.value = data.addressNumber || '';
            this.elements.floor.value = data.addressFloor || '';
            this.elements.unit.value = data.addressUnit || '';
        });
    }
} 