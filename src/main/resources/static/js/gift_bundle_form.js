document.addEventListener('DOMContentLoaded', function () {
    const API_BASE_URL = '/api/v1/gift-bundles';
    const EXTERNAL_PRODUCTS_API_URL = '/api/v1/external-products';

    const form = document.getElementById('gift-bundle-form');
    const bundleIdField = document.getElementById('bundleId');
    const bundleNameField = document.getElementById('bundleName');
    const mainProductDisplayField = document.getElementById('mainProductDisplay');
    const mainProductBarcodeField = document.getElementById('mainProductBarcode');
    const selectMainProductBtn = document.getElementById('selectMainProductBtn');
    const startTimeField = document.getElementById('startTime');
    const endTimeField = document.getElementById('endTime');
    const isActiveField = document.getElementById('isActive');
    const addGiftItemBtn = document.getElementById('addGiftItemBtn');
    const giftItemsTableBody = document.getElementById('giftItemsTableBody');
    const noGiftItemsMessage = document.getElementById('noGiftItemsMessage');
    
    const productSearchModalEl = document.getElementById('productSearchModal');
    const productSearchModal = new bootstrap.Modal(productSearchModalEl);
    const productSearchModalLabel = document.getElementById('productSearchModalLabel');
    const productSearchKeywordInput = document.getElementById('productSearchKeywordInput');
    const productSearchExecuteBtn = document.getElementById('productSearchExecuteBtn');
    const productSearchResultsContainer = document.getElementById('productSearchResultsContainer');
    const noModalProductResultsMessage = document.getElementById('noModalProductResultsMessage');
    
    const formErrorMessage = document.getElementById('bundleFormErrorMessage');
    const formModeBreadcrumb = document.getElementById('form-mode-breadcrumb');
    const formTitle = document.getElementById('form-title');

    const urlParams = new URLSearchParams(window.location.search);
    const currentBundleId = urlParams.get('id');
    const mode = urlParams.get('mode') || (currentBundleId ? 'edit' : 'add');
    
    let giftBundleItems = []; // { giftProductBarcode, giftProductName, quantity }
    let productSearchContext = 'main'; // 'main' or 'gift'

    function showError(message) {
        formErrorMessage.textContent = message;
        formErrorMessage.classList.remove('d-none');
    }
    function clearError() {
        formErrorMessage.classList.add('d-none');
        formErrorMessage.textContent = '';
    }

    async function initializeForm() {
        await loadSharedHTML();
        if (mode === 'edit' && currentBundleId) {
            formTitle.textContent = '編輯商品套裝';
            formModeBreadcrumb.textContent = '編輯商品套裝';
            loadBundleData(currentBundleId);
        } else {
            formTitle.textContent = '新增商品套裝';
            formModeBreadcrumb.textContent = '新增商品套裝';
            updateGiftItemsTable();
        }
    }

    async function loadBundleData(id) {
        try {
            const response = await window.fetchAuthenticated(`${API_BASE_URL}/${id}`);
            if (!response.ok) throw new Error('無法載入套裝資料');
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                const bundle = apiResult.data;
                bundleIdField.value = bundle.bundleId;
                bundleNameField.value = bundle.bundleName;
                mainProductBarcodeField.value = bundle.mainProductBarcode;
                mainProductDisplayField.value = `${bundle.mainProductName} (${bundle.mainProductBarcode})`;
                startTimeField.value = bundle.startTime ? bundle.startTime.slice(0, 16) : '';
                endTimeField.value = bundle.endTime ? bundle.endTime.slice(0, 16) : '';
                isActiveField.checked = bundle.isActive === undefined ? true : bundle.isActive;
                giftBundleItems = bundle.items || [];
                updateGiftItemsTable();
            } else {
                showError(apiResult.message || '無法載入套裝資料');
            }
        } catch (error) {
            console.error('Error loading bundle data:', error);
            showError('載入套裝資料時發生錯誤: ' + error.message);
        }
    }

    function updateGiftItemsTable() {
        giftItemsTableBody.innerHTML = '';
        if (giftBundleItems.length === 0) {
            noGiftItemsMessage.classList.remove('d-none');
            return;
        }
        noGiftItemsMessage.classList.add('d-none');
        giftBundleItems.forEach((item, index) => {
            const row = giftItemsTableBody.insertRow();
            row.insertCell().textContent = item.giftProductBarcode;
            row.insertCell().textContent = item.giftProductName || 'N/A';
            
            const qtyCell = row.insertCell();
            const qtyInput = document.createElement('input');
            qtyInput.type = 'number';
            qtyInput.className = 'form-control form-control-sm gift-item-quantity';
            qtyInput.value = item.quantity;
            qtyInput.min = "1";
            qtyInput.required = true;
            qtyInput.dataset.barcode = item.giftProductBarcode;
            qtyInput.addEventListener('change', (e) => {
                const barcode = e.target.dataset.barcode;
                const newQuantity = parseInt(e.target.value);
                const foundItem = giftBundleItems.find(it => it.giftProductBarcode === barcode);
                if (foundItem && newQuantity >= 1) {
                    foundItem.quantity = newQuantity;
                } else if (foundItem) {
                    e.target.value = foundItem.quantity; // Revert if invalid
                    showToast('數量必須至少為1', 'warning');
                }
            });
            qtyCell.appendChild(qtyInput);

            const deleteBtnCell = row.insertCell();
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-sm btn-outline-danger';
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            deleteBtn.onclick = () => {
                giftBundleItems.splice(index, 1);
                updateGiftItemsTable();
            };
            deleteBtnCell.appendChild(deleteBtn);
        });
    }

    selectMainProductBtn.addEventListener('click', () => {
        productSearchContext = 'main';
        productSearchModalLabel.textContent = '選擇主商品';
        productSearchKeywordInput.value = '';
        productSearchResultsContainer.innerHTML = '';
        noModalProductResultsMessage.classList.add('d-none');
        productSearchModal.show();
    });

    addGiftItemBtn.addEventListener('click', () => {
        productSearchContext = 'gift';
        productSearchModalLabel.textContent = '新增贈品';
        productSearchKeywordInput.value = '';
        productSearchResultsContainer.innerHTML = '';
        noModalProductResultsMessage.classList.add('d-none');
        productSearchModal.show();
    });

    productSearchExecuteBtn.addEventListener('click', async () => {
        const keyword = productSearchKeywordInput.value.trim();
        if (!keyword) {
            showToast('請輸入商品關鍵字', 'warning');
            return;
        }
        
        // Determine which API to call based on context
        let searchUrl = '';
        if (productSearchContext === 'main') {
            searchUrl = `/api/v1/product-settings/search-main-products?keyword=${encodeURIComponent(keyword)}`;
        } else { // 'gift'
            searchUrl = `/api/v1/product-settings/search-non-main-products?keyword=${encodeURIComponent(keyword)}`;
        }

        productSearchResultsContainer.innerHTML = '<div class="list-group-item">搜尋中...</div>';
        noModalProductResultsMessage.classList.add('d-none');
        try {
            const response = await window.fetchAuthenticated(searchUrl);
            if (!response.ok) throw new Error('商品搜尋失敗');
            
            // The new APIs directly return List<ProductSearchResultDto>, not wrapped in ApiResponse
            const products = await response.json();
            
            productSearchResultsContainer.innerHTML = '';
            if (products && products.length > 0) {
                products.forEach(product => {
                    const item = document.createElement('a');
                    item.href = '#';
                    item.className = 'list-group-item list-group-item-action product-search-item';
                    item.dataset.productBarcode = product.productBarcode;
                    item.dataset.productName = product.productName;
                    item.innerHTML = `${product.productName} <small class="text-muted">(${product.productBarcode})</small>`;
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        if (productSearchContext === 'main') {
                            mainProductBarcodeField.value = product.productBarcode;
                            mainProductDisplayField.value = `${product.productName} (${product.productBarcode})`;
                        } else if (productSearchContext === 'gift') {
                            if (!giftBundleItems.find(gi => gi.giftProductBarcode === product.productBarcode)) {
                                giftBundleItems.push({
                                    giftProductBarcode: product.productBarcode,
                                    giftProductName: product.productName,
                                    quantity: 1
                                });
                                updateGiftItemsTable();
                            } else {
                                showToast('此贈品已在列表中。', 'warning');
                            }
                        }
                        productSearchModal.hide();
                    });
                    productSearchResultsContainer.appendChild(item);
                });
            } else {
                noModalProductResultsMessage.classList.remove('d-none');
            }
        } catch (error) {
            console.error('Error searching products for bundle:', error);
            showToast('搜尋商品時發生錯誤', 'error');
            noModalProductResultsMessage.classList.remove('d-none');
        }
    });
    
    form.addEventListener('submit', async function(event) {
        event.preventDefault();
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            showToast('請檢查所有必填欄位。', 'warning');
            return;
        }
        form.classList.remove('was-validated');
        clearError();

        if (!mainProductBarcodeField.value) {
            showError('必須選擇一個主商品。');
            return;
        }
        if (giftBundleItems.length === 0) {
            showError('必須至少加入一個贈品。');
            return;
        }
        for (const item of giftBundleItems) {
            if (!item.quantity || item.quantity < 1) {
                showError(`贈品 ${item.giftProductName} 的數量必須至少為1。`);
                return;
            }
        }

        const payload = {
            bundleName: bundleNameField.value,
            mainProductBarcode: mainProductBarcodeField.value,
            mainProductName: mainProductDisplayField.value.split(' (')[0], // Extract name part
            startTime: startTimeField.value ? new Date(startTimeField.value).toISOString() : null,
            endTime: endTimeField.value ? new Date(endTimeField.value).toISOString() : null,
            isActive: isActiveField.checked,
            items: giftBundleItems
        };
        if (payload.endTime && payload.startTime && payload.endTime < payload.startTime) {
            showError("套裝結束時間不能早於開始時間。");
            return;
        }

        // Confirmation Dialog
        const confirmMessage = mode === 'edit' ? "確定要儲存對此商品套裝的變更嗎？" : "確定要新增此商品套裝嗎？";
        if (!window.confirm(confirmMessage)) {
            return; // User cancelled
        }

        const method = currentBundleId ? 'PUT' : 'POST';
        const url = currentBundleId ? `${API_BASE_URL}/${currentBundleId}` : API_BASE_URL;

        try {
            const response = await window.fetchAuthenticated(url, { method: method, body: JSON.stringify(payload) });
            const result = await response.json();
            if (response.ok && (result.code === (method === 'POST' ? 201 : 200))) {
                showToast(`商品套裝已成功${method === 'POST' ? '新增' : '更新'}！`, 'success');
                window.location.href = 'gift_bundles.html';
            } else {
                showError(result.message || '儲存失敗');
            }
        } catch (error) {
            console.error('Error saving gift bundle:', error);
            showError('儲存套裝時發生錯誤: ' + error.message);
        }
    });

    initializeForm();
}); 