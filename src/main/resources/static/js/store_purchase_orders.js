// static/js/store_purchase_orders.js
document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.getElementById('purchase-orders-table-body');
    const paginationControls = document.getElementById('pagination-controls');
    const filterForm = document.getElementById('filter-form');
    const noDataMessage = document.getElementById('no-data-message');

    const filterShipmentDateFrom = document.getElementById('filterShipmentDateFrom');
    const filterShipmentDateTo = document.getElementById('filterShipmentDateTo');
    const filterStatus = document.getElementById('filterStatus');
    const filterKeyword = document.getElementById('filterKeyword');
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');

    let currentPage = 0; // 0-indexed for API
    const pageSize = 10; 
    const API_URL = '/api/v1/store-purchase-orders'; // Placeholder, will be defined later
    const STATUS_ENUM_URL = '/api/v1/enums/store-purchase-order-statuses'; // Placeholder

    async function populateStatusFilter() {
        try {
            const response = await window.fetchAuthenticated(STATUS_ENUM_URL);
            if (!response.ok) throw new Error('Failed to fetch status enums');
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                filterStatus.innerHTML = '<option value="">全部狀態</option>';
                apiResult.data.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status.code; // Assuming EnumValueDto has code/description
                    option.textContent = status.description;
                    filterStatus.appendChild(option);
                });
            } else {
                console.warn('Could not load status options:', apiResult.message);
            }
        } catch (error) {
            console.error('Error populating status filter:', error);
            filterStatus.innerHTML = '<option value="">狀態載入失敗</option>';
        }
    }

    async function fetchPurchaseOrders(page = 0) {
        currentPage = page;
        let queryParams = `?page=${page}&size=${pageSize}&sort=shipmentTime,desc`;

        if (filterShipmentDateFrom.value) queryParams += `&shipmentDateFrom=${new Date(filterShipmentDateFrom.value).toISOString()}`;
        if (filterShipmentDateTo.value) queryParams += `&shipmentDateTo=${new Date(filterShipmentDateTo.value).toISOString()}`;
        if (filterStatus.value) queryParams += `&purchaseOrderStatus=${filterStatus.value}`;
        if (filterKeyword.value) queryParams += `&keyword=${encodeURIComponent(filterKeyword.value)}`;
        
        tableBody.innerHTML = `<tr><td colspan="8" class="text-center">載入中...</td></tr>`;

        try {
            const response = await window.fetchAuthenticated(API_URL + queryParams);
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
            const apiResult = await response.json();

            tableBody.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && apiResult.data.list && apiResult.data.list.length > 0) {
                noDataMessage.classList.add('d-none');
                apiResult.data.list.forEach((order, index) => {
                    const row = tableBody.insertRow();
                    row.insertCell().textContent = (currentPage * pageSize) + index + 1;
                    row.insertCell().textContent = order.purchaseOrderNumber;
                    row.insertCell().textContent = order.shipmentTime ? new Date(order.shipmentTime).toLocaleString() : '-';
                    row.insertCell().textContent = order.storeName || 'N/A';
                    row.insertCell().textContent = order.createdByEmployeeName || (order.createdByEmployeeId || '-');
                    
                    const statusCell = row.insertCell();
                    const statusBadge = document.createElement('span');
                    statusBadge.classList.add('badge');
                    // Assuming order.status is the code and order.statusDescription is the text
                    statusBadge.textContent = order.statusDescription || order.purchaseOrderStatus; 
                     switch (order.purchaseOrderStatus) {
                        case 0: statusBadge.classList.add('bg-info'); break; // 配送中
                        case 1: statusBadge.classList.add('bg-primary'); break; // 部分到貨
                        case 2: statusBadge.classList.add('bg-warning', 'text-dark'); break; // 已到貨(待確認)
                        case 3: statusBadge.classList.add('bg-danger'); break; // 數量異常
                        case 4: statusBadge.classList.add('bg-success'); break; // 已完成
                        default: statusBadge.classList.add('bg-secondary'); break;
                    }
                    statusCell.appendChild(statusBadge);
                    row.insertCell().textContent = order.originalWholesaleOrderId || '-';
                    
                    const actionsCell = row.insertCell();
                    actionsCell.innerHTML = `<a href="store_purchase_order_detail.html?id=${order.storePurchaseOrderId}" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i> 查看</a>`;
                });
                renderPagination(apiResult.data.page);
            } else {
                noDataMessage.classList.remove('d-none');
                tableBody.innerHTML = `<tr><td colspan="8" class="text-center">${apiResult.message || '查無進貨單資料'}</td></tr>`;
                paginationControls.innerHTML = '';
            }
        } catch (error) {
            console.error('Error fetching purchase orders:', error);
            tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">無法載入資料: ${error.message}</td></tr>`;
            noDataMessage.classList.remove('d-none');
            paginationControls.innerHTML = '';
        }
    }

    function renderPagination(pageData) {
        paginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 1) return;

        const createPageItem = (pageNum, text, isDisabled = false, isActive = false) => {
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = text;
            if (!isDisabled) {
                link.addEventListener('click', (e) => { e.preventDefault(); fetchPurchaseOrders(pageNum); });
            }
            li.appendChild(link);
            return li;
        };

        paginationControls.appendChild(createPageItem(pageData.page - 1, '上一頁', pageData.page === 0));
        
        const maxPagesToShow = 5;
        let startPage = Math.max(0, pageData.page - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(pageData.totalPages - 1, startPage + maxPagesToShow - 1);
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(0, endPage - maxPagesToShow + 1);
        }

        if (startPage > 0) {
            paginationControls.appendChild(createPageItem(0, '1'));
            if (startPage > 1) paginationControls.appendChild(createPageItem(-1, '...', true));
        }
        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i, i + 1, false, i === pageData.page));
        }
        if (endPage < pageData.totalPages - 1) {
            if (endPage < pageData.totalPages - 2) paginationControls.appendChild(createPageItem(-1, '...', true));
            paginationControls.appendChild(createPageItem(pageData.totalPages - 1, pageData.totalPages));
        }

        paginationControls.appendChild(createPageItem(pageData.page + 1, '下一頁', pageData.page >= pageData.totalPages - 1));
    }

    filterForm.addEventListener('submit', function(event) {
        event.preventDefault();
        fetchPurchaseOrders(0);
    });

    clearFiltersBtn.addEventListener('click', function() {
        filterForm.reset();
        fetchPurchaseOrders(0);
    });

    async function init() {
        await loadSharedHTML(); 
        await populateStatusFilter();
        fetchPurchaseOrders();
    }

    init();
}); 