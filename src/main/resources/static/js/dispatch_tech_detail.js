document.addEventListener('DOMContentLoaded', function () {
    const infoCardContainer = document.getElementById('info-card-container');
    const processStepsContainer = document.getElementById('process-steps-container');
    const inviteBtn = document.getElementById('invite-btn');
    const technicianListContainer = document.getElementById('technician-list-container');
    const submitInvitationBtn = document.getElementById('submit-invitation-btn');
    const inviteModal = new bootstrap.Modal(document.getElementById('inviteCollaboratorModal'));

    const urlParams = new URLSearchParams(window.location.search);
    const dispatchRepairId = urlParams.get('id');

    if (!dispatchRepairId) {
        infoCardContainer.innerHTML = '<div class="alert alert-danger">錯誤：找不到派工單ID。</div>';
        return;
    }

    fetchDispatchOrderDetail(dispatchRepairId);

    async function fetchDispatchOrderDetail(id) {
        try {
            const url = `/api/v1/dispatch-orders/${id}`;
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) {
                throw new Error('無法獲取派工單詳情');
            }
            const orderDetail = await response.json();
            renderDispatchOrderDetail(orderDetail);
        } catch (error) {
            console.error('獲取派工單詳情失敗:', error);
            infoCardContainer.innerHTML = `<div class="alert alert-danger">無法載入派工單詳情：${error.message}</div>`;
        }
    }

    function renderDispatchOrderDetail(order) {
        if (!order) {
            console.error("renderDispatchOrderDetail received undefined order object");
            return;
        }
        window.renderDispatchOrderHeader(order, infoCardContainer);
        renderProcessSteps(order);

        // 根據狀態隱藏按鈕(29:待排單)
        if (order.statusCode && order.statusCode >= 29) {
            const transferBtn = document.getElementById('transfer-btn');
            if (transferBtn) transferBtn.style.display = 'none';
            if (inviteBtn) inviteBtn.style.display = 'none';
        }
    }

    function renderProcessSteps(order) {
        const container = document.getElementById('process-steps-container');
        if (!container) return;

        const steps = [
            { name: '待聯繫', status: 26, href: `dispatch_step_contact.html?id=${order.id}` },
            { name: '待排單', status: 29, href: `dispatch_step_schedule.html?id=${order.id}` },
            { name: '待領料', status: 35, href: `dispatch_step_material.html?id=${order.id}` },
            { name: '在途中', status: 40, href: `dispatch_step_depart.html?id=${order.id}` },
            { name: '裝修中', status: 45, href: `dispatch_step_work.html?id=${order.id}` },
            { name: '收款中', status: 55, href: `dispatch_step_payment.html?id=${order.id}` },
            { name: '簽確中', status: 60, href: `dispatch_step_sign.html?id=${order.id}` },
            { name: '處理方式', status: 80, href: `dispatch_step_handling.html?id=${order.id}` }
        ];

        container.innerHTML = '';
        const currentStatusCode = order.statusCode;

        steps.forEach(step => {
            const isClickable = (currentStatusCode === step.status);
            const isCompleted = (currentStatusCode > step.status);
            
            let statusText = '未處理';
            let statusClass = 'bg-light text-dark';
            let itemClass = 'disabled';
            let href = isClickable ? step.href : '#';

            if (isCompleted) {
                statusText = '已完成';
                statusClass = 'bg-success';
            } else if (isClickable) {
                statusText = '進行中';
                statusClass = 'bg-primary';
                itemClass = 'active';
            }

            const stepEl = document.createElement('a');
            stepEl.href = href;
            stepEl.className = `list-group-item list-group-item-action d-flex justify-content-between align-items-center ${itemClass}`;
            
            stepEl.innerHTML = `
                <span>${step.name}</span>
                <div>
                    <span class="badge ${statusClass} me-2">${statusText}</span>
                    <i class="bi bi-chevron-right"></i>
                </div>
            `;
            container.appendChild(stepEl);
        });
    }

    async function loadTechniciansIntoModal() {
        try {
            technicianListContainer.innerHTML = '<p>正在載入可邀請的技師列表...</p>';
            // --- START: MODIFICATION ---
            // Changed the API endpoint to the new one that provides filtered results.
            const response = await window.fetchAuthenticated(`/api/v1/users/selectable-collaborators?dispatchRepairId=${dispatchRepairId}`);
            // --- END: MODIFICATION ---

            if (!response.ok) throw new Error('無法獲取技師列表');
            
            const apiResponse = await response.json();
            const technicians = apiResponse.data;
            
            // --- REMOVED: Client-side filtering is no longer needed as the backend handles it. ---

            if (!technicians || !Array.isArray(technicians) || technicians.length === 0) {
                technicianListContainer.innerHTML = '<p>目前沒有其他可邀請的技師。</p>';
                return;
            }
            
            let listHtml = '';
            technicians.forEach(tech => {
                // The new API already filters out ineligible technicians.
                listHtml += `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="${tech.userAccountId}" id="tech-${tech.userAccountId}">
                        <label class="form-check-label" for="tech-${tech.userAccountId}">
                            ${tech.userName}
                        </label>
                    </div>
                `;
            });
            technicianListContainer.innerHTML = listHtml;

        } catch (error) {
            console.error('載入技師列表失敗:', error);
            technicianListContainer.innerHTML = `<div class="alert alert-danger">無法載入技師列表：${error.message}</div>`;
        }
    }
    
    async function handleSubmitInvitation() {
        const selectedTechIds = Array.from(technicianListContainer.querySelectorAll('input[type="checkbox"]:checked'))
            .map(checkbox => checkbox.value);

        if (selectedTechIds.length === 0) {
            alert('請至少選擇一位技師。');
            return;
        }

        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}/collaborators`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ technicianIds: selectedTechIds })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '邀請失敗');
            }

            alert('邀請已成功送出！');
            inviteModal.hide();

        } catch (error) {
            console.error('邀請失敗:', error);
            alert(`邀請失敗：${error.message}`);
        }
    }

    inviteBtn.addEventListener('click', () => {
        loadTechniciansIntoModal();
        inviteModal.show();
    });

    submitInvitationBtn.addEventListener('click', handleSubmitInvitation);
}); 