console.log("WAREHOUSE_MANAGEMENT.JS: <PERSON>ript start.");

document.addEventListener('DOMContentLoaded', function () {
    console.log("WAREHOUSE_MANAGEMENT.JS: DOMContentLoaded event fired.");

    const zonesContainer = document.getElementById('zones-container');
    const addWarehouseModalElement = document.getElementById('addWarehouseModalTemplate');
    const mainWarehouseDisplayArea = document.getElementById('warehouse-display-area');
    
    let addWarehouseModal = null;
    if (addWarehouseModalElement) {
        try {
            addWarehouseModal = new bootstrap.Modal(addWarehouseModalElement);
            console.log("WAREHOUSE_MANAGEMENT.JS: Add warehouse modal initialized.");
        } catch (e) {
            console.error("WAREHOUSE_MANAGEMENT.JS: Error initializing addWarehouseModal:", e);
        }
    } else {
        console.warn("WAREHOUSE_MANAGEMENT.JS: addWarehouseModalTemplate element not found.");
    }
    const addWarehouseForm = document.getElementById('addWarehouseForm');
    const externalWarehouseSelect = document.getElementById('externalWarehouseSelect');
    const addWarehouseRegionNameSpan = document.getElementById('addWarehouseRegionName');
    const addWarehouseRegionIdInput = document.getElementById('addWarehouseRegionId');
    const newWarehouseCodeInput = document.getElementById('newWarehouseCode');
    const newWarehouseNameInput = document.getElementById('newWarehouseName');

    console.log("WAREHOUSE_MANAGEMENT.JS: Initial elements obtained.", {
        mainWarehouseDisplayArea: !!mainWarehouseDisplayArea,
        zonesContainer: !!zonesContainer,
        addWarehouseModalElement: !!addWarehouseModalElement,
        addWarehouseForm: !!addWarehouseForm,
        externalWarehouseSelect: !!externalWarehouseSelect
    });

    let currentRegionIdForAdd = null;
    let allRegions = []; 

    async function displayWarehousesInMainArea() {
        console.log("WAREHOUSE_MANAGEMENT.JS: displayWarehousesInMainArea() called.");
        if (!mainWarehouseDisplayArea) {
            console.error('WAREHOUSE_MANAGEMENT.JS: #warehouse-display-area element not found! Cannot display main content.');
            return;
        }
        mainWarehouseDisplayArea.innerHTML = '<p class="text-center">主畫面：正在載入區域與倉庫資訊...</p>';
        try {
            console.log('WAREHOUSE_MANAGEMENT.JS: Attempting to fetch active regions from /api/v1/regions/active');
            const regionsResponse = await window.fetchAuthenticated('/api/v1/regions/active');
            console.log('WAREHOUSE_MANAGEMENT.JS: Regions fetch response status:', regionsResponse.status);

            if (!regionsResponse.ok) {
                const errorText = await regionsResponse.text();
                console.error('WAREHOUSE_MANAGEMENT.JS: Failed to fetch regions. Status:', regionsResponse.status, 'Response:', errorText);
                throw new Error('無法載入區域資料: ' + regionsResponse.status);
            }
            const regionsApiResp = await regionsResponse.json();
            console.log('WAREHOUSE_MANAGEMENT.JS: Regions API Response data:', regionsApiResp);
            const regions = regionsApiResp.data || [];

            if (regions.length === 0) {
                mainWarehouseDisplayArea.innerHTML = '<p class="text-center">尚未設定任何區域。</p>';
                console.log('WAREHOUSE_MANAGEMENT.JS: No regions found.');
                return;
            }
            console.log(`WAREHOUSE_MANAGEMENT.JS: Found ${regions.length} regions.`, regions);

            let contentHtml = '<div class="row">';
            for (const region of regions) {
                contentHtml += `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header fw-bold">
                                ${region.regionName} (ID: ${region.regionId})
                            </div>
                            <ul class="list-group list-group-flush" id="main-warehouse-list-${region.regionId}">
                                <li class="list-group-item placeholder-glow"><span class="placeholder col-8">載入中...</span></li>
                            </ul>
                        </div>
                    </div>
                `;
            }
            contentHtml += '</div>';
            mainWarehouseDisplayArea.innerHTML = contentHtml;
            console.log('WAREHOUSE_MANAGEMENT.JS: Main region display structure rendered. Now fetching warehouses for each region.');

            for (const region of regions) {
                fetchAndDisplayMainWarehousesForRegion(region.regionId, region.regionName);
            }

        } catch (error) {
            console.error("WAREHOUSE_MANAGEMENT.JS: Error in displayWarehousesInMainArea:", error);
            mainWarehouseDisplayArea.innerHTML = '<p class="text-danger text-center">無法載入主要倉庫資訊。</p>';
            if(window.showToast) window.showToast('無法載入主要倉庫資訊: ' + error.message, 'error');
        }
    }

    async function fetchAndDisplayMainWarehousesForRegion(regionId, regionName) {
        const listElement = document.getElementById(`main-warehouse-list-${regionId}`);
        if (!listElement) {
            console.warn(`WAREHOUSE_MANAGEMENT.JS: List element for main display not found: main-warehouse-list-${regionId}`);
            return;
        }
        console.log(`WAREHOUSE_MANAGEMENT.JS: Fetching main warehouses for region ID: ${regionId} (${regionName})`);
        try {
            const response = await window.fetchAuthenticated(`/api/v1/warehouses/by-region/${regionId}`);
            console.log(`WAREHOUSE_MANAGEMENT.JS: Warehouses for region ${regionId} fetch response status:`, response.status);
            if (!response.ok) {
                const errorText = await response.text();
                console.error(`WAREHOUSE_MANAGEMENT.JS: Failed to fetch warehouses for region ${regionId}. Status:`, response.status, 'Response:', errorText);
                throw new Error(`無法載入 ${regionName} 區域的倉庫`);
            }
            const apiResp = await response.json();
            console.log(`WAREHOUSE_MANAGEMENT.JS: Warehouses API response for region ${regionId} (main display):`, apiResp);
            const warehouses = apiResp.data || [];

            listElement.innerHTML = ''; 
            if (warehouses.length === 0) {
                listElement.innerHTML = '<li class="list-group-item fst-italic">此區域尚無倉庫</li>';
            } else {
                warehouses.forEach(wh => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.textContent = `${wh.warehouseName} (${wh.warehouseCode})`;
                    listElement.appendChild(listItem);
                });
            }
            console.log(`WAREHOUSE_MANAGEMENT.JS: Successfully displayed warehouses for region ${regionId} in main area.`);
        } catch (error) {
            console.error(`WAREHOUSE_MANAGEMENT.JS: Error fetching/displaying main warehouses for region ${regionId}:`, error);
            listElement.innerHTML = '<li class="list-group-item text-danger"><em>載入倉庫失敗</em></li>';
        }
    }

    async function fetchRegionsAndWarehousesForModal() {
        console.log('WAREHOUSE_MANAGEMENT.JS: fetchRegionsAndWarehousesForModal() called.');
        if (!zonesContainer) {
            console.error("WAREHOUSE_MANAGEMENT.JS: #zones-container (for modal) not found!");
            return;
        }
        zonesContainer.innerHTML = '<p class="text-center">載入管理用區域資訊...</p>';
        try {
            console.log('WAREHOUSE_MANAGEMENT.JS: (Modal) Fetching active regions from /api/v1/regions/active');
            const regionsResponse = await window.fetchAuthenticated('/api/v1/regions/active'); 
            console.log('WAREHOUSE_MANAGEMENT.JS: (Modal) Regions fetch response status:', regionsResponse.status);
            if (!regionsResponse.ok) {
                const errorText = await regionsResponse.text();
                console.error('WAREHOUSE_MANAGEMENT.JS: (Modal) Failed to fetch regions. Status:', regionsResponse.status, 'Response:', errorText);
                throw new Error('無法為管理彈窗載入區域資料: ' + regionsResponse.status);
            }
            const regionsApiResp = await regionsResponse.json();
            allRegions = regionsApiResp.data || [];
            console.log('WAREHOUSE_MANAGEMENT.JS: (Modal) Regions API Response data:', regionsApiResp);
            
            zonesContainer.innerHTML = ''; 

            if (allRegions.length === 0) {
                zonesContainer.innerHTML = '<p class="text-center">尚未設定任何區域，無法管理倉庫。</p>';
                return;
            }
            console.log(`WAREHOUSE_MANAGEMENT.JS: (Modal) Found ${allRegions.length} regions.`, allRegions);

            for (const region of allRegions) {
                const regionDiv = document.createElement('div');
                regionDiv.className = 'mb-4 p-3 border rounded';
                regionDiv.innerHTML = `
                    <h5>${region.regionName}
                        <button class="btn btn-sm btn-success float-end add-warehouse-btn" data-region-id="${region.regionId}" data-region-name="${region.regionName}">
                            <i class="bi bi-plus-circle"></i> 新增倉庫至此區
                        </button>
                    </h5>
                    <table class="table table-sm table-hover mt-2">
                        <thead><tr><th>倉庫代碼</th><th>倉庫名稱</th><th class="text-end">操作</th></tr></thead>
                        <tbody id="modal-warehouse-list-${region.regionId}">
                            <tr><td colspan="3" class="text-center">載入中...</td></tr>
                        </tbody>
                    </table>
                `;
                zonesContainer.appendChild(regionDiv);
                fetchWarehousesForRegionInModal(region.regionId, region.regionName);
            }
            addEventListenersForAddButtonsInModal();

        } catch (error) {
            console.error("WAREHOUSE_MANAGEMENT.JS: Error in fetchRegionsAndWarehousesForModal:", error);
            zonesContainer.innerHTML = '<p class="text-danger text-center">無法載入區域倉庫管理資訊。</p>';
            if(window.showToast) window.showToast('無法載入區域倉庫管理資訊: ' + error.message, 'error');
        }
    }

    async function fetchWarehousesForRegionInModal(regionId, regionName) {
        const warehouseListBody = document.getElementById(`modal-warehouse-list-${regionId}`);
        if(!warehouseListBody) {
            console.warn(`WAREHOUSE_MANAGEMENT.JS: Warehouse list body for modal not found: modal-warehouse-list-${regionId}`);
            return;
        }
        console.log(`WAREHOUSE_MANAGEMENT.JS: Fetching modal warehouses for region ID: ${regionId} (${regionName})`);
        try {
            const response = await window.fetchAuthenticated(`/api/v1/warehouses/by-region/${regionId}`);
            console.log(`WAREHOUSE_MANAGEMENT.JS: Warehouses for region ${regionId} (modal) fetch response status:`, response.status);
            if (!response.ok) {
                const errorText = await response.text();
                console.error(`WAREHOUSE_MANAGEMENT.JS: Failed to fetch warehouses for region ${regionId} in modal. Status:`, response.status, 'Response:', errorText);
                throw new Error('無法為管理彈窗載入 ' + regionName + ' 區域的倉庫');
            }
            const apiResp = await response.json();
            console.log(`WAREHOUSE_MANAGEMENT.JS: Warehouses API response for region ${regionId} (modal):`, apiResp);
            const warehouses = apiResp.data || [];

            warehouseListBody.innerHTML = '';
            if (warehouses.length === 0) {
                warehouseListBody.innerHTML = '<tr><td colspan="3" class="text-center fst-italic">此區域尚無倉庫</td></tr>';
            } else {
                warehouses.forEach(wh => {
                    const row = warehouseListBody.insertRow();
                    row.insertCell().textContent = wh.warehouseCode;
                    row.insertCell().textContent = wh.warehouseName;
                    const actionsCell = row.insertCell();
                    actionsCell.className = 'text-end';
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'btn btn-sm btn-danger delete-warehouse-btn';
                    deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
                    deleteBtn.dataset.warehouseId = wh.warehouseId;
                    deleteBtn.dataset.warehouseName = wh.warehouseName;
                    deleteBtn.dataset.regionId = regionId; 
                    actionsCell.appendChild(deleteBtn);
                });
            }
            addEventListenersForDeleteButtonsInModal();
            console.log(`WAREHOUSE_MANAGEMENT.JS: Successfully displayed warehouses for region ${regionId} in modal.`);
        } catch (error) {
            console.error(`WAREHOUSE_MANAGEMENT.JS: Error fetching/displaying modal warehouses for region ${regionId}:`, error);
            warehouseListBody.innerHTML = `<tr><td colspan="3" class="text-danger text-center">載入倉庫失敗</td></tr>`;
        }
    }
    
    function addEventListenersForAddButtonsInModal() {
        document.querySelectorAll('#zoneManagementModal .add-warehouse-btn').forEach(button => {
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            newButton.addEventListener('click', function() {
                console.log("WAREHOUSE_MANAGEMENT.JS: Add warehouse button in modal clicked.");
                currentRegionIdForAdd = this.dataset.regionId;
                const regionName = this.dataset.regionName;
                if(addWarehouseRegionNameSpan) addWarehouseRegionNameSpan.textContent = regionName;
                if(addWarehouseRegionIdInput) addWarehouseRegionIdInput.value = currentRegionIdForAdd;
                loadExternalWarehousesForModal(currentRegionIdForAdd);
                if(addWarehouseForm) addWarehouseForm.reset(); 
                if(newWarehouseCodeInput) newWarehouseCodeInput.value = ''; 
                if(newWarehouseNameInput) newWarehouseNameInput.value = '';
                if(addWarehouseModal) addWarehouseModal.show();
            });
        });
    }

    async function loadExternalWarehousesForModal(regionId) {
        console.log(`WAREHOUSE_MANAGEMENT.JS: Loading external warehouses for modal, region ID: ${regionId}`);
        if(!externalWarehouseSelect) {
            console.error("WAREHOUSE_MANAGEMENT.JS: externalWarehouseSelect element not found for modal.");
            return;
        }
        externalWarehouseSelect.innerHTML = '<option value="">載入中...</option>';
        try {
            const response = await window.fetchAuthenticated(`/api/v1/warehouses/external/for-region/${regionId}`);
            console.log(`WAREHOUSE_MANAGEMENT.JS: External warehouses fetch status for modal: ${response.status}`);
            if (!response.ok) throw new Error('Failed to fetch external warehouses for modal');
            const apiResp = await response.json();
            const externalWarehouses = apiResp.data || [];
            console.log(`WAREHOUSE_MANAGEMENT.JS: External warehouses for modal:`, externalWarehouses);
            
            externalWarehouseSelect.innerHTML = '<option value="">請選擇倉庫</option>';
            externalWarehouses.forEach(wh => {
                const option = document.createElement('option');
                option.value = wh.warehouseCode;
                option.textContent = `${wh.warehouseName} (${wh.warehouseCode})`;
                option.dataset.warehouseName = wh.warehouseName; 
                externalWarehouseSelect.appendChild(option);
            });
        } catch (error) {
            console.error('WAREHOUSE_MANAGEMENT.JS: Error loading external warehouses for modal:', error);
            externalWarehouseSelect.innerHTML = '<option value="">載入外部倉庫失敗</option>';
            if(window.showToast) window.showToast('載入外部倉庫列表失敗: ' + error.message, 'error');
        }
    }
    
    if (externalWarehouseSelect) {
        externalWarehouseSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (newWarehouseCodeInput && newWarehouseNameInput) {
                if (selectedOption && selectedOption.value) {
                    newWarehouseCodeInput.value = selectedOption.value;
                    newWarehouseNameInput.value = selectedOption.dataset.warehouseName || selectedOption.text.split('(')[0].trim();
                } else {
                    newWarehouseCodeInput.value = '';
                    newWarehouseNameInput.value = '';
                }
            }
        });
    }

    if (addWarehouseForm) {
        addWarehouseForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            console.log("WAREHOUSE_MANAGEMENT.JS: Add warehouse form submitted.");
            const warehouseCode = newWarehouseCodeInput ? newWarehouseCodeInput.value : null;
            const warehouseName = newWarehouseNameInput ? newWarehouseNameInput.value : null; 
            const regionId = addWarehouseRegionIdInput ? addWarehouseRegionIdInput.value : null;

            if (!warehouseCode || !warehouseName || !regionId) {
                if(window.showToast) window.showToast('請從下拉選單選擇一個倉庫並確認區域ID。', 'error');
                console.error("WAREHOUSE_MANAGEMENT.JS: Add warehouse form validation failed.", {warehouseCode, warehouseName, regionId});
                return;
            }
            const payload = { warehouseCode, warehouseName, regionId };
            console.log("WAREHOUSE_MANAGEMENT.JS: Add warehouse payload:", payload);

            // Confirmation Dialog
            if (!window.confirm(`確定要將倉庫 "${warehouseName}" (${warehouseCode}) 新增到所選區域嗎？`)) {
                return; // User cancelled
            }

            try {
                const response = await window.fetchAuthenticated('/api/v1/warehouses', {
                    method: 'POST',
                    body: JSON.stringify(payload)
                });
                console.log("WAREHOUSE_MANAGEMENT.JS: Add warehouse API response status:", response.status);
                const apiResp = await response.json();
                if (!response.ok) {
                    throw new Error(apiResp.message || '新增倉庫失敗');
                }
                if(window.showToast) window.showToast('倉庫新增成功!', 'success');
                if(addWarehouseModal) addWarehouseModal.hide();
                fetchWarehousesForRegionInModal(regionId); 
                displayWarehousesInMainArea(); 
            } catch (error) {
                console.error('WAREHOUSE_MANAGEMENT.JS: Error adding warehouse:', error);
                if(window.showToast) window.showToast('新增倉庫失敗: ' + error.message, 'error');
            }
        });
    }
    
    function addEventListenersForDeleteButtonsInModal() {
         document.querySelectorAll('#zoneManagementModal .delete-warehouse-btn').forEach(button => {
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            newButton.addEventListener('click', async function() {
                const warehouseId = this.dataset.warehouseId;
                const warehouseName = this.dataset.warehouseName;
                const regionIdForRefresh = this.dataset.regionId;
                console.log(`WAREHOUSE_MANAGEMENT.JS: Delete button clicked for warehouse ID: ${warehouseId}`);

                if (confirm(`確定要刪除倉庫 "${warehouseName}"?`)) {
                    try {
                        const response = await window.fetchAuthenticated(`/api/v1/warehouses/${warehouseId}`, {
                            method: 'DELETE'
                        });
                        console.log(`WAREHOUSE_MANAGEMENT.JS: Delete warehouse API response status for ID ${warehouseId}:`, response.status);
                        const apiResp = await response.json(); 
                        if (!response.ok) {
                             throw new Error(apiResp.message || '刪除倉庫失敗');
                        }
                        if(window.showToast) window.showToast('倉庫刪除成功!', 'success');
                        fetchWarehousesForRegionInModal(regionIdForRefresh); 
                        displayWarehousesInMainArea(); 
                    } catch (error) {
                        console.error('WAREHOUSE_MANAGEMENT.JS: Error deleting warehouse:', error);
                        if(window.showToast) window.showToast('刪除倉庫失敗: ' + error.message, 'error');
                    }
                }
            });
        });
    }

    const zoneManagementModalElement = document.getElementById('zoneManagementModal');
    if (zoneManagementModalElement) {
        console.log("WAREHOUSE_MANAGEMENT.JS: Adding event listener for zoneManagementModal 'shown.bs.modal'.");
        zoneManagementModalElement.addEventListener('shown.bs.modal', fetchRegionsAndWarehousesForModal);
    }
    
    if (mainWarehouseDisplayArea) { 
      console.log("WAREHOUSE_MANAGEMENT.JS: mainWarehouseDisplayArea found, calling displayWarehousesInMainArea initially.");
      displayWarehousesInMainArea();
    } else {
      console.error("WAREHOUSE_MANAGEMENT.JS: mainWarehouseDisplayArea was NOT found on this page during initial setup.");
    }
}); 