// js/user_form.js
document.addEventListener('DOMContentLoaded', function () {
    const userAccountForm = document.getElementById('user-account-form');
    const formTitle = document.getElementById('user-form-title');
    const formModeBreadcrumb = document.getElementById('user-form-mode-breadcrumb');
    const userAccountIdField = document.getElementById('userAccountId');
    const employeeIdField = document.getElementById('employeeId');
    const userNameField = document.getElementById('userName');
    const passwordField = document.getElementById('password');
    const passwordRequiredIndicator = document.getElementById('password-required-indicator');
    const passwordHelpText = document.getElementById('password-help-text');
    const isActiveYesRadio = document.getElementById('isActiveYes');
    const isActiveNoRadio = document.getElementById('isActiveNo');
    const rolesCheckboxGroup = document.getElementById('roles-checkbox-group');
    const errorMessageDiv = document.getElementById('user-form-error-message');
    const accountTypeField = document.getElementById('accountType');
    const erpCompanyDivisionField = document.getElementById('erpCompanyDivision');

    const API_URL_USERS = '/api/v1/users';
    const API_URL_ROLES_LIST = '/api/v1/role-permissions/list';
    const API_URL_ACCOUNT_TYPES = '/api/v1/enums/user-account-types';
    const API_URL_ERP_COMPANIES = '/api/v1/enums/erp-company-divisions';

    const urlParams = new URLSearchParams(window.location.search);
    const currentUserId = urlParams.get('id');
    const mode = urlParams.get('mode') || (currentUserId ? 'edit' : 'add');

    let availableAccountTypes = [];
    let availableErpCompanyDivisions = [];

    function showError(message) {
        if (errorMessageDiv) {
            errorMessageDiv.textContent = message;
            errorMessageDiv.classList.remove('d-none');
        }
    }
    function clearError() {
        if (errorMessageDiv) {
            errorMessageDiv.classList.add('d-none');
            errorMessageDiv.textContent = '';
        }
    }

    async function loadRolesForCheckboxes() {
        if (!rolesCheckboxGroup) return;
        rolesCheckboxGroup.innerHTML = '<p class="text-muted">正在載入角色...</p>';
        try {
            const response = await window.fetchAuthenticated(API_URL_ROLES_LIST);
            if (!response.ok) throw new Error('Failed to fetch roles');
            const apiResult = await response.json();

            rolesCheckboxGroup.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && Array.isArray(apiResult.data)) {
                if (apiResult.data.length === 0) {
                    rolesCheckboxGroup.innerHTML = '<p class="text-muted">沒有可指派的角色。</p>';
                    return;
                }
                apiResult.data.forEach(role => {
                    const div = document.createElement('div');
                    div.className = 'form-check form-check-inline';
                    div.innerHTML = `
                        <input class="form-check-input role-checkbox" type="checkbox" name="roleIds" value="${role.roleId}" id="role-${role.roleId}">
                        <label class="form-check-label" for="role-${role.roleId}">${role.roleName}</label>
                    `;
                    rolesCheckboxGroup.appendChild(div);
                });
            } else {
                rolesCheckboxGroup.innerHTML = '<p class="text-danger">無法載入角色列表。</p>';
                console.error("Error or bad format in roles list API response:", apiResult.message || apiResult);
            }
        } catch (error) {
            console.error('Error fetching roles for checkboxes:', error);
            rolesCheckboxGroup.innerHTML = `<p class="text-danger">載入角色時發生錯誤: ${error.message}</p>`;
        }
    }

    async function fetchUserAccountTypes() {
        if (!accountTypeField) { 
            console.warn("USER_FORM.JS: accountTypeField element not found.");
            return;
        }
        console.log("USER_FORM.JS: Fetching user account types from:", API_URL_ACCOUNT_TYPES);
        accountTypeField.innerHTML = '<option value="">載入中...</option>';
        try {
            const response = await window.fetchAuthenticated(API_URL_ACCOUNT_TYPES);
            if (!response.ok) throw new Error('Failed to fetch user account types');
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                availableAccountTypes = apiResult.data;
                console.log("USER_FORM.JS: User account types fetched:", availableAccountTypes);
                populateAccountTypeDropdown();
            } else {
                throw new Error(apiResult.message || '無法載入帳號類型資料');
            }
        } catch (error) {
            console.error('Error fetching user account types:', error);
            if(accountTypeField) accountTypeField.innerHTML = '<option value="">類型載入失敗</option>';
            if(window.showToast) window.showToast('載入帳號類型失敗: ' + error.message, 'error');
        }
    }

    async function fetchErpCompanyDivisions() {
        if (!erpCompanyDivisionField) { 
            console.warn("USER_FORM.JS: erpCompanyDivisionField element not found.");
            return;
        }
        erpCompanyDivisionField.innerHTML = '<option value="">載入中...</option>';
        try {
            const response = await window.fetchAuthenticated(API_URL_ERP_COMPANIES);
            if (!response.ok) throw new Error('Failed to fetch ERP company divisions');
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                availableErpCompanyDivisions = apiResult.data.filter(c => c.id === 0 || c.id === 1);
                populateErpCompanyDropdown();
            } else {
                throw new Error(apiResult.message || '無法載入公司別資料');
            }
        } catch (error) {
            console.error('Error fetching ERP company divisions:', error);
            if(erpCompanyDivisionField) erpCompanyDivisionField.innerHTML = '<option value="">公司別載入失敗</option>';
            if(window.showToast) window.showToast('載入公司別失敗: ' + error.message, 'error');
        }
    }

    function populateAccountTypeDropdown() {
        if (!accountTypeField || !availableAccountTypes) return;
        console.log("USER_FORM.JS: Populating account type dropdown with data:", availableAccountTypes);
        const currentValue = accountTypeField.dataset.pendingValue || accountTypeField.value;
        accountTypeField.innerHTML = '<option value="">請選擇帳號類型</option>';
        availableAccountTypes.forEach(type => {
            const option = document.createElement('option');
            option.value = type.id;
            option.textContent = type.description;
            accountTypeField.appendChild(option);
        });
        if (currentValue) {
            accountTypeField.value = currentValue;
        }
        delete accountTypeField.dataset.pendingValue;
    }

    function populateErpCompanyDropdown() {
        if (!erpCompanyDivisionField || !availableErpCompanyDivisions) return;
        const currentValue = erpCompanyDivisionField.dataset.pendingValue || erpCompanyDivisionField.value;
        erpCompanyDivisionField.innerHTML = '<option value="">請選擇歸屬公司別</option>';
        availableErpCompanyDivisions.forEach(company => {
            const option = document.createElement('option');
            option.value = company.id;
            option.textContent = company.description;
            erpCompanyDivisionField.appendChild(option);
        });
        if (currentValue) {
            erpCompanyDivisionField.value = currentValue;
        }
        delete erpCompanyDivisionField.dataset.pendingValue;
    }

    async function setupFormForMode() {
        clearError();
        await Promise.all([
            loadRolesForCheckboxes(),
            fetchUserAccountTypes(),
            fetchErpCompanyDivisions()
        ]);

        if (mode === 'add') {
            formTitle.textContent = '新增使用者';
            formModeBreadcrumb.textContent = '新增使用者';
            if(passwordField) passwordField.required = true;
            if(passwordRequiredIndicator) passwordRequiredIndicator.style.display = 'inline';
            if(passwordHelpText) passwordHelpText.textContent = '新增使用者時為必填。';
            if(employeeIdField) employeeIdField.readOnly = false;
            if (accountTypeField && availableAccountTypes.length > 0) {
                const generalStaff = availableAccountTypes.find(t => t.description === '一般員工');
                if (generalStaff) accountTypeField.value = generalStaff.id;
            }
        } else if (mode === 'edit' && currentUserId) {
            formTitle.textContent = '編輯使用者';
            formModeBreadcrumb.textContent = '編輯使用者';
            if(passwordField) passwordField.required = false;
            if(passwordRequiredIndicator) passwordRequiredIndicator.style.display = 'none';
            if(passwordHelpText) passwordHelpText.textContent = '留空則不變更密碼。';
            if(employeeIdField) employeeIdField.readOnly = true;
            loadUserData();
        }
    }

    async function loadUserData() {
        try {
            const response = await window.fetchAuthenticated(`${API_URL_USERS}/${currentUserId}`);
            if (!response.ok) throw new Error('Failed to load user data');
            const apiResult = await response.json();

            if (apiResult.code === 200 && apiResult.data) {
                const user = apiResult.data;
                if(userAccountIdField) userAccountIdField.value = user.userAccountId;
                if(employeeIdField) employeeIdField.value = user.employeeId;
                if(userNameField) userNameField.value = user.userName;
                if(isActiveYesRadio) isActiveYesRadio.checked = user.isActive === true;
                if(isActiveNoRadio) isActiveNoRadio.checked = !(user.isActive === true);
                
                if (erpCompanyDivisionField) {
                    if (availableErpCompanyDivisions.length > 0) {
                        erpCompanyDivisionField.value = user.erpCompanyDivision;
                    } else {
                        erpCompanyDivisionField.dataset.pendingValue = user.erpCompanyDivision;
                    }
                }

                if (accountTypeField) {
                    if (availableAccountTypes.length > 0) {
                        accountTypeField.value = user.accountType; 
                    } else {
                        accountTypeField.dataset.pendingValue = user.accountType;
                    }
                }

                if (user.roleIds && Array.isArray(user.roleIds)) {
                    user.roleIds.forEach(roleId => {
                        const checkbox = document.getElementById(`role-${roleId}`);
                        if (checkbox) checkbox.checked = true;
                    });
                }
            } else {
                showError(apiResult.message || '無法載入使用者資料');
            }
        } catch (error) {
            console.error('Error loading user data:', error);
            showError('載入使用者資料時發生錯誤: ' + error.message);
        }
    }

    if (userAccountForm) {
        userAccountForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            clearError();

            const selectedRoleIds = [];
            if(rolesCheckboxGroup) {
                rolesCheckboxGroup.querySelectorAll('.role-checkbox:checked').forEach(cb => {
                    selectedRoleIds.push(cb.value);
                });
            }

            const payload = {
                employeeId: employeeIdField ? employeeIdField.value : null,
                userName: userNameField ? userNameField.value : null,
                isActive: isActiveYesRadio ? isActiveYesRadio.checked : true,
                erpCompanyDivision: null,
                accountType: null,
                roleIds: selectedRoleIds
            };
            
            const rawAccountTypeValue = accountTypeField ? accountTypeField.value : null;
            if (rawAccountTypeValue && rawAccountTypeValue !== "") {
                const numValue = Number(rawAccountTypeValue);
                if (!isNaN(numValue)) {
                    payload.accountType = numValue;
                } else {
                    console.warn("USER_FORM.JS: accountType value '" + rawAccountTypeValue + "' is not a valid number. Setting to null.");
                }
            }

            const rawErpCompanyValue = erpCompanyDivisionField ? erpCompanyDivisionField.value : null;
            if (rawErpCompanyValue && rawErpCompanyValue !== "") {
                const numValue = Number(rawErpCompanyValue);
                if (!isNaN(numValue)) {
                    payload.erpCompanyDivision = numValue;
                } else {
                    console.warn("USER_FORM.JS: erpCompanyDivision value '" + rawErpCompanyValue + "' is not a valid number. Setting to null.");
                }
            }

            if (mode === 'add' || (mode === 'edit' && passwordField && passwordField.value)) {
                if (!passwordField.value || passwordField.value.length < 6) {
                    showError('密碼長度必須至少為6個字符。');
                    return;
                }
                payload.password = passwordField.value;
            }
            if (currentUserId && mode === 'edit') {
                 payload.userAccountId = currentUserId;
            }

            // Confirmation Dialog
            const confirmMessage = mode === 'add' ? "確定要新增此使用者嗎？" : "確定要儲存對此使用者的變更嗎？";
            if (!window.confirm(confirmMessage)) {
                return; // User cancelled
            }

            let url = API_URL_USERS;
            let method = 'POST';
            if (mode === 'edit') {
                url = `${API_URL_USERS}/${currentUserId}`;
                method = 'PUT';
            }
            console.log("USER_FORM.JS: Submitting payload:", payload);
            try {
                const response = await window.fetchAuthenticated(url, {
                    method: method,
                    body: JSON.stringify(payload)
                });
                const result = await response.json();
                if (response.ok && (result.code === 201 || result.code === 200)) {
                    alert(`使用者已${mode === 'add' ? '新增' : '更新'}！`);
                    window.location.href = 'user_management.html';
                } else {
                    showError(result.message || '儲存失敗');
                }
            } catch (error) {
                console.error('Error saving user account:', error);
                showError('儲存使用者時發生錯誤: ' + error.message);
            }
        });
    }
    
    document.getElementById('cancelUserFormBtn')?.addEventListener('click', () => {
        window.location.href = 'user_management.html';
    });

    setupFormForMode();
}); 