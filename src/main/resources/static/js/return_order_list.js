document.addEventListener('DOMContentLoaded', function() {
    const tableBody = document.getElementById('return-order-list');
    const statusFilter = document.getElementById('status-filter');
    // Add other filter elements...

    async function loadStatusFilter() {
        if (!statusFilter) return;
        try {
            // Assuming this new endpoint will be created
            const response = await window.fetchAuthenticated('/api/v1/enums/return-order-statuses');
            if (!response.ok) throw new Error('無法獲取狀態列表');
            const result = await response.json();
            if (result.code === 200 && Array.isArray(result.data)) {
                result.data.forEach(status => {
                    statusFilter.innerHTML += `<option value="${status.value}">${status.label}</option>`;
                });
            }
        } catch (error) {
            console.error('載入退機單狀態失敗:', error);
        }
    }
    
    async function searchReturnOrders(page = 1) {
        const keyword = document.getElementById('keyword-filter').value;
        const statusCode = statusFilter.value;
        const startDate = document.getElementById('start-date-filter').value;
        const endDate = document.getElementById('end-date-filter').value;
        
        const params = new URLSearchParams({ page: page - 1, size: 15, sort: 'createTime,desc' });
        if (keyword) params.append('keyword', keyword);
        if (statusCode) params.append('statusCode', statusCode);
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        
        tableBody.innerHTML = '<tr><td colspan="7" class="text-center">查詢中...</td></tr>';
        
        try {
            const response = await window.fetchAuthenticated(`/api/v1/repairs/returns?${params.toString()}`);
            if(!response.ok) throw new Error("查詢失敗");
            const result = await response.json();
            renderTable(result.data.list || []);
            // renderPagination(result.data); // TODO
        } catch (error) {
            tableBody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">查詢失敗: ${error.message}</td></tr>`;
        }
    }

    function renderTable(orders) {
        tableBody.innerHTML = '';
        if (!orders || orders.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center">查無資料</td></tr>';
            return;
        }
        orders.forEach(order => {
            const row = tableBody.insertRow();
            row.innerHTML = `
                <td><a href="return_order_detail.html?id=${order.returnOrderId}">${order.returnOrderNumber}</a></td>
                <td>${new Date(order.createTime).toLocaleDateString()}</td>
                <td>${order.scheduledDate || 'N/A'}</td>
                <td>${order.customerName || 'N/A'}</td>
                <td>${order.technicianName || 'N/A'}</td>
                <td><span class="badge bg-secondary">${order.statusDescription}</span></td>
                <td><a href="return_order_detail.html?id=${order.returnOrderId}" class="btn btn-sm btn-outline-primary">查看</a></td>
            `;
        });
    }

    // Add event listeners for filters
    document.getElementById('keyword-filter').addEventListener('input', () => searchReturnOrders(1));
    statusFilter.addEventListener('change', () => searchReturnOrders(1));
    document.getElementById('start-date-filter').addEventListener('change', () => searchReturnOrders(1));
    document.getElementById('end-date-filter').addEventListener('change', () => searchReturnOrders(1));

    // Initial load
    loadStatusFilter();
    searchReturnOrders(1);
}); 