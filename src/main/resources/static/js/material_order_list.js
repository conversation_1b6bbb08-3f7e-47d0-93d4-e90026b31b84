document.addEventListener('DOMContentLoaded', function () {
    
    const materialOrderList = document.getElementById('material-order-list');
    const statusFilter = document.getElementById('status-filter');
    const startDateFilter = document.getElementById('start-date-filter');
    const endDateFilter = document.getElementById('end-date-filter');
    const orderNumberFilter = document.getElementById('order-number-filter');
    const paginationControls = document.createElement('div');
    paginationControls.id = 'pagination-controls';
    materialOrderList.after(paginationControls);

    async function loadStatusFilter() {
        if (!statusFilter) return;
        try {
            const response = await window.fetchAuthenticated('/api/v1/enums/material-order-statuses');
            if (!response.ok) throw new Error('無法獲取狀態列表');
            const result = await response.json();
            if (result.code === 200 && Array.isArray(result.data)) {
                populateDropdown(statusFilter, result.data, '所有狀態');
            }
        } catch (error) {
            console.error('載入領料單狀態失敗:', error);
        }
    }
    
    function populateDropdown(selectElement, options, defaultText) {
        selectElement.innerHTML = `<option value="">${defaultText}</option>`;
        if (options && Array.isArray(options)) {
            options.forEach(option => {
                selectElement.innerHTML += `<option value="${option.value}">${option.label}</option>`;
            });
        }
    }

    async function searchMaterialOrders(page = 1) {
        const getFilterValue = (element) => {
            return element.value === element.options[0].text ? "" : element.value;
        };
        
        const filters = {
            statusCode: getFilterValue(statusFilter),
            startDate: startDateFilter.value,
            endDate: endDateFilter.value,
            orderNumber: orderNumberFilter.value
        };

        const params = new URLSearchParams({ page: page - 1, size: 15, sort: 'createTime,asc' });
        for (const key in filters) {
            if (filters[key]) {
                params.append(key, filters[key]);
            }
        }
        
        materialOrderList.innerHTML = '<div class="text-center p-4"><span class="spinner-border spinner-border-sm"></span> 查詢中...</div>';

        try {
            const response = await window.fetchAuthenticated(`/api/v1/material-orders?${params.toString()}`);
            if (!response.ok) throw new Error('無法獲取領料單列表');
            const result = await response.json();
            if (result.data) {
                renderMaterialOrders(result.data.list || []);
                renderPagination(result.data);
            } else {
                renderMaterialOrders([]);
                renderPagination(null);
            }
        } catch (error) {
            console.error('查詢領料單失敗:', error);
            materialOrderList.innerHTML = '<div class="alert alert-danger">無法載入領料單。</div>';
        }
    }

    function renderMaterialOrders(orders) {
        const materialOrderList = document.getElementById('material-order-list');
        materialOrderList.innerHTML = '';
        if (!orders || orders.length === 0) {
            materialOrderList.innerHTML = '<div class="text-center p-4 text-muted">查無相關領料單。</div>';
            return;
        }
        orders.forEach((order, index) => {
            const card = document.createElement('div');
            card.className = 'card';
            card.innerHTML = `
                <div class="item-seq">${String((index + 1)).padStart(2, '0')}</div>
                <div class="item-info">
                    <p><strong>單號：</strong>${order.materialOrderNumber}</p>
                    <p><strong>建單時間：</strong>${new Date(order.createTime).toLocaleString()}</p>
                    <p><strong>領料倉庫：</strong>${order.warehouseName || 'N/A'}</p>
                    <p><strong>領料單狀態：</strong><span class="badge ${getStatusBadgeClass(order.statusCode)}">${order.statusDescription}</span></p>
                </div>
                <div class="item-action">
                    <a href="material_order_detail.html?id=${order.materialOrderId}" class="btn btn-outline-primary"><i class="bi bi-search"></i></a>
                </div>
            `;
            materialOrderList.appendChild(card);
        });
    }

    function getStatusBadgeClass(statusCode) {
        switch (statusCode) {
            case 10: // 待揀料
                return 'bg-secondary';
            case 20: // 已揀料
                return 'bg-primary';
            case 30: // 領料完成
                return 'bg-success';
            case 40: // 取消領料
                return 'bg-danger';
            default:
                return 'bg-info text-dark';
        }
    }

    function renderPagination(pageData) {
        const paginationControls = document.getElementById('pagination-controls');
        if (!paginationControls) return;
        paginationControls.innerHTML = '';
        if (!pageData) return;
        
        const { number: page, totalPages } = pageData;

        if (totalPages <= 1) return;

        let paginationHtml = '<ul class="pagination justify-content-center">';

        // Previous button
        paginationHtml += `<li class="page-item ${page === 0 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page - 1}">‹</a></li>`;

        // Page numbers
        for (let i = 0; i < totalPages; i++) {
            paginationHtml += `<li class="page-item ${i === page ? 'active' : ''}"><a class="page-link" href="#" data-page="${i}">${i + 1}</a></li>`;
        }

        // Next button
        paginationHtml += `<li class="page-item ${page === totalPages - 1 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page + 1}">›</a></li>`;

        paginationHtml += '</ul>';
        paginationControls.innerHTML = paginationHtml;

        paginationControls.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const pageNum = this.dataset.page;
                if (!this.parentElement.classList.contains('disabled')) {
                    searchMaterialOrders(parseInt(pageNum) + 1);
                }
            });
        });
    }
    
    [statusFilter, startDateFilter, endDateFilter, orderNumberFilter].forEach(el => {
        el.addEventListener('change', () => searchMaterialOrders(1));
    });
    
    // Initial render
    loadStatusFilter();
    searchMaterialOrders(1);
}); 