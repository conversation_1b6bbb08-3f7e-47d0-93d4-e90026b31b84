document.addEventListener('DOMContentLoaded', function() {
    const configEditorPlaceholder = document.getElementById('config-editor-placeholder');
    const addCurrentMonthBtn = document.getElementById('add-current-month-config-btn');
    const addNextMonthBtn = document.getElementById('add-next-month-config-btn');
    const yearSelect = document.getElementById('year-select');
    const monthSelect = document.getElementById('month-select');
    const formActions = document.querySelector('.form-actions');
    const confirmActions = document.querySelector('.confirm-actions');
    const nextStepBtn = document.getElementById('next-step-btn');
    const prevStepBtn = document.getElementById('prev-step-btn');
    const confirmBtn = document.getElementById('confirm-btn');
    const cancelBtn = document.getElementById('cancel-btn');
    const toolbar = document.querySelector('.toolbar');

    let allTechnicians = [];
    let allCities = [];
    let currentConfigData = [];
    let isEditMode = false;

    // --- INITIALIZATION ---
    async function initializePage() {
        populateYearMonthSelects();
        await Promise.all([
            fetchAllTechnicians(),
            fetchAllCitiesAndDistricts()
        ]);
        await loadAndRenderConfig();
        attachEventListeners();
    }

    function attachEventListeners() {
        yearSelect.addEventListener('change', loadAndRenderConfig);
        monthSelect.addEventListener('change', loadAndRenderConfig);
        addCurrentMonthBtn.addEventListener('click', () => enterEditMode(false));
        addNextMonthBtn.addEventListener('click', () => enterEditMode(true));
        
        configEditorPlaceholder.addEventListener('click', handleEditorClicks);
        configEditorPlaceholder.addEventListener('change', handleEditorChanges);
        
        cancelBtn.addEventListener('click', exitEditMode);
        nextStepBtn.addEventListener('click', showConfirmationView);
        prevStepBtn.addEventListener('click', () => renderEditableConfig(currentConfigData));
        confirmBtn.addEventListener('click', saveConfiguration);
    }
    
    // --- DATA FETCHING & SAVING ---
    async function fetchAllTechnicians() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/users/technicians'); 
            if (!response.ok) throw new Error('無法獲取技師列表');
            const result = await response.json();
            allTechnicians = result.data || [];
        } catch (error) {
            console.error(error);
            if(window.showToast) window.showToast(error.message, 'danger');
        }
    }

    async function fetchAllCitiesAndDistricts() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/address/cities-with-districts');
            if (!response.ok) throw new Error('無法獲取縣市區域資料');
            const result = await response.json();
            allCities = result.data || [];
        } catch (error) {
            console.error(error);
            if(window.showToast) window.showToast(error.message, 'danger');
        }
    }

    async function loadAndRenderConfig() {
        const year = yearSelect.value;
        const month = monthSelect.value.padStart(2, '0');
        const configMonth = `${year}-${month}`;
        
        try {
            const response = await window.fetchAuthenticated(`/api/v1/repairs/technician-areas?month=${configMonth}`);
            if (!response.ok) throw new Error('無法獲取配置資料');
            const result = await response.json();
            currentConfigData = result.data || [];
            renderReadOnlyConfig(currentConfigData);
            updateToolbarButtons(currentConfigData.length > 0);
        } catch (error) {
            console.error(error);
            if(window.showToast) window.showToast(error.message, 'danger');
        }
    }

    async function saveConfiguration() {
        const month = `${yearSelect.value}-${monthSelect.value.padStart(2, '0')}`;
        // Use currentConfigData if available (confirmation mode), otherwise build from UI (direct save)
        const payload = currentConfigData && currentConfigData.length > 0 ? currentConfigData : buildPayloadFromUI();

        if (payload.some(p => !p.technicianId || p.serviceAreas.length === 0)) {
            if (window.showToast) window.showToast('尚有技師或服務區域未選取', 'warning');
            return;
        }

        try {
            const response = await window.fetchAuthenticated(`/api/v1/repairs/technician-areas?month=${month}`, {
                method: 'POST',
                body: JSON.stringify(payload)
            });
            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '儲存失敗');
            }
            if(window.showToast) window.showToast('配置儲存成功！', 'success');
            exitEditMode();
            loadAndRenderConfig();
        } catch (error) {
            console.error("儲存配置失敗:", error);
            if(window.showToast) window.showToast(`儲存失敗: ${error.message}`, 'danger');
        }
    }
    
    // --- UI RENDERING & STATE MANAGEMENT ---

    function enterEditMode(isNextMonth) {
        isEditMode = true;
        let targetData = JSON.parse(JSON.stringify(currentConfigData)); // Deep copy

        if (isNextMonth && currentConfigData.length === 0) {
             if(window.showToast) window.showToast('無法新增次月配置，因當月無資料可複製。', 'info');
             return;
        }
        
        if (!isNextMonth && currentConfigData.length === 0) {
            // Start with a blank slate for new config
            targetData = [];
        }

        toggleToolbarAndActions(true);
        renderEditableConfig(targetData);
    }
    
    function exitEditMode() {
        isEditMode = false;
        currentConfigData = []; // Reset captured data when exiting edit mode
        toggleToolbarAndActions(false);
        loadAndRenderConfig(); // Reload fresh data from server
    }

    function toggleToolbarAndActions(isEditing) {
        toolbar.style.display = isEditing ? 'none' : 'flex';
        formActions.style.display = isEditing ? 'block' : 'none';
        confirmActions.style.display = 'none';
    }

    function renderReadOnlyConfig(configData) {
        configEditorPlaceholder.innerHTML = '';
        if (configData.length === 0) {
            configEditorPlaceholder.innerHTML = '<p class="text-muted text-center">此月份尚無配置資料。</p>';
            return;
        }

        const container = document.createElement('div');
        configData.forEach((config, index) => {
            const rowHtml = `
                <div class="config-row">
                    <div class="sort-handle"><i class="bi bi-grip-vertical"></i> ${index + 1}</div>
                    <div class="flex-grow-1">
                        <div class="mb-2"><strong>技師：</strong>${config.technicianName}</div>
                        <div class="service-area-group">
                            ${config.serviceAreas.map(area => `
                                <div class="mb-2">
                                    <strong>縣市：</strong>${getCityName(area.cityCode)} <br>
                                    <strong>行政區：</strong>${getDistrictNames(area.cityCode, area.districtCodes).map(d => `<span class="tag">${d}</span>`).join('')}
                                </div>
                            `).join('<hr class="my-1">')}
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += rowHtml;
        });
        configEditorPlaceholder.appendChild(container);
    }

    function renderEditableConfig(configData) {
        configEditorPlaceholder.innerHTML = '';
        const container = document.createElement('div');
        container.id = 'config-editor';

        const technicianRowsHtml = configData.map((config, index) => 
            createTechnicianRowHtml(config, index)
        ).join('');
        
        container.innerHTML = `
            ${technicianRowsHtml}
            <div class="mt-3">
                <button type="button" class="btn btn-outline-success btn-sm" id="add-technician-btn">
                    <i class="bi bi-plus-circle"></i> 新增技師
                </button>
            </div>
        `;
        configEditorPlaceholder.appendChild(container);

        // After rendering, initialize all district dropdowns
        container.querySelectorAll('.service-area-row').forEach(row => {
            const citySelect = row.querySelector('.city-select');
            if (citySelect.value) {
                const savedDistrictCodes = JSON.parse(row.dataset.savedDistricts || '[]');
                updateDistrictOptions(citySelect, savedDistrictCodes);
            }
        });
    }
    
    function showConfirmationView() {
        currentConfigData = buildPayloadFromUI(); // Capture current state
        renderReadOnlyConfig(currentConfigData); // Show it in read-only mode
        formActions.style.display = 'none';
        confirmActions.style.display = 'block';
    }


    // --- EVENT HANDLERS ---
    
    function handleEditorClicks(event) {
        const target = event.target;
        
        if (target.closest('.district-checkbox-container')) {
            event.stopPropagation();
        }

        if (target.closest('#add-technician-btn')) {
            const currentPayload = buildPayloadFromUI();
            currentPayload.push({ technicianId: '', technicianName: '', serviceAreas: [] });
            renderEditableConfig(currentPayload);
            return;
        }

        if (target.classList.contains('add-area-btn')) {
            const areaContainer = target.closest('.config-row').querySelector('.service-area-container');
            const newAreaRow = createServiceAreaRowHtml();
            areaContainer.insertAdjacentHTML('beforeend', newAreaRow);
        } else if (target.classList.contains('remove-area-btn')) {
            target.closest('.service-area-row').remove();
            const cityCode = target.closest('.service-area-row').querySelector('.city-select').value;
            if (cityCode) {
                syncAllDistrictPickers(cityCode);
            }
        } else if (target.classList.contains('remove-tech-btn')) {
            target.closest('.config-row').remove();
            renderEditableConfig(buildPayloadFromUI());
        }
    }

    function handleEditorChanges(event) {
        const target = event.target;
        const serviceAreaRow = target.closest('.service-area-row');

        if (target.classList.contains('city-select')) {
            updateDistrictOptions(target);
        } else if (target.classList.contains('technician-select')) {
            const selectedTechId = target.value;
            const configRow = target.closest('.config-row');

            if (selectedTechId) {
                const allTechSelects = document.querySelectorAll('.technician-select');
                const otherSelectedIds = Array.from(allTechSelects)
                                              .filter(select => select !== target)
                                              .map(select => select.value)
                                              .filter(value => value);

                if (otherSelectedIds.includes(selectedTechId)) {
                    if (window.showToast) {
                        window.showToast('此技師已被選擇，請選擇其他技師。 ', 'warning');
                    } else {
                        alert('此技師已被選擇，請選擇其他技師。 ');
                    }
                    target.value = ''; // Reset the selection
                    configRow.dataset.techId = '';
                    syncAllDistrictPickers();
                    return;
                }
            }
            
            configRow.dataset.techId = selectedTechId;
            syncAllDistrictPickers();
        } else if (target.classList.contains('select-all-districts') || target.classList.contains('district-checkbox')) {
            if (target.classList.contains('select-all-districts')) {
                const checkboxContainer = target.closest('.district-checkbox-container');
                checkboxContainer.querySelectorAll('.district-checkbox').forEach(cb => {
                    if (!cb.disabled) {
                        cb.checked = target.checked;
                    }
                });
            }
            updateDistrictSelectionText(serviceAreaRow);
            const cityCode = serviceAreaRow.querySelector('.city-select').value;
            if (cityCode) {
                syncAllDistrictPickers(cityCode, serviceAreaRow);
            }
        }
    }
    
    // --- HTML TEMPLATE & DYNAMIC UI ---
    
    function createTechnicianRowHtml(config, index) {
        const availableTechOptions = allTechnicians
            .map(t => `<option value="${t.userAccountId}" ${t.userAccountId == config.technicianId ? 'selected' : ''}>${t.userName}</option>`)
            .join('');

        return `
            <div class="config-row" data-tech-id="${config.technicianId || ''}">
                <div class="sort-handle"><i class="bi bi-grip-vertical"></i> ${index + 1}</div>
                <div class="flex-grow-1">
                     <div class="d-flex align-items-center mb-2">
                        <select class="form-select technician-select me-2" style="width: 200px;">
                            <option value="">選擇技師</option>
                            ${availableTechOptions}
                        </select>
                        <button type="button" class="btn btn-sm btn-outline-info add-area-btn"><i class="bi bi-plus"></i> 新增服務區域</button>
                         <button type="button" class="btn btn-sm btn-danger remove-tech-btn ms-auto"><i class="bi bi-trash"></i></button>
                    </div>
                    <div class="service-area-container">
                        ${config.serviceAreas.map(area => createServiceAreaRowHtml(area)).join('')}
                    </div>
                </div>
            </div>`;
    }

    function createServiceAreaRowHtml(area = {}) {
        const cityOptions = allCities.map(c => `<option value="${c.cityCode}" ${area.cityCode == c.cityCode ? 'selected' : ''}>${c.cityName}</option>`).join('');
        const savedDistricts = JSON.stringify(area.districtCodes || []);

        return `
            <div class="service-area-row d-flex align-items-center gap-2 mb-2" data-saved-districts='${savedDistricts}'>
                <select class="form-select city-select" style="width: 150px;">
                    <option value="">選擇縣市</option>
                    ${cityOptions}
                </select>
                <div class="flex-grow-1 dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle w-100 text-start" type="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false">
                        選擇行政區
                    </button>
                    <ul class="dropdown-menu w-100 district-checkbox-container">
                        <!-- Checkboxes populated by JS -->
                    </ul>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger remove-area-btn"><i class="bi bi-x-lg"></i></button>
            </div>`;
    }

    function syncAllDistrictPickers(cityCodeToSync, originatingRow) {
        document.querySelectorAll('.service-area-row').forEach(row => {
            if (row === originatingRow) return; // Don't update the one that triggered the event
            
            const citySelect = row.querySelector('.city-select');
            if (citySelect && (!cityCodeToSync || citySelect.value === cityCodeToSync)) {
                const selectedDistricts = getSelectedDistrictsFromElement(row);
                updateDistrictOptions(citySelect, selectedDistricts);
            }
        });
    }

    function updateDistrictOptions(citySelectElement, selectedCodes = []) {
        const serviceAreaRow = citySelectElement.closest('.service-area-row');
        const checkboxContainer = serviceAreaRow.querySelector('.district-checkbox-container');
        const selectedCityCode = citySelectElement.value;
        const currentTechId = citySelectElement.closest('.config-row').dataset.techId;

        if (!selectedCityCode) {
            checkboxContainer.innerHTML = '<li><span class="dropdown-item-text text-muted">請先選擇縣市</span></li>';
            return;
        }

        const assignedDistrictCodes = new Set();
        buildPayloadFromUI().forEach(config => {
            // Only consider other technicians for disabling
            if (config.technicianId !== currentTechId) {
                config.serviceAreas.forEach(area => {
                    if (area.cityCode === selectedCityCode) {
                        area.districtCodes.forEach(code => assignedDistrictCodes.add(code));
                    }
                });
            }
        });

        const city = allCities.find(c => c.cityCode === selectedCityCode);
        if (!city) {
            checkboxContainer.innerHTML = '<li><span class="dropdown-item-text text-danger">縣市資料錯誤</span></li>';
            return;
        }

        if (!city.districts || city.districts.length === 0) {
            checkboxContainer.innerHTML = '<li><span class="dropdown-item-text text-muted">此縣市無行政區資料</span></li>';
            updateDistrictSelectionText(serviceAreaRow);
            return;
        }

        let checkboxHTML = `
            <li>
                <div class="form-check dropdown-item">
                    <input class="form-check-input select-all-districts" type="checkbox" id="select-all-${Math.random()}">
                    <label class="form-check-label fw-bold" for="select-all-${Math.random()}">全選</label>
                </div>
            </li>
            <li><hr class="dropdown-divider"></li>
        `;

        checkboxHTML += city.districts.map(d => {
            const isSelected = selectedCodes.includes(d.districtCode);
            const isDisabled = assignedDistrictCodes.has(d.districtCode);
            const checkboxId = `dist-${d.districtCode}-${Math.random()}`;
            return `
                <li>
                    <div class="form-check dropdown-item">
                        <input class="form-check-input district-checkbox" type="checkbox" value="${d.districtCode}" id="${checkboxId}" ${isSelected ? 'checked' : ''} ${isDisabled ? 'disabled' : ''}>
                        <label class="form-check-label ${isDisabled ? 'text-muted' : ''}" for="${checkboxId}">${d.districtName}</label>
                    </div>
                </li>`;
        }).join('');
        
        checkboxContainer.innerHTML = checkboxHTML;
        updateDistrictSelectionText(serviceAreaRow);
    }

    function updateDistrictSelectionText(serviceAreaRow) {
        const dropdownButton = serviceAreaRow.querySelector('.dropdown-toggle');
        const selectedCount = getSelectedDistrictsFromElement(serviceAreaRow).length;
        if (selectedCount === 0) {
            dropdownButton.textContent = '選擇行政區';
        } else {
            dropdownButton.textContent = `已選擇 ${selectedCount} 個區域`;
        }
    }
    
    function buildPayloadFromUI() {
        const payload = [];
        document.querySelectorAll('#config-editor .config-row').forEach(row => {
            const techSelect = row.querySelector('.technician-select');
            const techId = techSelect ? techSelect.value : null;
            if (!techId) return;

            const serviceAreas = [];
            row.querySelectorAll('.service-area-row').forEach(areaRow => {
                 const cityCode = areaRow.querySelector('.city-select').value;
                 const districtCodes = getSelectedDistrictsFromElement(areaRow);
                 if (cityCode && districtCodes.length > 0) {
                     serviceAreas.push({ cityCode, districtCodes });
                 }
            });
            
            payload.push({
                technicianId: techId,
                technicianName: allTechnicians.find(t => t.userAccountId == techId)?.userName || '',
                serviceAreas: serviceAreas
            });
        });
        return payload;
    }

    // --- Helper functions ---
    function getSelectedDistrictsFromElement(areaRowElement) {
        const checkedCheckboxes = areaRowElement.querySelectorAll('.district-checkbox:checked');
        return Array.from(checkedCheckboxes).map(cb => cb.value);
    }

    function populateYearMonthSelects() {
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth() + 1;

        for (let i = currentYear - 2; i <= currentYear + 1; i++) {
            yearSelect.innerHTML += `<option value="${i}">${i}年</option>`;
        }
        for (let i = 1; i <= 12; i++) {
            monthSelect.innerHTML += `<option value="${i}">${i}月</option>`;
        }
        
        yearSelect.value = currentYear;
        monthSelect.value = currentMonth;
    }

    function updateToolbarButtons(hasData) {
        // Logic to enable/disable "Add Current/Next Month" buttons based on data
    }
    
    function getCityName(cityCode) {
        const city = allCities.find(c => c.cityCode === cityCode);
        return city ? city.cityName : '未知縣市';
    }

    function getDistrictNames(cityCode, districtCodes) {
        const city = allCities.find(c => c.cityCode === cityCode);
        if (!city || !city.districts || !Array.isArray(districtCodes)) return [];
        return districtCodes.map(code => {
            const district = city.districts.find(d => d.districtCode === code);
            return district ? district.districtName : '未知區域';
        });
    }

    initializePage();
});
 