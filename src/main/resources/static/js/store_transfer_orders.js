// static/js/store_transfer_orders.js
document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.getElementById('transfer-orders-table-body');
    const paginationControls = document.getElementById('pagination-controls');
    const filterForm = document.getElementById('filter-form');
    const noDataMessage = document.getElementById('no-data-message');

    const filterTransferType = document.getElementById('filterTransferType');
    const filterDateFrom = document.getElementById('filterDateFrom');
    const filterDateTo = document.getElementById('filterDateTo');
    const filterStatus = document.getElementById('filterStatus');
    const filterKeyword = document.getElementById('filterKeyword');
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');

    let currentPage = 0; // 0-indexed for API
    const pageSize = 10;
    const API_URL = '/api/v1/store-transfer-orders'; // To be defined
    const STATUS_ENUM_URL = '/api/v1/enums/store-transfer-order-statuses'; // To be defined

    async function populateStatusFilter() {
        try {
            const response = await window.fetchAuthenticated(STATUS_ENUM_URL);
            if (!response.ok) throw new Error('Failed to fetch status enums for transfers');
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                filterStatus.innerHTML = '<option value="">全部狀態</option>';
                apiResult.data.forEach(status => {
                    const option = document.createElement('option');
                    option.value = status.code;
                    option.textContent = status.description;
                    filterStatus.appendChild(option);
                });
            } else {
                console.warn('Could not load transfer status options:', apiResult.message);
            }
        } catch (error) {
            console.error('Error populating transfer status filter:', error);
            filterStatus.innerHTML = '<option value="">狀態載入失敗</option>';
        }
    }

    async function fetchTransferOrders(page = 0) {
        currentPage = page;
        let queryParams = `?page=${page}&size=${pageSize}&sort=requestDate,desc`;
        
        if (filterTransferType.value !== 'ALL') queryParams += `&transferType=${filterTransferType.value}`;
        if (filterDateFrom.value) queryParams += `&dateFrom=${new Date(filterDateFrom.value).toISOString()}`;
        if (filterDateTo.value) queryParams += `&dateTo=${new Date(filterDateTo.value).toISOString()}`;
        if (filterStatus.value) queryParams += `&transferStatus=${filterStatus.value}`;
        if (filterKeyword.value) queryParams += `&keyword=${encodeURIComponent(filterKeyword.value)}`;

        tableBody.innerHTML = `<tr><td colspan="8" class="text-center">載入中...</td></tr>`;

        try {
            const response = await window.fetchAuthenticated(API_URL + queryParams);
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
            const apiResult = await response.json();
            
            tableBody.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && apiResult.data.list && apiResult.data.list.length > 0) {
                noDataMessage.classList.add('d-none');
                apiResult.data.list.forEach((order, index) => {
                    const row = tableBody.insertRow();
                    row.insertCell().textContent = (currentPage * pageSize) + index + 1;
                    row.insertCell().textContent = order.requestDate ? new Date(order.requestDate).toLocaleDateString() : '-';
                    row.insertCell().textContent = order.transferOrderNumber;
                    row.insertCell().textContent = order.requestingStoreName || '-';
                    row.insertCell().textContent = order.requestingUserName || '-';
                    row.insertCell().textContent = order.supplyingStoreName || '-';
                    
                    const statusCell = row.insertCell();
                    const statusBadge = document.createElement('span');
                    statusBadge.classList.add('badge');
                    statusBadge.textContent = order.statusDescription || order.transferStatus;
                    // TODO: Add color coding for StoreTransferOrderStatusEnum
                    switch (order.transferStatus) {
                        case 0: statusBadge.classList.add('bg-secondary'); break; // PENDING_APPROVAL
                        case 1: statusBadge.classList.add('bg-info'); break;       // APPROVED (待出庫)
                        case 2: statusBadge.classList.add('bg-primary'); break;    // DISPATCHED
                        case 3: statusBadge.classList.add('bg-success'); break;    // RECEIVED_COMPLETE
                        case 4: statusBadge.classList.add('bg-danger'); break;     // RECEIVED_DISCREPANCY
                        case 5: statusBadge.classList.add('bg-dark'); break;       // CANCELLED
                        default: statusBadge.classList.add('bg-light', 'text-dark'); break;
                    }
                    statusCell.appendChild(statusBadge);

                    const actionsCell = row.insertCell();
                    actionsCell.innerHTML = `<a href="store_transfer_order_detail.html?id=${order.storeTransferOrderId}" class="btn btn-sm btn-outline-primary"><i class="bi bi-eye"></i> 查看</a>`;
                });
                renderPagination(apiResult.data.page);
            } else {
                noDataMessage.classList.remove('d-none');
                tableBody.innerHTML = `<tr><td colspan="8" class="text-center">${apiResult.message || '查無調撥單資料'}</td></tr>`;
                paginationControls.innerHTML = '';
            }
        } catch (error) {
            console.error('Error fetching transfer orders:', error);
            tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">無法載入資料: ${error.message}</td></tr>`;
            noDataMessage.classList.remove('d-none');
        }
    }

    function renderPagination(pageData) {
        // Simplified pagination - adapt from other list pages if more complex one is needed
        paginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 1) return;

        for (let i = 0; i < pageData.totalPages; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === pageData.page ? 'active' : ''}`;
            const link = document.createElement('a');
            link.className = 'page-link';
            link.href = '#';
            link.textContent = i + 1;
            link.addEventListener('click', (e) => { e.preventDefault(); fetchTransferOrders(i); });
            li.appendChild(link);
            paginationControls.appendChild(li);
        }
    }

    filterForm.addEventListener('submit', function(event) {
        event.preventDefault();
        fetchTransferOrders(0);
    });

    clearFiltersBtn.addEventListener('click', function() {
        filterForm.reset();
        fetchTransferOrders(0);
    });

    async function init() {
        await loadSharedHTML();
        await populateStatusFilter();
        fetchTransferOrders();
    }

    init();
}); 