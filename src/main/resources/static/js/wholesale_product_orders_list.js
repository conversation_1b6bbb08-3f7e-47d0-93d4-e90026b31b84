document.addEventListener('DOMContentLoaded', function () {
    initDispatchOrderListPage();
});

async function initDispatchOrderListPage() {
        await Promise.all([
        populateStoreSelect(),
        populateOrderStatusSelect(),
        populateDispatchStatusSelect()
        ]);

    const searchForm = document.getElementById('search-orders-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function (event) {
            event.preventDefault();
            searchOrders(1); 
        });
    }

    const resetBtn = document.getElementById('resetSearchBtn');
    if(resetBtn) {
        resetBtn.addEventListener('click', function() {
            searchForm.reset();
            searchOrders(1);
        });
    }
    
    searchOrders(1); // Initial search
}

async function populateStoreSelect() {
    const selectElement = document.getElementById('searchStore');
    if (!selectElement) return;

    try {
        const response = await window.fetchAuthenticated('/api/v1/auth/operable-stores');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        
        const apiResponse = await response.json();
        if (apiResponse.code === 200 && Array.isArray(apiResponse.data)) {
            selectElement.innerHTML = '<option value="" selected>所有門市</option>';
            apiResponse.data.forEach(store => {
                const option = document.createElement('option');
                option.value = store.storeId;
                option.textContent = store.storeName;
                selectElement.appendChild(option);
            });
        } else {
            selectElement.innerHTML = '<option value="">無法載入門市</option>';
        }
    } catch (error) {
        if (selectElement) selectElement.innerHTML = '<option value="">載入門市失敗</option>';
    }
}

async function populateOrderStatusSelect() {
    const selectElement = document.getElementById('searchOrderStatus');
    if (!selectElement) return;
    try {
        const response = await window.fetchAuthenticated('/api/v1/enums/order-statuses');
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        const result = await response.json();
        if (result.code === 200 && Array.isArray(result.data)) {
            populateDropdown(selectElement, result.data, '所有訂單狀態');
        } else {
            console.error('Failed to populate order statuses:', result.message);
        }
    } catch(error) {
        console.error('Error fetching order statuses:', error);
        selectElement.innerHTML = '<option value="">載入失敗</option>';
    }
}

async function populateDispatchStatusSelect() {
    const selectElement = document.getElementById('searchDispatchStatus');
    if(!selectElement) return;
    try {
        const response = await window.fetchAuthenticated('/api/v1/enums/dispatch-statuses');
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        const result = await response.json();
        if (result.code === 200 && Array.isArray(result.data)) {
            populateDropdown(selectElement, result.data, '所有派工狀態');
        } else {
            console.error('Failed to populate dispatch statuses:', result.message);
        }
    } catch(error) {
        console.error('Error fetching dispatch statuses:', error);
        selectElement.innerHTML = '<option value="">載入失敗</option>';
    }
}

async function searchOrders(pageNumber = 1) {
    const storeId = document.getElementById('searchStore')?.value;
    const orderNumber = document.getElementById('searchOrderNumber')?.value;
    const customerKeyword = document.getElementById('searchCustomerKeyword')?.value;
    const orderStatusCode = document.getElementById('searchOrderStatus')?.value;
    const dispatchStatusCode = document.getElementById('searchDispatchStatus')?.value;
    const dateFrom = document.getElementById('searchOrderDateFrom')?.value;
    const dateTo = document.getElementById('searchOrderDateTo')?.value;
    const technician = document.getElementById('searchTechnician')?.value;
    
    const tableBody = document.getElementById('orders-table-body');
    const paginationControls = document.getElementById('pagination-controls');

    if (!tableBody || !paginationControls) return;

    tableBody.innerHTML = '<tr><td colspan="10" class="text-center p-4"><span class="spinner-border spinner-border-sm"></span> 查詢中...</td></tr>';
    paginationControls.innerHTML = '';

    const params = new URLSearchParams({
        page: pageNumber - 1,
        size: 10,
        sort: 'updateTime,desc',
        orderTypeCode: '3' // IMPORTANT: Dispatch orders only
    });

    const companyDivisionCode = window.getCompanyDivisionCode();
    if (companyDivisionCode !== null) {
        params.append('companyDivisionCode', companyDivisionCode);
    } else {
        console.error("無法獲取公司別，查詢中止。");
        tableBody.innerHTML = `<tr><td colspan="10" class="text-center p-4 text-danger">錯誤: 無法確定公司別，請重新登入。</td></tr>`;
        return;
    }

    if (storeId) params.append('storeId', storeId);
    if (orderNumber) params.append('orderNumber', orderNumber);
    if (customerKeyword) params.append('customerKeyword', customerKeyword);
    if (orderStatusCode) params.append('orderStatusCodes', orderStatusCode);
    if (dispatchStatusCode) params.append('dispatchStatusCodes', dispatchStatusCode);
    if (dateFrom) params.append('orderDateFrom', new Date(dateFrom).toISOString());
    if (dateTo) {
        const endDate = new Date(dateTo);
        endDate.setHours(23, 59, 59, 999);
        params.append('orderDateTo', endDate.toISOString());
    }
    if (technician) params.append('technicianKeyword', technician);

    try {
        const response = await window.fetchAuthenticated(`/api/v1/orders?${params.toString()}`);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        
        const apiResponse = await response.json();
        if (apiResponse.code === 200 && apiResponse.data) {
            renderTable(apiResponse.data.list);
            renderPagination(pageNumber, apiResponse.data.totalPages);
        } else {
            throw new Error(apiResponse.message || '查詢訂單失敗');
        }
    } catch (error) {
        console.error('查詢派工訂單時發生錯誤:', error);
        tableBody.innerHTML = `<tr><td colspan="10" class="text-center p-4 text-danger">查詢失敗: ${error.message}</td></tr>`;
    }
}

function renderTable(orders) {
    const tableBody = document.getElementById('orders-table-body');
    tableBody.innerHTML = '';

    if (!orders || orders.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="10" class="text-center p-4">查無資料</td></tr>';
        return;
    }

    orders.forEach(order => {
        const row = document.createElement('tr');
        
        // Determine the correct link based on order status
        let linkHref;
        if (order.orderStatusCode === 10) { // 10: 草稿
            linkHref = `dispatch_product_order_form.html?orderId=${order.orderId}`;
        } else {
            linkHref = `order_detail.html?orderId=${order.orderId}&orderType=3`;
        }

        row.innerHTML = `
            <td><a href="${linkHref}">${order.orderNumber}</a></td>
            <td>${order.orderDate ? new Date(order.orderDate).toLocaleDateString() : 'N/A'}</td>
            <td>${order.customerName || 'N/A'}</td>
            <td>${order.installationAddress || 'N/A'}</td>
            <td>${order.technicianName || '未指派'}</td>
            <td>${order.grandTotalAmount != null ? `NT$ ${order.grandTotalAmount.toFixed(2)}` : 'N/A'}</td>
            <td><span class="badge ${getStatusBadgeClass(order.orderStatusCode)}">${order.orderStatusDescription || 'N/A'}</span></td>
            <td><span class="badge ${getDispatchStatusBadgeClass(order.dispatchStatusCode)}">${order.dispatchStatusDescription || 'N/A'}</span></td>
            <td>${order.updateTime ? new Date(order.updateTime).toLocaleString() : 'N/A'}</td>
            <td>
                <a href="${linkHref}" class="btn btn-sm btn-outline-primary" title="檢視/編輯">
                    <i class="bi bi-search"></i>
                </a>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function renderPagination(currentPage, totalPages) {
    const paginationControls = document.getElementById('pagination-controls');
    paginationControls.innerHTML = '';
    if (totalPages <= 1) return;

    const createPageItem = (text, page, isDisabled = false, isActive = false) => {
        const li = document.createElement('li');
        li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        if (!isDisabled) {
            a.onclick = (e) => { e.preventDefault(); searchOrders(page); };
        }
        li.appendChild(a);
        return li;
    };

    paginationControls.appendChild(createPageItem('‹', currentPage - 1, currentPage === 1));
    for (let i = 1; i <= totalPages; i++) {
        paginationControls.appendChild(createPageItem(i, i, false, i === currentPage));
    }
    paginationControls.appendChild(createPageItem('›', currentPage + 1, currentPage === totalPages));
}

function getStatusBadgeClass(statusCode) {
    // New rules for specific "in review" statuses
    if (statusCode === 20 || statusCode === 40) {
        return 'bg-primary'; // Blue for pending reviews
    }
    if (statusCode === -70) {
        return 'bg-info'; // Light blue for return review
    }

    // Existing rules
    if(statusCode >= 70) return 'bg-success';
    if(statusCode < 0) return 'bg-danger';
    return 'bg-secondary';
}

function getDispatchStatusBadgeClass(dispatchStatusCode) {
    if(dispatchStatusCode === 2) return 'bg-success'; // Completed
    if(dispatchStatusCode === 1) return 'bg-primary'; // Dispatched
    return 'bg-warning text-dark'; // Pending
}

function populateDropdown(selectElement, options, defaultText) {
    selectElement.innerHTML = `<option value="">${defaultText}</option>`;
    if (options && Array.isArray(options)) {
        options.forEach(option => {
            selectElement.innerHTML += `<option value="${option.value}">${option.label}</option>`;
        });
    }
}

// End of script for dispatch_product_orders_list.js 