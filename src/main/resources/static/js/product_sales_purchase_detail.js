document.addEventListener('DOMContentLoaded', function () {
    console.log('product_sales_purchase_detail.js loaded');

    const urlParams = new URLSearchParams(window.location.search);
    const productBarcode = urlParams.get('barcode');
    const productName = urlParams.get('name');

    const pageTitle = document.getElementById('pageTitle');
    const productBarcodeDisplay = document.getElementById('productBarcodeDisplay');
    const productNameDisplay = document.getElementById('productNameDisplay');
    const detailDateFromFilter = document.getElementById('detailDateFromFilter');
    const detailDateToFilter = document.getElementById('detailDateToFilter');
    const filterDetailButton = document.getElementById('filterDetailButton');
    const transactionDetailTableBody = document.getElementById('transactionDetailTableBody');
    const detailPaginationControls = document.getElementById('detailPaginationControls');
    const backToListButton = document.getElementById('backToListBtn');

    let currentPage = 0;
    const DEFAULT_PAGE_SIZE = 10; // Can be different from summary page

    if (productName) {
        pageTitle.textContent = `商品進銷明細: ${productName}`;
    }
    if (productBarcodeDisplay) productBarcodeDisplay.textContent = productBarcode || 'N/A';
    if (productNameDisplay) productNameDisplay.textContent = productName || 'N/A';

    async function fetchAndDisplayTransactionDetails(page = 0) {
        currentPage = page;
        if (!productBarcode) {
            showToast('未提供商品條碼', 'error');
            transactionDetailTableBody.innerHTML = '<tr><td colspan="5" class="text-center">未指定商品</td></tr>';
            return;
        }

        const dateFrom = detailDateFromFilter.value ? new Date(detailDateFromFilter.value).toISOString() : '';
        const dateTo = detailDateToFilter.value ? new Date(detailDateToFilter.value).toISOString() : '';

        let url = `/api/v1/product-sales-purchase/details/${encodeURIComponent(productBarcode)}?page=${currentPage}&size=${DEFAULT_PAGE_SIZE}`;
        if (dateFrom) url += `&dateFrom=${encodeURIComponent(dateFrom)}`;
        if (dateTo) url += `&dateTo=${encodeURIComponent(dateTo)}`;
        
        console.log(`Fetching transaction details from: ${url}`);
        transactionDetailTableBody.innerHTML = '<tr><td colspan="5" class="text-center">載入中...</td></tr>';

        try {
            const response = await fetchAuthenticated(url);
            if (!response.ok) {
                const errData = await response.json().catch(() => ({ message: '獲取交易明細失敗' }));
                throw new Error(errData.message || `HTTP Error ${response.status}`);
            }
            const apiResponse = await response.json();

            if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.list) {
                renderDetailTable(apiResponse.data.list);
                renderDetailPagination(apiResponse.data.page);
                console.log("Transaction details displayed.");
            } else {
                transactionDetailTableBody.innerHTML = '<tr><td colspan="5" class="text-center">查詢無交易明細或發生錯誤</td></tr>';
                renderDetailPagination(null);
                showToast(apiResponse.message || '查詢無明細資料', 'info');
            }
        } catch (error) {
            console.error('Error fetching transaction details:', error);
            transactionDetailTableBody.innerHTML = '<tr><td colspan="5" class="text-center">載入明細失敗</td></tr>';
            showToast(`載入明細失敗: ${error.message}`, 'error');
        }
    }

    function renderDetailTable(transactions) {
        transactionDetailTableBody.innerHTML = '';
        if (!transactions || transactions.length === 0) {
            transactionDetailTableBody.innerHTML = '<tr><td colspan="5" class="text-center">此期間內無交易記錄</td></tr>';
            return;
        }
        transactions.forEach(tx => {
            const row = transactionDetailTableBody.insertRow();
            row.insertCell().textContent = tx.transactionDate ? new Date(tx.transactionDate).toLocaleDateString() : 'N/A';
            row.insertCell().textContent = tx.transactionType;
            
            const orderNumberCell = row.insertCell();
            if (tx.orderUrl && tx.orderNumber !== 'N/A') {
                const link = document.createElement('a');
                link.href = tx.orderUrl; // URLs are constructed in ProductSalesPurchaseServiceImpl
                link.textContent = tx.orderNumber;
                link.target = '_blank'; // Optional: open in new tab
                orderNumberCell.appendChild(link);
            } else {
                orderNumberCell.textContent = tx.orderNumber;
            }
            
            row.insertCell().textContent = tx.purchaseQuantity > 0 ? tx.purchaseQuantity : '-';
            row.insertCell().textContent = tx.salesQuantity > 0 ? tx.salesQuantity : '-';
        });
    }

    function renderDetailPagination(pageData) {
        detailPaginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 0) {
            return;
        }
        const createPageItem = (text, pageNumber, isDisabled = false, isActive = false) => {
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const a = document.createElement('a');
            a.className = 'page-link';
            a.href = '#';
            a.textContent = text;
            if (!isDisabled) {
                a.addEventListener('click', (e) => {
                    e.preventDefault();
                    fetchAndDisplayTransactionDetails(pageNumber);
                });
            }
            li.appendChild(a);
            return li;
        };
        detailPaginationControls.appendChild(createPageItem('上一頁', pageData.page - 1, pageData.page === 0));
        const maxPagesToShow = 5;
        let startPage = Math.max(0, pageData.page - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(pageData.totalPages - 1, startPage + maxPagesToShow - 1);
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(0, endPage - maxPagesToShow + 1);
        }
        for (let i = startPage; i <= endPage; i++) {
            detailPaginationControls.appendChild(createPageItem(i + 1, i, false, i === pageData.page));
        }
        detailPaginationControls.appendChild(createPageItem('下一頁', pageData.page + 1, pageData.page === pageData.totalPages - 1));
    }

    if (filterDetailButton) {
        filterDetailButton.addEventListener('click', () => fetchAndDisplayTransactionDetails(0));
    }

    if (backToListButton) {
        backToListButton.addEventListener('click', function() {
            window.history.back();
        });
    }

    // Initial load
    if (productBarcode) {
        fetchAndDisplayTransactionDetails();
    } else {
        pageTitle.textContent = "商品進銷明細 - 錯誤";
        transactionDetailTableBody.innerHTML = '<tr><td colspan="5" class="text-center">未指定商品條碼</td></tr>';
        showToast('錯誤：未指定商品條碼', 'error');
    }
}); 