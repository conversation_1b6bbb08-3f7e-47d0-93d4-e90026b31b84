document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.getElementById('repair-orders-table-body');
    const searchInput = document.getElementById('keyword-search');
    const paginationControls = document.getElementById('pagination-controls');
    let searchTimeout;

    async function searchDispatchRepairs(page = 1) {
        const keyword = searchInput.value.trim();
        const params = new URLSearchParams({
            page: page - 1,
            size: 15,
            sort: 'scheduledDate,asc'
        });
        if (keyword) {
            // This keyword search needs to be implemented in the backend if it's a new requirement
            // For now, let's assume it searches the 'number' field
            params.append('number', keyword); 
        }

        tableBody.innerHTML = '<tr><td colspan="7" class="text-center">查詢中...</td></tr>';
        
        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders?${params.toString()}`);
            if (!response.ok) throw new Error("查詢失敗");
            const result = await response.json();
            
            renderTable(result.data.list || []); 
            renderPagination(result.data.page);
        } catch (error) {
            tableBody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">查詢失敗: ${error.message}</td></tr>`;
        }
    }

    // Reuse the color logic from dispatch_order_list.js
    function getOrderTypeClass(typeCode) {
        const typeClassMap = {
            10: 'bg-primary',   // 派工
            20: 'bg-warning',   // 維修
            30: 'bg-secondary'  // 退機
        };
        return typeClassMap[typeCode] || 'bg-info';
    }

    function renderTable(orders) {
        tableBody.innerHTML = '';
        if (!orders || orders.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" class="text-center">查無任何派工維修單資料</td></tr>';
            return;
        }
        orders.forEach(order => {
            const row = tableBody.insertRow();
            const typeBadge = `<span class="badge ${getOrderTypeClass(order.dispatchTypeCode)}">${order.dispatchTypeDescription}</span>`;
            
            // Determine the link based on the status code
            const detailPage = (order.dispatchStatusCode === 10) ? 'dispatch_mgr_form.html' : 'dispatch_mgr_detail.html';
            const detailLink = `<a href="${detailPage}?id=${order.dispatchOrderId}">${order.dispatchOrderNumber}</a>`;
            const urgentText = order.isUrgent === 1 ? '<span class="text-danger ms-2 fw-bold">急單</span>' : '';

            row.innerHTML = `
                <td>${detailLink}</td>
                <td>${typeBadge}</td>
                <td>${order.scheduledDate || '-'}</td>
                <td>${order.customerName || 'N/A'}</td>
                <td>${order.deviceSerialNumber || 'N/A'}</td>
                <td>${order.assignedTechnicianName || 'N/A'}</td>
                <td><span class="badge bg-secondary">${order.dispatchStatusDescription}</span>${urgentText}</td>
            `;
        });
    }

    function renderPagination(pageData) {
        paginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 1) return;

        const { number: page, totalPages } = pageData;
        let paginationHtml = '<ul class="pagination justify-content-center">';
        paginationHtml += `<li class="page-item ${page === 0 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page}">‹</a></li>`;
        for (let i = 0; i < totalPages; i++) {
            paginationHtml += `<li class="page-item ${i === page ? 'active' : ''}"><a class="page-link" href="#" data-page="${i + 1}">${i + 1}</a></li>`;
        }
        paginationHtml += `<li class="page-item ${page >= totalPages - 1 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page + 2}">›</a></li>`;
        paginationHtml += '</ul>';
        paginationControls.innerHTML = paginationHtml;

        paginationControls.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                if (!this.parentElement.classList.contains('disabled')) {
                    searchDispatchRepairs(parseInt(this.dataset.page));
                }
            });
        });
    }

    searchInput.addEventListener('input', () => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => searchDispatchRepairs(1), 500);
    });

    document.querySelector('.page-header a.btn').href = 'dispatch_mgr_form.html';

    // Initial load
    searchDispatchRepairs(1);
}); 