// static/js/store_transfer_order_form.js
document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('transfer-order-form');
    const transferOrderIdField = document.getElementById('transferOrderId');
    const requestingStoreIdSelect = document.getElementById('requestingStoreIdSelect');
    const requestingUserNameField = document.getElementById('requestingUserName');
    const requestingUserIdField = document.getElementById('requestingUserId');
    const supplyingStoreIdField = document.getElementById('supplyingStoreId');
    const notesField = document.getElementById('notes');
    const addProductItemBtn = document.getElementById('addProductItemBtn');
    const transferItemsTableBody = document.getElementById('transferItemsTableBody');
    const noTransferItemsMessage = document.getElementById('noTransferItemsMessage');
    const formErrorMessage = document.getElementById('formErrorMessage');
    const formTitle = document.getElementById('form-title');
    const formModeBreadcrumb = document.getElementById('form-mode-breadcrumb');

    const productSearchModalEl = document.getElementById('productSearchModal');
    const productSearchModal = new bootstrap.Modal(productSearchModalEl);
    const productSearchKeywordInput = document.getElementById('productSearchKeywordInput');
    const productSearchExecuteBtn = document.getElementById('productSearchExecuteBtn');
    const productSearchResultsContainer = document.getElementById('productSearchResultsContainer');
    const noModalProductResultsMessage = document.getElementById('noModalProductResultsMessage');

    const API_URL_TRANSFER_ORDERS = '/api/v1/store-transfer-orders'; // To be defined
    const API_URL_STORES_SELECTABLE = '/api/v1/stores/selectable'; // Corrected endpoint
    const API_URL_OPERABLE_STORES = '/api/v1/auth/operable-stores'; // Added API endpoint
    const API_URL_PRODUCTS = '/api/v1/external-products'; // For searching products

    const urlParams = new URLSearchParams(window.location.search);
    const currentTransferId = urlParams.get('id');
    const mode = urlParams.get('mode') || (currentTransferId ? 'view' : 'add'); // 'add', 'edit', 'view'

    let transferItems = [];

    function showError(message) {
        formErrorMessage.textContent = message;
        formErrorMessage.classList.remove('d-none');
    }
    function clearError() {
        formErrorMessage.classList.add('d-none');
        formErrorMessage.textContent = '';
    }

    async function initializeForm() {
        await loadSharedHTML();
        await populateOperableRequestingStores();
        requestingUserNameField.value = localStorage.getItem('userName') || '未知使用者';

        if (mode === 'edit' && currentTransferId) {
            formTitle.textContent = '編輯門市調撥單';
            formModeBreadcrumb.textContent = '編輯門市調撥單';
            // TODO: Load transfer order data
        } else if (mode === 'view' && currentTransferId) {
            formTitle.textContent = '查看門市調撥單';
            formModeBreadcrumb.textContent = '查看門市調撥單';
            // TODO: Load transfer order data and disable form fields
            document.getElementById('saveTransferOrderBtn').style.display = 'none';
            addProductItemBtn.style.display = 'none';
        } else { // add mode
            formTitle.textContent = '新增門市調撥單';
            formModeBreadcrumb.textContent = '新增門市調撥單';
            updateTransferItemsTable();
        }
    }

    async function populateOperableRequestingStores() {
        if (!requestingStoreIdSelect) return;
        requestingStoreIdSelect.innerHTML = '<option value="">載入可操作門市...</option>';
        let operableStoresData = []; // Store fetched data to access regionId
        try {
            const response = await window.fetchAuthenticated(API_URL_OPERABLE_STORES);
            const apiResult = await response.json();
            if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
                operableStoresData = apiResult.data; // Save the data
                requestingStoreIdSelect.innerHTML = '<option value="">請選擇申請門市...</option>';
                if (apiResult.data.length === 0) {
                    requestingStoreIdSelect.innerHTML = '<option value="">無可用門市</option>';
                    showError('您目前沒有權限操作任何門市，無法建立調撥單。');
                    // Disable form or parts of it
                    document.getElementById('saveTransferOrderBtn').disabled = true;
                    addProductItemBtn.disabled = true;
                    supplyingStoreIdField.disabled = true;
                } else {
                     document.getElementById('saveTransferOrderBtn').disabled = false;
                     addProductItemBtn.disabled = false;
                     supplyingStoreIdField.disabled = false;
                }

                apiResult.data.forEach(store => {
                    const option = document.createElement('option');
                    option.value = store.storeId;
                    option.textContent = store.storeName;
                    option.dataset.regionId = store.regionId; // Store regionId for potential future use
                    requestingStoreIdSelect.appendChild(option);
                });

                // Try to pre-select based on localStorage 'selectedStore' if it's in the operable list
                const previouslySelectedStoreStr = localStorage.getItem('selectedStore');
                if (previouslySelectedStoreStr) {
                    const previouslySelectedStore = JSON.parse(previouslySelectedStoreStr);
                    if (apiResult.data.some(s => s.storeId === previouslySelectedStore.storeId)) {
                        requestingStoreIdSelect.value = previouslySelectedStore.storeId;
                    }
                }
                // Trigger change to repopulate supplying stores, excluding the newly selected requesting store
                requestingStoreIdSelect.dispatchEvent(new Event('change')); 

            } else {
                requestingStoreIdSelect.innerHTML = '<option value="">無法載入門市</option>';
                showToast(apiResult.message || '載入申請門市選項失敗', 'error');
                showError('無法載入您的可操作門市列表。');
            }
        } catch (error) {
            console.error('Error populating operable requesting stores:', error);
            requestingStoreIdSelect.innerHTML = '<option value="">載入門市失敗</option>';
            showToast(`載入申請門市失敗: ${error.message}`, 'error');
            showError('載入可操作門市列表時發生錯誤。');
        }
    }

    async function populateSupplyingStoreDropdown() {
        if (!supplyingStoreIdField || !requestingStoreIdSelect) return;
        const selectedRequestingStoreId = requestingStoreIdSelect.value;
        let selectedRequestingStoreRegionId = null;

        // Find the selected requesting store from the operableStoresData to get its regionId
        // This requires operableStoresData to be accessible here. We might need to scope it higher or pass it.
        // For simplicity, let's re-fetch operable stores if not available, or better, store it more globally in this script.
        // Assuming operableStoresData is available from populateOperableRequestingStores call.
        // This part needs adjustment if operableStoresData is not in scope.
        // Let's fetch operable stores again to get regionId for the selected requesting store.
        // Ideally, the store object (with regionId) from operableStoresData would be stored when selected.

        if (selectedRequestingStoreId) {
            // Find the selected store in the list populated by populateOperableRequestingStores
            // This assumes that `operableStoresData` (from populateOperableRequestingStores) is accessible or passed around.
            // For now, as a direct fix, we know the `requestingStoreIdSelect` options were populated from `operableStoresData`.
            // We need to find the selected option and get its regionId which should have been stored as a data attribute.
            
            const selectedOption = requestingStoreIdSelect.options[requestingStoreIdSelect.selectedIndex];
            if (selectedOption && selectedOption.dataset.regionId) {
                selectedRequestingStoreRegionId = selectedOption.dataset.regionId;
            } else if (selectedOption && selectedOption.value) {
                // Fallback: if regionId not on data attribute, try to find it in a locally scoped array if populated earlier.
                // This part is tricky without knowing the exact structure of `operableStoresData` persistence here.
                // The safest way is to ensure regionId is available when populating requestingStoreIdSelect options.
                console.warn("Region ID not found on selected requesting store option. Supplying stores might not be filtered by region.");
            }
        }

        let url = API_URL_STORES_SELECTABLE;
        if (selectedRequestingStoreRegionId && selectedRequestingStoreRegionId !== "null" && selectedRequestingStoreRegionId !== "undefined") {
            url += `?regionId=${selectedRequestingStoreRegionId}`;
        }

        supplyingStoreIdField.innerHTML = '<option value="">載入中...</option>';
        try {
            const response = await window.fetchAuthenticated(url);
            const apiResult = await response.json();
            if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
                supplyingStoreIdField.innerHTML = '<option value="">請選擇調撥門市...</option>';
                apiResult.data.forEach(store => {
                    if (selectedRequestingStoreId && store.storeId === selectedRequestingStoreId) return;
                    const option = document.createElement('option');
                    option.value = store.storeId;
                    option.textContent = store.storeName + (store.storeCode ? ` (${store.storeCode})` : '');
                    option.dataset.regionId = store.regionId; // Store regionId for potential future use
                    supplyingStoreIdField.appendChild(option);
                });
            } else {
                console.error("Error fetching stores for dropdown, API response:", apiResult);
                supplyingStoreIdField.innerHTML = '<option value="">無法載入門市</option>';
                showToast(apiResult.message || '載入調撥門市選項失敗', 'error');
            }
        } catch (error) {
            console.error('Error populating supplying stores:', error);
            supplyingStoreIdField.innerHTML = '<option value="">載入門市失敗</option>';
            showToast(`載入調撥門市失敗: ${error.message}`, 'error');
        }
    }
    
    function updateTransferItemsTable() {
        transferItemsTableBody.innerHTML = '';
        if (transferItems.length === 0) {
            noTransferItemsMessage.classList.remove('d-none');
            return;
        }
        noTransferItemsMessage.classList.add('d-none');
        transferItems.forEach((item, index) => {
            const row = transferItemsTableBody.insertRow();
            row.insertCell().textContent = item.productBarcode;
            row.insertCell().textContent = item.productName || 'N/A';
            
            const qtyCell = row.insertCell();
            const qtyInput = document.createElement('input');
            qtyInput.type = 'number';
            qtyInput.className = 'form-control form-control-sm transfer-item-quantity';
            qtyInput.value = item.requestedQuantity;
            qtyInput.min = "1";
            qtyInput.required = true;
            qtyInput.dataset.barcode = item.productBarcode;
            qtyInput.addEventListener('change', (e) => {
                const barcode = e.target.dataset.barcode;
                const newQuantity = parseInt(e.target.value);
                const foundItem = transferItems.find(it => it.productBarcode === barcode);
                if (foundItem && newQuantity >= 1) {
                    foundItem.requestedQuantity = newQuantity;
                } else if(foundItem) {
                     e.target.value = foundItem.requestedQuantity; // Revert
                }
            });
            qtyCell.appendChild(qtyInput);

            const deleteBtnCell = row.insertCell();
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-sm btn-outline-danger';
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            deleteBtn.onclick = () => {
                transferItems.splice(index, 1);
                updateTransferItemsTable();
            };
            deleteBtnCell.appendChild(deleteBtn);
        });
    }

    addProductItemBtn.addEventListener('click', () => {
        productSearchKeywordInput.value = '';
        productSearchResultsContainer.innerHTML = '';
        noModalProductResultsMessage.classList.add('d-none');
        productSearchModal.show();
    });

    productSearchExecuteBtn.addEventListener('click', async () => {
        const keyword = productSearchKeywordInput.value.trim();
        if (!keyword) { showToast('請輸入商品關鍵字', 'warning'); return; }
        productSearchResultsContainer.innerHTML = '<div class="list-group-item">搜尋中...</div>';
        noModalProductResultsMessage.classList.add('d-none');
        try {
            const response = await window.fetchAuthenticated(`${API_URL_PRODUCTS}?keyword=${encodeURIComponent(keyword)}`);
            if (!response.ok) throw new Error('商品搜尋失敗');
            const apiResult = await response.json();
            productSearchResultsContainer.innerHTML = '';
            if (apiResult.code === 200 && apiResult.data && apiResult.data.length > 0) {
                apiResult.data.forEach(product => {
                    const itemEl = document.createElement('a');
                    itemEl.href = '#';
                    itemEl.className = 'list-group-item list-group-item-action';
                    itemEl.textContent = `${product.productName} (${product.productBarcode})`;
                    itemEl.addEventListener('click', (e) => {
                        e.preventDefault();
                        if (!transferItems.find(ti => ti.productBarcode === product.productBarcode)) {
                            transferItems.push({
                                productBarcode: product.productBarcode,
                                productName: product.productName,
                                requestedQuantity: 1 // Default quantity
                            });
                            updateTransferItemsTable();
                        } else {
                            showToast('此商品已在調撥列表中。', 'warning');
                        }
                        productSearchModal.hide();
                    });
                    productSearchResultsContainer.appendChild(itemEl);
                });
            } else {
                noModalProductResultsMessage.classList.remove('d-none');
            }
        } catch (error) {
            console.error('Error searching products for transfer:', error);
            showToast('搜尋商品時發生錯誤', 'error');
            noModalProductResultsMessage.classList.remove('d-none');
        }
    });
    
    form.addEventListener('submit', async function(event) {
        event.preventDefault();
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            showToast('請檢查所有必填欄位。', 'warning');
            return;
        }
        form.classList.remove('was-validated');
        clearError();

        if (!supplyingStoreIdField.value) {
            showError('必須選擇一個調撥對象門市。');
            return;
        }
        if (!requestingStoreIdSelect.value) {
            showError('必須選擇申請門市。');
            return;
        }
        if (transferItems.length === 0) {
            showError('必須至少加入一個調撥商品。');
            return;
        }
        for (const item of transferItems) {
            if (!item.requestedQuantity || item.requestedQuantity < 1) {
                showError(`商品 ${item.productName} 的調撥數量必須至少為1。`);
                return;
            }
        }

        const payload = {
            requestingStoreId: requestingStoreIdSelect.value,
            // requestingUserId will be set by backend based on authenticated user
            supplyingStoreId: supplyingStoreIdField.value,
            notes: notesField.value || null,
            items: transferItems.map(item => ({ 
                productBarcode: item.productBarcode, 
                requestedQuantity: item.requestedQuantity 
            }))
        };
        
        // Confirmation Dialog
        if (!window.confirm("確定要送出此門市調撥申請嗎？")) {
            return; // User cancelled
        }

        try {
            // For add mode, method is POST, URL is base URL
            const response = await window.fetchAuthenticated(API_URL_TRANSFER_ORDERS, {
                method: 'POST',
                body: JSON.stringify(payload)
            });
            const result = await response.json();
            if (response.ok && result.code === 201) { // 201 Created for POST
                showToast('門市調撥單已成功建立！', 'success');
                window.location.href = 'store_transfer_orders.html';
            } else {
                showError(result.message || '儲存失敗');
            }
        } catch (error) {
            console.error('Error saving transfer order:', error);
            showError('儲存調撥單時發生錯誤: ' + error.message);
        }
    });

    initializeForm();

    // Add event listener to re-populate supplying stores when requesting store changes
    if(requestingStoreIdSelect) {
        requestingStoreIdSelect.addEventListener('change', populateSupplyingStoreDropdown);
    }
}); 