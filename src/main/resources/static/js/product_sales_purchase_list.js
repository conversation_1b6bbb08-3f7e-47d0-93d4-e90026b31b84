// Placeholder for product_sales_purchase_list.js
document.addEventListener('DOMContentLoaded', function () {
    console.log('product_sales_purchase_list.js loaded');

    const productCategoryFilter = document.getElementById('productCategoryFilter');
    const dateFromFilter = document.getElementById('dateFromFilter');
    const dateToFilter = document.getElementById('dateToFilter');
    const keywordFilter = document.getElementById('keywordFilter');
    const searchButton = document.getElementById('searchButton');
    const resetButton = document.getElementById('resetButton'); 
    const salesPurchaseTableBody = document.getElementById('salesPurchaseTableBody');
    const paginationControls = document.getElementById('paginationControls');

    let currentPage = 0;
    const DEFAULT_PAGE_SIZE = 20;

    async function loadProductCategories() {
        console.log("Fetching product categories for filter...");
        try {
            const response = await fetchAuthenticated('/api/v1/product-menus/tree/DISPATCH'); 
            if (!response.ok) {
                 const errData = await response.json().catch(() => ({ message: 'Failed to load product categories tree' }));
                throw new Error(errData.message || `HTTP Error ${response.status}`);
            }
            const apiResponse = await response.json();
            
            if (apiResponse.code === 200 && Array.isArray(apiResponse.data)) {
                 productCategoryFilter.innerHTML = '<option value="" selected>全部分類</option>';
                 const extractCategories = (nodes, allCategories) => {
                     if (!nodes) return;
                     nodes.forEach(node => {
                         if (node.type === 'CATEGORY') {
                             allCategories.push({ id: node.id, name: node.name });
                             if (node.children && node.children.length > 0) {
                                 extractCategories(node.children, allCategories);
                             }
                         }
                     });
                 };
                 let categories = [];
                 extractCategories(apiResponse.data, categories);
                 const uniqueCategories = categories.filter((cat, index, self) => index === self.findIndex((c) => c.id === cat.id));
                 
                 uniqueCategories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id; 
                    option.textContent = category.name;
                    productCategoryFilter.appendChild(option);
                 });
                 console.log("Product categories loaded for filter.");
            } else {
                console.error('Error or unexpected format for product categories:', apiResponse);
                showToast(apiResponse.message || '無法載入商品分類選項', 'error');
            }
        } catch (e) {
            console.error("Error loading product categories:", e);
            showToast(`載入商品分類失敗: ${e.message}`, 'error');
        }
    }

    async function fetchAndDisplaySalesPurchaseSummary(page = 0) {
        currentPage = page;
        const categoryId = productCategoryFilter.value;
        const dateFrom = dateFromFilter.value ? new Date(dateFromFilter.value).toISOString() : '';
        const dateTo = dateToFilter.value ? new Date(dateToFilter.value).toISOString() : '';
        const keyword = keywordFilter.value.trim();

        let url = `/api/v1/product-sales-purchase/summary?page=${currentPage}&size=${DEFAULT_PAGE_SIZE}`;
        if (categoryId) url += `&productCategoryId=${encodeURIComponent(categoryId)}`;
        if (dateFrom) url += `&dateFrom=${encodeURIComponent(dateFrom)}`;
        if (dateTo) url += `&dateTo=${encodeURIComponent(dateTo)}`;
        if (keyword) url += `&keyword=${encodeURIComponent(keyword)}`;
        
        console.log(`Fetching product sales/purchase summary from: ${url}`);
        salesPurchaseTableBody.innerHTML = '<tr><td colspan="6" class="text-center">載入中...</td></tr>';

        try {
            const response = await fetchAuthenticated(url);
            if (!response.ok) {
                const errData = await response.json().catch(() => ({ message: '獲取進銷列表失敗' }));
                throw new Error(errData.message || `HTTP Error ${response.status}`);
            }
            const apiResponse = await response.json();

            if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.list) {
                renderTable(apiResponse.data.list);
                renderPagination(apiResponse.data.page);
                console.log("Product sales/purchase data displayed.");
            } else {
                salesPurchaseTableBody.innerHTML = '<tr><td colspan="6" class="text-center">查詢無資料或發生錯誤</td></tr>';
                renderPagination(null);
                console.warn('No sales/purchase data or error in API response:', apiResponse.message);
                showToast(apiResponse.message || '查詢無資料', 'info');
            }
        } catch (error) {
            console.error('Error fetching product sales/purchase data:', error);
            salesPurchaseTableBody.innerHTML = '<tr><td colspan="6" class="text-center">載入資料失敗</td></tr>';
            showToast(`載入資料失敗: ${error.message}`, 'error');
        }
    }

    function renderTable(summaryItems) {
        salesPurchaseTableBody.innerHTML = ''; 
        if (!summaryItems || summaryItems.length === 0) {
            salesPurchaseTableBody.innerHTML = '<tr><td colspan="6" class="text-center">無符合條件的資料</td></tr>';
            return;
        }
        summaryItems.forEach(item => {
            const row = salesPurchaseTableBody.insertRow();
            row.insertCell().textContent = item.productCategory || 'N/A';
            row.insertCell().textContent = item.productBarcode;
            
            const productNameCell = row.insertCell();
            const productNameLink = document.createElement('a');
            productNameLink.href = `product_sales_purchase_detail.html?barcode=${encodeURIComponent(item.productBarcode)}&name=${encodeURIComponent(item.productName)}`;
            productNameLink.textContent = item.productName;
            productNameCell.appendChild(productNameLink);
            
            row.insertCell().textContent = item.totalPurchaseQuantity || 0;
            row.insertCell().textContent = item.totalSalesQuantity || 0;
            
            const actionsCell = row.insertCell();
            const viewDetailsButton = document.createElement('button');
            viewDetailsButton.className = 'btn btn-sm btn-outline-dark';
            viewDetailsButton.innerHTML = '<i class="bi bi-eye"></i> 查看明細';
            viewDetailsButton.onclick = function() {
                window.location.href = `product_sales_purchase_detail.html?barcode=${encodeURIComponent(item.productBarcode)}&name=${encodeURIComponent(item.productName)}`;
            };
            actionsCell.appendChild(viewDetailsButton);
        });
    }

    function renderPagination(pageData) {
        paginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 0) {
            return;
        }
        const createPageItem = (text, pageNumber, isDisabled = false, isActive = false) => {
            const li = document.createElement('li');
            li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
            const a = document.createElement('a');
            a.className = 'page-link';
            a.href = '#';
            a.textContent = text;
            if (!isDisabled) {
                a.addEventListener('click', (e) => {
                    e.preventDefault();
                    fetchAndDisplaySalesPurchaseSummary(pageNumber);
                });
            }
            li.appendChild(a);
            return li;
        };
        paginationControls.appendChild(createPageItem('上一頁', pageData.page - 1, pageData.page === 0));
        const maxPagesToShow = 5;
        let startPage = Math.max(0, pageData.page - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(pageData.totalPages - 1, startPage + maxPagesToShow - 1);
        if (endPage - startPage + 1 < maxPagesToShow) {
            startPage = Math.max(0, endPage - maxPagesToShow + 1);
        }
        for (let i = startPage; i <= endPage; i++) {
            paginationControls.appendChild(createPageItem(i + 1, i, false, i === pageData.page));
        }
        paginationControls.appendChild(createPageItem('下一頁', pageData.page + 1, pageData.page === pageData.totalPages - 1));
    }

    if (searchButton) {
        searchButton.addEventListener('click', () => fetchAndDisplaySalesPurchaseSummary(0));
    }
    if (resetButton) {
        resetButton.addEventListener('click', () => {
            productCategoryFilter.value = '';
            dateFromFilter.value = '';
            dateToFilter.value = '';
            keywordFilter.value = '';
            fetchAndDisplaySalesPurchaseSummary(0);
        });
    }

    loadProductCategories();
    fetchAndDisplaySalesPurchaseSummary();
}); 