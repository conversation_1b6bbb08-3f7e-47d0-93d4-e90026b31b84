document.addEventListener('DOMContentLoaded', function () {
    // --- DOM Elements ---
    const infoCardContainer = document.getElementById('info-card-container');
    const mainItemsContainer = document.getElementById('main-items-container');
    const otherItemsContainer = document.getElementById('other-items-container');
    const workForm = document.getElementById('work-form');
    const detailBreadcrumbLink = document.getElementById('detail-breadcrumb-link');
    // const partSearchModalEl = document.getElementById('partSearchModal'); // No longer needed globally
    // let currentTargetItemId = null; // Removed for better context handling
    // ... other elements

    // --- State ---
    const urlParams = new URLSearchParams(window.location.search);
    const dispatchRepairId = urlParams.get('id');
    let originalOrderData = null;
    let maxUploadSizeKB = 1024; // Default value

    // --- Initialization ---
    async function initializePage() {
        // Fetch config first
        try {
            const configResponse = await window.fetchAuthenticated('/api/v1/enums/config/upload-max-size');
            if (configResponse.ok) {
                const configResult = await configResponse.json();
                maxUploadSizeKB = configResult.data;
            }
        } catch (error) {
            console.warn('Could not fetch upload size config. Using default.', error);
        }

        if (!dispatchRepairId) return;
        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}`);
            if (!response.ok) throw new Error('無法獲取派工單資料');
            
            originalOrderData = await response.json();
            
            if (!originalOrderData) throw new Error('從API獲取的訂單資料為空');
            
            window.renderDispatchOrderHeader(originalOrderData, infoCardContainer);
            renderWorkSections(originalOrderData);

            // Set breadcrumb link immediately
            if(detailBreadcrumbLink) {
                detailBreadcrumbLink.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;
            }

            attachEventListeners();

        } catch (error) {
            console.error('頁面初始化失敗:', error);
            infoCardContainer.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
        }
    }

    // --- Rendering ---
    function renderWorkSections(order) {
        mainItemsContainer.innerHTML = '';
        otherItemsContainer.innerHTML = '';
        document.getElementById('other-items-card').style.display = 'none';

        if (!order || !order.items || order.items.length === 0) return;

        const mainItems = order.items.filter(item => item.isMain);
        const otherItems = order.items.filter(item => !item.isMain);

        // Render main items with full controls
        mainItems.forEach(item => {
            const sectionHtml = createMainItemSection(item);
            mainItemsContainer.insertAdjacentHTML('beforeend', sectionHtml);
        });
        
        // Render other items as a simple list
        if (otherItems.length > 0) {
            document.getElementById('other-items-card').style.display = 'block';
            let otherItemsHtml = '<ul class="list-group list-group-flush">';
            otherItems.forEach(item => {
                otherItemsHtml += `<li class="list-group-item">${item.productName} (x${item.quantity})</li>`;
            });
            otherItemsHtml += '</ul>';
            otherItemsContainer.innerHTML = otherItemsHtml;
        }
    }

    function createMainItemSection(mainItem) {
        const orderItemId = mainItem.orderItemId || mainItem.id; // Fallback for different DTOs
        return `
            <div class="card mb-3 main-item-section" data-order-item-id="${orderItemId}">
                <div class="card-header">
                    <h5>主商品: ${mainItem.productName}</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">機身編號</label>
                            <div class="input-group">
                                <input type="text" class="form-control device-serial-number" value="${mainItem.deviceSerialNumber || ''}">
                                <button class="btn btn-outline-secondary scan-barcode-btn" type="button">掃描</button>
                            </div>
                        </div>
                        <div class="col-md-6 align-self-end text-end">
                            <button class="btn btn-outline-success add-part-btn" type="button">新增裝修零件</button>
                        </div>
                    </div>
                    <hr>
                    <h6>裝修零件列表</h6>
                    <div class="table-responsive">
                        <table class="table table-sm repaired-parts-table">
                            <thead><tr><th>名稱</th><th>數量</th><th>單價</th><th>小計</th><th></th></tr></thead>
                            <tbody><!-- Repaired parts will be added here --></tbody>
                        </table>
                    </div>
                    <hr>
                    <div class="mb-3">
                        <label class="form-label">裝修訊息</label>
                        <div class="input-group">
                            <div class="input-group-text">
                                <input class="form-check-input mt-0 is-abnormal-checkbox" type="checkbox" title="是否異常" id="abnormalCheck-${orderItemId}">
                                <label class="form-check-label ms-2" for="abnormalCheck-${orderItemId}">異常</label>
                            </div>
                            <textarea class="form-control dispatch-remarks" rows="2">${mainItem.issueDescription || ''}</textarea>
                        </div>
                    </div>
                    <hr>
                    <h6>現場照片 (最多6張)</h6>
                    <div class="image-upload-area">
                        <!-- Image uploaders will be added here -->
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-info add-image-btn mt-2">+ 新增照片</button>
                </div>
            </div>
        `;
    }

    function handleAddImageUploader(event) {
        const imageArea = event.target.previousElementSibling;
        if (imageArea.children.length >= 6) {
            showToast('最多上傳6張照片', 'info');
            return;
        }
        const newUploaderId = `image-upload-${Date.now()}`;
        const uploaderHtml = `
            <div class="image-uploader-item mb-2">
                <div class="input-group">
                    <input type="file" class="form-control image-upload-input" id="${newUploaderId}" accept="image/jpeg, image/png">
                    <input type="text" class="form-control image-description" placeholder="照片說明">
                    <button class="btn btn-outline-danger remove-image-btn" type="button" style="display: none;"><i class="bi bi-trash"></i></button>
                </div>
                <img src="" alt="圖片預覽" class="img-preview img-thumbnail mt-2" style="display: none; max-height: 150px;">
            </div>
        `;
        imageArea.insertAdjacentHTML('beforeend', uploaderHtml);
        const newItem = imageArea.lastElementChild;
        newItem.querySelector('.image-upload-input').addEventListener('change', handleImagePreview);
        newItem.querySelector('.remove-image-btn').addEventListener('click', handleImageRemove);
    }

    async function handleImagePreview(event) {
        const input = event.target;
        const file = input.files[0];
        const preview = input.closest('.image-uploader-item').querySelector('.img-preview');
        const removeBtn = input.closest('.image-uploader-item').querySelector('.remove-image-btn');

        if (file) {
            // Show loading state on the preview
            preview.style.display = 'block';
            preview.src = 'img/loading_spinner.gif'; // A placeholder loading gif
            removeBtn.style.display = 'none';

            try {
                const compressedDataUrl = await window.compressImage(file, maxUploadSizeKB);
                preview.src = compressedDataUrl;
                removeBtn.style.display = 'inline-block';
            } catch (error) {
                console.error("Image compression failed:", error);
                showToast('圖片處理失敗', 'danger');
                preview.src = '';
                preview.style.display = 'none';
            }
        } else {
            preview.src = '';
            preview.style.display = 'none';
            removeBtn.style.display = 'none';
        }
    }

    function handleImageRemove(event) {
        const uploaderItem = event.target.closest('.image-uploader-item');
        const input = uploaderItem.querySelector('.image-upload-input');
        const preview = uploaderItem.querySelector('.img-preview');
        const removeBtn = uploaderItem.querySelector('.remove-image-btn');

        input.value = ''; // Clear the file input
        preview.src = '';
        preview.style.display = 'none';
        removeBtn.style.display = 'none';
    }
    
    function handleScanBarcode(event) {
        // ... logic to use BarcodeDetector API
    }

    function handleSearchParts(event) {
        const mainItemSection = event.target.closest('.main-item-section');
        if (!mainItemSection) return;
    
        const partsTableBody = mainItemSection.querySelector('.repaired-parts-table tbody');
        const partSearchModalEl = document.getElementById('partSearchModal');
        const partSearchModal = bootstrap.Modal.getOrCreateInstance(partSearchModalEl);
        const resultsContainer = document.getElementById('part-search-results');
    
        // This handler is created within a closure, capturing the correct 'partsTableBody'
        const specificPartSelectionHandler = (selectionEvent) => {
            const target = selectionEvent.target.closest('.select-part-btn');
            if (!target) return;
    
            const partRow = target.closest('.list-group-item');
            const quantityInput = partRow.querySelector('.part-quantity');
            const quantity = parseInt(quantityInput.value, 10);
    
            if (!quantity || quantity < 1) {
                showToast('請輸入有效數量', 'warning');
                return; // Don't close modal, don't remove listener, allow user to correct
            }
    
            const part = {
                barcode: target.dataset.barcode,
                name: target.dataset.name,
                price: parseFloat(target.dataset.price) || 0, // Default price to 0 if invalid
                quantity: quantity
            };
            
            const subtotal = part.price * part.quantity;
    
            const newRow = partsTableBody.insertRow();
            newRow.dataset.barcode = part.barcode;
            newRow.dataset.price = part.price;
            newRow.innerHTML = `
                <td>${part.name}</td>
                <td>${part.quantity}</td>
                <td>${part.price.toLocaleString()}</td>
                <td>${subtotal.toLocaleString()}</td>
                <td><button type="button" class="btn btn-sm btn-danger remove-part-btn"><i class="bi bi-trash"></i></button></td>
            `;
            newRow.querySelector('.remove-part-btn').addEventListener('click', () => newRow.remove());
            
            // IMPORTANT: Cleanup and hide only on success
            cleanupAndHide();
        };
    
        const cleanupAndHide = () => {
            resultsContainer.removeEventListener('click', specificPartSelectionHandler);
            partSearchModalEl.removeEventListener('hidden.bs.modal', cleanupAndHide);
            partSearchModal.hide();
        };
    
        // Attach event listeners
        resultsContainer.addEventListener('click', specificPartSelectionHandler);
        partSearchModalEl.addEventListener('hidden.bs.modal', cleanupAndHide, { once: true });
    
        // Prepare and show modal
        document.getElementById('part-search-keyword').value = '';
        resultsContainer.innerHTML = '';
        partSearchModal.show();
    }
    
    async function execPartSearch() {
        const keyword = document.getElementById('part-search-keyword').value.trim();
        const resultsContainer = document.getElementById('part-search-results');
        const technicianId = originalOrderData.assignedTechnicianId;
        
        if (!technicianId) { showToast('未指派技師，無法查詢車存。', 'warning'); return; }

        resultsContainer.innerHTML = '<div class="text-center p-3"><span class="spinner-border spinner-border-sm"></span> 搜尋中...</div>';
        try {
            const response = await window.fetchAuthenticated(`/api/v1/warehouse-inventory/technician-stock?technicianId=${technicianId}&keyword=${encodeURIComponent(keyword)}`);
            if (!response.ok) throw new Error('搜尋零件失敗');
            const result = await response.json();
            
            resultsContainer.innerHTML = '';
            if (result.data && result.data.length > 0) {
                result.data.forEach(part => {
                    const stock = part.quantityOnHand || 0;
                    resultsContainer.innerHTML += `
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${part.productName}</h6>
                                <small class="text-muted">條碼: ${part.productBarcode} | 庫存: ${stock}</small>
                            </div>
                            <div class="input-group" style="width: 200px;">
                                <input type="number" class="form-control form-control-sm part-quantity" placeholder="數量" min="1" value="1">
                                <button class="btn btn-sm btn-primary select-part-btn" data-barcode="${part.productBarcode}" data-name="${part.productName}" data-price="${part.salePrice || 0}" ${stock <= 0 ? 'disabled' : ''}>選定</button>
                            </div>
                        </div>
                    `;
                });
            } else {
                resultsContainer.innerHTML = '<p class="text-muted p-3">查無零件</p>';
            }
        } catch (error) {
            resultsContainer.innerHTML = `<p class="text-danger p-3">${error.message}</p>`;
        }
    }

    // --- Form Submission ---
    async function handleFormSubmit(event) {
        event.preventDefault();

        // --- New Validation Logic ---
        let validationPassed = true;
        document.querySelectorAll('.main-item-section').forEach(section => {
            const productName = section.querySelector('h5').textContent;
            
            // 1. Check Serial Number
            const serialInput = section.querySelector('.device-serial-number');
            if (!serialInput.value.trim()) {
                showToast(`請為「${productName}」輸入機身編號`, 'warning');
                serialInput.classList.add('is-invalid');
                validationPassed = false;
            } else {
                serialInput.classList.remove('is-invalid');
            }
            
            // 2. Check for at least one image
            const imageArea = section.querySelector('.image-upload-area');
            const uploadedImage = imageArea.querySelector('.img-preview[src^="data:image"]');
            if (!uploadedImage) {
                showToast(`請為「${productName}」上傳至少一張現場照片`, 'warning');
                // You might want to highlight the image area or the "add photo" button
                validationPassed = false;
            }
        });

        if (!validationPassed) {
            return; // Stop submission if validation fails
        }
        // --- End Validation Logic ---

        // Disable button to prevent double-submission
        const completeBtn = document.getElementById('complete-btn');
        completeBtn.disabled = true;
        completeBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 處理中...';

        const payload = await buildPayload();

        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}/complete-working`, {
                method: 'POST',
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '操作失敗');
            }
            
            showToast('裝修步驟已完成！', 'success');
            setTimeout(() => {
                window.location.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;
            }, 1500);

        } catch (error) {
            console.error('完成裝修步驟失敗:', error);
            showToast(`操作失敗: ${error.message}`, 'danger');
            completeBtn.disabled = false;
            completeBtn.innerHTML = '已完成裝修';
        }
    }

    function buildPayload() {
        const items = [];
        document.querySelectorAll('.main-item-section').forEach(section => {
            const repairedParts = [];
            section.querySelectorAll('.repaired-parts-table tbody tr').forEach(row => {
                repairedParts.push({
                    productBarcode: row.dataset.barcode,
                    quantity: parseInt(row.cells[1].textContent),
                    unitPrice: parseFloat(row.cells[2].textContent.replace(/,/g, ''))
                });
            });

            const images = [];
            section.querySelectorAll('.image-uploader-item').forEach(uploader => {
                const img = uploader.querySelector('.img-preview');
                const fileInput = uploader.querySelector('.image-upload-input');
                if (img.src && img.src.startsWith('data:image')) {
                    images.push({
                        fileName: fileInput.files.length > 0 ? fileInput.files[0].name : 'image.png',
                        base64Data: img.src,
                        description: uploader.querySelector('.image-description').value
                    });
                }
            });

            const itemDto = {
                orderItemId: section.dataset.orderItemId,
                deviceSerialNumber: section.querySelector('.device-serial-number').value,
                dispatchRemarks: section.querySelector('.dispatch-remarks').value,
                isAbnormal: section.querySelector('.is-abnormal-checkbox').checked,
                repairedParts: repairedParts,
                images: images
            };
            items.push(itemDto);
        });

        return { items: items };
    }

    // --- Attach Listeners ---
    function attachEventListeners() {
        workForm.addEventListener('submit', handleFormSubmit);
        document.getElementById('exec-part-search-btn').addEventListener('click', execPartSearch);
        // document.getElementById('part-search-results').addEventListener('click', handlePartSelection); // REMOVED
        
        // Use event delegation on the main container for dynamic buttons
        mainItemsContainer.addEventListener('click', (event) => {
            if (event.target.closest('.add-part-btn')) {
                handleSearchParts(event);
            }
            if (event.target.closest('.add-image-btn')) {
                handleAddImageUploader(event);
            }
            if (event.target.closest('.scan-barcode-btn')) {
                // handleScanBarcode(event);
            }
        });
        
        mainItemsContainer.addEventListener('change', (event) => {
            if (event.target.closest('.image-upload-input')) {
                handleImagePreview(event);
            }
        });
    }

    // --- Start ---
    initializePage();
}); 