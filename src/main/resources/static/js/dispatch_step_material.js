document.addEventListener('DOMContentLoaded', function () {
    const infoCard = document.getElementById('info-card');
    const materialItemsList = document.getElementById('material-items-list');
    const backBtn = document.getElementById('back-btn');
    const completeBtn = document.getElementById('complete-btn');
    const detailBreadcrumbLink = document.getElementById('detail-breadcrumb-link');

    const urlParams = new URLSearchParams(window.location.search);
    const dispatchRepairId = urlParams.get('id');

    if (!dispatchRepairId) {
        infoCard.innerHTML = '<div class="alert alert-danger">錯誤：找不到派工單ID。</div>';
        return;
    }
    
    detailBreadcrumbLink.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;

    async function initializePage() {
        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}`);
            if (!response.ok) throw new Error('無法獲取派工單資料');
            
            const order = await response.json();
            
            if (!order) {
                throw new Error('從API獲取的訂單資料為空');
            }

            window.renderDispatchOrderHeader(order, infoCard);
            renderMaterialList(order.items || []);

            if (order.statusCode !== 35) { // 35: 待領料
                completeBtn.disabled = true;
                if(window.showToast) window.showToast('此派工單狀態已變更，不可操作。', 'warning');
            }

        } catch (error) {
            console.error('頁面初始化失敗:', error);
            infoCard.innerHTML = `<div class="alert alert-danger">${error.message}</div>`;
        }
    }
    
    function renderMaterialList(items) {
        materialItemsList.innerHTML = '';
        if (!items || items.length === 0) {
            materialItemsList.innerHTML = '<tr><td colspan="4" class="text-center text-muted">無領料品項</td></tr>';
            return;
        }
        items.forEach(item => {
            const row = `
                <tr>
                    <td>${item.productBarcode}</td>
                    <td>${item.productName}</td>
                    <td>${item.quantity}</td>
                    <td>${item.warehouseName || 'N/A'}</td>
                </tr>`;
            materialItemsList.innerHTML += row;
        });
    }

    async function handleComplete() {
        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}/complete-material-collection`, {
                method: 'POST'
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '操作失敗');
            }
            
            if(window.showToast) window.showToast('領料步驟已完成！', 'success');
            setTimeout(() => {
                window.location.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;
            }, 1500);

        } catch (error) {
            console.error('完成領料步驟失敗:', error);
            if(window.showToast) window.showToast(`操作失敗: ${error.message}`, 'danger');
        }
    }

    backBtn.addEventListener('click', () => window.history.back());
    // completeBtn.addEventListener('click', handleComplete);

    initializePage();
}); 