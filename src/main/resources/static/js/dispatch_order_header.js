function getDispatchOrderTypeClass(typeCode) {
    const typeClassMap = {
        10: 'bg-primary',   // 派工
        20: 'bg-warning',   // 維修
        30: 'bg-secondary'  // 退機
    };
    return typeClassMap[typeCode] || 'bg-info';
}

function getDispatchCustomerInfoBackgroundColorClass(typeCode) {
    const colorClassMap = {
        10: 'bg-primary-subtle',   // 派工 (淺藍)
        20: 'bg-warning-subtle',   // 維修 (淺黃)
        30: 'bg-secondary-subtle'  // 退機 (淺灰)
    };
    return colorClassMap[typeCode] || 'bg-light';
}

window.renderDispatchOrderHeader = function(order, containerElement) {
    if (!order || !containerElement) return;

    const mainItem = order.items && order.items.length > 0 ? order.items[0] : {};
    let backgroundClass = getDispatchCustomerInfoBackgroundColorClass(order.typeCode);
    if (order.isUrgent === 1) {
        backgroundClass = 'bg-urgent';
    }

    // --- START: Prepare collaborators HTML ---
    let collaboratorsHtml = '';
    if (order.collaborators && order.collaborators.length > 0) {
        collaboratorsHtml = order.collaborators.map(c => 
            `, ${c.technicianName} <span class="text-muted small">(${c.statusDescription || '未知'})</span>`
        ).join('');
    }
    // --- END: Prepare collaborators HTML ---

    containerElement.innerHTML = `
    <div class="card info-card mb-3 ${backgroundClass}" id="info-card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>單號:</strong> ${order.number || '-'}</p>
                    <p><strong>類型:</strong> <span class="badge ${getDispatchOrderTypeClass(order.typeCode)}">${order.typeDescription || '-'}</span></p>
                    <p><strong>狀態:</strong> <span class="badge bg-secondary">${order.statusDescription || '-'}</span></p>
                    <p><strong>預約時間:</strong> ${order.scheduledDate ? new Date(order.scheduledDate).toLocaleString() : '未定'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>客戶:</strong> ${order.customerName || '-'}</p>
                    <p><strong>電話:</strong> ${order.customerPhone || '-'}</p>
                    <p><strong>地址:</strong> ${order.installationAddress || '-'}</p>
                    <p><strong>技師:</strong> <strong>${order.assignedTechnicianName || '未指派'}</strong>${collaboratorsHtml}</p>
                    <p><strong>問題描述:</strong> ${mainItem.issueDescription || '-'}</p>
                </div>
            </div>
            <hr class="my-2">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>訂單備註:</strong> <span class="text-danger">${order.originalOrderRemarks || '(無)'}</span></p>
                </div>
                <div class="col-md-6">
                    <p><strong>派工備註:</strong> ${order.remarks || '(無)'}</p>
                </div>
            </div>
        </div>
    </div>
    `;
}; 