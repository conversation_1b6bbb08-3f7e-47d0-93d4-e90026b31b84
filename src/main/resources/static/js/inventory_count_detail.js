document.addEventListener('DOMContentLoaded', function () {
    const urlParams = new URLSearchParams(window.location.search);
    const sheetId = urlParams.get('id');

    if (sheetId) {
        loadSheetDetails(sheetId);
    } else {
        document.getElementById('detail-content').innerHTML = '<div class="alert alert-danger">未提供盤點單ID。</div>';
    }
});

async function loadSheetDetails(sheetId) {
    const loadingIndicator = document.getElementById('loading-indicator');
    const detailContent = document.getElementById('detail-content');

    try {
        const response = await window.fetchAuthenticated(`/api/v1/inventory-counts/${sheetId}`);
        const apiResponse = await response.json();

        if (apiResponse.code === 200 && apiResponse.data) {
            renderDetails(apiResponse.data);
            loadingIndicator.classList.add('d-none');
            detailContent.classList.remove('d-none');
        } else {
            throw new Error(apiResponse.message || '無法載入盤點單資料');
        }
    } catch (error) {
        console.error('Error fetching sheet details:', error);
        loadingIndicator.innerHTML = `<div class="alert alert-danger">載入失敗: ${error.message}</div>`;
    }
}

function renderDetails(data) {
    document.getElementById('approvalStatusBadge').textContent = data.approvalStatusDescription || 'N/A';
    document.getElementById('approvalStatusBadge').className = `badge fs-6 ${getApprovalStatusBadge(data.approvalStatusCode)}`;
    
    document.getElementById('countMonth').textContent = data.countMonth || 'N/A';
    document.getElementById('countedBy').textContent = data.countedByUserName || 'N/A';
    document.getElementById('countDate').textContent = data.countDate ? new Date(data.countDate).toLocaleDateString() : 'N/A';
    document.getElementById('sheetNumber').textContent = data.sheetNumber || 'N/A';
    
    const itemsTableBody = document.getElementById('items-table-body');
    itemsTableBody.innerHTML = '';
    if (data.items && data.items.length > 0) {
        data.items.forEach((item, index) => {
            const row = itemsTableBody.insertRow();
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${item.productBarcode}</td>
                <td>${item.productName}</td>
                <td>${item.countedQuantity}</td>
                <td>${item.remarks || ''}</td>
            `;
        });
    } else {
        itemsTableBody.innerHTML = '<tr><td colspan="5" class="text-center">此盤點單無商品項目。</td></tr>';
    }

    const actionButtonsContainer = document.getElementById('action-buttons');
    actionButtonsContainer.innerHTML = '';
    
    // Logic from Notion: If status is '審核未通過', show '複盤' button.
    if (data.approvalStatusCode === 40) { // 40: 審核未通過
        const recountButton = document.createElement('button');
        recountButton.className = 'btn btn-primary mx-1';
        recountButton.textContent = '複盤';
        recountButton.onclick = () => {
            // TODO: Redirect to recount page or open recount modal
            alert(`準備對盤點單 ${data.sheetNumber} 進行複盤 (功能待實現)`);
        };
        actionButtonsContainer.appendChild(recountButton);
    }

    const backButton = document.createElement('button');
    backButton.className = 'btn btn-secondary mx-1';
    backButton.textContent = '返回';
    backButton.onclick = () => {
        window.location.href = 'inventory_count_list.html';
    };
    actionButtonsContainer.appendChild(backButton);
}

function getApprovalStatusBadge(statusCode) {
    // Duplicated from list.js, should be in a shared utility file.
    switch(statusCode) {
        case 30: return 'bg-success'; // 審核通過
        case 50: return 'bg-success'; // 複盤通過
        case 40: return 'bg-danger';  // 審核未通過
        case 20: return 'bg-warning text-dark'; // 已送審
        default: return 'bg-secondary'; // 未送審
    }
} 