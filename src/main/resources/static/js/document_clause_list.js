// JavaScript for document_clause_list.html

document.addEventListener('DOMContentLoaded', function () {
    console.log('document_clause_list.js loaded');

    if (typeof window.fetchAuthenticated !== 'function') {
        console.error('Main.js or fetchAuthenticated function not loaded');
        return;
    }

    const searchForm = document.getElementById('search-clauses-form');
    const clausesTableBody = document.getElementById('clauses-table-body');
    const paginationControls = document.getElementById('pagination-controls');

    let currentPage = 0;
    const pageSize = 10;

    async function fetchClauses(page = 0) {
        currentPage = page;
        const keyword = document.getElementById('searchKeyword').value;
        const isActive = document.getElementById('searchIsActive').value;
        const isDefault = document.getElementById('searchIsDefault').value;
        const dateFrom = document.getElementById('searchDateFrom').value;
        const dateTo = document.getElementById('searchDateTo').value;
        // const dateType = ... ; // Need to decide if client sets this or default to one type

        let queryParams = `?page=${page}&size=${pageSize}&sort=sequenceOrder,asc`;

        const companyDivisionCode = window.getCompanyDivisionCode();
        if (companyDivisionCode !== null) {
            queryParams += `&companyDivisionCode=${companyDivisionCode}`;
        } else {
             clausesTableBody.innerHTML = `<tr><td colspan="6" class="text-center p-4 text-danger">錯誤: 無法確定公司別，請重新登入。</td></tr>`;
             return;
        }

        if (keyword) queryParams += `&keyword=${encodeURIComponent(keyword)}`;
        if (isActive !== "") queryParams += `&isActive=${isActive}`;
        if (isDefault !== "") queryParams += `&isDefault=${isDefault}`;
        // if (dateType && dateFrom) queryParams += `&dateType=${dateType}`;
        if (dateFrom) queryParams += `&dateFrom=${dateFrom}`;
        if (dateTo) queryParams += `&dateTo=${dateTo}`;

        clausesTableBody.innerHTML = `<tr><td colspan="6" class="text-center p-4">載入中...</td></tr>`;

        try {
            const response = await window.fetchAuthenticated(`/api/v1/document-clauses${queryParams}`);
            if (!response.ok) {
                clausesTableBody.innerHTML = `<tr><td colspan="6" class="text-center p-4 text-danger">查詢失敗: ${response.statusText}</td></tr>`;
                console.error('Failed to fetch clauses', response);
                return;
            }
            const apiResponse = await response.json();

            if (apiResponse.code === 200 && apiResponse.data && apiResponse.data.list) {
                renderClauses(apiResponse.data.list);
                renderPagination(apiResponse.data.page);
            } else {
                clausesTableBody.innerHTML = `<tr><td colspan="6" class="text-center p-4">查無資料或查詢錯誤。</td></tr>`;
                console.error('Error in API response:', apiResponse.message);
                renderPagination(null);
            }
        } catch (error) {
            clausesTableBody.innerHTML = `<tr><td colspan="6" class="text-center p-4 text-danger">查詢時發生錯誤。</td></tr>`;
            console.error('Error fetching clauses:', error);
            renderPagination(null);
        }
    }

    function renderClauses(clauses) {
        if (!clauses || clauses.length === 0) {
            clausesTableBody.innerHTML = '<tr><td colspan="6" class="text-center p-4">查無表尾條文資料</td></tr>';
            return;
        }

        clausesTableBody.innerHTML = '';
        clauses.forEach(clause => {
            const row = clausesTableBody.insertRow();
            row.insertCell().textContent = clause.clauseTitle || '-';
            row.insertCell().textContent = clause.startTime ? new Date(clause.startTime).toLocaleString() : '-';
            row.insertCell().textContent = clause.endTime ? new Date(clause.endTime).toLocaleString() : '永久有效';
            row.insertCell().innerHTML = clause.isActive ? '<span class="badge bg-success">是</span>' : '<span class="badge bg-secondary">否</span>';
            row.insertCell().innerHTML = clause.isDefault ? '<span class="badge bg-primary">是</span>' : '<span class="badge bg-light text-dark">否</span>';
            
            const actionsCell = row.insertCell();
            actionsCell.classList.add('text-center');
            actionsCell.innerHTML = `
                <button class="btn btn-sm btn-outline-secondary me-1 action-btn edit-btn" data-id="${clause.documentClauseId}" title="編輯">
                    <i class="bi bi-pencil-fill"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger action-btn delete-btn" data-id="${clause.documentClauseId}" title="刪除">
                    <i class="bi bi-trash-fill"></i>
                </button>
            `;
        });

        document.querySelectorAll('.edit-btn').forEach(btn => btn.addEventListener('click', (e) => editClause(e.currentTarget.dataset.id)));
        document.querySelectorAll('.delete-btn').forEach(btn => btn.addEventListener('click', (e) => deleteClause(e.currentTarget.dataset.id)));
    }

    function renderPagination(pageData) {
        // Identical to pagination in wholesale_order_list.js - can be refactored to main.js
        paginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 1) {
            return;
        }
        const createPageLink = (page, text, isActive, isDisabled) => {
            const li = document.createElement('li');
            li.classList.add('page-item');
            if (isActive) li.classList.add('active');
            if (isDisabled) li.classList.add('disabled');
            const a = document.createElement('a');
            a.classList.add('page-link');
            a.href = '#';
            a.textContent = text;
            if (!isDisabled) {
                a.addEventListener('click', (e) => { 
                    e.preventDefault(); 
                    fetchClauses(page);
                });
            }
            li.appendChild(a);
            return li;
        };

        paginationControls.appendChild(createPageLink(pageData.page - 1, '上一頁', false, pageData.page === 0));
        for (let i = 0; i < pageData.totalPages; i++) {
            paginationControls.appendChild(createPageLink(i, i + 1, i === pageData.page, false));
        }
        paginationControls.appendChild(createPageLink(pageData.page + 1, '下一頁', false, pageData.page === pageData.totalPages - 1));
    }

    function editClause(id) {
        window.location.href = `document_clause_form.html?id=${id}`;
    }

    async function deleteClause(id) {
        if (confirm('確定要刪除此表尾條文嗎？')) {
            try {
                const response = await window.fetchAuthenticated(`/api/v1/document-clauses/${id}`, { method: 'DELETE' });
                const apiResponse = await response.json();
                if (response.ok && apiResponse.code === 200) {
                    window.showToast('表尾條文已刪除', 'success');
                    fetchClauses(currentPage);
                } else {
                    window.showToast(`刪除失敗: ${apiResponse.message || response.statusText}`, 'error');
                }
            } catch (error) {
                console.error('Error deleting clause:', error);
                window.showToast('刪除表尾條文時發生錯誤', 'error');
            }
        }
    }

    if (searchForm) {
        searchForm.addEventListener('submit', function (event) {
            event.preventDefault();
            fetchClauses(0);
        });
    }

    fetchClauses(0); // Initial load
}); 