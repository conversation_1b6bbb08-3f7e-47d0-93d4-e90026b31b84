// js/sms_template_form.js // Path comment updated
document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('sms-template-form');
    const templateIdField = document.getElementById('smsTemplateId');
    const templateNameField = document.getElementById('templateName');
    const templateTypeField = document.getElementById('templateType');
    const smsContentField = document.getElementById('smsContent');
    const submitBtn = document.getElementById('saveSmsTemplateBtn');
    const formModeBreadcrumb = document.getElementById('sms-form-mode-breadcrumb');
    const formTitle = document.getElementById('sms-form-title');
    const errorMessageDiv = document.getElementById('sms-form-error-message');

    const API_URL = '/api/v1/sms-templates';
    const urlParams = new URLSearchParams(window.location.search);
    const currentId = urlParams.get('id');
    const mode = urlParams.get('mode') || (currentId ? 'edit' : 'add');

    function showError(message) {
        if(errorMessageDiv) {
            errorMessageDiv.textContent = message;
            errorMessageDiv.classList.remove('d-none');
        }
    }
    function clearError() {
        if(errorMessageDiv) {
            errorMessageDiv.classList.add('d-none');
            errorMessageDiv.textContent = '';
        }
    }

    function setupFormForMode() {
        clearError();
        if (mode === 'add') {
            formModeBreadcrumb.textContent = '新增簡訊模板';
            formTitle.textContent = '新增簡訊模板';
            submitBtn.textContent = '儲存模板';
        } else if (mode === 'edit') {
            formModeBreadcrumb.textContent = '編輯簡訊模板';
            formTitle.textContent = '編輯簡訊模板';
            submitBtn.textContent = '更新模板';
            loadTemplateData();
        } 
    }

    async function loadTemplateData() {
        if (!currentId) return;
        try {
            const response = await window.fetchAuthenticated(`${API_URL}/${currentId}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const apiResult = await response.json();

            if (apiResult.code === 200 && apiResult.data) {
                const data = apiResult.data;
                templateIdField.value = data.smsTemplateId;
                templateNameField.value = data.templateName;
                templateTypeField.value = data.templateType || '';
                smsContentField.value = data.smsContent;
            } else {
                showError(apiResult.message || '無法載入模板資料');
            }
        } catch (error) {
            console.error('Error loading SMS template data:', error);
            showError(`無法載入模板資料: ${error.message}`);
        }
    }

    form.addEventListener('submit', async function(event) {
        event.preventDefault();
        clearError();

        const requestBody = {
            templateName: templateNameField.value,
            templateType: templateTypeField.value || null,
            smsContent: smsContentField.value,
        };
        if (currentId) {
            requestBody.smsTemplateId = currentId;
        }

        // Confirmation Dialog
        const confirmMessage = mode === 'add' ? "確定要新增此簡訊模板嗎？" : "確定要儲存對此簡訊模板的變更嗎？";
        if (!window.confirm(confirmMessage)) {
            return; // User cancelled
        }

        try {
            let response;
            let alertMessage = '';
            if (mode === 'add') {
                response = await window.fetchAuthenticated(API_URL, {
                    method: 'POST',
                    body: JSON.stringify(requestBody)
                });
                alertMessage = '簡訊模板已成功新增！';
            } else { // edit
                response = await window.fetchAuthenticated(`${API_URL}/${currentId}`, {
                    method: 'PUT',
                    body: JSON.stringify(requestBody)
                });
                alertMessage = '簡訊模板已成功更新！';
            }

            const apiResult = await response.json();
            if (response.ok && (apiResult.code === 201 || apiResult.code === 200)) {
                alert(alertMessage); // Using standard alert, assuming showGlobalSuccess is not defined
                setTimeout(() => { window.location.href = 'sms_templates.html'; }, 1000);
            } else {
                showError(apiResult.message || '儲存失敗');
            }
        } catch (error) {
            console.error('Error saving SMS template:', error);
            showError(`儲存模板時發生錯誤: ${error.message}`);
        }
    });
    
    document.getElementById('cancelSmsTemplateFormBtn').addEventListener('click', () => {
        window.location.href = 'sms_templates.html';
    });

    setupFormForMode();
}); 