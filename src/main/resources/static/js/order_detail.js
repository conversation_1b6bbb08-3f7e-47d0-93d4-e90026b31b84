let currentOrderData = null;
let reasonModalInstance = null;
let currentActionHandler = null;

// Global state for enums fetched from backend
let availableOrderStatuses = {};
let availableOrderTypes = {};
let availableDispatchStatuses = {};

document.addEventListener('DOMContentLoaded', async function () {
    // Fetch all enums first, then initialize the page
    try {
        await Promise.all([
            fetchEnums('order-statuses', 'availableOrderStatuses'),
            fetchEnums('order-types', 'availableOrderTypes'),
            fetchEnums('dispatch-statuses', 'availableDispatchStatuses')
        ]);
        console.log("All enums loaded successfully.");
    } catch (error) {
        console.error("Failed to load critical enum data. Page functionality may be limited.", error);
        displayError("無法載入頁面基礎設定，請重新整理。");
        return;
    }
    
    await initOrderDetailPage();
});

async function fetchEnums(enumType, targetVariable) {
    try {
        const response = await window.fetchAuthenticated(`/api/v1/enums/${enumType}`);
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        const result = await response.json();
        if (result.code === 200 && Array.isArray(result.data)) {
            // Convert array to a map for easy lookup by code
            window[targetVariable] = result.data.reduce((acc, current) => {
                acc[current.code] = current.description;
                return acc;
            }, {});
        } else {
            throw new Error(`Invalid data format for ${enumType}`);
        }
    } catch (error) {
        console.error(`Error fetching ${enumType}:`, error);
        throw error; // Re-throw to be caught by Promise.all
    }
}

async function initOrderDetailPage() {
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('orderId');

    if (!orderId) {
        displayError('未提供訂單ID，無法載入詳情。');
        return;
    }

    // Initialize the reason modal instance (if it exists)
    const reasonModalEl = document.getElementById('reasonModal');
    if (reasonModalEl) {
        reasonModalInstance = new bootstrap.Modal(reasonModalEl);
        document.getElementById('submitReasonBtn')?.addEventListener('click', handleSubmitReason);
    }
    
    await fetchOrderDetail(orderId);
}

function showLoading(isLoading) {
    const loadingIndicator = document.getElementById('loading-indicator');
    const detailContent = document.getElementById('order-detail-content');
    if (loadingIndicator) loadingIndicator.style.display = isLoading ? 'block' : 'none';
    if (detailContent) detailContent.style.display = isLoading ? 'none' : 'block';
}

function displayError(message) {
    const errorDisplay = document.getElementById('error-display');
    if (errorDisplay) {
        errorDisplay.textContent = message;
        errorDisplay.style.display = 'block';
    }
    const detailContent = document.getElementById('order-detail-content');
    if (detailContent) detailContent.style.display = 'none'; // Hide content on error
}

async function fetchOrderDetail(orderId) {
    showLoading(true);
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const orderType = urlParams.get('orderType');

        let apiUrl = `/api/v1/orders/${orderId}`;
        if (orderType === '2') { // It's a dispatch order
            apiUrl = `/api/v1/orders/dispatch/${orderId}`;
        }

        const response = await window.fetchAuthenticated(apiUrl);
        if (!response.ok) {
            const errData = await response.json().catch(() => null);
            throw new Error(errData?.message || `無法獲取訂單詳情 (HTTP ${response.status})`);
        }
        const result = await response.json();
        if (result.code === 200 && result.data) {
            currentOrderData = result.data;
            populateOrderDetailView(currentOrderData);
            renderDynamicActions(currentOrderData);
            document.getElementById('error-display').style.display = 'none';
        } else {
            throw new Error(result.message || '獲取訂單資料格式錯誤。');
        }
    } catch (error) {
        console.error('獲取訂單詳情失敗:', error);
        displayError(error.message);
    } finally {
        showLoading(false);
    }
}

function populateOrderDetailView(order) {
    if (!order) return;

    // Helper to set text content safely
    const setText = (id, value) => {
        const el = document.getElementById(id);
        if (el) el.textContent = value !== null && value !== undefined ? value : '-';
    };
    const formatDate = (dateString) => dateString ? new Date(dateString).toLocaleDateString() : '-';
    const formatDateTime = (dateString) => dateString ? new Date(dateString).toLocaleString() : '-';
    const formatCurrency = (amount) => amount !== null && amount !== undefined ? `NT$ ${parseFloat(amount).toFixed(2)}` : '-';

    setText('detailOrderNumber', `#${order.orderNumber}`);
    document.title = `訂單 ${order.orderNumber} - 詳情`; // Update page title

    // Dynamically set breadcrumb based on order type
    const sourceOrderListPageLink = document.getElementById('sourceOrderListPageLink');
    if (sourceOrderListPageLink) {
        if (order.orderTypeCode === 1) { // 1: STORE_PRODUCT_ORDER
            sourceOrderListPageLink.href = 'store_product_orders_list.html';
            sourceOrderListPageLink.textContent = '門市商品訂單查詢';
        } else if (order.orderTypeCode === 2) { // 2: DISPATCH_PRODUCT_ORDER
            sourceOrderListPageLink.href = 'dispatch_product_orders_list.html';
            sourceOrderListPageLink.textContent = '派工商品訂單查詢';
        } else if (order.orderTypeCode === 3) { // 3: WHOLESALE_ORDER
             sourceOrderListPageLink.href = 'wholesale_order_list.html';
             sourceOrderListPageLink.textContent = '批發訂單列表';
        } else {
            sourceOrderListPageLink.href = 'home.html'; // Fallback
            sourceOrderListPageLink.textContent = '訂單查詢';
        }
    }

    // Basic Info
    setText('orderNumber', order.orderNumber);
    setText('orderDate', formatDate(order.orderDate));
    setText('orderTypeDescription', order.orderTypeDescription || '未知類型');
    setText('storeName', order.storeName);
    setText('orderStatusDescription', order.orderStatusDescription || '未知狀態');
    setText('paymentStatusDescription', order.paymentStatusDescription || '未知狀態');
    setText('dispatchStatusDescription', order.dispatchStatusDescription || '-');
    setText('createdByName', order.createdByName);
    setText('createTime', formatDateTime(order.createTime));
    setText('updatedByName', order.updatedByName);
    setText('updateTime', formatDateTime(order.updateTime));

    // Promotion Info
    const promotionRow = document.getElementById('promotion-display-row');
    const promotionNameSpan = document.getElementById('promotionName');
    if (promotionRow && promotionNameSpan && order.promotionName) {
        promotionNameSpan.textContent = order.promotionName;
        promotionRow.style.display = 'block';
    } else if (promotionRow) {
        promotionRow.style.display = 'none';
    }

    // Customer Info
    setText('customerName', order.customerName);
    setText('customerPhone', order.customerPhone);
    setText('memberLevelName', order.memberLevelName);
    setText('contactName', order.contactName);
    setText('contactPhone', order.contactPhone);
    setText('installationAddress', order.installationAddress);

    // Toggle dispatch-only columns based on order type
    const isDispatchOrder = order.orderTypeCode === 2;
    document.querySelectorAll('.dispatch-only').forEach(el => {
        el.style.display = isDispatchOrder ? '' : 'none';
    });

    // Items Table
    const mainItemsContainer = document.getElementById('main-items-table-body');
    const giftsContainer = document.getElementById('gifts-table-body');
    const addonsContainer = document.getElementById('addons-table-body');
    mainItemsContainer.innerHTML = '';
    giftsContainer.innerHTML = '';
    addonsContainer.innerHTML = '';

        if (order.items && order.items.length > 0) {
            order.items.forEach(item => {
            if (item.itemTypeCode === 0) { // Main Product
                // For main product, "unitPrice" is the original price, "finalPricePerItem" is the sale price
                let mainProductRowHtml = `
                    <tr>
                        <td>${item.productName}</td>`;

                if (isDispatchOrder) {
                    mainProductRowHtml += `
                        <td>${item.requiresDispatch === 0 ? '店取' : item.requiresDispatch === 1 ? '派工' : item.requiresDispatch === 2 ? '自載展示機' : '未知'}</td>
                        <td>${(item.listPrice || 0).toLocaleString()}</td>
                        <td>${(item.finalPricePerItem || 0).toLocaleString()}</td>
                        <td>${item.mahjongTableSerialNumber || '-'}</td>
                        <td>${item.quantity}</td>
                        <td>${(item.subtotalAmount || 0).toLocaleString()}</td>
                        <td>${item.warehouseName || 'N/A'}</td>
                    `;
                } else { // Store Order
                    mainProductRowHtml += `
                        <td>${(item.listPrice || 0).toLocaleString()}</td>
                        <td>${(item.finalPricePerItem || 0).toLocaleString()}</td>
                        <td>${item.quantity}</td>
                        <td>${(item.subtotalAmount || 0).toLocaleString()}</td>
                    `;
                }
                mainProductRowHtml += `</tr>`;
                mainItemsContainer.innerHTML += mainProductRowHtml;

                if (item.originalGiftTotal !== null || item.exchangedGiftTotal !== null) {
                    const giftSummaryEl = document.querySelector('.gift-summary');
                    const originalTotalEl = document.getElementById('original-gift-total');
                    const exchangedTotalEl = document.getElementById('exchanged-gift-total');
                    
                    if(giftSummaryEl && originalTotalEl && exchangedTotalEl) {
                        const originalTotal = item.originalGiftTotal || 0;
                        const exchangedTotal = item.exchangedGiftTotal || 0;

                        originalTotalEl.textContent = originalTotal.toLocaleString();
                        exchangedTotalEl.textContent = exchangedTotal.toLocaleString();

                        if (exchangedTotal > originalTotal) {
                            exchangedTotalEl.classList.add('text-danger');
        } else {
                            exchangedTotalEl.classList.remove('text-danger');
                        }
                        giftSummaryEl.style.display = 'block';
                    }
                }

                // Process dispatch groups for this main item
                if (item.itemGroups && item.itemGroups.length > 0) {
                    item.itemGroups.forEach(group => {
                        const targetContainer = group.itemTypeCode === 1 ? giftsContainer : addonsContainer;
                        // For groups, "listPrice" is original, "unitPrice" is sale price
                        targetContainer.innerHTML += `
                            <tr>
                                <td>${group.productName}</td>
                                <td>${group.requiresDispatch === 0 ? '店取' : group.requiresDispatch === 1 ? '派工' : group.requiresDispatch === 2 ? '自載展示機' : '未知'}</td>
                                <td>${(group.listPrice || 0).toLocaleString()}</td>
                                <td>${(group.unitPrice || 0).toLocaleString()}</td>
                                <td>${group.quantity}</td>
                                <td>${(group.subtotalAmount || 0).toLocaleString()}</td>
                                <td>${group.warehouseName || 'N/A'}</td>
                                <td>${group.isAwait === 1 ? '是' : ''}</td>
                            </tr>`;
                    });
                }
            }
        });
    }

    // Amounts Info
    setText('productsTotalAmount', formatCurrency(order.productsTotalAmount));
    setText('discountAmount', formatCurrency(order.discountAmount));
    setText('netAmount', formatCurrency(order.netAmount));
    setText('taxAmount', formatCurrency(order.taxAmount));
    setText('grandTotalAmount', formatCurrency(order.grandTotalAmount));
    setText('paidAmount', formatCurrency(order.paidAmount));

    // Invoice Info
    setText('invoiceTypeDescription', order.invoiceTypeDescription);
    setText('invoiceNumber', order.invoiceNumber);
    setText('invoiceDate', formatDate(order.invoiceDate));
    setText('taxIdNumber', order.taxIdNumber);
    setText('invoiceCompanyTitle', order.invoiceCompanyTitle);

    // Conditionally display sections based on Order Type
    const dispatchSection = document.getElementById('dispatch-info-section');
    const giftsSection = document.getElementById('gifts-info-section');
    const addonsSection = document.getElementById('addons-info-section');

    if (order.orderTypeCode === 2) { // 2: DISPATCH_PRODUCT_ORDER
        if (dispatchSection) dispatchSection.style.display = 'block';
        if (giftsSection) giftsSection.style.display = 'block';
        if (addonsSection) addonsSection.style.display = 'block';

        // Dispatch specific fields
        setText('installationDate', formatDate(order.installationDate));
        setText('installationTimeSlot', order.installationTimeSlot || '-');
        setText('expectedCompletionDate', formatDate(order.expectedCompletionDate));
        setText('actualCompletionDate', formatDate(order.actualCompletionDate));
        setText('technicianName', order.technicianName);
        setText('dispatchNotes', order.dispatchNotes);
    } else {
        if (dispatchSection) dispatchSection.style.display = 'none';
        if (giftsSection) giftsSection.style.display = 'none';
        if (addonsSection) addonsSection.style.display = 'none';
    }
    
    // Order Remarks
    setText('orderRemarks', order.remarks || '(無備註)');

    // Determine if the page should be editable based on the order status
    const editableStatuses = [10, 23, 43]; // 草稿, 訂單待補正 (店經理), 訂單待補正 (總公司)
    const isEditable = editableStatuses.includes(order.orderStatusCode);

    setPageEditable(isEditable, order);

    // Render Payment Details
    renderPaymentDetails(order.paymentDetails || []);

    const giftsHeader = document.querySelector('#gifts-info-section table thead tr');
    const addonsHeader = document.querySelector('#addons-info-section table thead tr');

    if (giftsHeader && addonsHeader) {
        giftsHeader.innerHTML += '<th>待料</th>';
        addonsHeader.innerHTML += '<th>待料</th>';
    }
}

function setPageEditable(isEditable, order) {
    const pageTitle = document.getElementById('page-main-title');
    if (!pageTitle) {
        console.error("setPageEditable: 'page-main-title' element not found.");
        return;
    }

    const existingBanner = document.getElementById('status-banner');
    if(existingBanner) existingBanner.remove();

    if (!isEditable) {
        const banner = document.createElement('div');
        banner.id = 'status-banner';
        banner.className = 'alert alert-warning mt-3';
        banner.setAttribute('role', 'alert');
        banner.textContent = `此訂單目前狀態為「${order.orderStatusDescription || '未知'}」，內容不可編輯。`;
        
        // Insert banner after the main title
        pageTitle.parentNode.insertBefore(banner, pageTitle.nextSibling);
    }
}

function getEditUrl(orderTypeCode, orderId) {
    if (orderTypeCode === 1) return `store_product_order_form.html?orderId=${orderId}`;
    if (orderTypeCode === 2) return `dispatch_product_order_form.html?orderId=${orderId}`;
    if (orderTypeCode === 3) return `wholesale_order_form.html?orderId=${orderId}`;
    return '#'; // Fallback
}

function getReturnUrl(orderTypeCode, orderId) {
    // 門市商品訂單
    if (orderTypeCode === 1) return `store_return_form.html?orderId=${orderId}&actionType=RETURN`;
    // 派工商品訂單
    if (orderTypeCode === 2) return `dispatch_return_form.html?orderId=${orderId}&actionType=RETURN`;
    // 批發商品訂單
    if (orderTypeCode === 3) return `wholesale_return_form.html?orderId=${orderId}&actionType=RETURN`;
    return '#'; // Fallback
}

function copyOrderToNew(orderId) {
    // 為了實現門市商品換貨流程，我們讓換貨按鈕也跳轉到退貨頁面，但帶上不同的 actionType
    window.location.href = `store_return_form.html?orderId=${orderId}&actionType=CHANGE`;
}

function copyDispatchOrderToNew(orderId) {
    // 為了實現派工商品換貨流程，我們讓換貨按鈕也跳轉到退貨頁面，但帶上不同的 actionType
    window.location.href = `dispatch_return_form.html?orderId=${orderId}&actionType=CHANGE`;
}

function renderDynamicActions(order) {
    const container = document.getElementById('dynamic-actions-container');
    if (!container) return;
    container.innerHTML = '';

    const createButton = (text, style, action, requiresReason = false, reasonTitle = '提供原因') => {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = `btn ${style} mx-1`;
        button.innerHTML = text;
        button.addEventListener('click', () => {
            if (requiresReason) {
                document.getElementById('reasonModalLabel').textContent = reasonTitle;
                document.getElementById('actionReason').value = '';
                currentActionHandler = (reason) => action(reason);
                reasonModalInstance.show();
            } else {
                action();
            }
        });
        container.appendChild(button);
    };
    
    const createLink = (text, style, href) => {
        const link = document.createElement('a');
        link.href = href;
        link.className = `btn ${style} mx-1`;
        link.innerHTML = text;
        container.appendChild(link);
    };

    const orderTypeCode = order.orderTypeCode;

    // 專為 "門市商品訂單" 且狀態為 "草稿(10)" 或 "門市結帳(30)" 的情況設計按鈕
    if (orderTypeCode === 1 && (order.orderStatusCode === 10 || order.orderStatusCode === 30)) {
        if (order.orderStatusCode === 10) {
            // 草稿狀態按鈕
            createButton('刪除', 'btn-danger me-2', () => handleDeleteDraftOrder(order.orderId));
            createLink('編輯訂單', 'btn-info me-2', `store_product_order_form.html?orderId=${order.orderId}`);
        }
        
        // 送出結帳按鈕 (草稿和門市結帳狀態都顯示)
        createButton('送出結帳', 'btn-primary', () => {
            // --- NEW VALIDATION ---
            if (!order.items || order.items.length === 0) {
                showToast('訂單中沒有任何商品，無法結帳。', 'warning');
                return;
            }
            if (!order.customerName) {
                showToast('沒有客戶資訊，無法結帳。', 'warning');
                return;
            }
            // --- END VALIDATION ---
            handleStoreCheckout(order.orderId)
        });
        
        return; // 處理完畢，直接返回
    }

    // 專為 "派工商品訂單" 且狀態為 "草稿(10)" 的情況設計按鈕
    if (order.orderStatusCode === 10 && orderTypeCode === 2) {
        createButton('刪除', 'btn-danger', () => handleDeleteDraftOrder(order.orderId));
        createLink('編輯訂單', 'btn-info', getEditUrl(orderTypeCode, order.orderId));
        createButton('送出', 'btn-primary', () => handleAction('submit', 'POST', order, '訂單已送出', true));
        return; 
    }

    // 原有的其他訂單狀態和類型的按鈕邏輯
    switch (order.orderStatusCode) {
        case 10: // 草稿 (非門市或派工訂單，例如批發訂單)
            createButton('刪除', 'btn-danger', () => handleDeleteDraftOrder(order.orderId));
            createLink('編輯訂單', 'btn-info', getEditUrl(orderTypeCode, order.orderId));
            createButton('送出審核', 'btn-primary', () => handleAction('submit-for-approval', 'POST', order, '訂單已送出審核', true));
            break;
        case 20: // 訂單審核中 (店經理)
            createButton('退回補正', 'btn-warning', (reason) => handleAction('store-return-for-correction', 'POST', order, '訂單已退回補正。', true, { reason }), true, '退回補正原因');
            createButton('審核通過', 'btn-success', () => handleAction('store-approve', 'POST', order, '訂單已審核通過。', true));
            break;
        case 23: // 訂單待補正 (店經理)
        case 43: // 訂單待補正 (總公司)
            createButton('刪除', 'btn-danger', () => handleAction('delete', 'DELETE', order, '訂單已刪除', true));
            createLink('編輯訂單', 'btn-info', getEditUrl(orderTypeCode, order.orderId));
            createButton('送出審核', 'btn-primary', () => handleAction('submit-for-approval', 'POST', order, '訂單已送出審核', true));
            break;
        case 30: // 門市結帳
            if (order.orderTypeCode === 2) { // 僅派工商品訂單
                createButton('刪除', 'btn-danger', () => handleDeleteDraftOrder(order.orderId));
                createButton('刪除重建', 'btn-warning', () => handleDeleteAndRecreate(order.orderId));
                createLink('修改訂單', 'btn-info', getEditUrl(orderTypeCode, order.orderId));
                createButton('結帳送出', 'btn-success', () => {
                    if (validateCheckoutPrerequisites(order)) {
                        handleAction('dispatch-checkout', 'POST', order, '訂單已結帳送出。', true)
                    }
                });
            }
            break;
        case 36: // 庫存不足 (總倉)
            createButton('取消訂單', 'btn-danger', (reason) => handleAction('request-cancellation', 'POST', order, '取消訂單申請已送出。', true, { reason }), true, '取消訂單原因');
            break;
        case 37: // 已有庫存 (總倉)
            createButton('取消訂單', 'btn-danger', (reason) => handleAction('request-cancellation', 'POST', order, '取消訂單申請已送出。', true, { reason }), true, '取消訂單原因');
            createButton('送出審核', 'btn-primary', () => handleAction('submit-hq-approval', 'POST', order, '訂單已送至總公司審核。', true));
            break;
        case 40: // 訂單審核中 (總公司)
            if (order.canApproveDispatch) {
                createButton('退回補正', 'btn-warning', (reason) => handleAction('hq-return-for-correction', 'POST', order, '訂單已退回補正。', true, { reason }), true, '退回補正原因');
                createButton('訂單駁回', 'btn-danger', (reason) => handleAction('hq-reject', 'POST', order, '訂單已駁回。', true, { reason }), true, '訂單駁回原因');
                createLink('修改訂單', 'btn-info', getEditUrl(orderTypeCode, order.orderId));
                createButton('審核通過', 'btn-success', () => handleAction('hq-approve', 'POST', order, '訂單已審核通過。', true));
            } else {
                createButton('取消訂單', 'btn-alert', (reason) => handleAction('initiate-cancellation', 'POST', order, '取消訂單申請已送出。', true, { reason }), true, '取消訂單原因');
                //container.innerHTML = '<p class="text-muted">您沒有審核此訂單的權限。</p>';
            }
            break;
        case 46: // 訂單駁回
            if (orderTypeCode === 1) {
                createButton('複製資料到新訂單', 'btn-info', () => copyOrderToNew(order.orderId));
            } else if (orderTypeCode === 2) {
                createButton('複製資料到新訂單', 'btn-info', () => copyDispatchOrderToNew(order.orderId));
            } else {
            }
            break;
        case -77: // 退貨駁回
            if (orderTypeCode === 1) {
                createButton('複製資料到新訂單', 'btn-info', () => copyOrderToNew(order.orderId));
            } else if (orderTypeCode === 2) {
                createButton('複製資料到新訂單', 'btn-info', () => copyDispatchOrderToNew(order.orderId));
            } else {
            }
            break;
        case 49: // 總公司審核通過
            if (order.canApproveDispatch) {
                if (orderTypeCode === 2) { // 派工商品
                    createButton('建立派工單', 'btn-success', () => handleCreateDispatchRepair(order.orderId));
                } else if (orderTypeCode === 3) { // 批發商品
                    createButton('列印銷貨憑單', 'btn-success', () => alert('執行列印 (待辦)'));
                }
            } else {
                createButton('取消訂單', 'btn-alert', (reason) => handleAction('initiate-cancellation', 'POST', order, '取消訂單申請已送出。', true, { reason }), true, '取消訂單原因');
                //container.innerHTML = '<p class="text-muted">您沒有審核此訂單的權限。</p>';
            }
            break;
        case 60: // 派工商品待取貨
            createButton('訂單退貨', 'btn-warning', () => window.location.href = getReturnUrl(orderTypeCode, order.orderId));
            break;
        case 63: // 技師裝貨中
            createButton('訂單退貨', 'btn-warning', () => window.location.href = getReturnUrl(orderTypeCode, order.orderId));
            break;
        case 66: // 技師裝貨完成
            createButton('訂單退貨', 'btn-warning', () => window.location.href = getReturnUrl(orderTypeCode, order.orderId));
            break;
        case 70: // 已出貨結案
            createLink('訂單退貨', 'btn-danger', getReturnUrl(orderTypeCode, order.orderId));
            createButton('訂單換貨', 'btn-warning', () => copyOrderToNew(order.orderId));
            break;
        case -40: // 訂單取消審核中
            if (order.canApproveDispatch) {
                createButton('訂單取消駁回', 'btn-danger', (reason) => handleAction('reject-cancellation', 'POST', order, '訂單取消已被駁回。', true, { reason }), true, '駁回取消原因');
                createButton('訂單取消成立', 'btn-success', () => handleAction('approve-cancellation', 'POST', order, '訂單取消已成立。', true));
            } else {
                container.innerHTML = '<p class="text-muted">您沒有審核此訂單的權限。</p>';
            }
            break;
        case -49: // 訂單取消成立
        case -82: // 部分退貨成立未取貨
        case -83: // 退貨成立未取貨
        case -85: // 部分退貨成立已取貨
        case -86: // 退貨成立已取貨
            createButton('退款完成', 'btn-success', 
                () => {
                    // 彈出一個新的 Modal 或使用現有的 reasonModal 來收集退款金額和原因
                    document.getElementById('reasonModalLabel').textContent = '完成退款';
                    // 可以在 modal body 內動態新增金額輸入框
                    // 為了簡化，我們先假設退款金額就是原訂單總額
                    const refundAmount = order.grandTotalAmount;
                    currentActionHandler = (reason) => {
                        const payload = { refundAmount, reason };
                        // 這裡需要知道 refundId
                        // 我們需要修改後端 getOrderById 的回傳值，使其包含最新的 refundId
                        if (order.latestRefundId) {
                            handleAction(`complete-store-return`, 'POST', { orderId: order.orderId, refundId: order.latestRefundId }, '退款已完成。', true, payload);
                        } else {
                            showToast('找不到對應的退貨單記錄', 'error');
                        }
                    };
                    reasonModalInstance.show();
                }, 
                false // 不需要預先確認
            );
            break;
        case -88: // 門市已退款結帳
            container.innerHTML = '<p class="text-muted">此訂單退貨流程已結案。</p>';
            break;
        case -89: // 門市已轉換貨
            container.innerHTML = '<p class="text-muted">此訂單換貨流程已結案。</p>';
            break;
        case -50: // 門市已退款
        case 99: // 入正航銷賬失敗
            createButton('手動入正航', 'btn-primary', () => alert('執行手動入正航 (待辦)'));
            break;
        case -99: // 入正航銷退失敗
            createButton('手動入正航', 'btn-primary', () => alert('執行手動入正航 (待辦)'));
            break;
        default:
            container.innerHTML = '<p class="text-muted">目前狀態無可用操作。</p>';
            break;
    }
}

// 取得所有主商品的出貨方式
function getAllMainProductShipmentMethods(order) {
    const shipmentMethods = [];
    if (order.items && order.items.length > 0) {
        order.items.forEach(item => {
            if (item.itemTypeCode === 0) { // 主商品
                shipmentMethods.push(item.requiresDispatch);
            }
        });
    }
    return shipmentMethods;
}

// 判斷是否所有主商品都是店取
function areAllMainProductsStorePickup(order) {
    const shipmentMethods = getAllMainProductShipmentMethods(order);
    return shipmentMethods.length > 0 && shipmentMethods.every(method => method === 0);
}

// 判斷主商品是否需要倉庫驗證
function shouldValidateWarehouseForMainProduct(item) {
    if (item.itemTypeCode !== 0) return true; // 非主商品一律需要驗證
    const shipmentMethod = item.requiresDispatch;
    return shipmentMethod !== 0 && shipmentMethod !== 2; // 店取(0)或自載展示機(2)不需要驗證
}

function validateCheckoutPrerequisites(order) {
    if (!order) return false;

    // 1. 檢查客戶資料
    if (!order.customerName || !order.customerPhone || !order.installationAddress) {
        showToast('請先填寫完整的客戶資料。', 'warning');
        return false;
    }

    // 2. 檢查商品資料
    if (!order.items || order.items.length === 0) {
        showToast('訂單中沒有任何商品。', 'warning');
        return false;
    }

    // 3. 檢查商品是否都已指定倉庫
    for (const item of order.items) {
        if (item.itemTypeCode === 0) { // 主商品
            const needsWarehouseValidation = shouldValidateWarehouseForMainProduct(item);
            if (needsWarehouseValidation && (!item.warehouseName || item.warehouseName === 'N/A')) {
                showToast(`主商品「${item.productName}」尚未指定出貨倉庫。`, 'warning');
                return false;
            }
        }
        if (item.itemGroups && item.itemGroups.length > 0) {
            for (const group of item.itemGroups) {
                // 附屬品的倉庫驗證需要基於其所屬主商品的出貨方式
                const needsWarehouseValidation = shouldValidateWarehouseForMainProduct(item);
                if (needsWarehouseValidation && (!group.warehouseName || group.warehouseName === 'N/A')) {
                    showToast(`附屬品「${group.productName}」尚未指定出貨倉庫。`, 'warning');
                    return false;
                }
            }
        }
    }

    // 4. 檢查是否已指派技師
    // 僅當不是所有主商品都是店取時才檢查技師指派
    if (!areAllMainProductsStorePickup(order) && !order.technicianId) {
        showToast('尚未指派技師。', 'warning');
        return false;
    }

    return true; // 所有檢查通過
}

async function handleAction(endpoint, method, context, successMessage, confirmRequired = false, body = null) {
    if (confirmRequired && !confirm(`確定要執行此操作嗎？`)) return;

    showLoading(true);
    let newOrderData = null;

    let url;
    if (endpoint === 'complete-store-return') {
        url = `/api/v1/order-refunds/${context.refundId}/complete-store-return`;
    } else {
        url = `/api/v1/orders/${context.orderId}/${endpoint}`;
    }

    try {
        const response = await window.fetchAuthenticated(url, {
            method: method,
            body: body ? JSON.stringify(body) : null
        });
        
        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || `操作失敗 (HTTP ${response.status})`);
        }
        
        window.showToast(successMessage, 'success');
        
        if (endpoint === 'initiate-cancellation' ||
            endpoint === 'reject-cancellation' ||
            endpoint === 'approve-cancellation') {
            setTimeout(() => {
                const listPage = getListPageUrl(context.orderTypeCode);
                window.location.href = listPage;
            }, 1500); // 延遲 1.5 秒後跳轉
        } else if (endpoint === 'copy') {
            newOrderData = result.data;
        } else {
            await fetchOrderDetail(context.orderId); // Standard refresh
        }

    } catch (error) {
        console.error(`Error during action ${endpoint}:`, error);
        window.showToast(`操作失敗: ${error.message}`, 'error');
    } finally {
        showLoading(false);
    }
    return newOrderData; // Return data for chaining
}

function getListPageUrl(orderTypeCode) {
    if (orderTypeCode === 1) return 'store_product_orders_list.html';
    if (orderTypeCode === 2) return 'dispatch_product_orders_list.html';
    if (orderTypeCode === 3) return 'wholesale_order_list.html';
    return 'home.html'; // Fallback
}

async function handleSubmitReason() {
    if (currentActionHandler) {
        const reason = document.getElementById('actionReason').value;
        if (!reason) {
            alert('請輸入原因。');
            return;
        }
        reasonModalInstance.hide();
        await currentActionHandler(reason);
        currentActionHandler = null;
    }
}

async function handleDeleteDraftOrder(orderId) {
    if (confirm('您確定要刪除此草稿訂單嗎？')) {
        try {
            const response = await fetchAuthenticated(`/api/v1/orders/${orderId}`, { method: 'DELETE' });
            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '刪除失敗');
            }
            showToast('草稿訂單已刪除', 'success');
            window.location.href = 'store_product_orders_list.html';
        } catch (error) {
            showToast(`刪除失敗: ${error.message}`, 'danger');
        }
    }
}

async function handleStoreCheckout(orderId) {
    try {
        const response = await fetchAuthenticated(`/api/v1/orders/${orderId}/store-checkout`, { method: 'POST' });
        if (!response.ok) {
            const errData = await response.json();
            // 如果是庫存不足，跳轉回編輯頁面
            if (errData.message && errData.message.includes("庫存")) {
                 showToast(errData.message + "，將返回編輯頁面。", 'danger');
                 window.location.href = `store_product_order_form.html?orderId=${orderId}`;
            } else {
                throw new Error(errData.message || '結帳失敗');
            }
        } else {
            showToast('訂單已結帳並完成', 'success');
            window.location.href = 'store_product_orders_list.html'; // 跳轉回訂單列表頁
        }
    } catch (error) {
        showToast(`結帳失敗: ${error.message}`, 'danger');
    }
}

function handleDeleteAndRecreate(orderId) {
    if (confirm('您確定要刪除此訂單並用其資料建立一筆新的草稿訂單嗎？')) {
        handleAction('delete', 'DELETE', { orderId: orderId }, '訂單已刪除，正在轉至新訂單畫面...', false)
            .then(() => {
                // On successful deletion, redirect to the form with a copy-from instruction
                window.location.href = `dispatch_product_order_form.html?copyFromId=${orderId}`;
            });
    }
}

async function handleCreateDispatchRepair(orderId) {
    if (!orderId) return;
    if (!confirm('您確定要為此訂單建立派工單嗎？此操作無法復原。')) return;

    try {
        const url = `/api/v1/dispatch-orders/from-order/${orderId}`;
        const response = await fetchAuthenticated(url, { method: 'POST' });
        if (!response.ok) {
            const errData = await response.json();
            throw new Error(errData.message || '建立派工單失敗');
        }
        showToast('派工單已成功建立！', 'success');
        // Redirect to the dispatch product order list page as requested
        setTimeout(() => {
            window.location.href = 'dispatch_product_orders_list.html';
        }, 1500);
    } catch (error) {
        console.error('Failed to create dispatch repair orders:', error);
        showToast(`建立派工單失敗: ${error.message}`, 'danger');
    }
}

// Note: handleProcessCheckout is complex due to PaymentDetailDto array needed by backend.
// It's typically handled on the order form page where items and payments can be fully managed.
// If a simple "confirm checkout" for an already finalized order is needed, the backend API might need adjustment
// or this detail page button would link to the form in a specific checkout mode.
// For now, omitting direct checkout button that requires payment input on this detail page. 

function renderPaymentDetails(paymentDetails) {
    const container = document.getElementById('payment-details-list');
    if (!container) return;

    container.innerHTML = '';
    if (paymentDetails.length === 0) {
        container.innerHTML = '<p class="text-muted small mb-0">無付款紀錄</p>';
        return;
    }

    paymentDetails.forEach(payment => {
        const paymentMethodName = getPaymentMethodName(payment.paymentMethodCode);
        const paymentRow = document.createElement('p');
        paymentRow.className = 'text-muted small mb-1';
        paymentRow.innerHTML = ` - ${paymentMethodName}: <span class="fw-normal">NT$ ${payment.amount.toLocaleString()}</span>`;
        container.appendChild(paymentRow);
    });
}

function getPaymentMethodName(code) {
    // This is a temporary helper. Ideally, this comes from an enum API.
    const map = {
        'CASH': '現金', 'CREDIT_CARD': '信用卡', 'BANK_TRANSFER': '匯款', 'TECHNICIAN_COLLECTION': '技師代收款', 'EXCHANGE': '換貨轉入'
    };
    return map[code] || code;
} 