document.addEventListener('DOMContentLoaded', function () {
    // Initialization
    const searchForm = document.getElementById('search-form');

    Promise.all([
        populateStoreSelect(),
        populateApprovalStatusSelect()
    ]).then(() => {
        searchRecords(0); // Initial load after dropdowns are populated
    });

    searchForm.addEventListener('submit', function (event) {
        event.preventDefault();
        searchRecords(0);
    });
});

async function populateStoreSelect() {
    const select = document.getElementById('searchStore');
    try {
        const response = await window.fetchAuthenticated('/api/v1/auth/operable-stores');
        const apiResponse = await response.json();
        if (apiResponse.code === 200 && apiResponse.data) {
            select.innerHTML = '<option value="" selected>所有門市</option>';
            apiResponse.data.forEach(store => {
                select.add(new Option(store.storeName, store.storeId));
            });
        }
    } catch (error) {
        console.error('Failed to load stores:', error);
    }
}

async function populateApprovalStatusSelect() {
    const select = document.getElementById('searchApprovalStatus');
    try {
        // Assuming you'll create an endpoint for this enum
        const response = await window.fetchAuthenticated('/api/v1/enums/inventory-approval-statuses');
        const apiResponse = await response.json();
        if (apiResponse.code === 200 && apiResponse.data) {
             select.innerHTML = '<option value="" selected>全部審核狀態</option>';
            apiResponse.data.forEach(status => {
                select.add(new Option(status.description, status.code));
            });
        }
    } catch (error) {
        console.error('Failed to load approval statuses:', error);
    }
}

async function searchRecords(page = 0) {
    const tableBody = document.getElementById('records-table-body');
    const companyDivisionCode = window.getCompanyDivisionCode();
    
    if (companyDivisionCode === null) {
        tableBody.innerHTML = `<tr><td colspan="10" class="text-center p-4 text-danger">錯誤: 無法確定公司別，請重新登入。</td></tr>`;
        return;
    }

    const storeId = document.getElementById('searchStore').value;
    const countMonth = document.getElementById('searchMonth').value;
    const approvalStatusCode = document.getElementById('searchApprovalStatus').value;

    const params = new URLSearchParams({
        page,
        size: 10,
        sort: 'countDate,desc',
        companyDivisionCode
    });

    if (storeId) params.append('storeId', storeId);
    if (countMonth) params.append('countMonth', countMonth);
    if (approvalStatusCode) params.append('approvalStatusCode', approvalStatusCode);

    tableBody.innerHTML = `<tr><td colspan="10" class="text-center">載入中...</td></tr>`;

    try {
        const response = await window.fetchAuthenticated(`/api/v1/inventory-counts?${params.toString()}`);
        const apiResponse = await response.json();

        if (apiResponse.code === 200 && apiResponse.data) {
            renderTable(apiResponse.data.list, page, 10);
            renderPagination(apiResponse.data);
        } else {
            tableBody.innerHTML = `<tr><td colspan="10" class="text-center text-danger">${apiResponse.message || '查詢失敗'}</td></tr>`;
        }
    } catch (error) {
        console.error('Error fetching inventory count records:', error);
        tableBody.innerHTML = `<tr><td colspan="10" class="text-center text-danger">查詢時發生錯誤</td></tr>`;
    }
}

function renderTable(records, page, size) {
    const tableBody = document.getElementById('records-table-body');
    tableBody.innerHTML = '';
    if (!records || records.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="10" class="text-center">查無資料</td></tr>';
        return;
    }

    records.forEach((record, index) => {
        const row = tableBody.insertRow();
        row.innerHTML = `
            <td>${page * size + index + 1}</td>
            <td>${record.storeName || ''}</td>
            <td><a href="inventory_count_detail.html?id=${record.inventoryCountSheetId}">${record.sheetNumber || ''}</a></td>
            <td>${record.countMonth || ''}</td>
            <td>${record.countDate ? new Date(record.countDate).toLocaleDateString() : ''}</td>
            <td>${record.countedByUserName || ''}</td>
            <td>${record.originalSheetNumber || ''}</td>
            <td>${record.sheetStatusDescription || ''}</td>
            <td><span class="badge ${getApprovalStatusBadge(record.approvalStatusCode)}">${record.approvalStatusDescription || ''}</span></td>
            <td>
                <a href="inventory_count_detail.html?id=${record.inventoryCountSheetId}" class="btn btn-sm btn-outline-primary" title="查看">
                    <i class="bi bi-eye"></i>
                </a>
            </td>
        `;
    });
}

function renderPagination(pageData) {
    const paginationControls = document.getElementById('pagination-controls');
    paginationControls.innerHTML = '';
    const { page, totalPages } = pageData;
    if (totalPages <= 1) return;

    const createPageItem = (text, pageNum, isDisabled, isActive) => {
        const li = document.createElement('li');
        li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        if (!isDisabled) {
            a.addEventListener('click', e => {
                e.preventDefault();
                searchRecords(pageNum);
            });
        }
        li.appendChild(a);
        return li;
    };

    paginationControls.appendChild(createPageItem('«', 0, page === 0, false));
    paginationControls.appendChild(createPageItem('‹', page - 1, page === 0, false));

    for (let i = 0; i < totalPages; i++) {
        if(i === page || (i >= page - 2 && i <= page + 2) ){
             paginationControls.appendChild(createPageItem(i + 1, i, false, i === page));
        } else if (i === page - 3 || i === page + 3){
             const li = document.createElement('li');
             li.className = 'page-item disabled';
             li.innerHTML = `<span class="page-link">...</span>`;
             paginationControls.appendChild(li);
        }
    }

    paginationControls.appendChild(createPageItem('›', page + 1, page >= totalPages - 1, false));
    paginationControls.appendChild(createPageItem('»', totalPages - 1, page >= totalPages - 1, false));
}

function getApprovalStatusBadge(statusCode) {
    switch(statusCode) {
        case 30: return 'bg-success'; // 審核通過
        case 50: return 'bg-success'; // 複盤通過
        case 40: return 'bg-danger';  // 審核未通過
        case 20: return 'bg-warning'; // 已送審
        default: return 'bg-secondary'; // 未送審
    }
} 