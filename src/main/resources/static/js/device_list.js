document.addEventListener('DOMContentLoaded', function () {
    const tableBody = document.getElementById('devices-table-body');
    const searchInput = document.getElementById('keyword-search');
    const paginationControls = document.getElementById('pagination-controls');
    let searchTimeout;

    async function searchDevices(page = 1) {
        const keyword = searchInput.value.trim();
        const params = new URLSearchParams({
            page: page - 1,
            size: 15,
            sort: 'createTime,desc'
        });
        if (keyword) {
            params.append('keyword', keyword);
        }

        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">查詢中...</td></tr>';
        
        try {
            const response = await window.fetchAuthenticated(`/api/v1/devices?${params.toString()}`);
            if (!response.ok) throw new Error("查詢失敗");
            const result = await response.json();
            
            renderTable(result.data.list || []);
            renderPagination(result.data);
        } catch (error) {
            tableBody.innerHTML = `<tr><td colspan="6" class="text-center text-danger">查詢失敗: ${error.message}</td></tr>`;
        }
    }

    function renderTable(devices) {
        tableBody.innerHTML = '';
        if (!devices || devices.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center">查無設備資料</td></tr>';
            return;
        }
        devices.forEach(device => {
            const row = tableBody.insertRow();
            row.innerHTML = `
                <td>${device.deviceSerialNumber}</td>
                <td>${device.productName || 'N/A'}</td>
                <td>${device.customerName || 'N/A'}</td>
                <td>${device.installationAddress || 'N/A'}</td>
                <td>${device.warrantyDate ? new Date(device.warrantyDate).toLocaleDateString() : '-'}</td>
                <td>${device.lastRepairDate ? new Date(device.lastRepairDate).toLocaleDateString() : '-'}</td>
            `;
        });
    }

    function renderPagination(pageData) {
        paginationControls.innerHTML = '';
        if (!pageData || pageData.totalPages <= 1) return;

        // Simplified pagination logic (can be expanded)
        const { number: page, totalPages } = pageData;
        let paginationHtml = '<ul class="pagination justify-content-center">';
        paginationHtml += `<li class="page-item ${page === 0 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page}">‹</a></li>`;
        for (let i = 0; i < totalPages; i++) {
            paginationHtml += `<li class="page-item ${i === page ? 'active' : ''}"><a class="page-link" href="#" data-page="${i + 1}">${i + 1}</a></li>`;
        }
        paginationHtml += `<li class="page-item ${page >= totalPages - 1 ? 'disabled' : ''}"><a class="page-link" href="#" data-page="${page + 2}">›</a></li>`;
        paginationHtml += '</ul>';
        paginationControls.innerHTML = paginationHtml;

        paginationControls.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                if (!this.parentElement.classList.contains('disabled')) {
                    searchDevices(parseInt(this.dataset.page));
                }
            });
        });
    }

    searchInput.addEventListener('input', () => {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => searchDevices(1), 500); // Debounce search
    });

    // Initial load
    searchDevices(1);
}); 