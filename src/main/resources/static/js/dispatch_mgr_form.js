document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('repair-order-form');
    const searchDeviceBtn = document.getElementById('search-device-btn');
    const deviceSearchInput = document.getElementById('device-search');
    const itemList = document.getElementById('item-list');
    const addItemBtn = document.getElementById('add-item-btn');
    const saveDraftBtn = document.getElementById('save-draft-btn');
    const submitBtn = document.getElementById('submit-btn');
    const deleteBtn = document.getElementById('delete-btn');
    let availableItemTypes = []; // Global variable to store item types
    let isEditMode = false;
    let currentDispatchRepairId = null;

    async function initializeForm() {
        const urlParams = new URLSearchParams(window.location.search);
        currentDispatchRepairId = urlParams.get('id');
        isEditMode = !!currentDispatchRepairId;

        await Promise.all([
            loadTechnicians(),
            populateRepairTypes(),
            populateEnumSelect('warranty-status-select', '/api/v1/enums/warranty-statuses', '請選擇保固狀態'),
            loadItemTypes()
        ]);
        
        if (isEditMode) {
            document.querySelector('h3').textContent = '修改派工單';
            document.querySelector('.breadcrumb-item.active').textContent = '修改派工單';
            await loadAndPopulateForm(currentDispatchRepairId);
        } else {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('report-date').value = today;
            if (deleteBtn) deleteBtn.style.display = 'none';
        }
    }
    
    async function populateRepairTypes() {
        const selectId = 'repair-type-select';
        const selectElement = document.getElementById(selectId);
        if (!selectElement) return;

        try {
            const response = await window.fetchAuthenticated('/api/v1/enums/dispatch-order-types');
            if (!response.ok) throw new Error('無法載入單據類型');
            const result = await response.json();

            if (result.data && Array.isArray(result.data)) {
                // Filter out the "派工" (code 10) option
                const filteredTypes = result.data.filter(type => type.value !== '10');
                populateDropdown(selectElement, filteredTypes, '請選擇類型');
            }
        } catch (error) {
            console.error(`無法載入 ${selectId}:`, error);
            if(window.showToast) window.showToast(error.message, 'danger');
        }
    }

    async function loadItemTypes() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/enums/repair-order-types');
            if (!response.ok) throw new Error('無法載入品項類型');
            const result = await response.json();
            if (result.data) {
                availableItemTypes = result.data;
            }
        } catch (error) {
            console.error('無法載入品項類型:', error);
            if(window.showToast) window.showToast('無法載入品項類型', 'danger');
        }
    }

    async function loadTechnicians() {
        const selectId = 'technician-select';
        const selectElement = document.getElementById(selectId);
        if (!selectElement) return;

        try {
            const response = await window.fetchAuthenticated('/api/v1/users/technicians');
            if (!response.ok) throw new Error('無法載入技師列表');
            const apiResult = await response.json();

            if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
                selectElement.innerHTML = `<option value="">請選擇技師</option>`;
                apiResult.data.forEach(technician => {
                    const option = document.createElement('option');
                    // Use the correct properties from the technician object
                    option.value = technician.userAccountId; 
                    option.textContent = technician.userName;
                    selectElement.appendChild(option);
                });
            }
        } catch (error) {
            console.error(`無法載入 ${selectId}:`, error);
            selectElement.innerHTML = `<option value="">載入失敗</option>`;
        }
    }

    function populateDropdown(selectElement, options, defaultText) {
        selectElement.innerHTML = `<option value="">${defaultText}</option>`;
        if (options && Array.isArray(options)) {
            options.forEach(option => {
                selectElement.innerHTML += `<option value="${option.value}">${option.label}</option>`;
            });
        }
    }

    async function loadDropdown(selectId, url, valueField = 'value', labelField = 'label', defaultOptionText = '請選擇') {
        const selectElement = document.getElementById(selectId);
        if (!selectElement) return;
        try {
            const response = await window.fetchAuthenticated(url);
            if (!response.ok) throw new Error('無法載入資料');
            const result = await response.json();
            if (result.data) {
                populateDropdown(selectElement, result.data, defaultOptionText);
            }
        } catch (error) {
            console.error(`無法載入 ${selectId}:`, error);
        }
    }

    async function loadAndPopulateForm(id) {
        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${id}`);
            if (!response.ok) throw new Error('無法獲取派工單資料');
            const order = await response.json();
            populateForm(order);
        } catch (error) {
            console.error('載入派工單失敗:', error);
            if(window.showToast) window.showToast(`載入失敗: ${error.message}`, 'danger');
        }
    }

    function populateForm(order) {
        // Populate header fields
        document.getElementById('device-search').value = order.deviceSerialNumber || '';
        document.getElementById('customer-name').textContent = order.customerName || '-';
        document.getElementById('contact-name').value = order.customerName || '';
        document.getElementById('contact-phone').value = order.customerPhone || '';
        document.getElementById('contact-address').value = order.installationAddress || '';
        document.getElementById('report-date').value = order.createTime ? new Date(order.createTime).toISOString().split('T')[0] : '';
        document.getElementById('repair-date').value = order.scheduledDate || '';
        document.getElementById('technician-select').value = order.assignedTechnicianId || '';
        document.getElementById('repair-type-select').value = order.typeCode || '';
        
        // Populate item-specific fields (assuming one main item for simplicity in this view)
        const mainItem = order.items && order.items.length > 0 ? order.items[0] : {};
        document.getElementById('warranty-status-select').value = mainItem.warrantyStatusCode || '';
        document.getElementById('issue-description').value = mainItem.issueDescription || '';

        // Populate other textareas
        document.getElementById('handling-method').value = order.handlingMethod || '';
        document.getElementById('follow-up').value = order.followUpAction || '';
        document.getElementById('remarks').value = order.remarks || '';

        // Populate items table
        const itemListBody = document.getElementById('item-list');
        itemListBody.innerHTML = '';
        if (order.items) {
            order.items.forEach(item => {
                const row = itemListBody.insertRow();
                row.innerHTML = `
                    <td>...</td>
                    <td>${item.productName}</td>
                    <td><input type="number" class="form-control" value="${item.quantity}"></td>
                    <td><input type="number" class="form-control" value="${item.unitPrice}"></td>
                    <td>${item.subtotalAmount}</td>
                    <td>...</td>
                `;
            });
        }
        if (isEditMode && order.statusCode !== 10) { // 10 is draft
            if (deleteBtn) deleteBtn.style.display = 'none';
            if (saveDraftBtn) saveDraftBtn.style.display = 'none';
            if (submitBtn) submitBtn.style.display = 'none';
            // Disable all inputs
            form.querySelectorAll('input, select, textarea, button').forEach(el => {
                if (!el.classList.contains('non-readonly')) { // allow some buttons to be enabled
                    el.disabled = true;
                }
            });
        } else if (isEditMode && order.statusCode === 10) {
            if(deleteBtn) deleteBtn.style.display = 'inline-block';
        }
    }

    searchDeviceBtn.addEventListener('click', async () => {
        const serialNumber = deviceSearchInput.value.trim();
        if (!serialNumber) {
            showToast('請輸入機身序號', 'warning');
            return;
        }

        try {
            const response = await window.fetchAuthenticated(`/api/v1/devices/by-serial/${serialNumber}`);
            if (!response.ok) {
                 const errData = await response.json();
                 throw new Error(errData.message || '查詢失敗');
            }
            const result = await response.json();
            const device = result.data;

            if (device) {
                document.getElementById('customer-name').textContent = device.customerName || '-';
                document.getElementById('product-model').value = device.productName || '';
                document.getElementById('customer-device-id').value = device.customerDeviceId || '';
                document.getElementById('customer-id').value = device.customerId || '';
                // The form uses contact-name, contact-phone etc.
                document.getElementById('contact-name').value = device.customerName || ''; // Assuming contact name is customer name
                document.getElementById('contact-phone').value = device.customerPhone || ''; // Assuming contact phone is customer phone
                document.getElementById('contact-address').value = device.installationAddress || '';
            } else {
                showToast('查無此機身序號對應的設備資料', 'info');
            }
        } catch(error) {
            console.error('查詢設備失敗:', error);
            showToast(`查詢設備失敗: ${error.message}`, 'danger');
        }
    });

    addItemBtn.addEventListener('click', () => {
        const row = itemList.insertRow();
        row.innerHTML = `
            <td><select class="form-select form-select-sm item-type-select"></select></td>
            <td><input type="text" class="form-control form-control-sm product-barcode-input" placeholder="條碼"></td>
            <td><input type="number" class="form-control form-control-sm quantity-input" value="1" min="1"></td>
            <td><input type="number" class="form-control form-control-sm price-input" placeholder="單價"></td>
            <td class="subtotal-cell">0</td>
            <td><button type="button" class="btn btn-sm btn-danger remove-item-btn">移除</button></td>
        `;
        populateDropdown(row.querySelector('.item-type-select'), availableItemTypes, '請選擇類型');
        row.querySelector('.remove-item-btn').addEventListener('click', () => row.remove());
    });

    function buildPayload(statusCode) {
        const typeCode = document.getElementById('repair-type-select').value;
        if (!typeCode) {
            showToast('請選擇「單據類型」。', 'warning');
            return null; // Return null to indicate validation failure
        }

        const items = [];
        document.querySelectorAll('#item-list tr').forEach(row => {
            const itemTypeCodeValue = row.querySelector('.item-type-select')?.value;
            if (!itemTypeCodeValue) return; // Skip if no item type is selected
            
            items.push({
                productBarcode: row.querySelector('.product-barcode-input').value,
                productName: "TBD", // This needs to be looked up or handled differently
                quantity: parseInt(row.querySelector('.quantity-input').value) || 1,
                unitPrice: parseFloat(row.querySelector('.price-input').value) || 0,
                itemTypeCode: parseInt(itemTypeCodeValue)
            });
        });

        return {
            id: currentDispatchRepairId,
            typeCode: typeCode,
            statusCode: statusCode, // Use the passed status code
            customerName: document.getElementById('contact-name').value,
            customerPhone: document.getElementById('contact-phone').value,
            installationAddress: document.getElementById('contact-address').value,
            customerDeviceId: document.getElementById('customer-device-id').value,
            customerId: document.getElementById('customer-id').value,
            deviceSerialNumber: deviceSearchInput.value,
            issueDescription: document.getElementById('issue-description').value,
            handlingMethod: document.getElementById('handling-method').value,
            followUpAction: document.getElementById('follow-up').value,
            scheduledDate: document.getElementById('repair-date').value || null,
            assignedTechnicianId: document.getElementById('technician-select').value,
            remarks: document.getElementById('remarks').value,
            items: items
        };
    }

    async function handleSave(isDraft) {
        const statusCode = isDraft ? 10 : 20;
        const payload = buildPayload(statusCode);
        
        if (!payload) return; // Stop if payload is null (validation failed)

        const url = isEditMode ? `/api/v1/repairs/${currentDispatchRepairId}` : '/api/v1/repairs';
        const method = isEditMode ? 'PUT' : 'POST';

        try {
            const response = await window.fetchAuthenticated(url, {
                method: method,
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '操作失敗');
            }

            const result = await response.json();
            const savedOrderId = result.data.id;

            const successMessage = isDraft ? '派工單已儲存為草稿' : '派工單已成功送出';
            showToast(successMessage, 'success');
            setTimeout(() => {
                window.location.href = `dispatch_mgr_detail.html?id=${savedOrderId}`;
            }, 1500);

        } catch (error) {
            console.error('儲存派工單失敗:', error);
            showToast(`儲存失敗: ${error.message}`, 'danger');
        }
    }

    async function handleDelete() {
        if (!isEditMode || !currentDispatchRepairId) return;

        if (confirm('您確定要刪除此草稿派工單嗎？')) {
            try {
                const response = await window.fetchAuthenticated(`/api/v1/repairs/${currentDispatchRepairId}`, { method: 'DELETE' });
                if (!response.ok) {
                    const errData = await response.json();
                    throw new Error(errData.message || '刪除失敗');
                }
                if(window.showToast) window.showToast('派工單已刪除', 'success');
                setTimeout(() => window.location.href = 'dispatch_mgr_list.html', 1500);
            } catch (error) {
                if(window.showToast) window.showToast(`刪除失敗: ${error.message}`, 'danger');
            }
        }
    }

    saveDraftBtn.addEventListener('click', () => handleSave(true));
    submitBtn.addEventListener('click', () => handleSave(false));
    if (deleteBtn) deleteBtn.addEventListener('click', handleDelete);

    initializeForm();
});

function showToast(message, type = 'info') {
    // This function might be globally available from main.js, 
    // but having a local fallback that doesn't cause recursion is safer.
    if (window.bootstrap && window.bootstrap.Toast) {
         // Assuming a toast container exists in the HTML, e.g., <div id="toast-container"></div>
        const toastContainer = document.getElementById('toast-container');
        if (toastContainer) {
            // Create and show a Bootstrap toast
        } else {
             alert(message); // Fallback if container not found
        }
    } else {
        alert(message);
    }
} 