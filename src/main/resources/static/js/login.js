document.addEventListener('DOMContentLoaded', function () {
    const loginForm = document.getElementById('login-form');
    const errorMessageDiv = document.getElementById('error-message');

    loginForm.addEventListener('submit', function (event) {
        event.preventDefault();
        errorMessageDiv.classList.add('d-none'); // Hide error message on new submit

        const employeeId = document.getElementById('employeeId').value;
        const password = document.getElementById('password').value;
        const company = document.getElementById('company').value;

        // Basic validation (more can be added)
        if (!employeeId || !password || !company) {
            showError('帳號、密碼及公司別欄位皆必填。');
            return;
        }

        const loginData = {
            employeeId: employeeId,
            password: password,
            companyContext: company
        };

        // Make API call to backend
        fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(loginData),
        })
        .then(response => {
            // 先檢查 response header 中的 JWT token（與 fetchAuthenticated 保持一致）
            const authHeader = response.headers.get('Authorization');
            let tokenFromHeader = null;
            if (authHeader && authHeader.startsWith('Bearer ')) {
                tokenFromHeader = authHeader.substring(7);
                console.log('Login: JWT token received in Authorization header');
            }
            
            if (!response.ok) {
                // Try to parse error response from backend
                return response.json().then(errData => {
                    throw new Error(errData.message || '登入失敗，請檢查您的帳號或密碼。');
                }).catch(() => {
                    // If parsing JSON fails or no message, use generic error based on status
                    throw new Error(`登入失敗 (${response.status})，請檢查您的帳號或密碼。`);
                });
            }
            return response.json().then(apiResponse => {
                // 將 header 中的 token 傳遞給下一個處理
                return { apiResponse, tokenFromHeader };
            });
        })
        .then(({ apiResponse, tokenFromHeader }) => {
            // Check for our standard ApiResponse structure
            if (apiResponse.code === 200 && apiResponse.data) {
                const data = apiResponse.data; // Actual LoginResponse is in apiResponse.data
                
                // 優先使用 header 中的 token，如果沒有則使用 body 中的
                const finalToken = tokenFromHeader || data.token;
                
                if (finalToken && data.sessionUuid) {
                    // Login successful
                    console.log('Login successful:', data);
                    console.log('Using token from:', tokenFromHeader ? 'header' : 'body');
                    
                    localStorage.setItem('jwtToken', finalToken);
                    localStorage.setItem('sessionUuid', data.sessionUuid);
                    localStorage.setItem('userName', data.userName || '使用者'); 
                    localStorage.setItem('userRoles', JSON.stringify(data.roles));
                    localStorage.setItem('functionPermissions', JSON.stringify(data.functionPermissions || []));
                    localStorage.setItem('selectedCompanyContext', company); // Store selected company
                    
                    // Call the global timer reset function from main.js
                    if (typeof resetTokenExpirationTimer === 'function') {
                        resetTokenExpirationTimer(data.token);
                    } else {
                        console.error("Error: resetTokenExpirationTimer function not found. It should be in main.js.");
                    }
                    
                    if (data.requiresStoreSelection && data.availableStores && data.availableStores.length > 0) {
                        localStorage.setItem('availableStores', JSON.stringify(data.availableStores));
                        window.location.href = 'select_store.html';
                    } else if (data.selectedStore) {
                         localStorage.setItem('selectedStore', JSON.stringify(data.selectedStore));
                         window.location.href = 'home.html';
                    } else {
                        window.location.href = 'home.html';
                    }
                } else {
                    // This case might not be hit if errors are handled by HTTP status + ApiResponse.error
                    showError(data.message || '登入成功，但無法取得必要資訊。');
                }
            } else {
                // Handle cases where response is 200 OK but our custom 'code' indicates an issue, 
                // or if the structure is not as expected.
                showError(apiResponse.message || '登入回應格式錯誤。');
            }
        })
        .catch(error => {
            console.error('Login error details:', error);
            showError(error.message || '登入時發生錯誤，請稍後再試。');
        });
    });

    function showError(message) {
        errorMessageDiv.textContent = message;
        errorMessageDiv.classList.remove('d-none');
    }
}); 