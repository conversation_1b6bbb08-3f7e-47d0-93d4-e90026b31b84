document.addEventListener('DOMContentLoaded', async function () {
    const API_BASE_URL = '/api/v1/product-menus';
    const EXTERNAL_PRODUCTS_API_URL = '/api/v1/external-products'; // Placeholder for actual API

    const menuTypeSelect = document.getElementById('menuTypeSelect');
    const menuConfigContainer = document.querySelector('.menu-config-container');
    const saveMenuOrderBtn = document.getElementById('save-menu-order-btn');
    const cancelMenuOrderBtn = document.getElementById('cancel-menu-order-btn');

    const addCategoryModal = new bootstrap.Modal(document.getElementById('add-category-modal'));
    const addCategoryForm = document.getElementById('add-category-form');
    const saveCategoryBtn = document.getElementById('save-category-btn');
    
    const addItemModal = new bootstrap.Modal(document.getElementById('add-item-modal'));
    const productSearchInput = document.getElementById('productSearchInput');
    const productSearchBtn = document.getElementById('product-search-btn');
    const productSearchResultsContainer = document.getElementById('product-search-results');
    const noProductResultsMessage = document.getElementById('no-product-results-message');
    let currentTargetCategoryIdForItem = null;

    const deleteConfirmModal = new bootstrap.Modal(document.getElementById('delete-menu-item-confirm-modal'));
    const confirmDeleteMenuItemBtn = document.getElementById('confirm-delete-menu-item-btn');
    const deleteMenuItemMessage = document.getElementById('delete-menu-item-message');
    let itemToDelete = { id: null, type: null };

    let initialMenuState = {}; // To store the initial state for cancellation
    const MAX_LEVELS = 4; // Max depth of menu columns
    let sortableInstances = [];

    async function fetchMenuTree(menuType) {
        showLoading();
        try {
            const response = await fetch(`${API_BASE_URL}/tree/${menuType}`, {
                headers: { 'Authorization': `Bearer ${localStorage.getItem('jwtToken')}` }
            });
            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                    showToast('權限不足或登入逾時，請重新登入。', 'error');
                    localStorage.removeItem('jwtToken');
                    window.location.href = 'login.html';
                    return;
                }
                throw new Error('Failed to load menu tree');
            }
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data) {
                initialMenuState[menuType] = JSON.parse(JSON.stringify(apiResponse.data)); // Deep copy
                renderMenuColumns(apiResponse.data);
            } else {
                showToast(apiResponse.message || '無法載入選單結構', 'error');
                menuConfigContainer.innerHTML = '<p class="text-danger">無法載入選單結構。</p>';
            }
        } catch (error) {
            console.error('Error fetching menu tree:', error);
            showToast('載入選單結構失敗', 'error');
            menuConfigContainer.innerHTML = '<p class="text-danger">載入選單結構失敗。</p>';
        }
        hideLoading();
    }

    function renderMenuColumns(nodes, level = 0, parentId = null, parentName = '頂層分類') {
        if (level === 0) {
            menuConfigContainer.innerHTML = ''; // Clear previous columns
            sortableInstances.forEach(s => s.destroy());
            sortableInstances = [];
        }

        if (level >= MAX_LEVELS) return;

        const columnId = `menu-level-${level}-col`;
        const listId = `menu-level-${level}`;

        let column = document.getElementById(columnId);
        if (!column) {
            column = document.createElement('div');
            column.className = 'menu-column col border-end';
            column.id = columnId;
            column.innerHTML = `
                <div class="menu-column-header p-2 bg-light border-bottom">
                    <h5>${parentName} (第 ${level + 1} 層)</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary add-category-btn me-1" data-parent-id="${parentId || ''}" data-level="${level}" title="新增子分類"><i class="bi bi-folder-plus"></i></button>
                        <button class="btn btn-sm btn-outline-success add-item-btn" data-category-id="${parentId || ''}" data-level="${level}" title="新增商品至此分類" ${!parentId ? 'disabled' : ''}><i class="bi bi-box-seam"></i></button>
                    </div>
                </div>
                <ul class="list-group list-group-flush sortable-list" id="${listId}" data-level="${level}" data-parent-id="${parentId || ''}"></ul>
            `;
            menuConfigContainer.appendChild(column);
            
            const ulElement = column.querySelector('ul');
            sortableInstances.push(new Sortable(ulElement, {
                group: 'menu-items', 
                animation: 150,
                handle: '.handle',
                ghostClass: 'ghost-class',
                chosenClass: 'chosen-class',
                onEnd: handleDragEnd
            }));
        }

        const listElement = column.querySelector('#' + listId);
        listElement.innerHTML = ''; 

        if (nodes && nodes.length > 0) {
            nodes.forEach(node => {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item menu-item d-flex justify-content-between align-items-center';
                listItem.dataset.id = node.id;
                listItem.dataset.type = node.type;
                listItem.dataset.sortOrder = node.sortOrder;
                listItem.dataset.name = node.name;
                if(node.type === 'ITEM') listItem.dataset.barcode = node.productBarcode;
                if(node.parentCategoryId) listItem.dataset.parentId = node.parentCategoryId;

                listItem.innerHTML = `
                    <span class="flex-grow-1">
                        <i class="bi bi-grip-vertical me-2 handle"></i>
                        ${node.type === 'CATEGORY' ? '<i class="bi bi-folder text-warning me-1"></i>' : '<i class="bi bi-box text-info me-1"></i>'}
                        ${node.name}
                    </span>
                    <button class="btn btn-sm btn-outline-danger remove-item-btn" title="刪除此項目"><i class="bi bi-trash"></i></button>
                `;
                listElement.appendChild(listItem);
            });
        }
        addEventListenersToColumn(column);
    }
    
    function findNodeWithDetails(nodes, id, parent = null) {
        if (!nodes) return null;
        for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i];
            if (node.id === id) {
                return { node, parent, index: i, listRef: nodes };
            }
            if (node.children) {
                const found = findNodeWithDetails(node.children, id, node);
                if (found) return found;
            }
        }
        return null;
    }

    function handleDragEnd(evt) {
        const draggedItemId = evt.item.dataset.id;
        const newParentDomList = evt.to; // The UL element the item was dropped into
        const oldParentDomList = evt.from; // The UL element the item was dragged from
        const newIndex = evt.newIndex;
        const oldIndex = evt.oldIndex; 

        const menuType = menuTypeSelect.value;
        const currentMenuTree = initialMenuState[menuType];

        if (!currentMenuTree) {
            console.error('Menu tree not initialized for type:', menuType);
            showToast('選單資料未載入，無法更新排序。', 'error');
            // Potentially try to re-render from an empty state or fetch if appropriate
            renderMenuColumns([]); // Clears the view
            return;
        }

        // 1. Find and remove the dragged node from its old position in initialMenuState
        const findResult = findNodeWithDetails(currentMenuTree, draggedItemId);

        if (!findResult) {
            console.error('Dragged item not found in initialMenuState:', draggedItemId);
            showToast('拖曳操作錯誤(1)：項目狀態不一致，將嘗試恢復畫面。', 'error');
            renderMenuColumns(currentMenuTree); // Re-render to revert DOM to state
            return;
        }

        const { node: draggedNode, listRef: oldListRef, index: originalIndexInOldList } = findResult;
        
        // Temporarily remove from old list in state to correctly find new parent if moving within same list but different position
        oldListRef.splice(originalIndexInOldList, 1);

        // 2. Determine the new parent node in initialMenuState
        const newParentDomId = newParentDomList.dataset.parentId; // This is the ID of the parent CATEGORY, or "" for root.
        let targetChildrenListInState;
        let newParentNodeInState = null; // Explicitly define for clarity

        if (newParentDomId === "" || newParentDomId === null || newParentDomId === undefined) { // Dropped at root
            targetChildrenListInState = currentMenuTree;
            draggedNode.parentCategoryId = null; 
        } else {
            const newParentResult = findNodeWithDetails(currentMenuTree, newParentDomId);
            if (!newParentResult || newParentResult.node.type !== 'CATEGORY') {
                console.error('New parent category not found or not a category:', newParentDomId);
                // Revert: Add draggedNode back to its original position IN STATE
                oldListRef.splice(originalIndexInOldList, 0, draggedNode);
                showToast('拖曳操作錯誤(2)：目標父分類狀態不一致，將嘗試恢復畫面。', 'error');
                renderMenuColumns(currentMenuTree); // Re-render to revert DOM to state
                return;
            }
            newParentNodeInState = newParentResult.node;
            if (!newParentNodeInState.children) {
                newParentNodeInState.children = [];
            }
            targetChildrenListInState = newParentNodeInState.children;
            draggedNode.parentCategoryId = newParentNodeInState.id;
        }
        
        // 3. Insert the dragged node into its new position in initialMenuState
        targetChildrenListInState.splice(newIndex, 0, draggedNode);

        // 4. Update sortOrder for all items in the list it was dragged FROM (if it's different from where it was dragged TO)
        //    and for all items in the list it was dragged TO.
        //    SortableJS already reordered items in `evt.from` if it changed.
        //    So, we just need to iterate `oldListRef` (if it exists and is different from `targetChildrenListInState`)
        //    and `targetChildrenListInState` to update `sortOrder` based on current index.

        if (oldListRef !== targetChildrenListInState) {
            oldListRef.forEach((child, idx) => {
                child.sortOrder = idx + 1;
            });
        }
        targetChildrenListInState.forEach((child, idx) => {
            child.sortOrder = idx + 1;
        });
        
        // Preserve isNew and other flags on draggedNode. They are part of the object itself.
        // No explicit re-render call here (e.g. renderMenuColumns) to trust SortableJS DOM manipulation.
        // The next save will use the updated initialMenuState.
        console.log('initialMenuState updated after drag for item:', draggedItemId, 'New parent:', draggedNode.parentCategoryId, 'New index:', newIndex);
        showToast('排序已在本機變更，請點擊「確認排序修改」儲存至伺服器。', 'info');
    }

    function addEventListenersToColumn(columnElement) {
        columnElement.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                if(e.target.closest('.remove-item-btn')) return;
                
                this.closest('.sortable-list').querySelectorAll('.menu-item.selected').forEach(sel => sel.classList.remove('selected'));
                this.classList.add('selected');

                const currentLevel = parseInt(this.closest('.sortable-list').dataset.level);
                
                for (let i = currentLevel + 1; i < MAX_LEVELS; i++) {
                    document.getElementById(`menu-level-${i}-col`)?.remove();
                }

                if (this.dataset.type === 'CATEGORY') {
                    const categoryId = this.dataset.id;
                    const categoryName = this.dataset.name;
                    const menuType = menuTypeSelect.value;
                    fetchChildren(menuType, categoryId, currentLevel + 1, categoryName);
                }
            });
        });

        columnElement.querySelectorAll('.remove-item-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const listItem = this.closest('.menu-item');
                itemToDelete.id = listItem.dataset.id;
                itemToDelete.type = listItem.dataset.type;
                deleteMenuItemMessage.textContent = `您確定要刪除「${listItem.dataset.name}」嗎？${itemToDelete.type === 'CATEGORY' ? ' 如果這是分類，其下所有子項目也會被刪除。' : ''}`;
                deleteConfirmModal.show();
            });
        });

        columnElement.querySelectorAll('.add-category-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.getElementById('parentCategoryId').value = this.dataset.parentId || '';
                document.getElementById('currentMenuType').value = menuTypeSelect.value;
                addCategoryForm.reset();
                addCategoryForm.classList.remove('was-validated');
                addCategoryModal.show();
            });
        });
        
        columnElement.querySelectorAll('.add-item-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                currentTargetCategoryIdForItem = this.dataset.categoryId;
                if (!currentTargetCategoryIdForItem) {
                    showToast('請先選擇一個父分類以新增商品。', 'warning');
                    return;
                }
                productSearchInput.value = '';
                productSearchResultsContainer.innerHTML = '';
                noProductResultsMessage.classList.add('d-none');
                addItemModal.show();
            });
        });
    }

    async function fetchChildren(menuType, parentId, nextLevel, parentName) {
        const parentNode = findNodeById(initialMenuState[menuType], parentId);
        if (parentNode && parentNode.children) {
            renderMenuColumns(parentNode.children, nextLevel, parentId, parentName);
        } else {
            renderMenuColumns([], nextLevel, parentId, parentName);
        }
    }

    function findNodeById(nodes, id) {
        if (!nodes) return null;
        for (const node of nodes) {
            if (node.id === id) return node;
            if (node.children) {
                const found = findNodeById(node.children, id);
                if (found) return found;
            }
        }
        return null;
    }
    
    saveCategoryBtn.addEventListener('click', async function() {
        if (!addCategoryForm.checkValidity()) {
            addCategoryForm.classList.add('was-validated');
            return;
        }
        const categoryName = document.getElementById('categoryName').value;
        const parentCategoryId = document.getElementById('parentCategoryId').value || null;
        const menuType = document.getElementById('currentMenuType').value;

        const tempCategoryId = `temp-cat-${Date.now()}`;
        const newCategoryNode = {
            id: tempCategoryId,
            name: categoryName,
            type: 'CATEGORY',
            parentCategoryId: parentCategoryId,
            menuType: menuType,
            children: [],
            sortOrder: 0, 
            isNew: true 
        };

        if (!initialMenuState[menuType]) {
            initialMenuState[menuType] = [];
        }

        if (parentCategoryId) {
            const parentNode = findNodeById(initialMenuState[menuType], parentCategoryId);
            if (parentNode) {
                if (!parentNode.children) parentNode.children = [];
                parentNode.children.push(newCategoryNode);
            } else {
                console.error('Parent node not found in local state for new category:', parentCategoryId);
                initialMenuState[menuType].push(newCategoryNode); // Fallback
            }
        } else {
            initialMenuState[menuType].push(newCategoryNode);
        }

        addCategoryModal.hide();
        showToast(`分類 "${categoryName}" 已於畫面新增，請記得點選「確認排序修改」保存變更。`, 'info');
        
        if (parentCategoryId) {
            const parentDOMNode = document.querySelector(`.menu-item[data-id='${parentCategoryId}']`);
            if (parentDOMNode) {
                const parentLevel = parseInt(parentDOMNode.closest('.sortable-list').dataset.level);
                const parentName = parentDOMNode.dataset.name;

                parentDOMNode.closest('.sortable-list').querySelectorAll('.menu-item.selected').forEach(sel => sel.classList.remove('selected'));
                parentDOMNode.classList.add('selected');

                for (let i = parentLevel + 2; i < MAX_LEVELS; i++) {
                    document.getElementById(`menu-level-${i}-col`)?.remove();
                }
                fetchChildren(menuType, parentCategoryId, parentLevel + 1, parentName);
            }
        } else {
            // New top-level category, re-render level 0 and clear others
            renderMenuColumns(initialMenuState[menuType]); 
            for (let i = 1; i < MAX_LEVELS; i++) {
                document.getElementById(`menu-level-${i}-col`)?.remove();
            }
        }
    });

    productSearchBtn.addEventListener('click', async function() {
        const searchTerm = productSearchInput.value.trim();
        if (!searchTerm) {
            showToast('請輸入商品關鍵字', 'warning');
            return;
        }
        productSearchResultsContainer.innerHTML = '<div class="list-group-item">搜尋中...</div>';
        noProductResultsMessage.classList.add('d-none');
        try {
            // API call to search products from the external product source
            // Use window.fetchAuthenticated to include necessary headers (JWT, X-Company-Context)
            const response = await window.fetchAuthenticated(`${EXTERNAL_PRODUCTS_API_URL}?keyword=${encodeURIComponent(searchTerm)}`);
            
            if (!response.ok) {
                // fetchAuthenticated handles 401. For other errors:
                const errorData = await response.json().catch(() => ({ message: `商品搜尋 API 錯誤 (${response.status})` }));
                showToast(errorData.message, 'error');
                renderProductSearchResults([]); 
                return;
            }
            const apiResponse = await response.json();
            if (apiResponse.code === 200 && apiResponse.data && Array.isArray(apiResponse.data.list)) {
                renderProductSearchResults(apiResponse.data.list);
            } else if (apiResponse.code === 200 && apiResponse.data && Array.isArray(apiResponse.data)) { // Adapting for direct list in data
                 renderProductSearchResults(apiResponse.data);
            } else {
                showToast(apiResponse.message || '搜尋結果格式錯誤或無資料', 'warning');
                renderProductSearchResults([]); 
            }

        } catch (error) {
            console.error('Error searching products:', error);
            // Avoid double-toast if already handled above for non-OK response
            if (!(error.message && error.message.includes('Product search failed with status:'))) {
                 showToast('搜尋商品失敗，請檢查網路或洽詢管理員', 'error');
            }
            renderProductSearchResults([]);
        }
    });

    function renderProductSearchResults(products) {
        productSearchResultsContainer.innerHTML = '';
        if (!products || products.length === 0) {
            noProductResultsMessage.classList.remove('d-none');
            return;
        }
        noProductResultsMessage.classList.add('d-none');
        products.forEach(product => {
            const item = document.createElement('a');
            item.href = '#';
            item.className = 'list-group-item list-group-item-action';
            item.textContent = `${product.productName} (${product.productBarcode})`;
            item.dataset.barcode = product.productBarcode;
            item.dataset.name = product.productName;
            item.addEventListener('click', async function(e) {
                e.preventDefault();
                await addSelectedItemToCategory(this.dataset.barcode, this.dataset.name);
                addItemModal.hide();
            });
            productSearchResultsContainer.appendChild(item);
        });
    }

    async function addSelectedItemToCategory(barcode, name) {
        if (!currentTargetCategoryIdForItem) {
            showToast('未選擇目標分類', 'error');
            return;
        }
        const menuType = menuTypeSelect.value; 

        const tempItemId = `temp-item-${Date.now()}`;
        const newItemNode = {
            id: tempItemId,
            productBarcode: barcode,
            name: name,
            type: 'ITEM',
            parentCategoryId: currentTargetCategoryIdForItem,
            menuType: menuType,
            sortOrder: 0, 
            isNew: true 
        };

        const parentCategoryNode = findNodeById(initialMenuState[menuType], currentTargetCategoryIdForItem);
        if (parentCategoryNode) {
            if (!parentCategoryNode.children) parentCategoryNode.children = [];
            parentCategoryNode.children.push(newItemNode);
            showToast(`商品 "${name}" 已於畫面新增至分類，請記得點選「確認排序修改」保存變更。`, 'info');
            
            const parentDOMNode = document.querySelector(`.menu-item[data-id='${currentTargetCategoryIdForItem}']`);
            if (parentDOMNode) {
                const parentLevel = parseInt(parentDOMNode.closest('.sortable-list').dataset.level);
                const parentName = parentDOMNode.dataset.name;

                parentDOMNode.closest('.sortable-list').querySelectorAll('.menu-item.selected').forEach(sel => sel.classList.remove('selected'));
                parentDOMNode.classList.add('selected');

                for (let i = parentLevel + 2; i < MAX_LEVELS; i++) {
                    document.getElementById(`menu-level-${i}-col`)?.remove();
                }
                fetchChildren(menuType, currentTargetCategoryIdForItem, parentLevel + 1, parentName);
            } else {
                 // Fallback if parent DOM node somehow not found, full re-render from root.
                 // This case should be rare if UI is consistent.
                renderMenuColumns(initialMenuState[menuType]);
            }

        } else {
            console.error('Parent category node not found in local state for new item:', currentTargetCategoryIdForItem);
            showToast('新增商品至分類失敗：找不到目標分類(本機狀態)。', 'error');
        }
    }

    confirmDeleteMenuItemBtn.addEventListener('click', async function() {
        if (!itemToDelete.id || !itemToDelete.type) return;
        
        const menuType = menuTypeSelect.value;
        let deletedLocally = false;

        function removeNodeRecursive(nodes, id) {
            if (!nodes) return false;
            for (let i = 0; i < nodes.length; i++) {
                if (nodes[i].id === id) {
                    nodes.splice(i, 1);
                    return true;
                }
                if (nodes[i].children) {
                    if (removeNodeRecursive(nodes[i].children, id)) {
                        return true;
                    }
                }
            }
            return false;
        }

        if (initialMenuState[menuType] && removeNodeRecursive(initialMenuState[menuType], itemToDelete.id)) {
            deletedLocally = true;
            showToast('項目已於畫面移除，請點選「確認排序修改」以同步至伺服器。', 'info');
            renderMenuColumns(initialMenuState[menuType]); // Re-render from modified local state
            // Clear orphaned columns to the right if any (e.g., if a selected category was deleted)
            const selectedItem = menuConfigContainer.querySelector('.menu-item.selected');
            if (selectedItem) {
                const currentLevel = parseInt(selectedItem.closest('.sortable-list').dataset.level);
                 for (let i = currentLevel + 1; i < MAX_LEVELS; i++) {
                    document.getElementById(`menu-level-${i}-col`)?.remove();
                }
                // If the deleted item was selected, attempt to re-select its parent or clear selection
                const parentOfDeleted = findNodeById(initialMenuState[menuType], selectedItem.dataset.parentId);
                if (parentOfDeleted) {
                    const parentDom = document.querySelector(`.menu-item[data-id='${parentOfDeleted.id}']`);
                    parentDom?.click();
                } else if (currentLevel > 0) {
                    // Try to select item in previous column or clear
                     const prevColList = document.getElementById(`menu-level-${currentLevel -1}`);
                     prevColList?.querySelector('.menu-item')?.click();
                } else {
                    // No selection or clear further columns if needed.
                }
            } else { // If nothing was selected, or selection was lost, ensure no lingering deeper columns
                 for (let i = 0; i < MAX_LEVELS; i++) { // Start from 0 if nothing selected
                    const col = document.getElementById(`menu-level-${i}-col`);
                    if(col && col.querySelector('ul').children.length === 0 && i > 0){ // Heuristic: if a column beyond level 0 is empty
                         col.remove(); // Remove it
                    } else if (col && col.querySelector('ul').children.length > 0){
                        // This column has items, stop removing further columns
                        for (let j = i + 1; j < MAX_LEVELS; j++) {
                           document.getElementById(`menu-level-${j}-col`)?.remove();
                        }
                        break;
                    }
                 }
            }

        } else {
            showToast('在本地狀態中找不到要刪除的項目。', 'warning');
        }
        
        deleteConfirmModal.hide();
        itemToDelete = { id: null, type: null };
        // Original server-side delete call is removed as per user request for client-side first ops.
        // It will be handled by the main save button now by sending the full state.
    });
    
    function collectMenuDataForSave() {
        const menuType = menuTypeSelect.value;
        const collectedNodes = [];

        function buildSavePayloadRecursive(nodesInState, currentParentId) {
            if (!nodesInState || nodesInState.length === 0) {
                return;
            }

            nodesInState.forEach((nodeFromState, index) => {
                const saveNode = {
                    id: (nodeFromState.isNew || (typeof nodeFromState.id === 'string' && nodeFromState.id.startsWith('temp-'))) ? null : nodeFromState.id,
                    clientTempId: (nodeFromState.isNew || (typeof nodeFromState.id === 'string' && nodeFromState.id.startsWith('temp-'))) ? nodeFromState.id : null,
                    type: nodeFromState.type,
                    name: nodeFromState.name, 
                    sortOrder: index + 1, 
                    parentCategoryId: currentParentId,
                    menuType: menuType,
                };
                
                if (nodeFromState.type === 'ITEM') {
                    saveNode.productBarcode = nodeFromState.productBarcode;
                    // Ensure name is present for items if it wasn't universally above
                    if(!saveNode.name) saveNode.name = nodeFromState.name; 
                } else if (nodeFromState.type === 'CATEGORY') {
                    // Ensure name is present for categories if it wasn't universally above
                     if(!saveNode.name) saveNode.name = nodeFromState.name;
                }

                // If parentCategoryId is also a temporary ID for a new parent category, 
                // the backend needs to handle this. If it expects null for new parents too, 
                // this might need further adjustment based on backend capabilities.
                // For now, we send the temp parent ID if it exists.
                if (saveNode.parentCategoryId && typeof saveNode.parentCategoryId === 'string' && saveNode.parentCategoryId.startsWith('temp-')) {
                    // Option 1: Send null if the parent is also new and the backend creates it first based on its position.
                    // saveNode.parentCategoryId = null; // This assumes backend logic can infer parentage for new items under new parents.
                    // Option 2: Send the temp ID and backend resolves it. (Current approach implicitly)
                }

                collectedNodes.push(saveNode);

                if (nodeFromState.type === 'CATEGORY' && nodeFromState.children && nodeFromState.children.length > 0) {
                    buildSavePayloadRecursive(nodeFromState.children, nodeFromState.id); 
                }
            });
        }

        if (initialMenuState[menuType]) {
            buildSavePayloadRecursive(initialMenuState[menuType], null); 
        }
        
        return collectedNodes;
    }

    saveMenuOrderBtn.addEventListener('click', async function() {
        const orderedMenuData = collectMenuDataForSave();
        const menuType = menuTypeSelect.value;
        console.log('Saving menu order:', JSON.stringify(orderedMenuData, null, 2));
        if (orderedMenuData.length === 0 && !confirm('目前選單無任何項目，確定要以此空狀態儲存嗎？')){
            return;
        }
        try {
            const response = await fetch(`${API_BASE_URL}/order/${menuType}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${localStorage.getItem('jwtToken')}` },
                body: JSON.stringify(orderedMenuData)
            });
            const apiResponse = await response.json();
            if (response.ok && apiResponse.code === 200) {
                showToast('選單排序已成功儲存', 'success');
                fetchMenuTree(menuType); 
            } else {
                showToast(apiResponse.message || '儲存排序失敗', 'error');
            }
        } catch (error) {
            console.error('Error saving menu order:', error);
            showToast('儲存選單排序時發生錯誤', 'error');
        }
    });

    cancelMenuOrderBtn.addEventListener('click', function() {
        const currentType = menuTypeSelect.value;
        if (initialMenuState[currentType]) {
            renderMenuColumns(initialMenuState[currentType]);
            showToast('排序修改已取消', 'info');
        } else {
            fetchMenuTree(currentType); 
        }
    });

    menuTypeSelect.addEventListener('change', function() {
        fetchMenuTree(this.value);
    });

    // Initial Load
    await loadSharedHTML();
    fetchMenuTree(menuTypeSelect.value);
});

function showLoading() { /* Placeholder for actual loading indicator */ console.log("Loading..."); }
function hideLoading() { /* Placeholder for actual loading indicator */ console.log("Loading complete."); }
// showToast function is assumed to be in main.js 