// Fallback definitions for functions from main.js
if (typeof window.fetchWithAuth === 'undefined') {
    console.warn('fetchWithAuth not defined, creating a local fallback.');
    window.fetchWithAuth = function(url, options = {}) {
        const token = localStorage.getItem('jwtToken');
        if (!token) {
            console.error("JWT token not found. Redirecting to login.");
            window.location.href = 'login.html';
            return Promise.reject(new Error("JWT token not found."));
        }
        const headers = { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json', ...options.headers };
        return fetch(url, { ...options, headers });
    };
}
if (typeof window.showToast === 'undefined') {
    console.warn('showToast not defined, creating a local fallback.');
    window.showToast = function(message, type = 'info') { alert(`[${type}] ${message}`); };
}
if (typeof window.loadSharedHTML === 'undefined') {
    console.warn('loadSharedHTML not defined, creating a local fallback.');
    window.loadSharedHTML = function() { console.log("Sidebar/Header loading handled by main.js"); };
}

document.addEventListener('DOMContentLoaded', function () {
    const API_BASE_URL = '/api/v1/promotions';
    const EXTERNAL_PRODUCTS_API_URL = '/api/v1/external-products'; 

    const form = document.getElementById('promotion-form');
    const promotionIdField = document.getElementById('promotionId');
    const promotionNameField = document.getElementById('promotionName');
    const startTimeField = document.getElementById('startTime');
    const endTimeField = document.getElementById('endTime');
    const isActiveField = document.getElementById('isActive');
    const channelsContainer = document.getElementById('channels-container');
    const addPromotionProductBtn = document.getElementById('addPromotionProductBtn');
    const promotionProductsTableBody = document.getElementById('promotionProductsTableBody');
    const noPromotionProductsMessage = document.getElementById('noPromotionProductsMessage');
    
    const addProductBtn = document.getElementById('add-product-btn');
    const productSearchModalEl = document.getElementById('productSearchModal');
    const productSearchKeywordInput = document.getElementById('product-search-keyword');
    const productSearchExecuteBtn = document.getElementById('exec-product-search-btn');
    const productSearchResultsContainer = document.getElementById('product-search-results');
    const noModalProductResultsMessage = document.getElementById('noModalProductResultsMessage');
    
    const formErrorMessage = document.getElementById('promotionFormErrorMessage');
    const formModeBreadcrumb = document.getElementById('form-mode-breadcrumb');
    const formTitle = document.getElementById('form-title');

    const urlParams = new URLSearchParams(window.location.search);
    const currentPromotionId = urlParams.get('id');
    const mode = urlParams.get('mode') || (currentPromotionId ? 'edit' : 'add');
    
    let promotionProducts = []; // Array to hold product data
    let backendAvailableChannelTypes = []; // To store fetched channel types. Expected format: [{code, description, allowTargetSelection, targetPlaceholder}, ...]
    let availableStores = []; // To store fetched stores for dropdowns

    function showFormError(message) {
        if (formErrorMessage) {
            formErrorMessage.textContent = message;
            formErrorMessage.classList.remove('d-none');
            formErrorMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } else {
            console.warn("Promotion form error message div not found, but tried to show:", message);
            if(window.showToast) window.showToast(message, 'error');
        }
    }

    function clearFormError() {
        if (formErrorMessage) {
            formErrorMessage.textContent = '';
            formErrorMessage.classList.add('d-none');
        }
    }

    async function fetchStores() {
        try {
            const response = await window.fetchWithAuth('/api/v1/stores/selectable');
            if (!response.ok) throw new Error('無法獲取門市列表');
            const apiResult = await response.json();
            if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
                availableStores = apiResult.data;
            } else {
                console.error('獲取門市列表失敗:', apiResult.message);
                showToast('獲取門市列表失敗', 'error');
            }
        } catch (error) {
            console.error('獲取門市列表時發生錯誤:', error);
            showToast('載入門市列表時發生錯誤', 'error');
        }
    }

    async function fetchChannelTypes() {
        try {
            const response = await window.fetchAuthenticated('/api/v1/promotions/channel-types');
            if (!response.ok) {
                throw new Error(`Failed to fetch channel types. Status: ${response.status}`);
            }
            const apiResult = await response.json();
            if (apiResult.code === 200 && Array.isArray(apiResult.data)) {
                // Assuming backend now provides all necessary fields: code, description, allowTargetSelection, targetPlaceholder
                backendAvailableChannelTypes = apiResult.data.map(type => ({
                    code: type.code,
                    description: type.description,
                    allowTargetSelection: type.allowTargetSelection || false, // Default to false if not provided
                    targetPlaceholder: type.targetPlaceholder || '特定目標ID (若適用)' // Default placeholder
                }));
                console.log("Fetched and processed channel types:", backendAvailableChannelTypes);
            } else {
                console.error('Error fetching channel types:', apiResult.message || 'Invalid data format');
                showToast(`無法載入管道類型: ${apiResult.message || '資料格式錯誤'}`, 'error');
                backendAvailableChannelTypes = []; // Clear or handle as critical error
            }
        } catch (error) {
            console.error('Error in fetchChannelTypes:', error);
            showToast(`載入管道類型時發生嚴重錯誤: ${error.message}`, 'error');
            backendAvailableChannelTypes = []; // Clear or handle as critical error
            // Consider disabling the channel selection part of the form
            if(channelsContainer) channelsContainer.innerHTML = '<p class="text-danger">無法載入管道選項，請聯繫管理員。</p>';
        }
    }

    function renderChannels(selectedChannels = []) {
        channelsContainer.innerHTML = '';
        if (backendAvailableChannelTypes.length === 0) {
            channelsContainer.innerHTML = '<p class="text-danger">無法呈現管道選項，請稍後再試。</p>';
            return;
        }
        backendAvailableChannelTypes.forEach(channelType => {
            const isSelected = selectedChannels.some(sc => sc.channelType === channelType.code);
            const selectedChannelData = selectedChannels.find(sc => sc.channelType === channelType.code);

            const div = document.createElement('div');
            div.className = 'form-check mb-2 channel-type-group';
            div.innerHTML = `
                <input class="form-check-input channel-type-checkbox" type="checkbox" value="${channelType.code}" id="channel-${channelType.code}" ${isSelected ? 'checked' : ''}>
                <label class="form-check-label fw-bold" for="channel-${channelType.code}">${channelType.description}</label>
            `;

            const targetSelectionContainer = document.createElement('div');
            targetSelectionContainer.className = `channel-target-selection mt-2 ps-4 border-start`;
            if (!isSelected || channelType.code !== 'DIRECT_STORE') {
                 targetSelectionContainer.classList.add('d-none');
            }

            if (channelType.code === 'DIRECT_STORE') {
                const buttonGroup = document.createElement('div');
                buttonGroup.className = 'mb-2';

                const addButton = document.createElement('button');
                addButton.type = 'button';
                addButton.className = 'btn btn-sm btn-outline-success me-2';
                addButton.innerHTML = '<i class="bi bi-plus-circle"></i> 新增門市';
                addButton.onclick = () => addStoreSelectRow(targetSelectionContainer, null);
                buttonGroup.appendChild(addButton);

                const selectAllButton = document.createElement('button');
                selectAllButton.type = 'button';
                selectAllButton.className = 'btn btn-sm btn-outline-primary me-2';
                selectAllButton.innerHTML = '全選';
                selectAllButton.onclick = () => {
                    targetSelectionContainer.querySelectorAll('.store-select-row').forEach(row => row.remove());
                    availableStores.forEach(store => addStoreSelectRow(targetSelectionContainer, store.storeId));
                };
                buttonGroup.appendChild(selectAllButton);

                const deselectAllButton = document.createElement('button');
                deselectAllButton.type = 'button';
                deselectAllButton.className = 'btn btn-sm btn-outline-warning';
                deselectAllButton.innerHTML = '全不選';
                deselectAllButton.onclick = () => {
                    targetSelectionContainer.querySelectorAll('.store-select-row').forEach(row => row.remove());
                };
                buttonGroup.appendChild(deselectAllButton);

                targetSelectionContainer.appendChild(buttonGroup);
                
                if (isSelected) {
                    const storeChannels = selectedChannels.filter(sc => sc.channelType === 'DIRECT_STORE');
                    if (storeChannels.length > 0) {
                        storeChannels.forEach(sc => addStoreSelectRow(targetSelectionContainer, sc.channelTargetId));
                    } else {
                         addStoreSelectRow(targetSelectionContainer, null); // Add one empty row if channel is selected but no specific store
                    }
                }
            } else {
                 targetSelectionContainer.innerHTML = `
                    <input type="text" class="form-control form-control-sm channel-target-id" 
                           placeholder="${channelType.targetPlaceholder || '特定目標ID (若適用)'}" 
                           value="${selectedChannelData && selectedChannelData.channelTargetId ? selectedChannelData.channelTargetId : ''}" 
                           ${channelType.allowTargetSelection ? '' : 'disabled'}>
                `;
            }
            
            div.appendChild(targetSelectionContainer);
            channelsContainer.appendChild(div);

            const checkbox = div.querySelector('.channel-type-checkbox');
            checkbox.addEventListener('change', function() {
                targetSelectionContainer.classList.toggle('d-none', !this.checked);
                if (this.checked && channelType.code === 'DIRECT_STORE' && targetSelectionContainer.querySelectorAll('.store-select-row').length === 0) {
                    addStoreSelectRow(targetSelectionContainer, null);
                }
            });
        });
    }

    function addStoreSelectRow(container, selectedStoreId) {
        const rowId = `store-select-${Date.now()}`;
        const rowDiv = document.createElement('div');
        rowDiv.className = 'd-flex align-items-center mb-2 store-select-row';
        rowDiv.id = rowId;

        const select = document.createElement('select');
        select.className = 'form-select form-select-sm channel-target-id';
        select.innerHTML = '<option value="">請選擇門市</option>';
        availableStores.forEach(store => {
            select.innerHTML += `<option value="${store.storeId}" ${selectedStoreId === store.storeId ? 'selected' : ''}>${store.storeName}</option>`;
        });
        
        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'btn btn-sm btn-outline-danger ms-2';
        removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
        removeBtn.onclick = () => {
            rowDiv.remove();
             // If this was the last store select row, hide the whole container
            if (container.querySelectorAll('.store-select-row').length === 0) {
                const checkbox = container.closest('.channel-type-group').querySelector('.channel-type-checkbox');
                if (checkbox && !checkbox.checked) {
                    container.classList.add('d-none');
                } else if (checkbox && checkbox.checked) {
                    // if checkbox is still checked, add back one empty row.
                    addStoreSelectRow(container, null);
                }
            }
        };

        rowDiv.appendChild(select);
        rowDiv.appendChild(removeBtn);
        container.appendChild(rowDiv);
    }

    async function initializeForm() {
        await loadSharedHTML(); 
        await Promise.all([fetchChannelTypes(), fetchStores()]);
        renderChannels([]); // Initial render with no selections, using fetched types
        if (mode === 'edit' && currentPromotionId) {
            formTitle.textContent = '編輯促銷活動';
            formModeBreadcrumb.textContent = '編輯促銷活動';
            loadPromotionData(currentPromotionId);
        } else {
            formTitle.textContent = '新增促銷活動';
            formModeBreadcrumb.textContent = '新增促銷活動';
            updatePromotionProductsTable();
        }
    }

    async function loadPromotionData(id) {
        try {
            const response = await window.fetchAuthenticated(`${API_BASE_URL}/${id}`);
            if (!response.ok) throw new Error('Failed to load promotion data.');
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                const promo = apiResult.data;
                promotionIdField.value = promo.promotionId;
                promotionNameField.value = promo.promotionName;
                startTimeField.value = promo.startTime ? promo.startTime.slice(0, 16) : '';
                endTimeField.value = promo.endTime ? promo.endTime.slice(0, 16) : '';
                isActiveField.checked = promo.isActive === undefined ? true : promo.isActive;
                renderChannels(promo.channels || []);
                promotionProducts = promo.products || [];
                updatePromotionProductsTable();
            } else {
                showToast(apiResult.message || '無法載入促銷活動資料', 'error');
            }
        } catch (error) {
            console.error('Error loading promotion data:', error);
            showToast('載入促銷活動資料失敗', 'error');
        }
    }

    function updatePromotionProductsTable() {
        promotionProductsTableBody.innerHTML = '';
        if (promotionProducts.length === 0) {
            noPromotionProductsMessage.classList.remove('d-none');
            return;
        }
        noPromotionProductsMessage.classList.add('d-none');
        promotionProducts.forEach((product, index) => {
            const row = promotionProductsTableBody.insertRow();
            row.insertCell().textContent = product.productBarcode;
            row.insertCell().textContent = product.productName || 'N/A';
            row.insertCell().textContent = product.originalPrice !== null && product.originalPrice !== undefined ? product.originalPrice.toLocaleString() : '-';
            
            const priceInputCell = row.insertCell();
            const priceInput = document.createElement('input');
            priceInput.type = 'number';
            priceInput.className = 'form-control form-control-sm promo-price-input';
            priceInput.value = product.promoPrice;
            priceInput.min = "0";
            priceInput.step = "0.01";
            priceInput.required = true;
            priceInput.dataset.barcode = product.productBarcode;
            priceInput.addEventListener('change', (e) => {
                const foundProduct = promotionProducts.find(p => p.productBarcode === e.target.dataset.barcode);
                if(foundProduct) foundProduct.promoPrice = parseFloat(e.target.value) || 0;
            });
            priceInputCell.appendChild(priceInput);

            const deleteBtnCell = row.insertCell();
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-sm btn-outline-danger';
            deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
            deleteBtn.onclick = () => {
                promotionProducts.splice(index, 1);
                updatePromotionProductsTable();
            };
            deleteBtnCell.appendChild(deleteBtn);
        });
    }

    // Event listener for opening the product search modal
    addProductBtn.addEventListener('click', function() {
        // Clear previous search results and keyword
        productSearchKeywordInput.value = '';
        productSearchResultsContainer.innerHTML = '';
        // Show the modal
        const productSearchModal = new bootstrap.Modal(productSearchModalEl);
        productSearchModal.show();
    });

    // Event listener for executing search within the modal
    if (productSearchExecuteBtn) {
        productSearchExecuteBtn.addEventListener('click', searchProducts);
    }

    async function searchProducts() {
        const keyword = productSearchKeywordInput.value.trim();
        if (!keyword) {
            showToast('請輸入商品關鍵字', 'warning');
            return;
        }

        const resultsContainer = productSearchResultsContainer;
        resultsContainer.innerHTML = '<div class="list-group-item">搜尋中...</div>';

        try {
            const response = await fetchWithAuth(`/api/v1/product-settings/search?keyword=${encodeURIComponent(keyword)}`);
            if (!response.ok) throw new Error('搜尋商品失敗');
            
            const apiResponse = await response.json();
            const products = apiResponse.data;

            resultsContainer.innerHTML = '';
            if (products && products.length > 0) {
                products.forEach(product => {
                    const price = product.salePrice != null ? product.salePrice : (product.listPrice || 0);
                    const resultHtml = `
                        <a href="#" class="list-group-item list-group-item-action select-product-btn" 
                           data-product-barcode="${product.productBarcode}" 
                           data-product-name="${product.productName || ''}"
                           data-list-price="${price}">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${product.productName || '未命名商品'}</h6>
                                <small>原價: NT$ ${price.toLocaleString()}</small>
                            </div>
                            <p class="mb-1">${product.productBarcode}</p>
                        </a>
                    `;
                    resultsContainer.insertAdjacentHTML('beforeend', resultHtml);
                });
            } else {
                resultsContainer.innerHTML = '<div class="list-group-item">找不到符合條件的商品。</div>';
            }
        } catch (error) {
            console.error('商品搜尋錯誤:', error);
            resultsContainer.innerHTML = `<div class="list-group-item text-danger">搜尋時發生錯誤: ${error.message}</div>`;
        }
    }

    // Event listener for selecting a product from the modal
    productSearchResultsContainer.addEventListener('click', function(event) {
        event.preventDefault();
        const target = event.target.closest('.select-product-btn');
        if (!target) return;

        const product = {
            barcode: target.dataset.productBarcode,
            name: target.dataset.productName,
            listPrice: parseFloat(target.dataset.listPrice)
        };
        
        addProductRow(product);
        const modal = bootstrap.Modal.getInstance(productSearchModalEl);
        modal.hide();
    });

    function addProductRow(product) {
        if (!product || !product.barcode) return;

        const existingProduct = promotionProducts.find(p => p.productBarcode === product.barcode);
        if (existingProduct) {
            showToast('此商品已在促銷列表中。', 'warning');
            return;
        }

        promotionProducts.push({
            productBarcode: product.barcode,
            productName: product.name,
            originalPrice: product.listPrice,
            promoPrice: product.listPrice // Default promo price to original
        });

        updatePromotionProductsTable();
        showToast(`${product.name} 已加入促銷列表。`, 'success');
    }

    // Event listener for removing a product
    document.getElementById('promotion-products-container').addEventListener('click', function(event) {
        if (event.target.classList.contains('remove-product-btn')) {
            event.target.closest('.promotion-product-row').remove();
        }
    });

    if (form) {
        form.addEventListener('submit', async function(event) {
            event.preventDefault();
            clearFormError();

            // --- Validation ---
            if (!promotionNameField.value.trim() || !startTimeField.value || !endTimeField.value) {
                showFormError("請填寫所有標記 * 的必填欄位。");
                return;
            }

            const selectedChannels = [];
            channelsContainer.querySelectorAll('.channel-type-checkbox:checked').forEach(cb => {
                const channelType = cb.value;
                const group = cb.closest('.channel-type-group');
                
                if (channelType === 'DIRECT_STORE') {
                    group.querySelectorAll('.channel-target-id').forEach(select => {
                        if (select.value) {
                            selectedChannels.push({ channelType: channelType, channelTargetId: select.value });
                        }
                    });
                } else {
                    const targetIdInput = group.querySelector('.channel-target-id');
                    const channel = { channelType: channelType, channelTargetId: null };
                    if (targetIdInput && !targetIdInput.disabled && targetIdInput.value.trim()) {
                        channel.channelTargetId = targetIdInput.value.trim();
                    }
                    selectedChannels.push(channel);
                }
            });

            if (selectedChannels.length === 0) {
                showFormError('必須至少選擇一個適用管道。');
                return;
            }

            if (promotionProducts.length === 0) {
                showFormError('必須至少加入一個促銷商品。');
                return;
            }

            // Validate promo prices
            for (const prod of promotionProducts) {
                if (prod.promoPrice === null || prod.promoPrice === undefined || prod.promoPrice < 0) {
                    showFormError(`商品 ${prod.productName} 的活動價無效。`);
                    return;
                }
            }
            
            // --- Build Payload ---
            const payload = {
                promotionName: promotionNameField.value,
                startTime: startTimeField.value ? new Date(startTimeField.value).toISOString() : null,
                endTime: endTimeField.value ? new Date(endTimeField.value).toISOString() : null,
                isActive: isActiveField.checked,
                channels: selectedChannels,
                products: promotionProducts
            };
            if(currentPromotionId) payload.promotionId = currentPromotionId;

            if (payload.endTime && payload.startTime && payload.endTime < payload.startTime) {
                showFormError("活動結束時間不能早於開始時間。");
                return;
            }

            // --- API Call ---
            const confirmMessage = mode === 'edit' ? "確定要儲存對此促銷活動的變更嗎？" : "確定要新增此促銷活動嗎？";
            if (!window.confirm(confirmMessage)) return;

            const method = currentPromotionId ? 'PUT' : 'POST';
            const url = currentPromotionId ? `${API_BASE_URL}/${currentPromotionId}` : API_BASE_URL;

            try {
                const response = await window.fetchWithAuth(url, { method: method, body: JSON.stringify(payload) });
                const result = await response.json();
                if (response.ok && (result.code === (method === 'POST' ? 201 : 200))) {
                    showToast(`促銷活動已成功${method === 'POST' ? '新增' : '更新'}！`, 'success');
                    window.location.href = 'promotions.html';
                } else {
                    showFormError(result.message || '儲存失敗');
                }
            } catch (error) {
                console.error('Error saving promotion:', error);
                showFormError('儲存促銷活動時發生錯誤: ' + error.message);
            }
        });
    }

    initializeForm();
}); 