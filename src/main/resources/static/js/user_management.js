// js/user_management.js
document.addEventListener('DOMContentLoaded', function () {
    console.log("User management JS loaded.");
    const usersTableBody = document.getElementById('users-table-body');
    const paginationControls = document.getElementById('users-pagination-controls');
    const filterForm = document.getElementById('user-filter-form');
    const noUsersMessage = document.getElementById('no-users-message');

    const filterEmployeeId = document.getElementById('filterEmployeeId');
    const filterUserName = document.getElementById('filterUserName');
    const filterIsActive = document.getElementById('filterIsActive');
    const clearUserFiltersBtn = document.getElementById('clearUserFiltersBtn');

    const accountTypeFilterSelect = document.createElement('select');

    let currentPage = 0; // Internally use 0-indexed page for API calls
    const pageSize = 10;
    const API_URL = '/api/v1/users';
    const ACCOUNT_TYPES_API_URL = '/api/v1/enums/user-account-types';

    async function fetchUsers(page = 0) { 
        currentPage = page;
        
        let queryParamsList = [
            `page=${page}`,
            `size=${pageSize}`,
            `sort=employeeId,asc` 
        ];

        const employeeIdValue = filterEmployeeId ? filterEmployeeId.value.trim() : '';
        const userNameValue = filterUserName ? filterUserName.value.trim() : '';
        const isActiveValue = filterIsActive ? filterIsActive.value : '';
        const accountTypeValue = accountTypeFilterSelect ? accountTypeFilterSelect.value : '';

        if (employeeIdValue) queryParamsList.push(`employeeId=${encodeURIComponent(employeeIdValue)}`);
        if (userNameValue) queryParamsList.push(`userName=${encodeURIComponent(userNameValue)}`);
        if (isActiveValue !== "") queryParamsList.push(`isActive=${isActiveValue}`);
        if (accountTypeValue) queryParamsList.push(`accountType=${accountTypeValue}`);
        
        const queryParamsString = queryParamsList.join('&');
        const fullUrl = `${API_URL}?${queryParamsString}`; // Corrected: added '?'

        console.log("USER_MANAGEMENT.JS: Fetching users with URL:", fullUrl);

        if(usersTableBody) usersTableBody.innerHTML = `<tr><td colspan="7" class="text-center">載入中...</td></tr>`;

        try {
            const response = await window.fetchAuthenticated(fullUrl);
            if (!response.ok) {
                if (response.status === 401 || response.status === 403) {
                     console.error(`USER_MANAGEMENT.JS: Auth error fetching users (${response.status})`);
                     if(window.showToast) window.showToast(`權限不足或登入超時 (${response.status})`, 'error');
                     // main.js should handle redirect for 401
                     return; 
                }
                const errData = await response.json().catch(() => ({ message: `HTTP error! status: ${response.status}` }));
                throw new Error(errData.message || `HTTP error! status: ${response.status}`);
            }
            const apiResult = await response.json();

            if (!usersTableBody || !noUsersMessage || !paginationControls) {
                console.error("USER_MANAGEMENT.JS: One or more critical UI elements are missing for user management.");
                return;
            }
            usersTableBody.innerHTML = ''; 

            if (apiResult.code === 200 && apiResult.data && apiResult.data.list && Array.isArray(apiResult.data.list)) {
                if (apiResult.data.list.length > 0) {
                    if(noUsersMessage) noUsersMessage.classList.add('d-none');
                    apiResult.data.list.forEach((user, index) => {
                        const row = usersTableBody.insertRow();
                        row.insertCell().textContent = (apiResult.data.page.page * apiResult.data.page.pageSize) + index + 1;
                        row.insertCell().textContent = user.employeeId;
                        row.insertCell().textContent = user.userName;
                        row.insertCell().textContent = user.erpCompanyDivisionDescription || 'N/A';
                        row.insertCell().textContent = user.accountTypeDescription || 'N/A';
                        
                        const statusCell = row.insertCell();
                        const statusBadge = document.createElement('span');
                        statusBadge.classList.add('badge');
                        if (user.isActive) {
                            statusBadge.classList.add('bg-success');
                            statusBadge.textContent = '啟用';
                        } else {
                            statusBadge.classList.add('bg-danger');
                            statusBadge.textContent = '停用';
                        }
                        statusCell.appendChild(statusBadge);
                        row.insertCell().textContent = user.updateTime ? new Date(user.updateTime).toLocaleString() : (user.createTime ? new Date(user.createTime).toLocaleString() : '-');

                        const actionsCell = row.insertCell();
                        actionsCell.classList.add('text-nowrap');
                        actionsCell.innerHTML = `
                            <a href="user_form.html?id=${user.userAccountId}&mode=edit" class="btn btn-sm btn-outline-primary me-1" title="編輯">
                                <i class="bi bi-pencil-fill"></i> 編輯
                            </a>
                            <button class="btn btn-sm btn-outline-danger delete-user-btn" data-user-id="${user.userAccountId}" data-user-name="${user.userName} (${user.employeeId})" title="刪除">
                                <i class="bi bi-trash-fill"></i> 刪除
                            </button>
                        `;
                    });
                    renderUserPagination(apiResult.data.page.total, apiResult.data.page.page, apiResult.data.page.pageSize);
                    addDeleteEventListenersUser();
                } else {
                     if(noUsersMessage) noUsersMessage.classList.remove('d-none'); 
                     usersTableBody.innerHTML = '<tr><td colspan="7" class="text-center">查無使用者資料</td></tr>';
                     if(paginationControls) paginationControls.innerHTML = '';
                }
            } else {
                if(noUsersMessage) noUsersMessage.classList.remove('d-none');
                usersTableBody.innerHTML = '<tr><td colspan="7" class="text-center">查無使用者資料或回應錯誤</td></tr>';
                if(paginationControls) paginationControls.innerHTML = '';
                console.error("USER_MANAGEMENT.JS: Error or unexpected format in fetchUsers response:", apiResult.message || apiResult);
            }
        } catch (error) {
            console.error('USER_MANAGEMENT.JS: Error fetching users:', error);
            if(usersTableBody) usersTableBody.innerHTML = `<tr><td colspan="7" class="text-center text-danger">無法載入使用者資料: ${error.message}</td></tr>`;
            if(noUsersMessage && !noUsersMessage.classList.contains('d-none')) noUsersMessage.classList.remove('d-none');
            if(paginationControls) paginationControls.innerHTML = '';
        }
    }

    function renderUserPagination(totalItems, currentPageBackend, itemsPerPage) { 
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        const uiCurrentPage = currentPageBackend + 1; // Display 1-indexed page
        if (!paginationControls) return;
        paginationControls.innerHTML = '';

        if (totalPages <= 1) return;

        const ul = document.createElement('ul');
        ul.className = 'pagination justify-content-center';

        // Previous and First
        ul.appendChild(createPageItem(0, '«', currentPageBackend === 0)); // First page is 0 for API
        ul.appendChild(createPageItem(currentPageBackend - 1, '‹', currentPageBackend === 0));
        
        let startPage = Math.max(0, currentPageBackend - 2);
        let endPage = Math.min(totalPages - 1, currentPageBackend + 2);

        if (uiCurrentPage <= 3) { // If on first few pages, show more at the end
            endPage = Math.min(totalPages - 1, 4); // Show up to 5 pages
        }
        if (uiCurrentPage > totalPages - 3) { // If on last few pages, show more at the beginning
            startPage = Math.max(0, totalPages - 5);
        }

        if (startPage > 0) {
            ul.appendChild(createPageItem(0, '1'));
            if (startPage > 1) {
                 const ellipsis = document.createElement('li');
                 ellipsis.className = 'page-item disabled';
                 ellipsis.innerHTML = '<a class="page-link" href="#">...</a>';
                 ul.appendChild(ellipsis);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            ul.appendChild(createPageItem(i, i + 1, false, i === currentPageBackend));
        }

        if (endPage < totalPages - 1) {
            if (endPage < totalPages - 2) {
                const ellipsis = document.createElement('li');
                ellipsis.className = 'page-item disabled';
                ellipsis.innerHTML = '<a class="page-link" href="#">...</a>';
                ul.appendChild(ellipsis);
            }
            ul.appendChild(createPageItem(totalPages - 1, totalPages));
        }
        
        // Next and Last
        ul.appendChild(createPageItem(currentPageBackend + 1, '›', currentPageBackend >= totalPages - 1));
        ul.appendChild(createPageItem(totalPages - 1, '»', currentPageBackend >= totalPages - 1)); // Last page is totalPages - 1 for API
        
        paginationControls.appendChild(ul);
    }

    function createPageItem(pageToFetch, text, isDisabled = false, isActive = false) { 
        const li = document.createElement('li');
        li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;
        const link = document.createElement('a');
        link.className = 'page-link';
        link.href = '#';
        link.textContent = String(text);
        if (!isDisabled) { 
            link.addEventListener('click', (e) => {
                e.preventDefault();
                fetchUsers(pageToFetch); // Pass 0-indexed page for API
            });
        }
        li.appendChild(link);
        return li;
    }

    function addDeleteEventListenersUser() {
        document.querySelectorAll('.delete-user-btn').forEach(button => {
            // Clone and replace to ensure old listeners are removed if this function is called multiple times
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            newButton.addEventListener('click', async function () {
                const userId = this.dataset.userId;
                const userNameStr = this.dataset.userName;
                if (confirm(`確定要刪除使用者「${userNameStr}」嗎？此操作無法復原。`)) {
                    try {
                        const response = await window.fetchAuthenticated(`/api/v1/users/${userId}`, { method: 'DELETE' });
                        const result = await response.json(); 
                        if (response.ok && result.code === 200) {
                            if(window.showToast) window.showToast(result.message || '使用者已刪除', 'success');
                            else alert(result.message || '使用者已刪除');
                            fetchUsers(currentPage); 
                        } else {
                            if(window.showToast) window.showToast(`刪除失敗: ${result.message || response.statusText}`, 'error');
                            else alert(`刪除失敗: ${result.message || response.statusText}`);
                        }
                    } catch (error) {
                        console.error('Error deleting user:', error);
                         if(window.showToast) window.showToast('刪除使用者時發生錯誤。', 'error');
                        else alert('刪除使用者時發生錯誤。');
                    }
                }
            });
        });
    }

    async function populateAccountTypeFilter() {
        if (!accountTypeFilterSelect) return;
        accountTypeFilterSelect.id = 'filterAccountType';
        accountTypeFilterSelect.className = 'form-select form-select-sm';
        accountTypeFilterSelect.innerHTML = '<option value="">所有類型</option>';

        try {
            const response = await window.fetchAuthenticated(ACCOUNT_TYPES_API_URL);
            if(!response.ok) throw new Error('Failed to fetch account types');
            const apiResult = await response.json();
            if (apiResult.code === 200 && apiResult.data) {
                apiResult.data.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id; // Use the Short code for filtering
                    option.textContent = type.description;
                    accountTypeFilterSelect.appendChild(option);
                });
            } else {
                console.error("Failed to populate account type filter", apiResult.message);
            }
        } catch (error) {
            console.error("Error fetching account types for filter:", error);
        }
        
        const filterRow = filterForm ? filterForm.querySelector('.row') : null;
        if (filterRow) {
            const newCol = document.createElement('div');
            // Try to match styling of other filter columns, e.g., col-md-2 or col-md-3
            newCol.className = 'col-md-2'; 
            const label = document.createElement('label');
            label.htmlFor = 'filterAccountType';
            label.className = 'form-label';
            label.textContent = '帳號類型';
            newCol.appendChild(label);
            newCol.appendChild(accountTypeFilterSelect);
            // Append to the filter row. Adjust if a specific order or container is needed.
            const lastButtonContainer = filterForm.querySelector('.row > div:last-child');
            if (lastButtonContainer && lastButtonContainer.querySelector('button')) {
                 filterRow.insertBefore(newCol, lastButtonContainer);
            } else {
                 filterRow.appendChild(newCol);
            }
        } else if (filterForm) {
            // Fallback: append to form if specific row not found or structure is different
            const newCol = document.createElement('div');
            newCol.className = 'col-md-2 mb-3'; 
            const label = document.createElement('label');
            label.htmlFor = 'filterAccountType';
            label.className = 'form-label';
            label.textContent = '帳號類型';
            newCol.appendChild(label);
            newCol.appendChild(accountTypeFilterSelect);
            filterForm.appendChild(newCol);
        }
    }

    if (filterForm) {
        filterForm.addEventListener('submit', function (event) {
            event.preventDefault();
            fetchUsers(0); // Reset to first page on new filter submission
        });
    }

    if (clearUserFiltersBtn) {
        clearUserFiltersBtn.addEventListener('click', function() {
            if(filterForm) filterForm.reset();
            if(filterEmployeeId) filterEmployeeId.value = '';
            if(filterUserName) filterUserName.value = '';
            if(filterIsActive) filterIsActive.value = '';
            if(accountTypeFilterSelect) accountTypeFilterSelect.value = '';
            fetchUsers(0); // Reset to first page
        });
    }

    async function initPage() {
        console.log("USER_MANAGEMENT.JS: initPage() called");
        await populateAccountTypeFilter();
        fetchUsers(0); // Initial fetch, 0-indexed for API
    }

    initPage();
}); 