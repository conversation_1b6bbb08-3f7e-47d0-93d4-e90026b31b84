document.addEventListener('DOMContentLoaded', function () {
    // --- DOM Elements ---
    const infoCardContainer = document.getElementById('info-card-container');
    const paymentForm = document.getElementById('payment-form');
    const costSummaryList = document.getElementById('cost-summary-list');
    const paymentMethodsContainer = document.getElementById('payment-methods-container');
    const backBtn = document.getElementById('back-btn');

    // --- State ---
    const urlParams = new URLSearchParams(window.location.search);
    const dispatchRepairId = urlParams.get('id');
    let originalOrderData = null;

    // --- Initialization ---
    async function initializePage() {
        if (!dispatchRepairId) { /* ... */ return; }
        
        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}`);
            if (!response.ok) throw new Error('無法獲取派工單資料');
            originalOrderData = await response.json();
            
            window.renderDispatchOrderHeader(originalOrderData, infoCardContainer);
            renderPaymentMethods();
            document.getElementById('invoice-status-display').textContent = (originalOrderData.originalOrderTaxAmount > 0) ? '是' : '否';
            calculateAndDisplayTotals();
            setupEventListeners();
        } catch (error) { /* ... */ }
    }

    // --- Rendering & Calculation ---
    function calculateAndDisplayTotals() {
        const repairedParts = originalOrderData.repairedParts || [];
        const techCollectionAmount = originalOrderData.technicianCollectionAmount || 0;
        
        // --- Group repaired parts by main product ---
        const groupedParts = repairedParts.reduce((acc, part) => {
            const mainProduct = part.mainProductName || '其他零件';
            if (!acc[mainProduct]) {
                acc[mainProduct] = [];
            }
            acc[mainProduct].push(part);
            return acc;
        }, {});

        let repairCost = 0;
        let partsHtml = '';

        for (const mainProduct in groupedParts) {
            partsHtml += `<h6 class="mt-3 text-primary">主商品：${mainProduct}</h6>`;
            let groupSubtotal = 0;
            groupedParts[mainProduct].forEach(part => {
                const partSubtotal = part.subtotalAmount;
                groupSubtotal += partSubtotal;
                partsHtml += `<li class="list-group-item d-flex justify-content-between"><span> - 零件: ${part.productName} (x${part.quantity})</span> <span>${partSubtotal.toLocaleString()}</span></li>`;
            });
            repairCost += groupSubtotal;
        }

        const otherDiscount = parseFloat(document.getElementById('otherDiscount').value) || 0;
        const openInvoice = originalOrderData && originalOrderData.originalOrderTaxAmount > 0;
        
        const subtotal = repairCost - otherDiscount;
        const tax = openInvoice ? Math.round(subtotal * 0.05) : 0;
        const totalDue = subtotal + tax + techCollectionAmount;
        
        costSummaryList.innerHTML = `
            <ul class="list-group list-group-flush">
                ${partsHtml}
                <hr>
                <li class="list-group-item d-flex justify-content-between"><span>裝修費總額</span> <span>${repairCost.toLocaleString()}</span></li>
                <li class="list-group-item d-flex justify-content-between"><span>其他折扣</span> <span class="text-danger">-${otherDiscount.toLocaleString()}</span></li>
                <li class="list-group-item d-flex justify-content-between ${openInvoice ? '' : 'd-none'}"><span>稅金 (5%)</span> <span>${tax.toLocaleString()}</span></li>
                <li class="list-group-item d-flex justify-content-between"><span>技師代收款</span> <span>${techCollectionAmount.toLocaleString()}</span></li>
                <li class="list-group-item d-flex justify-content-between fw-bold fs-5"><span>本次應收總額</span> <span id="total-due">${totalDue.toLocaleString()}</span></li>
            </ul>`;

        // Calculate total paid and update display
        let totalPaid = 0;
        document.querySelectorAll('.payment-input').forEach(input => {
            totalPaid += parseFloat(input.value) || 0;
        });
        document.getElementById('total-paid-display').textContent = `NT$ ${totalPaid.toLocaleString()}`;

        const taxRow = document.getElementById('tax-row');
        if(taxRow) taxRow.style.display = openInvoice ? 'flex' : 'none';
    }
    
    function renderPaymentMethods() {
        paymentMethodsContainer.innerHTML = `
            <div class="mb-3">
                <label class="form-label">新增付款方式</label>
                <div id="add-payment-buttons">
                    <button type="button" class="btn btn-success me-2" data-payment-type="CASH">現金</button>
                    <button type="button" class="btn btn-success me-2" data-payment-type="CREDIT_CARD">信用卡</button>
                    <button type="button" class="btn btn-success" data-payment-type="BANK_TRANSFER">匯款</button>
                </div>
            </div>
            <div id="payment-details-container"></div>
            <div class="row mt-3">
                 <div class="col-md-6 offset-md-6 text-end">
                    <p class="mb-1 fs-5">支付總額: <span id="total-paid-display" class="fw-bold">NT$ 0</span></p>
                </div>
            </div>
        `;
    }

    function addPaymentMethodRow(type) {
        const container = document.getElementById('payment-details-container');
        const paymentId = `payment-${Date.now()}`;
        let contentHtml = '';
        
        const removeButtonHtml = `<div class="col-md-1"><button type="button" class="btn btn-danger btn-sm remove-payment-btn"><i class="bi bi-trash"></i></button></div>`;

        switch (type) {
            case 'CASH':
                contentHtml = `<div class="col-md-3"><input type="text" class="form-control" value="現金" readonly></div><div class="col-md-8"><input type="number" class="form-control payment-input" placeholder="金額" data-method="CASH"></div>`;
                break;
            case 'CREDIT_CARD':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="信用卡" readonly></div>
                    <div class="col-md-3 position-relative">
                        <input type="text" class="form-control card-number" placeholder="輸入卡號自動帶入資訊">
                        <span class="card-info-display text-muted small position-absolute top-100 start-0"></span>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select card-payment-type">
                            <option value="0" selected>單筆</option>
                            <option value="2">分期</option>
                        </select>
                    </div>
                    <div class="col-md-2"><input type="number" class="form-control card-installments" placeholder="期數" value="0" style="display: none;"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-input" placeholder="金額" data-method="CREDIT_CARD"></div>`;
                break;
            case 'BANK_TRANSFER':
                contentHtml = `
                    <div class="col-md-2"><input type="text" class="form-control" value="匯款" readonly></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-name" placeholder="銀行名稱"></div>
                    <div class="col-md-2"><input type="text" class="form-control remitter-account" placeholder="匯出帳號後五碼"></div>
                    <div class="col-md-2"><input type="text" class="form-control bank-account" placeholder="匯入帳號後五碼"></div>
                    <div class="col-md-2"><input type="number" class="form-control payment-input" placeholder="金額" data-method="BANK_TRANSFER"></div>`;
                break;
        }
        
        const rowHtml = `
            <div class="payment-item row g-2 mb-2 align-items-center" id="${paymentId}">
                ${contentHtml}${removeButtonHtml}
            </div>`;
        
        container.insertAdjacentHTML('beforeend', rowHtml);
    }
    
    // --- Event Handling & Submission ---
    function setupEventListeners() {
        paymentForm.addEventListener('submit', handleFormSubmit);
        backBtn.addEventListener('click', () => window.history.back());
        document.getElementById('otherDiscount').addEventListener('input', calculateAndDisplayTotals);
        document.querySelectorAll('input[name="invoiceOption"]').forEach(radio => {
            radio.addEventListener('change', calculateAndDisplayTotals);
        });

        paymentMethodsContainer.addEventListener('click', function(event) {
            if (event.target.matches('button[data-payment-type]')) {
                addPaymentMethodRow(event.target.dataset.paymentType);
            }
            if (event.target.closest('.remove-payment-btn')) {
                event.target.closest('.payment-item').remove();
                calculateAndDisplayTotals();
            }
        });
        
        paymentMethodsContainer.addEventListener('input', async function(event) {
            // Handle payment amount changes for real-time total update
            if (event.target.classList.contains('payment-input')) {
                calculateAndDisplayTotals();
            }
            
            // --- New logic for BIN lookup ---
            if (event.target.classList.contains('card-number')) {
                const cardNumberInput = event.target;
                const infoDisplay = cardNumberInput.closest('.position-relative').querySelector('.card-info-display');
                const cardNumber = cardNumberInput.value.replace(/\s+/g, '');
                
                infoDisplay.textContent = '';

                if (cardNumber.length >= 16) { // Start lookup after 8 digits
                    const prefix = cardNumber.substring(0, 8);
                    infoDisplay.textContent = '查詢中...';
                    try {
                        const response = await window.fetchAuthenticated(`/api/v1/utils/bin-lookup/${prefix}`);
                        if (response.ok) {
                            const result = await response.json();
                            if(result.data) {
                                infoDisplay.textContent = `${result.data.brand || ''} - ${result.data.issuer || ''}`;
                            } else {
                                infoDisplay.textContent = '查無卡片資訊';
                            }
                        } else {
                            infoDisplay.textContent = '查詢失敗';
                        }
                    } catch (error) {
                        console.error('BIN lookup error:', error);
                        infoDisplay.textContent = '查詢錯誤';
                    }
                }
            }
        });

        paymentMethodsContainer.addEventListener('change', function(event) {
             if (event.target.classList.contains('card-payment-type')) {
                const row = event.target.closest('.payment-item');
                const installmentsInput = row.querySelector('.card-installments');
                if (event.target.value === '2') {
                    installmentsInput.style.display = 'block';
                } else {
                    installmentsInput.style.display = 'none';
                }
            }
        });
    }
    
    async function handleFormSubmit(event) {
        event.preventDefault();
        const payload = buildPayload();
        
        // Final validation
        const totalPaid = payload.paymentDetails.reduce((sum, p) => sum + p.amount, 0);
        const totalDue = parseFloat(document.getElementById('total-due').textContent.replace(/,/g, ''));
        if (totalPaid < totalDue) {
            showToast('支付金額不足!', 'warning');
            return;
        }

        const completeBtn = document.getElementById('complete-btn');
        completeBtn.disabled = true;
        completeBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 處理中...';

        try {
            const response = await window.fetchAuthenticated(`/api/v1/dispatch-orders/${dispatchRepairId}/complete-payment`, {
                method: 'POST',
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errData = await response.json();
                throw new Error(errData.message || '操作失敗');
            }
            
            showToast('收款步驟已完成！', 'success');
            setTimeout(() => {
                window.location.href = `dispatch_tech_detail.html?id=${dispatchRepairId}`;
            }, 1500);

        } catch (error) {
            console.error('完成收款步驟失敗:', error);
            showToast(`操作失敗: ${error.message}`, 'danger');
            completeBtn.disabled = false;
            completeBtn.innerHTML = '完成收款';
        }
    }

    function buildPayload() {
        const paymentDetails = [];
        document.querySelectorAll('#payment-details-container .payment-item').forEach(row => {
            const amountInput = row.querySelector('.payment-input');
            const amount = parseFloat(amountInput.value);
            
            if (amount > 0) {
                const paymentMethodCode = amountInput.dataset.method;
                const detail = {
                    paymentMethodCode: paymentMethodCode,
                    amount: amount,
                    // Add all possible fields with fallbacks
                    cardNumber: row.querySelector('.card-number')?.value || null,
                    cardInstallments: row.querySelector('.card-installments')?.value ? parseInt(row.querySelector('.card-installments').value) : 1,
                    bankName: row.querySelector('.bank-name')?.value || null,
                    remitterAccountLastFive: row.querySelector('.remitter-account')?.value || null,
                    bankAccountLastFive: row.querySelector('.bank-account')?.value || null
                };
                paymentDetails.push(detail);
            }
        });

        return {
            openInvoice: originalOrderData && originalOrderData.originalOrderTaxAmount > 0,
            otherDiscount: parseFloat(document.getElementById('otherDiscount').value) || 0,
            paymentDetails: paymentDetails
        };
    }

    initializePage();
}); 