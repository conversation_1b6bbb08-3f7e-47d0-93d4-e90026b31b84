<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>職權折扣設定移除按鈕最終修正測試 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        body { padding: 20px; }
        .demo-section { margin-bottom: 30px; }
        .discount-row {
            padding-bottom: 15px;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .discount-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .comparison-bg {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>職權折扣設定移除按鈕最終修正測試</h2>
        <p class="text-muted">參考「新增門市商品訂單」頁面的刪除按鈕樣式進行修正</p>

        <div class="demo-section">
            <h4>參考標準：門市商品訂單表格中的刪除按鈕</h4>
            <div class="comparison-bg">
                <h6>商品選擇表格示例</h6>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>商品名稱</th>
                                <th>商品條碼</th>
                                <th>原定價</th>
                                <th>活動價</th>
                                <th>數量</th>
                                <th>小計</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>測試商品A</td>
                                <td>1234567890123</td>
                                <td><input type="number" class="form-control form-control-sm bg-light" value="100" readonly></td>
                                <td><input type="number" class="form-control form-control-sm bg-light" value="100" readonly></td>
                                <td><input type="number" class="form-control form-control-sm" value="1" min="1"></td>
                                <td class="item-subtotal">100</td>
                                <td>
                                    <button type="button" class="btn btn-danger btn-sm" title="刪除">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p class="text-info small mb-0">
                    <i class="bi bi-info-circle"></i> 
                    參考樣式：<code>btn btn-danger btn-sm</code> + 垃圾桶圖示，正方形外觀
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>修正前的職權折扣設定（問題版本）</h4>
            <div class="comparison-bg">
                <div class="row g-3 align-items-center mb-3">
                    <div class="col-md-5">
                        <label class="form-label">角色</label>
                        <select class="form-select">
                            <option selected>系統管理員</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">折扣金額 (NT $)</label>
                        <div class="input-group">
                            <span class="input-group-text">NT $</span>
                            <input type="number" class="form-control" value="500">
                        </div>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" class="btn btn-danger w-100">移除</button>
                    </div>
                </div>
                <p class="text-danger small mb-0">
                    <i class="bi bi-exclamation-triangle"></i> 
                    問題：按鈕樣式不一致，使用文字而非圖示，寬度過寬
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>修正後的職權折扣設定（最終版本）</h4>
            <div class="comparison-bg">
                <div class="row g-3 align-items-center mb-3">
                    <div class="col-md-5">
                        <label class="form-label">角色</label>
                        <select class="form-select">
                            <option selected>系統管理員</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">折扣金額 (NT $)</label>
                        <div class="input-group">
                            <span class="input-group-text">NT $</span>
                            <input type="number" class="form-control" value="500">
                        </div>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" class="btn btn-danger btn-sm" title="移除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <p class="text-success small mb-0">
                    <i class="bi bi-check-circle"></i> 
                    解決：按鈕樣式與參考頁面完全一致，正方形外觀，置底對齊
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>完整的職權折扣設定區塊示例</h4>
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    職權折扣設定
                    <button type="button" class="btn btn-sm btn-outline-success">
                        <i class="bi bi-plus-circle"></i> 新增角色折扣
                    </button>
                </div>
                <div class="card-body">
                    <!-- 第一個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option selected>系統管理員</option>
                                <option>部門主管</option>
                                <option>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="500" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-danger btn-sm" title="移除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第二個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option>系統管理員</option>
                                <option selected>部門主管</option>
                                <option>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="300" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-danger btn-sm" title="移除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第三個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option>系統管理員</option>
                                <option>部門主管</option>
                                <option selected>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="100" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-danger btn-sm" title="移除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>樣式對比分析</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">參考標準（門市商品訂單）</div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><strong>樣式類別：</strong><code>btn btn-danger btn-sm</code></li>
                                <li><strong>圖示：</strong><code>&lt;i class="bi bi-trash"&gt;&lt;/i&gt;</code></li>
                                <li><strong>外觀：</strong>正方形按鈕</li>
                                <li><strong>對齊：</strong>表格單元格內自然對齊</li>
                                <li><strong>提示：</strong><code>title="刪除"</code></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">修正後（職權折扣設定）</div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><strong>樣式類別：</strong><code>btn btn-danger btn-sm</code></li>
                                <li><strong>圖示：</strong><code>&lt;i class="bi bi-trash"&gt;&lt;/i&gt;</code></li>
                                <li><strong>外觀：</strong>正方形按鈕</li>
                                <li><strong>對齊：</strong><code>d-flex align-items-end</code></li>
                                <li><strong>提示：</strong><code>title="移除"</code></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>技術實作細節</h4>
            <div class="card">
                <div class="card-body">
                    <h6>修正前的程式碼：</h6>
                    <pre><code>&lt;div class="col-md-2 d-flex align-items-end"&gt;
    &lt;button type="button" class="btn btn-danger remove-discount-btn w-100" data-row-id="${discountId}"&gt;移除&lt;/button&gt;
&lt;/div&gt;</code></pre>
                    
                    <h6>修正後的程式碼：</h6>
                    <pre><code>&lt;div class="col-md-2 d-flex align-items-end"&gt;
    &lt;button type="button" class="btn btn-danger btn-sm remove-discount-btn" data-row-id="${discountId}" title="移除"&gt;
        &lt;i class="bi bi-trash"&gt;&lt;/i&gt;
    &lt;/button&gt;
&lt;/div&gt;</code></pre>
                </div>
            </div>
        </div>

        <div class="alert alert-success">
            <h5>✅ 修正完成</h5>
            <p class="mb-0">職權折扣設定區塊的移除按鈕現在：</p>
            <ul class="mb-0">
                <li>與「新增門市商品訂單」頁面的刪除按鈕樣式完全一致</li>
                <li>使用 <code>btn btn-danger btn-sm</code> 樣式，呈現正方形外觀</li>
                <li>採用垃圾桶圖示 <code>&lt;i class="bi bi-trash"&gt;&lt;/i&gt;</code></li>
                <li>使用 <code>align-items-end</code> 實現置底對齊</li>
                <li>移除了 <code>w-100</code> 類別，保持按鈕的正方形比例</li>
                <li>添加了 <code>title="移除"</code> 提示文字</li>
            </ul>
        </div>

        <div class="alert alert-info">
            <h5>📋 修正檔案</h5>
            <p>本次修正的檔案：</p>
            <ul class="mb-0">
                <li><strong>product_setting_form.js</strong>：修正 <code>addDiscountRow</code> 函數中的移除按鈕樣式</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 為測試頁面的按鈕添加提示功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
