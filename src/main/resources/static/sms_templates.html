<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡訊模板管理 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item">系統管理</li>
                            <li class="breadcrumb-item active" aria-current="page">簡訊模板管理</li>
                        </ol>
                    </nav>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>簡訊模板管理</h3>
                        <a href="sms_template_form.html?mode=add" class="btn btn-primary">新增簡訊模板</a>
                    </div>

                    <div class="card mb-3">
                        <div class="card-body">
                            <form id="sms-filter-form" class="row g-3 align-items-end">
                                <div class="col-md-4">
                                    <label for="filterTemplateType" class="form-label">模板類型</label>
                                    <input type="text" id="filterTemplateType" class="form-control" placeholder="例如: BIRTHDAY_GIFT">
                                </div>
                                <div class="col-md-4">
                                    <label for="filterTemplateKeyword" class="form-label">關鍵字 (模板名稱)</label>
                                    <input type="text" id="filterTemplateKeyword" class="form-control" placeholder="輸入模板名稱關鍵字">
                                </div>
                                <div class="col-md-2 align-self-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-search"></i> 查詢
                                    </button>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" id="clearSmsFiltersBtn" class="btn btn-outline-secondary w-100">清除</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover app-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>模板名稱</th>
                                    <th>模板類型</th>
                                    <th>更新時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="sms-templates-table-body">
                            </tbody>
                        </table>
                    </div>
                    <div id="no-sms-templates-message" class="text-center mt-3 d-none">
                        <p>查無簡訊模板資料</p>
                    </div>

                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="sms-pagination-controls">
                        </ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/sms_templates.js"></script>
</body>
</html> 