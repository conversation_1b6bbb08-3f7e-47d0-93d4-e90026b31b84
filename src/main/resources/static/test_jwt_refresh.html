<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT Token 更新測試</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>JWT Token 更新測試</h1>
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">測試步驟</h5>
                <ol>
                    <li>登入系統</li>
                    <li>點擊下方按鈕發送測試請求</li>
                    <li>查看 Console 日誌</li>
                    <li>檢查 localStorage 中的 JWT token 是否更新</li>
                </ol>
                
                <button id="testBtn" class="btn btn-primary mt-3">發送測試請求</button>
                <button id="clearLogBtn" class="btn btn-secondary mt-3 ms-2">清除日誌</button>
                
                <div class="mt-4">
                    <h6>當前 JWT Token (前20字元)：</h6>
                    <pre id="currentToken" class="bg-light p-2"></pre>
                </div>
                
                <div class="mt-3">
                    <h6>測試結果：</h6>
                    <div id="testResult" class="bg-light p-3" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                        等待測試...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/main.js"></script>
    <script>
        function updateTokenDisplay() {
            const token = localStorage.getItem('jwtToken');
            const tokenDisplay = document.getElementById('currentToken');
            if (token) {
                tokenDisplay.textContent = token.substring(0, 20) + '...';
            } else {
                tokenDisplay.textContent = '無 Token';
            }
        }

        function addLog(message, type = 'info') {
            const resultDiv = document.getElementById('testResult');
            const timestamp = new Date().toLocaleTimeString('zh-TW', { hour12: false, millisecond: true });
            const colorClass = type === 'error' ? 'text-danger' : 
                             type === 'success' ? 'text-success' : 
                             type === 'warning' ? 'text-warning' : '';
            
            resultDiv.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        document.getElementById('testBtn').addEventListener('click', async function() {
            updateTokenDisplay();
            addLog('開始測試 JWT token 更新機制...');
            
            const oldToken = localStorage.getItem('jwtToken');
            addLog('當前 Token: ' + (oldToken ? oldToken.substring(0, 20) + '...' : 'NULL'));
            
            try {
                addLog('發送請求到 /api/v1/auth/test-token-refresh');
                const response = await window.fetchAuthenticated('/api/v1/auth/test-token-refresh');
                
                addLog(`Response 狀態: ${response.status} ${response.statusText}`);
                
                // 列出所有 response headers
                addLog('Response Headers:');
                for (let [key, value] of response.headers.entries()) {
                    addLog(`  ${key}: ${value}`);
                }
                
                if (response.ok) {
                    const data = await response.json();
                    addLog('Response Data: ' + JSON.stringify(data, null, 2));
                    
                    const newToken = localStorage.getItem('jwtToken');
                    if (newToken !== oldToken) {
                        addLog('✅ JWT Token 已更新！', 'success');
                        addLog('新 Token: ' + newToken.substring(0, 20) + '...', 'success');
                    } else {
                        addLog('⚠️ JWT Token 沒有變化', 'warning');
                    }
                } else {
                    addLog('請求失敗: ' + response.status, 'error');
                }
            } catch (error) {
                addLog('發生錯誤: ' + error.message, 'error');
                console.error('Test error:', error);
            }
            
            updateTokenDisplay();
        });

        document.getElementById('clearLogBtn').addEventListener('click', function() {
            document.getElementById('testResult').innerHTML = '等待測試...';
        });

        // 初始化顯示
        updateTokenDisplay();
        
        // 檢查是否已登入
        if (!localStorage.getItem('jwtToken')) {
            addLog('⚠️ 請先登入系統', 'warning');
        } else {
            addLog('✅ 已檢測到登入狀態', 'success');
        }
    </script>
</body>
</html> 