<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統公告表單 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/announcements.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <!-- Breadcrumbs -->
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a href="announcements.html">系統公告列表</a></li>
                            <li class="breadcrumb-item active" aria-current="page" id="form-mode-breadcrumb">新增公告</li>
                        </ol>
                    </nav>

                    <h3 id="form-title" class="mb-3">新增系統公告</h3>

                    <div class="card">
                        <div class="card-body">
                            <form id="announcement-form">
                                <input type="hidden" id="announcementId">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="categoryCode" class="form-label">公告分類 <span class="text-danger">*</span></label>
                                        <select id="categoryCode" class="form-select" required>
                                            <option value="" disabled selected>請選擇分類</option>
                                            <!-- Options populated by JS -->
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="title" class="form-label">公告標題 <span class="text-danger">*</span></label>
                                        <input type="text" id="title" class="form-control" required maxlength="255">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="startTime" class="form-label">開始時間 <span class="text-danger">*</span></label>
                                        <input type="datetime-local" id="startTime" class="form-control" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="endTime" class="form-label">結束時間 <span class="text-danger">*</span></label>
                                        <input type="datetime-local" id="endTime" class="form-control" required>
                                    </div>
                                    <div class="col-md-12">
                                        <label for="content" class="form-label">公告內容 <span class="text-danger">*</span></label>
                                        <textarea id="content" class="form-control" rows="5" required></textarea>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" role="switch" id="isImportant" checked>
                                            <label class="form-check-label" for="isImportant">設為重要公告</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" role="switch" id="isEnabled" checked>
                                            <label class="form-check-label" for="isEnabled">啟用此公告</label>
                                        </div>
                                    </div>

                                    <div class="col-md-12">
                                        <hr>
                                        <h5>公告對象 (部門/員工 - 來自正航資料庫)</h5>
                                        <div id="targets-container">
                                            <!-- Target rows will be added here by JS -->
                                        </div>
                                        <button type="button" class="btn btn-outline-secondary btn-sm mt-2" id="add-target-btn">新增公告對象</button>
                                    </div>

                                </div>
                                <div class="mt-4 d-flex justify-content-end">
                                    <a href="announcements.html" class="btn btn-outline-secondary me-2">返回列表</a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">儲存公告</button>
                                </div>
                                <div id="form-error-message" class="alert alert-danger mt-3 d-none"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/announcement_form.js"></script>
</body>
</html> 