/* css/announcements.css - Styles for announcement list and form pages */

/* Styles for the target selection rows in announcement_form.html */
.target-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.target-row .form-select,
.target-row .form-control {
    margin-right: 0.5rem;
}

.target-row .form-select:last-child {
    margin-right: 0.5rem; /* Ensure space before remove button */
}

/* Ensure table action buttons have some spacing */
#announcements-table-body .btn-group .btn {
    margin-right: 5px;
}
#announcements-table-body .btn-group .btn:last-child {
    margin-right: 0;
}

/* Make sure filter buttons align nicely */
#filter-form .btn {
    min-height: 38px; /* Match input field height with Bootstrap defaults */
} 