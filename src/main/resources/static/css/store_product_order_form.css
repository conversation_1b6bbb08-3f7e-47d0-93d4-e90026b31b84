/* store_product_order_form.css */

/* Styles specific to the store product order form, if any, beyond main.css and Bootstrap. */

/* Example: If the filter card header needs specific styling not in main.css */
/* .filter-card .card-header { ... } */

/* Payment entry specific styling if needed beyond common form/input styles */
.payment-entry .form-select-sm,
.payment-entry .form-control-sm {
    /* These might already be covered by .app-table input styles if payment is in a table, 
       or by general form-control-sm. Only keep if specific override needed. */
    /* font-size: 0.875rem; */ 
}

.payment-entry .remove-payment-btn {
    padding: 0.25rem 0.5rem; /* Ensure consistent with other small buttons */
}

/* Product search modal specific adjustments if any */
#productSearchResultsModal {
    max-height: 450px; /* Keep this if specific to this form's modal */
    overflow-y: auto;
}

/* Styling for total amounts section - specific alignment or emphasis */
.card-body dl.row dt {
    font-weight: normal;
    text-align: left; 
     padding-right: 0; 
}
.card-body dl.row dd {
    font-weight: bold;
    text-align: right;
     padding-left: 0;
}

#amountDueDisplay.text-danger {
    color: #dc3545 !important; 
}

/* Table column width suggestions - these are specific to store form's item table layout */
/* These might need adjustment based on the new "Item Type" column */
#order-items-table th:nth-child(1) { width: 20%; } /* 商品名稱 */
#order-items-table th:nth-child(2) { width: 12%; } /* 商品條碼 */
#order-items-table th:nth-child(3) { width: 8%; }  /* 單價 */
#order-items-table th:nth-child(4) { width: 7%; }  /* 數量 */
#order-items-table th:nth-child(5) { width: 10%; } /* 品項類型 - NEW */
#order-items-table th:nth-child(6) { width: 10%; } /* 折扣率 */
#order-items-table th:nth-child(7) { width: 10%; } /* 折扣額 */
#order-items-table th:nth-child(8) { width: 8%; }  /* 小計 */
#order-items-table th:nth-child(9) { width: 10%; } /* 倉庫 */
#order-items-table th:nth-child(10){ width: 10%; } /* 備註 */
#order-items-table th:nth-child(11){ width: 5%; }  /* 操作 */

/* Ensure modals are above the potential Bootstrap sticky header/sidebar if z-index issues arise */
/* This rule is general and could also be in main.css if not already present */
.modal {
    z-index: 1060; 
}
.modal-backdrop {
    z-index: 1055; 
} 

/* Custom styles for store product order form */

#submit-order-btn:disabled {
    background-color: #6c757d; /* Bootstrap's secondary color (dark gray) */
    border-color: #6c757d;
    opacity: 0.65; /* Keep Bootstrap's default opacity for disabled state */
} 

/* Custom styles for the store product order form */
#order-items-table > thead > tr > th {
    background-color: #ffffff;
    color: #212529; /* Use a standard dark color for better readability */
} 