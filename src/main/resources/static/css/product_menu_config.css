/* Custom styles for product_menu_config.html */
.menu-config-container {
    display: flex;
    min-height: 600px; /* Adjust as needed */
    background-color: #f8f9fa;
}

.menu-column {
    flex: 1;
    padding: 0;
    display: flex;
    flex-direction: column;
    /* min-width: 250px; Prevent columns from becoming too narrow */
}

.menu-column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    font-weight: bold;
}

.menu-column-header h5 {
    font-size: 1rem;
    margin-bottom: 0;
}

.sortable-list {
    flex-grow: 1;
    min-height: 150px; /* Minimum height for drag & drop */
    overflow-y: auto;
}

.menu-item {
    cursor: grab;
    padding: 0.5rem 0.75rem;
    background-color: #fff;
    border-bottom: 1px solid #eee !important;
}

.menu-item:last-child {
    border-bottom: none !important;
}

.menu-item.selected {
    background-color: #e9ecef; 
    font-weight: bold;
}

.menu-item .handle {
    cursor: grab;
    opacity: 0.5;
}

.menu-item .btn-danger {
    padding: 0.1rem 0.3rem;
    font-size: 0.7rem;
}

/* SortableJS ghost class style */
.ghost-class {
    opacity: 0.4;
    background: #c8ebfb;
}

/* SortableJS chosen class style */
.chosen-class {
    /* background: #f0f0f0; */
    /* Optional: style for the item being dragged */
}

#product-search-results .list-group-item {
    cursor: pointer;
}
#product-search-results .list-group-item:hover {
    background-color: #f0f0f0;
} 