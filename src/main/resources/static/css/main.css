/* css/main.css - Shared styles for authenticated pages */
:root {
    --bs-primary: #0d6efd;
    --bs-secondary: #6c757d;
    --bs-success: #198754;
    --bs-info: #0dcaf0;
    --bs-warning: #ffc107;
    --bs-alert: #ff7300;
    --bs-danger: #dc3545;
    --bs-light: #f8f9fa;
    --bs-dark: #212529;

    /* --- Custom Color Palette --- */
    --ek-primary: #212529; /* 主色 - 深灰黑 */
    --ek-primary-hover: #3e444a; /* 主色懸停 */
    --ek-primary-text: #ffffff; /* 主色上的文字 - 白色 */
    --ek-secondary: #D3C69B; /* 次色 - 金色 */
    --ek-secondary-text: #212529; /* 次色上的文字 - 深灰黑 */
}

body {
    font-family: 'Noto Sans TC', sans-serif;
    background-color: #F0F0F0; /* Figma: 首頁(東方) background (Node 435:251) */
}

.app-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header Styles - Based on Figma Node 2129:10473 (Frame 1234) */
.app-header {
    background-color: #3D3D3D; /* Figma: fill_MI4Q1V */
    color: #FFFFFF; /* Figma: text color for title and user */
    padding: 16px 24px; /* Figma: padding of Frame 1234 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px; /* Figma: height of Frame 1234 */
}

.header-left {
    display: flex;
    align-items: center;
}

.logo-img {
    height: 40px; /* Adjust as needed, Figma logo _圖層_1 is complex */
    margin-right: 20px; /* Figma: gap in Frame 1233 */
    /* background-color: #C30D23; /* Placeholder for actual logo if it has a bg */
}

.header-title {
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 20px; /* Figma: 門市 POS 系統 text style */
}

.header-right {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.user-avatar-img {
    width: 32px; /* Figma: Ellipse 1 size approx */
    height: 32px;
    border-radius: 50%;
    border: 2px solid #AE0709; /* Figma: stroke_PJJ4TS */
    margin-right: 12px; /* Figma: gap in Frame 1051 */
    /* background-color: lightgray; /* Placeholder */
}

#userNameDisplay {
    font-family: 'Noto Sans', sans-serif; /* Figma */
    font-weight: 500;
    font-size: 16px; /* Figma */
}

/* Sidebar Navigation Styles - Based on Figma Node 2129:10492 (主要功能選單) */
.main-container {
    display: flex;
    flex-grow: 1;
}

.sidebar-nav {
    width: 300px; /* Figma: width of 主要功能選單 */
    background-color: #2B2B2B; /* Figma: fill_RBQDHP */
    padding: 12px 0px; /* Figma: padding */
    color: #FFFFFF;
    height: calc(100vh - 80px); /* Full height minus header */
    overflow-y: auto;
    transition: width 0.3s ease-in-out; /* Smooth transition for width change */
    flex-shrink: 0; /* Prevent sidebar from shrinking when content is long */
}

.sidebar-nav .nav-link {
    /* Figma: Frame 4 styling, e.g., icon/square-pen (Node 2129:10496) & text 訂單管理 (Node 2129:10497) */
    color: #FFFFFF; /* Figma: text fill */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 16px; /* Figma */
    padding: 12px 24px; /* Figma: padding of Frame 1149 etc. */
    display: flex;
    align-items: center;
}

.sidebar-nav .nav-link i.bi {
    font-size: 1.2rem;
    margin-right: 8px; /* Figma: gap in Frame 4 */
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
    background-color: #1E1E1E; /* Figma: fill_1DM0NQ for active/hover items */
}

.sidebar-nav .nav-link .bi-chevron-down {
    margin-left: auto;
    transition: transform 0.2s ease-in-out;
}

.sidebar-nav .nav-link[aria-expanded="true"] .bi-chevron-down {
    transform: rotate(180deg);
}

.sidebar-nav .collapse .nav-link.sub-link {
    padding-left: 48px; /* Indent sub-menu items */
    font-size: 15px;
    font-weight: 500;
}

/* === START: Sidebar Collapse Styles === */
#sidebar-placeholder.collapsed .sidebar-nav {
    width: 102px; /* Width to fit icons and some padding, adjusted */
}

#sidebar-placeholder.collapsed .sidebar-header {
    justify-content: center;
}

#sidebar-placeholder.collapsed .sidebar-nav .sidebar-text {
    display: none;
}

.sidebar-header {
    padding: 0 1rem;
    display: flex;
    justify-content: flex-end;
    background-color: #2B2B2B; /* Match sidebar background */
}

.sidebar-header #sidebar-toggle-btn {
    color: #fff;
    font-size: 1.25rem;
    background: none;
    border: none;
}

.sidebar-header #sidebar-toggle-btn:hover {
    color: var(--ek-secondary);
}

.sidebar-nav .nav-link span.sidebar-text {
    transition: opacity 0.2s ease-in-out;
}
/* === END: Sidebar Collapse Styles === */

/* Sidebar theme for QueYou */
.sidebar-theme-queyou {
    background-color: #4A004A; /* Dark Purple - adjust as needed */
}

/* Ensure QueYou theme links are still readable and hover/active states are distinct */
.sidebar-theme-queyou .nav-link {
    color: #FFFFFF; /* Keep white text */
}

.sidebar-theme-queyou .nav-link:hover,
.sidebar-theme-queyou .nav-link.active {
    background-color: #3A003A; /* Slightly darker purple for hover/active */
}

/* Page Content Styles */
.page-content {
    flex-grow: 1;
    padding: 20px;
    background-color: #F0F0F0; /* Consistent with body or specific content area background */
    overflow-y: auto;
    height: calc(100vh - 80px);
    transition: margin-left 0.3s ease-in-out; /* Not strictly needed with flexbox, but good practice */
}

/* Announcement Modal Styles - Based on Figma popover_最新公告 (Node 3516:8362) */
#announcementModal .modal-content {
    border-radius: 6px; /* Figma */
    border: 1px solid #DDDDDD; /* Figma: stroke_WECSGB */
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.09); /* Figma: effect_5Q47JS */
}

#announcementModal .modal-header {
    border-bottom: 1px solid #DDDDDD; /* Figma: Vector 2 (Node 3516:8368) */
    padding: 20px; /* Figma */
}

#announcementModal .modal-title {
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 16px; /* Figma: 最新公告 text style_XONZNV */
    color: #333333; /* Figma: fill_0OYG41 */
}

#announcementModal .modal-body {
    padding: 20px; /* Figma */
}

.announcement-item {
    margin-bottom: 16px; /* Figma: gap between items */
    padding-bottom: 16px;
    border-bottom: 1px solid #EEE; /* Separator for items */
}
.announcement-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.announcement-item .header {
    display: flex;
    align-items: center;
    margin-bottom: 8px; /* Figma */
}

.announcement-item .tag {
    /* Figma: Frame 6369 (Node 3516:8372) */
    background-color: #E0D7BA; /* Figma: fill_P5URKP */
    color: #333333; /* Figma */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 14px; /* Figma: style_4J9B3F */
    padding: 4px 8px;
    border-radius: 4px;
    margin-right: 12px; /* Figma */
}

.announcement-item .title {
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 16px; /* Figma: style_XONZNV */
    color: #333333; /* Figma */
}

.announcement-item .content-text {
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 400;
    font-size: 14px; /* Figma: style_GGK2RN */
    color: #333333; /* Figma */
    line-height: 1.6;
}

/* Store selector in header */
.store-selector-container .badge {
    font-size: 0.9rem;
    padding: 0.5em 0.75em;
}

/* --- Common Form Table Styles (e.g., for Order Item Tables) --- */
.app-table .item-input-price,
.app-table .item-input-qty,
.app-table .item-input-type, /* Added for item type dropdown */
.app-table .item-input-discount-rate input[type='number'],
.app-table .item-input-discount-amount input[type='number'],
.app-table .item-input-warehouse,
.app-table .item-input-notes input[type='text'] {
    min-width: 70px; 
    font-size: 0.875rem; /* Consistent small font for inputs */
}

.app-table .item-input-qty {
    max-width: 80px;
    text-align: center;
}

.app-table .item-input-type {
    min-width: 100px; /* Give item type dropdown enough space */
    max-width: 150px;
}

.app-table .item-input-price,
.app-table .item-input-discount-amount input[type='number'] {
    max-width: 110px;
}

.app-table .item-input-discount-rate .input-group {
    max-width: 120px; 
}
.app-table .item-input-discount-rate input[type='number']{
    min-width: 50px; /* Ensure input itself has some base width */
}

.app-table .item-input-warehouse {
    max-width: 130px;
}

.app-table .item-input-notes input[type='text'] {
    min-width: 140px;
}

/* Ensure table cells with these inputs don't collapse too much */
#order-items-table td,
.dispatch-items-table td /* If dispatch form has a differently ID'd table */
{
    /* white-space: nowrap; /* Consider if this helps or hinders responsiveness */
}

/* General app table header and cell styling */
.app-table th,
.app-table td {
    vertical-align: middle;
    font-size: 0.9rem; /* Default for tables, can be overridden by specific input styles above */
}

/* Override Bootstrap Primary Color */
.btn-primary {
    background-color: var(--ek-primary);
    border-color: var(--ek-primary);
    color: var(--ek-primary-text);
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active {
    background-color: var(--ek-primary-hover) !important;
    border-color: var(--ek-primary-hover) !important;
    color: var(--ek-primary-text) !important;
    box-shadow: 0 0 0 0.25rem rgba(80, 85, 90, 0.5);
}

/* Override Bootstrap Secondary Color for buttons */
.btn-secondary {
    background-color: var(--ek-secondary);
    border-color: var(--ek-secondary);
    color: var(--ek-secondary-text);
}

.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active,
.btn-secondary.active {
    background-color: #c7b98a !important; /* Slightly darker gold */
    border-color: #c7b98a !important;
    color: var(--ek-secondary-text) !important;
}

/* Primary Outline Buttons */
.btn-outline-primary {
    color: var(--ek-primary);
    border-color: var(--ek-primary);
}
.btn-outline-primary:hover {
    background-color: var(--ek-primary);
    color: var(--ek-primary-text);
}

/* New Dark Outline Button for View/Edit actions */
.btn-outline-dark {
    --bs-btn-color: var(--ek-primary);
    --bs-btn-border-color: var(--ek-primary);
    --bs-btn-hover-color: var(--ek-primary-text);
    --bs-btn-hover-bg: var(--ek-primary);
    --bs-btn-hover-border-color: var(--ek-primary);
    --bs-btn-focus-shadow-rgb: 49, 132, 253; /* Bootstrap default, can be customized */
    --bs-btn-active-color: var(--ek-primary-text);
    --bs-btn-active-bg: var(--ek-primary-hover);
    --bs-btn-active-border-color: var(--ek-primary-hover);
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: var(--ek-primary);
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: var(--ek-primary);
    --bs-gradient: none;
}

/* --- Component Theming --- */

.app-wrapper {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styling - Not changing as per request */
.sidebar {
    width: 250px;
    background-color: #fff;
    border-right: 1px solid #dee2e6;
    padding: 1rem;
    position: sticky;
    top: 0;
    height: 100vh;
    flex-shrink: 0;
}
.sidebar .nav-link {
    color: #495057;
}
.sidebar .nav-link.active {
    color: var(--bs-primary);
    font-weight: bold;
}

.main-container {
    display: flex;
    flex-grow: 1;
}

.page-content {
    flex-grow: 1;
    padding: 1.5rem;
}

.app-table {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    overflow: hidden;
}

/* 強制、高優先級地設定列表標題單元格的樣式 */
.app-table > thead > tr > th {
    background-color: var(--ek-secondary);
    color: var(--ek-secondary-text);
    font-weight: bold;
    border-color: #c7b98a; /* 邊框顏色也一併調整以求協調 */
}

.app-table th {
    font-weight: 600;
}

.card-header {
    background-color: var(--ek-secondary);
    color: var(--ek-secondary-text);
    font-weight: bold;
    border-bottom: 1px solid rgba(0,0,0,.125);
}

/* Pagination Theming */
.page-item.active .page-link {
    background-color: var(--ek-primary);
    border-color: var(--ek-primary);
}
.page-link {
    color: var(--ek-primary);
}
.page-link:hover {
    color: var(--ek-primary-hover);
}

/* Form Controls Theming */
.form-check-input:checked {
    background-color: var(--ek-primary);
    border-color: var(--ek-primary);
}
.form-check-input:focus {
    border-color: var(--ek-secondary);
    box-shadow: 0 0 0 0.25rem rgba(211, 198, 155, 0.5);
}

.form-control:focus {
    border-color: var(--ek-secondary);
    box-shadow: 0 0 0 0.25rem rgba(211, 198, 155, 0.5);
}

.form-select:focus {
     border-color: var(--ek-secondary);
     box-shadow: 0 0 0 0.25rem rgba(211, 198, 155, 0.5);
}

/* Icon Buttons for View/Edit */
.btn-icon-view, .btn-icon-edit {
    background-color: transparent;
    border: none;
    padding: 0.25rem 0.5rem;
}
.btn-icon-view .bi-eye-fill, .btn-icon-edit .bi-pencil-fill {
    color: var(--ek-primary);
    transition: color 0.2s ease-in-out;
}
.btn-icon-view:hover .bi-eye-fill, .btn-icon-edit:hover .bi-pencil-fill {
    color: var(--ek-primary-hover);
}

/* Toast Notifications */
.toast-container {
    z-index: 1100; /* Ensure toast is on top */
}

/* Custom style for Member Benefits accordion header */
.accordion-button {
    background-color: #f0f2f5; /* 淺灰色，與頁面背景色協調 */
    color: #212529; /* 深灰黑色文字 */
}

/* Style for the button when it's NOT collapsed (i.e., when the section is open) */
.accordion-button:not(.collapsed) {
    background-color: #e9ecef; /* 展開時使用稍深一點的灰色，以示區別 */
    color: #212529;
    box-shadow: none; /* 移除預設的藍色陰影 */
} 

/* General button styling */
.btn-disabled-custom,
.btn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
    opacity: 0.65;
    cursor: not-allowed !important;
} 

.bg-urgent {
    background-color: #FFB6C1 !important; /* Hot Pink for urgent orders */
} 

/* Custom Alert Button */
.btn-alert {
    --bs-btn-color: #fff;
    --bs-btn-bg: #ff7300;
    --bs-btn-border-color: #ff7300;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #e66800;
    --bs-btn-hover-border-color: #d96200;
    --bs-btn-focus-shadow-rgb: 255, 145, 51;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #d96200;
    --bs-btn-active-border-color: #cc5c00;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #ff7300;
    --bs-btn-disabled-border-color: #ff7300;
} 
