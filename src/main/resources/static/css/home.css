/* css/home.css - Home page specific styles */

.quick-actions .quick-action-btn {
    /* Figma: Frame 62 (Node 435:360) style for "門市商品銷售" */
    background-color: #FFFFFF; /* Figma: fill_23FI8W */
    color: #333333; /* Figma: fill_G7UVEO */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 24px; /* Figma: style_WSV3KS */
    border-radius: 100px; /* Figma */
    padding: 40px 20px; /* Adjusted from Figma's 40px 60px to fit content better */
    width: 100%;
    text-align: center;
    display: flex;
    flex-direction: column; /* To stack icon and text if needed, or align center */
    align-items: center;
    justify-content: center;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06), 0px 0px 4px 0px rgba(0, 0, 0, 0.04); /* Figma: effect_T5TFTJ */
    min-height: 120px; /* Figma: height 120px */
    text-decoration: none;
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.quick-actions .quick-action-btn i.bi {
    font-size: 2rem; /* Larger icons */
    margin-bottom: 0.5rem;
    color: #AE0709; /* Figma: icon stroke_PHI14P & stroke_GJEWQ8 suggests red variant for icons */
}

.quick-actions .quick-action-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0px 6px 12px 0px rgba(0, 0, 0, 0.1), 0px 0px 8px 0px rgba(0, 0, 0, 0.08);
    color: #AE0709;
}

.content-placeholder {
    margin-top: 2rem;
    padding: 2rem;
    background-color: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
} 