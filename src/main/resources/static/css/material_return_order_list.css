.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.list-container .card {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
}

.list-container .item-seq {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #AE0709;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    margin-right: 1rem;
}

.list-container .item-info {
    flex-grow: 1;
    font-size: 0.9rem;
    color: #6c757d;
}

.list-container .item-info p {
    margin-bottom: 0.25rem;
}

.list-container .item-action .btn {
    padding: 0.25rem 0.5rem;
} 