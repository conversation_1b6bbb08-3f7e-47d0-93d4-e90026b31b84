.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.info-card {
    background-color: #FFEAEA; /* Light red background as per Figma */
}

.accordion-item {
    margin-bottom: 1rem;
}

.accordion-button:not(.collapsed) {
    color: #0c63e4;
    background-color: #e7f1ff;
}

.accordion-button-status {
    margin-left: auto;
    margin-right: 1rem;
} 