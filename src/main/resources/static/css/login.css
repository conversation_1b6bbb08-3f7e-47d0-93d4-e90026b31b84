/* Login Page Styles based on Figma: 登入頁_電腦 (Node 435:705) */
body {
    margin: 0;
    padding: 0;
    font-family: 'Noto Sans TC', sans-serif; /* From Figma */
    height: 100vh;
    /* overflow: hidden; */ /* Removed */
}

.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1558978434-9397227c8854?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max'); /* New Placeholder Mahjong BG */
    background-size: cover;
    background-position: center;
    filter: blur(3px); /* Optional: if a blur effect is desired on background */
    z-index: -2;
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5); /* Figma: Rectangle 18, fill rgba(0, 0, 0, 0.5) */
    position: relative;
    /* z-index: -1; */ /* Removed */
}

.login-box {
    background-color: rgba(0, 0, 0, 0.7); /* Adjusted for better readability on a dark bg image, Figma has white box (fill_XV1LD1 on Frame 1207 might be mistake, it's same as overlay) */
    /* Figma node Frame 1207 (id: 435:708) uses padding: 60px 80px, borderRadius: 20px */
    padding: 60px 80px;
    border-radius: 20px;
    width: 608px; /* Figma: Frame 1207 width */
    box-shadow: 0 0 15px rgba(0,0,0,0.2);
    color: #FFFFFF; /* Figma: Text color for labels & title */
    position: relative;
    z-index: 1;
}

.login-box h2 {
    /* Figma: 員工登入 (id: 435:710) - Noto Sans TC, Bold 700, 40px */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 40px;
    color: #FFFFFF; /* Figma: fill_WF6PJQ */
}

.login-box .form-label {
    /* Figma: 使用者帳號 (id: 435:713) - Noto Sans TC, Bold 700, 20px */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 20px;
    color: #FFFFFF; /* Figma: fill_WF6PJQ */
}

.login-box .form-control {
    /* Figma: Frame 87 (id: 435:714) - background #FFFFFF, borderRadius: 4px, padding: 16px 12px */
    /* Text inside like "請輸入帳號" (id: 435:715) - Noto Sans TC, Bold 700, 20px, color #B0B0B0 */
    background-color: #FFFFFF;
    border-radius: 4px;
    padding: 16px 12px;
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700; /* For placeholder text */
    font-size: 20px; /* For placeholder text */
    color: #333333; /* Actual input text color */
}

.login-box .form-control::placeholder {
    color: #B0B0B0; /* Figma: fill_IIMJY9 */
    font-weight: 700;
    font-size: 20px;
}

/* Style for the company select dropdown */
.login-box #company.form-select {
    background-color: #FFFFFF;
    border-radius: 4px;
    padding-top: 16px;    /* Match top padding of form-control */
    padding-bottom: 16px; /* Match bottom padding of form-control */
    padding-left: 12px;   /* Match left padding of form-control */
    padding-right: 12px;  /* Match right padding of form-control (though arrow takes space) */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700; /* Match placeholder/input text weight */
    font-size: 18px;  /* Match placeholder/input text font size */
    color: #333333; /* Match actual input text color */
    height: calc(2.25rem + 2 * 14px); /* Approximate height based on Bootstrap input sizing: line-height (1.5 * 1rem (font-size for bs)) + (2*padding) + (2*border-width). Adjusted for 20px font and 16px padding. */
    line-height: 1.5; /* Ensure text is vertically centered reasonably */
}

/* Ensure the selected option also reflects the desired font style if needed */
.login-box #company.form-select option {
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: normal; /* Options might not need to be bold */
    font-size: 18px; /* Options can be slightly smaller or match parent */
}

.btn-login-custom {
    /* Figma: button (id: I441:6569;441:6521) - background #D3C69B, text color #333333 */
    /* padding: 8px 20px, borderRadius: 4px, height: 68px */
    /* Text: Noto Sans TC, Bold 700, 24px */
    background-color: #D3C69B; /* Figma: fill_XFDFM6 */
    color: #333333; /* Figma: fill_6W0AHV */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 24px;
    padding: 8px 20px;
    border-radius: 4px;
    border: none; /* Figma shows no border */
    min-height: 68px; /* Figma height */
    line-height: normal; /* Adjust for vertical centering if needed */
    display: flex;
    justify-content: center;
    align-items: center;
}

.btn-login-custom:hover {
    background-color: #c0b58a; /* Darken a bit for hover */
    color: #333333;
}

/* Styles for error message from Figma: 登入頁_帳密輸入錯誤 (Node 435:755) */
/* Error text (id: 435:777) - Noto Sans TC, Medium 500, 18px, color #FF5E3C */
#error-message {
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 500;
    font-size: 18px;
    background-color: transparent; /* Assuming the red color is for text only */
    color: #FF5E3C; /* Figma: fill_3Q2DK0 */
    border: none;
    padding: 0.5rem 0;
    text-align: left; /* Figma shows it aligned with inputs */
}

/* Remove default alert padding if it conflicts with design */
.alert.d-none {
    display: none !important;
}

.alert:not(.d-none) {
    margin-bottom: 1rem; /* Spacing before form fields */
} 