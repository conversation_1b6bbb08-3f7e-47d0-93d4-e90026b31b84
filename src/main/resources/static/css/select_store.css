/* Select Store Page Styles based on Figma: 選擇登入門市_電腦 (Node 1580:11130) */
body {
    margin: 0;
    padding: 0;
    font-family: 'Noto Sans TC', sans-serif;
    height: 100vh;
    overflow: hidden;
}

.page-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1558978434-9397227c8854?ixlib=rb-4.0.3&q=80&fm=jpg&crop=entropy&cs=tinysrgb&w=1080&fit=max'); /* New Placeholder Mahjong BG */
    background-size: cover;
    background-position: center;
    filter: blur(3px);
    z-index: -2;
}

.select-store-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5); /* Figma: Rectangle 18, fill rgba(0, 0, 0, 0.5) */
    position: relative;
    z-index: -1;
}

.select-store-box {
    background-color: rgba(0, 0, 0, 0.7); /* Figma: Frame 1207 uses rgba(0,0,0,0.5) over image, text is white. Similar to login */
    padding: 60px 80px; /* Figma: Frame 1207 */
    border-radius: 20px; /* Figma: Frame 1207 */
    width: 608px; /* Figma: Frame 1207 width */
    box-shadow: 0 0 15px rgba(0,0,0,0.2);
    color: #FFFFFF;
}

.select-store-box h2 {
    /* Figma: 選擇登入門市 (id: 1580:11135) - Noto Sans TC, Bold 700, 40px, color #FFFFFF */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 40px;
    color: #FFFFFF;
}

.select-store-box .form-label {
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700; /* Assuming same style as login labels */
    font-size: 20px;
    color: #FFFFFF;
}

.select-store-box .form-select {
    /* Figma: Frame 87 (id: 1580:11139) for dropdown area - background #FFFFFF, borderRadius: 4px, padding: 16px 12px */
    /* Text "中山門市" (id: 1580:11140) - Noto Sans TC, Bold 700, 20px, color #333333 */
    background-color: #FFFFFF;
    border-radius: 4px;
    padding: 16px 12px;
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 20px;
    color: #333333; /* Figma: fill_AB80RX */
    min-height: calc(1.5em + 1rem + 2px + 16px * 2 - 16px); /* Match padding, approx 68px total height like buttons */
}

.btn-confirm-custom {
    /* Figma: button (id: I1580:11148;441:6521) "確認" - background #D3C69B, text color #333333 */
    background-color: #D3C69B; /* Figma: fill_XSAZQY */
    color: #333333; /* Figma: fill_AB80RX */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 24px; /* Figma: style_243KT4 */
    padding: 8px 20px;
    border-radius: 4px;
    border: none;
    min-height: 68px; /* Figma: height 68px */
    display: flex;
    justify-content: center;
    align-items: center;
}

.btn-confirm-custom:hover {
    background-color: #c0b58a;
    color: #333333;
}

.btn-back-custom {
    /* Figma: button (id: I1580:11147;441:6521) "返回登入畫面" - background #FFFFFF, text color #333333 */
    background-color: #FFFFFF; /* Figma: fill_OT2N4N */
    color: #333333;    /* Figma: fill_AB80RX */
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 700;
    font-size: 24px; /* Figma: style_243KT4 */
    padding: 8px 20px;
    border-radius: 4px;
    border: 1px solid #CCCCCC; /* Add a subtle border for white button */
    min-height: 68px; /* Figma: height 68px */
    display: flex;
    justify-content: center;
    align-items: center;
}

.btn-back-custom:hover {
    background-color: #f0f0f0;
    color: #333333;
}

#error-message-store {
    font-family: 'Noto Sans TC', sans-serif;
    font-weight: 500;
    font-size: 18px;
    background-color: transparent;
    color: #FF5E3C;
    border: none;
    padding: 0.5rem 0;
    text-align: left;
}

.alert.d-none {
    display: none !important;
}

.alert:not(.d-none) {
    margin-bottom: 1rem;
} 