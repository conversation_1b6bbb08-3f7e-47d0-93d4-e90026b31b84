/* order_detail.css */

.page-content h2 .text-muted {
    font-size: 0.9rem; /* Smaller font for order number next to title */
}

#order-detail-content .card {
    margin-bottom: 1.5rem; /* Consistent spacing between cards */
}

#order-detail-content .card-header {
    font-weight: 500; /* Slightly bolder headers */
    /* background-color: #f8f9fa; */ /* Optional: subtle background for headers */
}

#order-detail-content .card-body p {
    margin-bottom: 0.6rem; /* Spacing for paragraph items */
    font-size: 0.95rem;
}

#order-detail-content .card-body p strong {
    display: inline-block;
    min-width: 120px; /* Align keys for better readability */
    margin-right: 5px;
}

#order-items-detail-table th,
#order-items-detail-table td {
    font-size: 0.9rem;
    vertical-align: middle;
}

#dispatch-info-section .card-body p strong {
    min-width: 100px; /* Adjust for dispatch info section if needed */
}

#order-remarks {
    white-space: pre-wrap; /* Preserve line breaks in remarks */
    font-size: 0.95rem;
}

#order-actions-placeholder .btn {
    margin-left: 0.5rem; /* Spacing between action buttons */
}

#loading-indicator p {
    font-size: 1rem;
    color: #6c757d;
}

/* Specific emphasis for certain fields */
#orderStatusDescription {
    padding: 0.2em 0.4em;
    border-radius: 0.25rem;
    font-size: 1em; /* Make it slightly larger or normal relative to its p tag */
    /* Color will depend on status, consider JS to add classes like .status-draft, .status-approved */
}

/* Example of status-specific styling (could be added by JS) */
.status-draft { background-color: #ffc107; color: #333; }
.status-approved { background-color: #28a745; color: white; }
.status-cancelled { background-color: #dc3545; color: white; }
.status-pending { background-color: #17a2b8; color: white; }
.status-shipped-closed { background-color: #6c757d; color: white; } 

/* Custom styles for the order detail page */

.app-table th {
    font-weight: 600;
}

.table-header-white th {
    background-color: #ffffff !important; /* Use !important to override Bootstrap default styles if necessary */
    color: #212529; /* Ensure text is dark for readability on a white background */
} 