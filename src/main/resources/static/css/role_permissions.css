/* css/role_permissions.css - Styles for role & permission management pages */

.function-permission-item,
.field-permission-item {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid #eee;
    border-radius: 0.25rem;
}

.function-permission-item strong {
    display: block;
    margin-bottom: 0.25rem;
}

.function-permission-item .form-check-inline {
    margin-right: 1rem;
}

.field-permission-item .row > div {
    margin-bottom: 0.5rem;
}

/* For hierarchical display of functions */
.function-group {
    padding-left: 20px;
    border-left: 2px solid #f0f0f0;
    margin-left: 5px;
    margin-bottom: 10px;
}

.function-group-title {
    font-weight: bold;
    margin-bottom: 5px;
    cursor: pointer; /* If we make them collapsible via JS */
}

.permission-label {
    font-weight: normal;
    margin-left: 0.25rem;
}

/* Ensure table action buttons have some spacing */
#roles-table-body .btn-group .btn {
    margin-right: 5px;
}
#roles-table-body .btn-group .btn:last-child {
    margin-right: 0;
} 