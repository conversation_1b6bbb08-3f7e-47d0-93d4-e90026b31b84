.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.toolbar > div {
    display: flex;
    align-items: center;
}

#year-select, #month-select {
    width: 120px;
}

#keyword-search {
    width: 250px;
}

.config-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.config-row:last-child {
    border-bottom: none;
}

.sort-handle {
    cursor: grab;
}

.service-area-group {
    border-left: 3px solid #0d6efd;
    padding-left: 1rem;
    margin-bottom: 1rem;
}

.tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: #e9ecef;
    border-radius: 4px;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
} 