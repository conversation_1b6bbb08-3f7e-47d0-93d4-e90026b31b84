/* dispatch_product_order_form.css */

/* Custom styles for dispatch product order form */

#submit-order-btn:disabled {
    background-color: #6c757d; /* Bootstrap's secondary color (dark gray) */
    border-color: #6c757d;
    opacity: 0.65; /* Keep Bootstrap's default opacity for disabled state */
}

.filter-card .card-body, /* Reusing if applicable, or remove */
.card-header {
    /* padding: 1rem; */
}

/* Sub-order card styling */
.sub-order-card {
    border: 1px solid #dee2e6;
    background-color: #f8f9fa; /* Light background for sub-orders */
}

.sub-order-card .card-header {
    background-color: #e9ecef;
    font-weight: 500;
}

.main-product-item {
    background-color: #fff; /* White background for the main product section */
    padding: 1rem;
    border-radius: 0.25rem;
}

.main-product-item p:first-child:last-child {
    margin-bottom: 0; /* Remove margin if only placeholder text */
}

.sub-item-table th {
    font-size: 0.8rem;
    padding: 0.4rem;
    white-space: nowrap;
}
.sub-item-table td {
    font-size: 0.85rem;
    padding: 0.4rem;
    vertical-align: middle;
}

.sub-item-table .quantity-input {
    max-width: 70px;
    font-size: 0.85rem;
}

.sub-item-table .actions-cell .btn {
    padding: 0.15rem 0.3rem;
    font-size: 0.7rem;
}

/* Ensure form-control-sm and form-select-sm are used consistently for a compact form */
#dispatch-order-form .form-label {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

#dispatch-order-form .form-control-sm,
#dispatch-order-form .form-select-sm {
    font-size: 0.875rem;
}

/* Payment details section, similar to store order form */
.payment-entry .form-select-sm,
.payment-entry .form-control-sm {
    font-size: 0.875rem;
}

.payment-entry .remove-payment-btn {
    padding: 0.25rem 0.5rem;
}

#productSearchResultsModal {
    max-height: 450px;
    overflow-y: auto;
}

/* Totals section styling */
#overallNetAmount,
#overallTaxAmount,
#overallGrandTotalAmount {
    font-weight: bold;
}

#overallGrandTotalAmount {
    color: #B32B2E; /* Or your theme's primary highlight color for totals */
}

.main-product-display {
    padding: 0.5rem;
    border: 1px dashed #ccc;
    border-radius: .25rem;
    min-height: 80px; /* Placeholder height */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.main-product-display .btn {
    align-self: center;
}

/* Styles for the dispatch information section */
#dispatch-order-form .card-header {
    /* Example: background-color: #e9f5ff; */ 
}

#dispatch-order-form #installationAddress {
    /* Custom styling if needed */
}

#dispatch-order-form #dispatchNotes {
    min-height: 70px; 
}

#dispatch-order-form #technicianId {
    /* Custom styling if needed */
}

/* Table column width suggestions - specific to dispatch form's item table layout */
/* These might need adjustment based on the new "Item Type" column */
#order-items-table th:nth-child(1) { width: 20%; } /* 商品名稱 */
#order-items-table th:nth-child(2) { width: 12%; } /* 商品條碼 */
#order-items-table th:nth-child(3) { width: 8%; }  /* 單價 */
#order-items-table th:nth-child(4) { width: 7%; }  /* 數量 */
#order-items-table th:nth-child(5) { width: 10%; } /* 品項類型 - NEW */
#order-items-table th:nth-child(6) { width: 10%; } /* 折扣率 */
#order-items-table th:nth-child(7) { width: 10%; } /* 折扣額 */
#order-items-table th:nth-child(8) { width: 8%; }  /* 小計 */
#order-items-table th:nth-child(9) { width: 10%; } /* 倉庫 */
#order-items-table th:nth-child(10){ width: 10%; } /* 備註 */
#order-items-table th:nth-child(11){ width: 5%; }  /* 操作 */

/* Ensure modals are above the potential Bootstrap sticky header/sidebar if z-index issues arise */
/* This rule is general and could also be in main.css if not already present there. */
.modal {
    z-index: 1060; 
}
.modal-backdrop {
    z-index: 1055; 
}

/* Custom TAB header color */
#dispatch-group-tabs .nav-link.active {
    background-color: #D3C69B;
    border-color: #D3C69B;
    color: #495057; /* A darker text color for better contrast */
}

#dispatch-group-tabs .nav-link {
    color: #6c757d; /* A default color for inactive tabs */
} 