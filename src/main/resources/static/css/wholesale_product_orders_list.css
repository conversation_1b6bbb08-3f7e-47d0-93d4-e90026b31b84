/* dispatch_product_orders_list.css */

.filter-card .card-body {
    padding: 1rem; /* Adjust padding for a slightly more compact filter area if needed */
}

.filter-card .form-label {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

/* Ensure filter inputs and selects are consistently sized if using form-control-sm */
.filter-card .form-control-sm,
.filter-card .form-select-sm {
    font-size: 0.875rem;
    /* height: calc(1.5em + .5rem + 2px); Ensure consistent height with buttons if needed */
}

.app-table th {
    background-color: #f8f9fa; /* Light grey for table header */
    font-weight: 600;
    white-space: nowrap; /* Prevent table headers from wrapping */
    font-size: 0.85rem; /* Slightly smaller font for table headers */
}

.app-table td,
.app-table th {
    vertical-align: middle;
    font-size: 0.85rem; /* Consistent font size for table cells */
}

.dispatch-orders-table td {
    white-space: nowrap; /* Prevent content from wrapping to keep rows compact */
    overflow: hidden;
    text-overflow: ellipsis; /* Add ellipsis for overflowed content */
    max-width: 150px; /* Adjust as needed for columns that might have long text */
}

/* Allow specific columns to wrap if necessary, e.g., product name */
.dispatch-orders-table td.product-name-column {
    white-space: normal;
    max-width: 200px; /* Or a suitable width */
}


.actions-cell .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    margin-right: 0.25rem;
}

.actions-cell .btn i {
    margin-right: 0.25rem;
}

#pagination-controls .page-link {
    font-size: 0.9rem;
}

#no-data-message {
    font-size: 1.1rem;
    color: #6c757d; /* Bootstrap secondary color */
}

#search-orders-form .form-label {
    font-size: 0.875rem; /* Slightly smaller labels for filters */
    margin-bottom: 0.25rem;
}

#search-orders-form .form-control-sm,
#search-orders-form .form-select-sm {
    font-size: 0.875rem;
}

#orders-table th {
    font-size: 0.9rem;
    font-weight: 500; /* Slightly bolder for table headers */
    white-space: nowrap; /* Prevent header text wrapping */
}

#orders-table td {
    font-size: 0.875rem;
    vertical-align: middle;
}

#orders-table .btn-sm i {
    font-size: 0.9rem; /* Adjust icon size within small buttons if needed */
}

.pagination .page-link {
    font-size: 0.9rem;
    padding: 0.3rem 0.6rem;
} 