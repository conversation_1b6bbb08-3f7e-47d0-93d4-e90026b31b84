.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.filter-card .card-body {
    padding: 1rem;
}

.order-list .card {
    border-left-width: 5px;
    margin-bottom: 1rem;
}

.order-list .card-body {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.order-info p {
    margin-bottom: 0.25rem;
}

.order-status-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 0.8rem;
}

.order-type-new { border-left-color: #0d6efd; }
.order-type-urgent { border-left-color: #dc3545; }
.order-type-repair { border-left-color: #6c757d; }
.order-type-addon { border-left-color: #198754; }

.modal-body .form-check {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #eee;
}
.modal-body .form-check:last-child {
    border-bottom: none;
}

