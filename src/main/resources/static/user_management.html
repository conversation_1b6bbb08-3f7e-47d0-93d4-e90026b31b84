<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用者管理 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a>系統管理</a></li>
                            <li class="breadcrumb-item active" aria-current="page">使用者管理</li>
                        </ol>
                    </nav>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>使用者管理</h3>
                        <a href="user_form.html?mode=add" class="btn btn-primary">新增使用者</a> 
                    </div>

                    <div class="card mb-3">
                        <div class="card-body">
                            <form id="user-filter-form" class="row g-3 align-items-end">
                                <div class="col-md-3">
                                    <label for="filterEmployeeId" class="form-label">員工編號</label>
                                    <input type="text" id="filterEmployeeId" class="form-control">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterUserName" class="form-label">使用者姓名</label>
                                    <input type="text" id="filterUserName" class="form-control">
                                </div>
                                <div class="col-md-2">
                                    <label for="filterIsActive" class="form-label">狀態</label>
                                    <select id="filterIsActive" class="form-select">
                                        <option value="">全部</option>
                                        <option value="true">啟用</option>
                                        <option value="false">停用</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary w-100">查詢</button>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" id="clearUserFiltersBtn" class="btn btn-outline-secondary w-100">清除</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover app-table">
                            <thead>
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">員工編號</th>
                                    <th scope="col">使用者姓名</th>
                                    <th scope="col">歸屬公司別</th>
                                    <th scope="col">帳號類型</th>
                                    <th scope="col">帳號狀態</th>
                                    <th scope="col">最後更新時間</th>
                                    <th scope="col">操作</th>
                                </tr>
                            </thead>
                            <tbody id="users-table-body">
                            </tbody>
                        </table>
                    </div>
                    <div id="no-users-message" class="text-center mt-3 d-none">
                        <p>查無使用者資料</p>
                    </div>

                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="users-pagination-controls">
                        </ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script> 
    <script src="js/user_management.js"></script> 
</body>
</html> 