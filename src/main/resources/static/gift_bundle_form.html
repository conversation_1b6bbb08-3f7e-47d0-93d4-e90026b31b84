<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品套裝表單 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/gift_bundles.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a href="gift_bundles.html">商品套裝列表</a></li>
                            <li class="breadcrumb-item active" id="form-mode-breadcrumb">新增商品套裝</li>
                        </ol>
                    </nav>

                    <h3 id="form-title" class="mb-4">新增商品套裝</h3>
                    <div id="bundleFormErrorMessage" class="alert alert-danger d-none"></div>

                    <form id="gift-bundle-form" novalidate>
                        <input type="hidden" id="bundleId">

                        <div class="card mb-3">
                            <div class="card-header">套裝基本資料</div>
                            <div class="card-body row g-3">
                                <div class="col-md-6">
                                    <label for="bundleName" class="form-label">套裝名稱 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="bundleName" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="mainProductSearchInput" class="form-label">主商品 <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="mainProductDisplay" placeholder="尚未選擇主商品" readonly required>
                                        <input type="hidden" id="mainProductBarcode">
                                        <button class="btn btn-outline-secondary" type="button" id="selectMainProductBtn">選擇主商品</button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="startTime" class="form-label">套裝啟用時間 <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control" id="startTime" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="endTime" class="form-label">套裝結束時間 <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control" id="endTime" required>
                                </div>
                                <div class="col-12">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="isActive" checked>
                                        <label class="form-check-label" for="isActive">啟用此套裝</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>贈品列表</span>
                                <button type="button" class="btn btn-sm btn-success" id="addGiftItemBtn">新增贈品</button>
                            </div>
                            <div class="card-body">
                                <div id="gift-items-table-container" class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>贈品條碼</th>
                                                <th>贈品名稱</th>
                                                <th>數量 <span class="text-danger">*</span></th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="giftItemsTableBody"></tbody>
                                    </table>
                                </div>
                                <p id="noGiftItemsMessage" class="text-muted mt-2 d-none">尚未加入任何贈品。</p>
                            </div>
                        </div>

                        <div class="mt-4 d-flex justify-content-end">
                            <a href="gift_bundles.html" class="btn btn-outline-secondary me-2">返回列表</a>
                            <button type="submit" class="btn btn-primary" id="saveBundleBtn">儲存套裝</button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Product Search Modal (for main product and gift items) -->
    <div class="modal fade" id="productSearchModal" tabindex="-1" aria-labelledby="productSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productSearchModalLabel">搜尋商品</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="input-group mb-3">
                        <input type="text" id="productSearchKeywordInput" class="form-control" placeholder="輸入商品條碼或名稱關鍵字">
                        <button class="btn btn-outline-secondary" type="button" id="productSearchExecuteBtn"><i class="bi bi-search"></i></button>
                    </div>
                    <div id="productSearchResultsContainer" style="max-height: 300px; overflow-y: auto;">
                        <!-- Search results populated here -->
                    </div>
                    <p id="noModalProductResultsMessage" class="text-muted d-none mt-2">查無商品。</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/gift_bundle_form.js"></script>
</body>
</html> 