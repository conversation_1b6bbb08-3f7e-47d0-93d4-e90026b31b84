<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品列表 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <!-- <link rel="stylesheet" href="css/product_list.css"> TODO: Create this CSS file if needed -->
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a>系統管理</a></li>
                            <li class="breadcrumb-item active" aria-current="page">商品列表</li>
                        </ol>
                    </nav>

                    <h3>商品列表 (資料來源：正航資料庫)</h3>

                    <div class="card mb-3">
                        <div class="card-body">
                            <form id="filter-form" class="row g-3 align-items-end">
                                <div class="col-md-3">
                                    <label for="filterProductId" class="form-label">商品編號</label>
                                    <input type="text" id="filterProductId" class="form-control">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterCategory" class="form-label">商品類別</label>
                                    <input type="text" id="filterCategory" class="form-control">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterSubjectCategory" class="form-label">科目類別</label>
                                    <input type="text" id="filterSubjectCategory" class="form-control">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterKeyword" class="form-label">關鍵字 (名稱/條碼)</label>
                                    <input type="text" id="filterKeyword" class="form-control">
                                </div>
                                <div class="col-12 mt-3 text-end">
                                    <button type="submit" class="btn btn-primary me-2"><i class="bi bi-search"></i> 查詢</button>
                                    <button type="button" id="clear-filters-btn" class="btn btn-outline-secondary"><i class="bi bi-eraser"></i> 清除</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>商品編號</th>
                                    <th>商品類別</th>
                                    <th>科目類別</th>
                                    <th>商品名稱</th>
                                    <th>國際條碼</th>
                                    <th>銷售價格</th>
                                </tr>
                            </thead>
                            <tbody id="product-table-body">
                                <!-- Rows will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    <div id="no-data-message" class="text-center mt-3 d-none">
                        <p>查無商品資料</p>
                    </div>

                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination-controls">
                        </ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/product_list.js"></script> 
</body>
</html> 