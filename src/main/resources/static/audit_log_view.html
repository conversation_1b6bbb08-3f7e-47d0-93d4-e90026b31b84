<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作歷程查詢 - 東方不敗管理後台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item">系統管理</li>
                            <li class="breadcrumb-item active" aria-current="page">操作歷程查詢</li>
                        </ol>
                    </nav>
                    <h3>操作歷程查詢</h3>
                    <form id="auditLogFilterForm" class="row g-3 mb-3 align-items-end">
                        <div class="col-md-3">
                            <label for="startTime" class="form-label">起始時間</label>
                            <input type="datetime-local" class="form-control form-control-sm" id="startTime">
                        </div>
                        <div class="col-md-3">
                            <label for="endTime" class="form-label">結束時間</label>
                            <input type="datetime-local" class="form-control form-control-sm" id="endTime">
                        </div>
                        <div class="col-md-2">
                            <label for="dataTypeSelect" class="form-label">資料種類</label>
                            <select class="form-select form-select-sm" id="dataTypeSelect">
                                <option value="">全部</option>
                            </select>
                        </div>
                         <div class="col-md-2">
                            <label for="departmentSelect" class="form-label">部門</label>
                            <select class="form-select form-select-sm" id="departmentSelect">
                                <option value="">全部</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="actionTypeSelect" class="form-label">操作行為</label>
                            <select class="form-select form-select-sm" id="actionTypeSelect">
                                <option value="">全部</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="employeeIdFilter" class="form-label">員工編號</label>
                            <input type="text" class="form-control form-control-sm" id="employeeIdFilter" placeholder="輸入員工編號">
                        </div>
                        <div class="col-md-3">
                            <label for="userNameFilter" class="form-label">員工姓名</label>
                            <input type="text" class="form-control form-control-sm" id="userNameFilter" placeholder="輸入員工姓名 (模糊)">
                        </div>
                        <div class="col-md-4">
                            <label for="searchText" class="form-label">關鍵字搜尋</label>
                            <input type="text" class="form-control form-control-sm" id="searchText" placeholder="員工姓名/編號/單號/資料名稱">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary btn-sm w-100">查詢</button>
                        </div>
                    </form>

                    <table class="table table-striped table-hover table-sm app-table">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col" style="cursor:pointer;" data-sort="operationTime">操作時間 <i class="bi bi-arrow-down-up"></i></th>
                                <th scope="col">資料種類</th>
                                <th scope="col">單號/實體ID</th>
                                <th scope="col">資料名稱</th>
                                <th scope="col">操作行為</th>
                                <th scope="col">單位</th>
                                <th scope="col">操作人員</th>
                                <th scope="col">IP位址</th>
                                <th scope="col">詳情</th>
                            </tr>
                        </thead>
                        <tbody id="auditLogTableBody">
                            <tr><td colspan="10" class="text-center">請設定查詢條件並按查詢。</td></tr>
                        </tbody>
                    </table>
                    <nav id="paginationControls" aria-label="Audit Log Pagination"></nav>
                </div>
            </main>
        </div>
    </div>

    <!-- Details Modal -->
    <div class="modal fade" id="logDetailsModal" tabindex="-1" aria-labelledby="logDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logDetailsModalLabel">操作詳情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <pre><code id="logDetailsJson" class="json"></code></pre>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/audit_log_view.js"></script>
</body>
</html> 