<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>門市調撥單 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/store_transfer_orders.css"> 
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a href="store_transfer_orders.html">門市調撥單列表</a></li>
                            <li class="breadcrumb-item active" id="form-mode-breadcrumb">新增調撥單</li>
                        </ol>
                    </nav>

                    <h3 id="form-title" class="mb-4">新增門市調撥單</h3>
                    <div id="formErrorMessage" class="alert alert-danger d-none"></div>

                    <form id="transfer-order-form" novalidate>
                        <input type="hidden" id="transferOrderId">

                        <div class="card mb-3">
                            <div class="card-header">調撥資訊</div>
                            <div class="card-body row g-3">
                                <div class="col-md-4">
                                    <label for="requestingStoreIdSelect" class="form-label">申請門市 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="requestingStoreIdSelect" required>
                                        <option value="">載入中...</option>
                                        <!-- Operable stores populated by JS -->
                                    </select>
                                    <div class="invalid-feedback">請選擇申請門市。</div>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">申請人員</label>
                                    <input type="text" class="form-control" id="requestingUserName" readonly>
                                    <input type="hidden" id="requestingUserId">
                                </div>
                                <div class="col-md-4">
                                    <label for="supplyingStoreId" class="form-label">調撥對象門市 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="supplyingStoreId" required>
                                        <option value="">請選擇...</option>
                                        <!-- Store options populated by JS -->
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="notes" class="form-label">備註</label>
                                    <textarea class="form-control" id="notes" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>調撥商品列表</span>
                                <button type="button" class="btn btn-sm btn-success" id="addProductItemBtn">新增調撥商品</button>
                            </div>
                            <div class="card-body">
                                <div id="transfer-items-table-container" class="table-responsive">
                                    <table class="table table-sm" id="transfer-items-table">
                                        <thead>
                                            <tr>
                                                <th>國際條碼</th>
                                                <th>商品名稱</th>
                                                <th class="text-end">調撥數量 <span class="text-danger">*</span></th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="transferItemsTableBody"></tbody>
                                    </table>
                                </div>
                                <p id="noTransferItemsMessage" class="text-muted mt-2 d-none">尚未加入任何調撥商品。</p>
                            </div>
                        </div>

                        <div class="mt-4 d-flex justify-content-end">
                            <a href="store_transfer_orders.html" class="btn btn-outline-secondary me-2">取消並返回列表</a>
                            <button type="submit" class="btn btn-primary" id="saveTransferOrderBtn">確認送出調撥申請</button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Product Search Modal (for adding transfer items) -->
    <div class="modal fade" id="productSearchModal" tabindex="-1" aria-labelledby="productSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productSearchModalLabel">搜尋商品</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="input-group mb-3">
                        <input type="text" id="productSearchKeywordInput" class="form-control" placeholder="輸入商品條碼或名稱關鍵字">
                        <button class="btn btn-outline-secondary" type="button" id="productSearchExecuteBtn"><i class="bi bi-search"></i></button>
                    </div>
                    <div id="productSearchResultsContainer" style="max-height: 300px; overflow-y: auto;">
                        <!-- Search results populated here -->
                    </div>
                    <p id="noModalProductResultsMessage" class="text-muted d-none mt-2">查無商品。</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/store_transfer_order_form.js"></script>
</body>
</html> 