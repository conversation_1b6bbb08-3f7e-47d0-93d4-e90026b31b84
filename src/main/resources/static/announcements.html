<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統公告管理 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/announcements.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <!-- Breadcrumbs -->
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a>系統管理</a></li>
                            <li class="breadcrumb-item active" aria-current="page">系統公告管理</li>
                        </ol>
                    </nav>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>系統公告列表</h3>
                        <a href="announcement_form.html?mode=add" class="btn btn-primary">新增公告</a>
                    </div>

                    <!-- Search Filters -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <form id="filter-form" class="row g-3 align-items-end">
                                <div class="col-md-3">
                                    <label for="filterCategory" class="form-label">公告分類</label>
                                    <select id="filterCategory" class="form-select">
                                        <option value="">所有分類</option>
                                        <!-- Options populated by JS -->
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="filterStartDate" class="form-label">公告開始時間 (起)</label>
                                    <input type="datetime-local" id="filterStartDate" class="form-control">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterEndDate" class="form-label">公告結束時間 (迄)</label>
                                    <input type="datetime-local" id="filterEndDate" class="form-control">
                                </div> 
                                <div class="col-md-3">
                                    <label for="filterKeyword" class="form-label">關鍵字 (標題)</label>
                                    <input type="text" id="filterKeyword" class="form-control" placeholder="輸入公告標題關鍵字">
                                </div>
                                <div class="col-md-2">
                                    <label for="filterIsEnabled" class="form-label">是否啟用</label>
                                    <select id="filterIsEnabled" class="form-select">
                                        <option value="">全部</option>
                                        <option value="true">是</option>
                                        <option value="false">否</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="filterIsImportant" class="form-label">是否重要</label>
                                    <select id="filterIsImportant" class="form-select">
                                        <option value="">全部</option>
                                        <option value="true">是</option>
                                        <option value="false">否</option>
                                    </select>
                                </div>
                                <div class="col-md-2 align-self-end">
                                    <button type="submit" class="btn btn-primary w-100">查詢</button>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" id="clearFiltersBtn" class="btn btn-outline-secondary w-100">清除</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Announcements Table -->
                    <div class="table-responsive">
                        <table class="table table-hover app-table">
                            <thead>
                                <tr>
                                    <th>公告分類</th>
                                    <th>公告標題</th>
                                    <th>開始時間</th>
                                    <th>結束時間</th>
                                    <th>是否啟用</th>
                                    <th>是否重要</th>
                                    <th>更新時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="announcements-table-body">
                                <!-- Rows populated by JS -->
                            </tbody>
                        </table>
                    </div>
                    <div id="no-data-message" class="text-center mt-3 d-none">
                        <p>查無資料</p>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination-controls">
                            <!-- Pagination controls populated by JS -->
                        </ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/announcements.js"></script>
</body>
</html> 