<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>門市查詢 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item">系統管理</li>
                            <li class="breadcrumb-item active" aria-current="page">門市設定</li>
                        </ol>
                    </nav>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>門市查詢</h3>
                        <a href="store_form.html?mode=add" class="btn btn-primary">新增門市</a>
                    </div>

                    <div class="card mb-3">
                        <div class="card-body">
                            <form id="store-filter-form" class="row g-3 align-items-end">
                                <div class="col-md-3">
                                    <label for="filterRegion" class="form-label">地區</label>
                                    <select id="filterRegion" class="form-select">
                                        <option value="">全部地區</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="filterKeyword" class="form-label">關鍵字查詢 (門市名稱)</label>
                                    <div class="input-group">
                                        <input type="text" id="filterKeyword" class="form-control" placeholder="輸入門市名稱關鍵字">
                                        <button class="btn btn-outline-secondary" type="button" id="searchIconBtn"><i class="bi bi-search"></i></button>
                                    </div>
                                </div>
                                <div class="col-md-2 align-self-end">
                                    <button type="submit" class="btn btn-primary w-100">查詢</button>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" id="clearStoreFiltersBtn" class="btn btn-outline-secondary w-100">清除</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover app-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>門市代碼</th>
                                    <th>地區</th>
                                    <th>門市名稱</th>
                                    <th>歸屬公司別</th>
                                    <th>啟用狀態</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="stores-table-body">
                            </tbody>
                        </table>
                    </div>
                    <div id="no-stores-message" class="text-center mt-3 d-none">
                        <p>查無門市資料</p>
                    </div>

                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="stores-pagination-controls">
                        </ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script> 
    <script src="js/store_settings.js"></script> 
</body>
</html> 