<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>門市調撥單詳情 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/store_transfer_orders.css"> 
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a href="store_transfer_orders.html">門市調撥單列表</a></li>
                            <li class="breadcrumb-item active" aria-current="page">調撥單詳情</li>
                        </ol>
                    </nav>

                    <div id="loading-message" class="text-center mt-3"><p>載入調撥單詳情中...</p></div>
                    <div id="error-message" class="alert alert-danger d-none"></div>

                    <div id="transfer-order-detail-content" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3 id="detail-title">調撥單詳情</h3>
                             <div id="action-buttons-top" class="d-flex align-items-center">
                                <!-- <button id="backToListBtn" class="btn btn-outline-secondary btn-sm">返回列表</button> -->
                                <!-- Action buttons will be dynamically added here by JS based on status and user role -->
                                <button id="approveTransferBtn" class="btn btn-success btn-sm ms-2 d-none">核准</button>
                                <button id="rejectTransferBtn" class="btn btn-danger btn-sm ms-2 d-none">駁回</button>
                                <button id="confirmDispatchBtn" class="btn btn-info btn-sm ms-2 d-none">確認轉出</button>
                                <button id="confirmReceiveBtn" class="btn btn-info btn-sm ms-2 d-none">確認轉入</button>
                                <button id="cancelTransferBtn" class="btn btn-warning btn-sm ms-2 d-none">取消調撥</button>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header">調撥單資訊</div>
                            <div class="card-body row g-3">
                                <div class="col-md-4"><strong>調撥單號:</strong> <span id="detailTransferOrderNumber"></span></div>
                                <div class="col-md-4"><strong>申請狀態:</strong> <span id="detailTransferStatus"></span></div>
                                <div class="col-md-4"><strong>申請日期:</strong> <span id="detailRequestDate"></span></div>
                                <div class="col-md-4"><strong>申請門市:</strong> <span id="detailRequestingStore"></span></div>
                                <div class="col-md-4"><strong>申請人員:</strong> <span id="detailRequestingUser"></span></div>
                                <div class="col-md-4"><strong>調撥門市:</strong> <span id="detailSupplyingStore"></span></div>
                                <div class="col-md-4"><strong>出庫日期:</strong> <span id="detailDispatchDate"></span></div>
                                <div class="col-md-4"><strong>出庫人員:</strong> <span id="detailDispatchingUser"></span></div>
                                <div class="col-md-4"><strong>入庫日期:</strong> <span id="detailReceivedDate"></span></div>
                                 <div class="col-md-4"><strong>入庫人員:</strong> <span id="detailReceivingUser"></span></div>
                                <div class="col-12"><strong>備註:</strong> <span id="detailNotes"></span></div>
                            </div>
                        </div>

                        <div class="card mb-3">
                            <div class="card-header">調撥商品明細</div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>國際條碼</th>
                                                <th>商品名稱</th>
                                                <th class="text-end">申請數量</th>
                                                <th class="text-end">已出庫數量</th>
                                                <th class="text-end">已入庫數量</th>
                                            </tr>
                                        </thead>
                                        <tbody id="transfer-order-items-table-body"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Rejection Reason Modal -->
    <div class="modal fade" id="rejectionReasonModal" tabindex="-1" aria-labelledby="rejectionReasonModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectionReasonModalLabel">輸入駁回原因</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="rejectionReasonForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="rejectionReasonInput" class="form-label">駁回原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="rejectionReasonInput" rows="3" required maxlength="500"></textarea>
                            <div class="invalid-feedback">駁回原因不得為空。</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-danger" id="confirmRejectBtn">確認駁回</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Confirmation/Quantity Input Modal -->
    <div class="modal fade" id="transferActionModal" tabindex="-1" aria-labelledby="transferActionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="transferActionModalLabel">確認操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="transferActionForm">
                    <div class="modal-body">
                        <p><strong>調撥單號:</strong> <span id="modalActionOrderNumber"></span></p>
                        <p>請確認或輸入各商品的<strong id="modalActionType"></strong>數量：</p>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>商品條碼</th>
                                        <th>商品名稱</th>
                                        <th class="text-end">申請數量</th>
                                        <th class="text-end"><span id="modalActionQuantityLabel"></span>數量</th>
                                    </tr>
                                </thead>
                                <tbody id="transferActionItemsTableBody"></tbody>
                            </table>
                        </div>
                        <div id="actionFormErrorMessage" class="alert alert-danger d-none mt-2"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="confirmTransferActionBtn">確認送出</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/store_transfer_order_detail.js"></script>
</body>
</html> 