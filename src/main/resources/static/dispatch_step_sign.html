<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <title>派工流程 - 客戶簽收</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dispatch_tech_detail.css">
    <style>
        .signature-pad-container {
            border: 1px dashed #ccc;
            border-radius: .375rem;
            position: relative;
            width: 100%;
            height: 250px;
        }
        #signature-pad {
            display: block;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dispatch_tech_list.html">技師派工單列表</a></li>
                            <li class="breadcrumb-item"><a id="detail-breadcrumb-link" href="#">派工單詳情</a></li>
                            <li class="breadcrumb-item active" aria-current="page">客戶簽收</li>
                        </ol>
                    </nav>
                    
                    <div id="info-card-container"></div>

                    <!-- New Cost Summary Section -->
                    <div class="card mb-3">
                        <div class="card-header"><h5>費用明細</h5></div>
                        <div class="card-body" id="cost-summary-list">
                            <!-- Cost summary will be populated by JS -->
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header"><h5>客戶簽名</h5></div>
                        <div class="card-body">
                            <div class="signature-pad-container">
                                <canvas id="signature-pad"></canvas>
                            </div>
                            <div class="mt-2 text-center">
                                <button id="clear-signature-btn" class="btn btn-sm btn-outline-secondary">清除</button>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-secondary me-3" id="back-btn">返回</button>
                        <button type="button" class="btn btn-primary" id="complete-btn">完成簽收</button>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/dispatch_order_header.js"></script>
    <script src="js/dispatch_step_sign.js"></script>
</body>
</html> 