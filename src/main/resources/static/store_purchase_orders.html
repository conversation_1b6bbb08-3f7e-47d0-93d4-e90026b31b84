<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>門市進貨單列表 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/store_purchase_orders.css"> 
</head>
<body>
<div class="app-wrapper">
    <div id="header-placeholder"></div>

    <div class="main-container">
        <div id="sidebar-placeholder"></div>
        <div class="page-content">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item">庫存管理</li>
                            <li class="breadcrumb-item active" aria-current="page">門市批發進貨管理</li>
                </ol>
            </nav>

                    <h3>門市進貨單列表</h3>

                    <div class="card mb-3">
                        <div class="card-body">
                            <form id="filter-form" class="row g-3 align-items-end">
                                <div class="col-md-3">
                                    <label for="filterShipmentDateFrom" class="form-label">出貨日期 (起)</label>
                                    <input type="date" id="filterShipmentDateFrom" class="form-control form-control-sm">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterShipmentDateTo" class="form-label">出貨日期 (迄)</label>
                                    <input type="date" id="filterShipmentDateTo" class="form-control form-control-sm">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterStatus" class="form-label">進貨狀態</label>
                                    <select id="filterStatus" class="form-select form-select-sm">
                                        <option value="">全部狀態</option>
                                        <!-- Status options populated by JS -->
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="filterKeyword" class="form-label">關鍵字 (單號)</label>
                                    <input type="text" id="filterKeyword" class="form-control form-control-sm" placeholder="輸入進貨單號">
                                </div>
                                <div class="col-12 text-end mt-2">
                                    <button type="button" id="clearFiltersBtn" class="btn btn-outline-secondary btn-sm">清除</button>
                                    <button type="submit" class="btn btn-primary btn-sm">查詢</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover table-sm align-middle app-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>進貨單編號</th>
                                    <th>出貨時間</th>
                                    <th>收貨對象</th>
                                    <th>建單人員</th>
                                    <th>進貨單狀態</th>
                                    <th>原單編號</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="purchase-orders-table-body">
                                <!-- Rows populated by JS -->
                            </tbody>
                        </table>
                    </div>
                    <div id="no-data-message" class="text-center mt-3 d-none">
                        <p>查無進貨單資料</p>
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination-controls"></ul>
                    </nav>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/store_purchase_orders.js"></script>
</body>
</html> 