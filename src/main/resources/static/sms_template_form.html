<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡訊模板設定 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a href="sms_templates.html">簡訊模板管理</a></li>
                            <li class="breadcrumb-item active" aria-current="page" id="sms-form-mode-breadcrumb">新增簡訊模板</li>
                        </ol>
                    </nav>

                    <h3 id="sms-form-title" class="mb-4">新增簡訊模板</h3>
                    <div id="sms-form-error-message" class="alert alert-danger d-none" role="alert"></div>

                    <form id="sms-template-form">
                        <input type="hidden" id="smsTemplateId">
                        <div class="card">
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="templateName" class="form-label">模板名稱 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="templateName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="templateType" class="form-label">模板類型</label>
                                    <input type="text" class="form-control" id="templateType" placeholder="例如: BIRTHDAY_GIFT, PROMOTION (英文大寫與底線)">
                                    <div class="form-text">用於系統內部歸類，建議使用英文大寫與底線。</div>
                                </div>
                                <div class="mb-3">
                                    <label for="smsContent" class="form-label">簡訊內容 <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="smsContent" rows="5" required></textarea>
                                    <div class="form-text">可使用預留位置，例如: [會員姓名], [門市名稱], [訂單編號], [優惠券代碼] 等。</div>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-secondary me-2" id="cancelSmsTemplateFormBtn">取消</button>
                                    <button type="submit" class="btn btn-primary" id="saveSmsTemplateBtn">儲存模板</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/sms_template_form.js"></script>
</body>
</html> 