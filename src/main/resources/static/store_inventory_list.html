<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>門市商品庫存列表 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/store_inventory_list.css"> 
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item">庫存管理</li>
                            <li class="breadcrumb-item active" aria-current="page">門市商品庫存查詢</li>
                        </ol>
                    </nav>

                    <h3>門市商品庫存列表</h3>

                    <div class="card mb-3">
                        <div class="card-body">
                            <form id="inventory-filter-form" class="row g-3 align-items-end">
                                <div class="col-md-4">
                                    <label for="filterStore" class="form-label">選擇門市</label>
                                    <select id="filterStore" class="form-select form-select-sm">
                                        <option value="">請選擇門市...</option>
                                        <!-- Store options populated by JS -->
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="filterKeyword" class="form-label">關鍵字 (條碼/品名)</label>
                                    <input type="text" id="filterKeyword" class="form-control form-control-sm" placeholder="輸入商品條碼或名稱">
                                </div>
                                <div class="col-md-2">
                                   <button type="button" id="clearFiltersBtn" class="btn btn-outline-secondary btn-sm w-100">清除</button>
                                </div>
                                <div class="col-md-3 align-self-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-search"></i> 查詢
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover app-table">
                            <thead>
                                <tr>
                                    <th>門市</th>
                                    <th>商品條碼</th>
                                    <th>商品名稱</th>
                                    <th class="text-end">庫存數量</th>
                                    <th>最後庫存異動時間</th>
                                </tr>
                            </thead>
                            <tbody id="inventory-table-body">
                                <!-- Inventory rows will be populated here -->
                            </tbody>
                        </table>
                    </div>
                    <div id="no-inventory-message" class="text-center mt-3 d-none">
                        <p>查無庫存資料或請先選擇門市。</p>
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination-controls"></ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/store_inventory_list.js"></script>
</body>
</html> 