<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增派工商品訂單 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dispatch_product_order_form.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>

        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <div class="page-content">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                        <li class="breadcrumb-item"><a href="dispatch_product_orders_list.html">派工商品訂單查詢</a></li>
                        <li class="breadcrumb-item active" aria-current="page" id="form-title">新增派工商品訂單</li>
                    </ol>
                </nav>
                
                <h2 id="page-main-title">
                    新增派工商品訂單
                    <span id="currentStatusBadge" class="badge rounded-pill bg-secondary fs-6 ms-2" style="display: none;"></span>
                </h2>

                <form id="dispatch-order-form" class="mt-3">
                    
                    <!-- 訂單資訊 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            訂單資訊
                            <span id="orderNumberDisplay" class="text-muted ms-3 fw-normal"></span>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="companyDivision" class="form-label">公司別 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="companyDivision" required disabled></select>
                                </div>
                                <div class="col-md-3">
                                    <label for="storeSelect" class="form-label">開單門市</label>
                                    <select class="form-select" id="storeSelect"></select>
                                </div>
                                <div class="col-md-3">
                                    <label for="distributorSelect" class="form-label">銷售據點</label>
                                    <select class="form-select" id="distributorSelect"></select>
                                </div>
                                <div class="col-md-3">
                                    <label for="salespersonSelect" class="form-label">銷售人員</label>
                                    <select class="form-select" id="salespersonSelect"></select>
                                </div>
                                <div class="col-md-3">
                                    <label for="orderDate" class="form-label">訂單日期 <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="orderDate" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="card mb-3">
                        <div class="card-header">客戶資訊</div>
                        <div class="card-body">
                            <div class="row g-3 align-items-center">
                                <div class="col-md-4">
                                    <label for="customerPhoneSearch" class="form-label">會員電話查詢</label>
                                        <input type="text" class="form-control" id="customerPhoneSearch" placeholder="輸入電話號碼">
                                    </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-primary w-100" type="button" id="searchCustomerBtn">會員查詢</button>
                                </div>
                                <div class="col-md-3">
                                    <label for="customerName" class="form-label">會員姓名</label>
                                    <input type="text" class="form-control" id="customerName" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label for="customerPhoneDisplay" class="form-label">會員電話</label>
                                    <input type="text" class="form-control" id="customerPhoneDisplay" readonly>
                                </div>
                                <input type="hidden" id="customerId">
                            </div>
                        </div>
                    </div>

                    <!-- 搜尋與篩選 -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="productKeyword" class="form-label">商品關鍵字</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="productKeyword" placeholder="請填入關鍵字">
                                        <button class="btn btn-outline-secondary" type="button" id="dispatch-product-search-btn">
                                            <i class="bi bi-search"></i>
                                    </button>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="promotionSelect" class="form-label">選擇優惠活動</label>
                                    <select class="form-select" id="promotionSelect">
                                        <option value="" selected>無</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="benefitSelect" class="form-label">選擇會員福利</label>
                                    <select class="form-select" id="benefitSelect">
                                        <option value="" selected>無</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- TAB 頁籤導航 -->
                    <ul class="nav nav-tabs" id="dispatch-group-tabs" role="tablist">
                        <!-- TABs 將由 JS 動態生成 -->
                    </ul>

                    <!-- TAB 內容 -->
                    <div class="tab-content" id="dispatch-group-tab-content">
                        <!-- TAB Panes 將由 JS 動態生成 -->
                    </div>

                    <!-- Dispatch Specific Information -->
                    <div class="card mb-3">
                        <div class="card-header">派工資訊</div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="contactName" class="form-label">聯絡人姓名</label>
                                    <input type="text" class="form-control" id="contactName">
                                </div>
                                <div class="col-md-6">
                                    <label for="contactPhone" class="form-label">聯絡電話</label>
                                    <input type="tel" class="form-control" id="contactPhone">
                                </div>
                                <div class="col-12">
                                    <label class="form-label">安裝地址</label>
                                    <div id="installation-address-component-placeholder"></div>
                                </div>
                                <div class="col-12">
                                    <label for="installationAddress" class="form-label">完整地址</label>
                                    <input type="text" class="form-control" id="installationAddress" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label for="installationDate" class="form-label">預計安裝日期</label>
                                    <input type="date" class="form-control" id="installationDate">
                                    <div class="invalid-feedback" id="installationDateError">
                                        預計安裝日期不能選擇今天以前的日期
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="installationTimeSlot" class="form-label">預計安裝時段</label>
                                    <select class="form-select" id="installationTimeSlot">
                                        <option value="">請選擇</option>
                                        <option value="MORNING">上午 (09:00-12:00)</option>
                                        <option value="AFTERNOON">下午 (13:00-17:00)</option>
                                        <option value="EVENING">晚上 (18:00-21:00)</option>
                                        <option value="ANY">皆可</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="technicianId" class="form-label">指派技師</label>
                                    <select class="form-select" id="technicianId">
                                        <option value="">稍後指派</option>
                                        <!-- TODO: Populate with technicians from API -->
                                    </select>
                                </div>
                                <div class="col-md-4" hidden>
                                    <label for="expectedCompletionDate" class="form-label">預計完工日期</label>
                                    <input type="date" class="form-control" id="expectedCompletionDate">
                                </div>
                                <div class="col-12">
                                    <label for="dispatchNotes" class="form-label">派工備註 (給技師)</label>
                                    <textarea class="form-control" id="dispatchNotes" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Amounts (這部分保持在 TAB 外部，計算整個訂單的總額) -->
                    <div class="card mb-3 mt-3">
                        <div class="card-header">金額總計</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 offset-md-6">
                                    <dl class="row text-end">
                                        <dt class="col-sm-6">商品總金額 (不含贈品):</dt>
                                        <dd class="col-sm-6" id="productsTotalAmount">NT$ 0</dd>
                                        <dt class="col-sm-6">折扣總金額:</dt>
                                        <dd class="col-sm-6" id="discountAmount">NT$ 0</dd>
                                        <dt class="col-sm-6">小計 (未稅):</dt>
                                        <dd class="col-sm-6" id="netAmount">NT$ 0</dd>
                                        <dt class="col-sm-6">營業稅 (5%):</dt>
                                        <dd class="col-sm-6" id="taxAmount">NT$ 0</dd>
                                        <dt class="col-sm-6 fs-5">應付總金額:</dt>
                                        <dd class="col-sm-6 fs-5 fw-bold" id="grandTotalAmount">NT$ 0</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Info -->
                    <div class="card mb-3">
                        <div class="card-header">發票資訊</div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label d-block">開立發票</label>
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="invoiceTypeBtn" id="invoiceBtnTwoPart" value="2" autocomplete="off" checked>
                                    <label class="btn btn-outline-primary" for="invoiceBtnTwoPart">開立二聯發票</label>
                                    <input type="radio" class="btn-check" name="invoiceTypeBtn" id="invoiceBtnThreePart" value="3" autocomplete="off">
                                    <label class="btn btn-outline-primary" for="invoiceBtnThreePart">開立三聯發票</label>
                                    <input type="radio" class="btn-check" name="invoiceTypeBtn" id="invoiceBtnNone" value="0" autocomplete="off">
                                    <label class="btn btn-outline-primary" for="invoiceBtnNone">不開立發票</label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label d-block">稅務類型</label>
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="taxTypeBtn" id="taxTypeExclusive" value="exclusive" autocomplete="off" checked>
                                    <label class="btn btn-outline-secondary" for="taxTypeExclusive">稅外</label>
                                    <input type="radio" class="btn-check" name="taxTypeBtn" id="taxTypeInclusive" value="inclusive" autocomplete="off">
                                    <label class="btn btn-outline-secondary" for="taxTypeInclusive">稅內</label>
                                </div>
                            </div>
                            <div id="invoice-details-container" class="row g-3">
                                <div class="col-md-4">
                                    <label for="storeInvoiceAmount" class="form-label">開立發票金額(門市)</label>
                                    <input type="number" class="form-control" id="storeInvoiceAmount">
                                </div>
                                <div class="col-md-4">
                                    <label for="storeTaxIdNumber" class="form-label">門市發票統編</label>
                                    <input type="text" class="form-control" id="storeTaxIdNumber">
                                </div>
                                <div class="col-md-4">
                                    <label for="storeInvoiceCompanyTitle" class="form-label">門市發票抬頭</label>
                                    <input type="text" class="form-control" id="storeInvoiceCompanyTitle">
                                </div>
                                <div class="col-md-4">
                                    <label for="taxIdNumber" class="form-label">統一編號</label>
                                    <input type="text" class="form-control" id="taxIdNumber" disabled>
                                </div>
                                <div class="col-md-4">
                                    <label for="invoiceCompanyTitle" class="form-label">發票抬頭</label>
                                    <input type="text" class="form-control" id="invoiceCompanyTitle" disabled>
                                </div>
                                <div class="col-md-4">
                                    <label for="invoiceDate" class="form-label">發票日期</label>
                                    <input type="date" class="form-control" id="invoiceDate">
                                </div>
                                <div class="col-md-4">
                                    <label for="invoiceNumber" class="form-label">發票號碼</label>
                                    <input type="text" class="form-control" id="invoiceNumber">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="card mb-3">
                        <div class="card-header">付款方式</div>
                        <div class="card-body">
                             <div class="mb-3">
                                <label class="form-label">新增付款方式</label>
                                <div id="add-payment-buttons">
                                    <button type="button" class="btn btn-success me-2" data-payment-type="cash">現金</button>
                                    <button type="button" class="btn btn-success me-2" data-payment-type="credit_card">信用卡</button>
                                    <button type="button" class="btn btn-success me-2" data-payment-type="bank_transfer">匯款</button>
                                    <button type="button" class="btn btn-info me-2" data-payment-type="technician_collection">技師代收款</button>
                                    <button type="button" class="btn btn-info" data-payment-type="staff_collection">人員代收款</button>
                                </div>
                            </div>
                            <div id="payment-methods-container" class="mb-3">
                                <!-- Payment methods added by JS -->
                            </div>
                            <div class="row mt-3">
                                 <div class="col-md-6 offset-md-6 text-end">
                                    <p class="mb-1">已付金額: <span id="totalPaidAmountDisplay" class="fw-bold">NT$ 0</span></p>
                                    <p class="mb-0">待付差額: <span id="amountDueDisplay" class="fw-bold text-danger">NT$ 0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Remarks -->
                    <div class="card mb-3">
                        <div class="card-header">訂單備註 (給內部)</div>
                        <div class="card-body">
                            <textarea class="form-control" id="orderRemarks" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="text-center mt-4 mb-5" id="form-action-buttons">
                        <!-- Default buttons -->
                        <button type="button" class="btn btn-danger me-2" id="delete-btn" style="display: none;">刪除</button>
                        <button type="button" class="btn btn-secondary me-2" id="save-draft-btn" style="display: none;">存檔</button>
                        <button type="button" class="btn btn-primary" id="submit-order-btn" style="display: none;">送出</button>
                        <button type="button" id="checkout-btn" class="btn btn-success" style="display: none;">確認結帳送出</button>

                        <!-- Buttons for status 40 -->
                        <!-- <button type="button" class="btn btn-warning" id="hq-return-btn" style="display: none;">退回補正</button>
                        <button type="button" class="btn btn-danger" id="hq-reject-btn" style="display: none;">訂單駁回</button>
                        <button type="button" class="btn btn-success" id="hq-approve-btn" style="display: none;">審核通過</button> -->
                        <button type="button" class="btn btn-secondary me-2" id="save-draft-btn" style="display: none;">存檔</button>
                        
                        <!-- Button for status 43 -->
                        <button type="button" class="btn btn-primary" id="resubmit-for-hq-review-btn" style="display: none;">送出審核</button>
                        
                        <!-- Button for status 46 -->
                        <button type="button" class="btn btn-info" id="copy-order-btn" style="display: none;">複製建立訂單</button>
                        
                        <!-- Button for status 49 -->
                        <button type="button" class="btn btn-primary" id="create-dispatch-repair-btn" style="display: none;">建立派工單</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div class="modal fade" id="productSearchModal" tabindex="-1" aria-labelledby="productSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productSearchModalLabel">搜尋並選擇商品</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="productSearchKeywordModal" placeholder="輸入商品條碼或名稱">
                        <button class="btn btn-outline-secondary" type="button" id="execProductSearchModalBtn"><i class="bi bi-search"></i></button>
                    </div>
                    <div class="list-group" id="productSearchResultsModal">
                        <!-- Search results here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="quickCreateCustomerModal" tabindex="-1" aria-labelledby="quickCreateCustomerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quickCreateCustomerModalLabel">快速新增會員</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quick-customer-form">
                        <div class="mb-3 row">
                            <label for="modalCustomerName" class="col-sm-3 col-form-label">會員姓名 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="modalCustomerName" required>
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="modalCustomerPhone" class="col-sm-3 col-form-label">會員電話 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="tel" class="form-control" id="modalCustomerPhone" required>
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="modalCustomerAddress" class="col-sm-3 col-form-label">地址</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="modalCustomerAddress">
                            </div>
                        </div>
                         <div class="mb-3 row">
                            <label for="modalCustomerEmail" class="col-sm-3 col-form-label">Email</label>
                            <div class="col-sm-9">
                                <input type="email" class="form-control" id="modalCustomerEmail">
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="modalCustomerRemarks" class="col-sm-3 col-form-label">備註</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="modalCustomerRemarks" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveQuickCustomerBtn">儲存會員</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="customerSelectModal" tabindex="-1" aria-labelledby="customerSelectModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="customerSelectModalLabel">選擇會員</h5>
                    <p class="ms-3 mb-0 text-muted">查到多筆符合條件的會員，請選擇一位</p>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="list-group" id="customerSelectModalBody">
                        <!-- Customer list will be populated here by JS -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/address_component.js"></script>
    <script src="js/dispatch_product_order_form.js"></script>
</body>
</html> 