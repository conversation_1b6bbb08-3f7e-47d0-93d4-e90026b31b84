<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>門市日結</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <div class="page-content p-4">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                        <li class="breadcrumb-item">門市管理</li>
                        <li class="breadcrumb-item active" aria-current="page">門市日結</li>
                    </ol>
                </nav>

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">門市日結</h2>
                </div>

                <!-- Main Content -->
                <div class="card">
                    <div class="card-body">
                        <!-- Date and Store Selection -->
                        <div class="row mb-3 align-items-end">
                            <div class="col-md-4">
                                <label for="settlementDate" class="form-label">日結日期</label>
                                <input type="date" class="form-control" id="settlementDate">
                            </div>
                            <div class="col-md-4">
                                <label for="storeSelect" class="form-label">門市</label>
                                <select class="form-select" id="storeSelect"></select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary" id="queryBtn">查詢</button>
                            </div>
                        </div>
                        
                        <!-- Settlement Details -->
                        <div id="settlementDetails" class="d-none">
                            <!-- Sales & Returns -->
                            <hr>
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>銷售 <span class="badge bg-secondary" id="salesTotal">0</span></h5>
                                    <div class="accordion" id="salesAccordion">
                                        <!-- Sales details will be populated here -->
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h5>銷退 <span class="badge bg-secondary" id="returnsTotal">0</span></h5>
                                     <div class="accordion" id="returnsAccordion">
                                        <!-- Returns details will be populated here -->
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <!-- Income -->
                            <h5>收入</h5>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">現金</label>
                                    <input type="text" class="form-control" id="cashIncome" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">刷卡</label>
                                    <input type="text" class="form-control" id="creditCardIncome" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">匯款</label>
                                    <input type="text" class="form-control" id="remittanceIncome" readonly>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">未收尾款</label>
                                    <input type="text" class="form-control" id="unpaidBalance" readonly>
                                </div>
                                 <div class="col-md-4">
                                    <label class="form-label">回款金額</label>
                                    <input type="text" class="form-control" id="repaymentAmount" readonly>
                                </div>
                            </div>
                            <hr>
                             <!-- Expenditure -->
                            <h5>支出</h5>
                            <div id="expenditureList">
                                <!-- Expenditure items will be added here -->
                            </div>
                            <button class="btn btn-outline-primary btn-sm mt-2" id="addExpenditureBtn">新增支出</button>
                            <hr>
                            <!-- Summary -->
                            <h5>結算</h5>
                            <div class="row g-3 align-items-end">
                                <div class="col-md-3">
                                    <label class="form-label">本日營收</label>
                                    <input type="text" class="form-control" id="totalRevenue" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">本日支出</label>
                                    <input type="text" class="form-control" id="totalExpenditure" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">本日應回款金額</label>
                                    <input type="text" class="form-control fw-bold" id="expectedRepayment" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label for="actualCashIncome" class="form-label">實際現金收入</label>
                                    <input type="number" class="form-control" id="actualCashIncome" placeholder="輸入實際金額">
                                </div>
                                 <div class="col-md-9">
                                    <label class="form-label">備註</label>
                                    <textarea class="form-control" id="remarks" rows="1"></textarea>
                                </div>
                            </div>
                             <div class="d-flex justify-content-end mt-4">
                                <button class="btn btn-success" id="submitSettlementBtn">確認送出</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal for Unsettled Reminder -->
    <div class="modal fade" id="unsettledModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">提醒</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p>尚有未完成日結：<span id="unsettledDate"></span></p>
            <p>請先完成該日結</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-primary" id="confirmUnsettledBtn">確認</button>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/store_daily_settlement_form.js"></script>
</body>
</html> 