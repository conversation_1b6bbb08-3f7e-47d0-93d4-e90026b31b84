<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品設定表單移除按鈕修正測試 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        body { padding: 20px; }
        .demo-section { margin-bottom: 30px; }
        .discount-row {
            padding-bottom: 15px;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .discount-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>商品設定表單移除按鈕修正測試</h2>
        <p class="text-muted">此頁面展示職權折扣設定區塊中移除按鈕的修正效果</p>

        <div class="demo-section">
            <h4>修正前的問題</h4>
            <div class="alert alert-warning">
                <ul class="mb-0">
                    <li>移除按鈕使用文字「移除」而不是圖示</li>
                    <li>按鈕樣式為 <code>btn btn-danger</code>，與系統其他移除按鈕不一致</li>
                    <li>缺少 <code>title</code> 屬性作為提示文字</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h4>修正後的效果示例</h4>
            
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    職權折扣設定
                    <button type="button" class="btn btn-sm btn-outline-success">
                        <i class="bi bi-plus-circle"></i> 新增角色折扣
                    </button>
                </div>
                <div class="card-body">
                    <!-- 第一個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option selected>系統管理員</option>
                                <option>部門主管</option>
                                <option>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="500" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-sm btn-outline-danger w-100" title="移除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第二個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option>系統管理員</option>
                                <option selected>部門主管</option>
                                <option>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="300" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-sm btn-outline-danger w-100" title="移除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第三個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option>系統管理員</option>
                                <option>部門主管</option>
                                <option selected>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="100" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-sm btn-outline-danger w-100" title="移除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>修正前後對比</h4>
            <div class="row">
                <div class="col-md-6">
                    <h6>修正前</h6>
                    <div class="card">
                        <div class="card-body">
                            <div class="row g-3 align-items-center">
                                <div class="col-md-5">
                                    <select class="form-select">
                                        <option selected>系統管理員</option>
                                    </select>
                                </div>
                                <div class="col-md-5">
                                    <div class="input-group">
                                        <span class="input-group-text">NT $</span>
                                        <input type="number" class="form-control" value="500">
                                    </div>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="button" class="btn btn-danger w-100">移除</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>修正後</h6>
                    <div class="card">
                        <div class="card-body">
                            <div class="row g-3 align-items-center">
                                <div class="col-md-5">
                                    <select class="form-select">
                                        <option selected>系統管理員</option>
                                    </select>
                                </div>
                                <div class="col-md-5">
                                    <div class="input-group">
                                        <span class="input-group-text">NT $</span>
                                        <input type="number" class="form-control" value="500">
                                    </div>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="button" class="btn btn-sm btn-outline-danger w-100" title="移除">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>程式碼修正重點</h4>
            <div class="card">
                <div class="card-body">
                    <h6>修正前的程式碼：</h6>
                    <pre><code>&lt;button type="button" class="btn btn-danger remove-discount-btn w-100" data-row-id="${discountId}"&gt;移除&lt;/button&gt;</code></pre>
                    
                    <h6>修正後的程式碼：</h6>
                    <pre><code>&lt;button type="button" class="btn btn-sm btn-outline-danger remove-discount-btn w-100" data-row-id="${discountId}" title="移除"&gt;
    &lt;i class="bi bi-trash"&gt;&lt;/i&gt;
&lt;/button&gt;</code></pre>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>系統中其他移除按鈕的一致性</h4>
            <div class="alert alert-info">
                <h6>系統中其他移除按鈕的實作方式：</h6>
                <ul>
                    <li><strong>簡訊模板管理：</strong><code>btn btn-sm btn-outline-danger</code> + <code>&lt;i class="bi bi-trash-fill"&gt;&lt;/i&gt;</code></li>
                    <li><strong>庫存盤點表單：</strong><code>btn btn-sm btn-danger</code> + <code>&lt;i class="bi bi-trash"&gt;&lt;/i&gt;</code></li>
                    <li><strong>門市表單：</strong><code>btn btn-sm btn-outline-danger</code> + <code>&lt;i class="bi bi-trash"&gt;&lt;/i&gt;</code></li>
                    <li><strong>訂單表單：</strong><code>btn btn-danger btn-sm</code> + <code>&lt;i class="bi bi-trash"&gt;&lt;/i&gt;</code></li>
                </ul>
                <p class="mb-0">修正後的按鈕樣式與系統中大部分移除按鈕保持一致。</p>
            </div>
        </div>

        <div class="alert alert-success">
            <h5>✅ 修正完成</h5>
            <p class="mb-0">職權折扣設定區塊的移除按鈕現在：</p>
            <ul class="mb-0">
                <li>使用垃圾桶圖示 <code>&lt;i class="bi bi-trash"&gt;&lt;/i&gt;</code> 而不是文字</li>
                <li>採用一致的按鈕樣式 <code>btn btn-sm btn-outline-danger</code></li>
                <li>添加了 <code>title="移除"</code> 屬性作為提示文字</li>
                <li>與系統中其他移除按鈕保持視覺和功能的一致性</li>
            </ul>
        </div>

        <div class="alert alert-info">
            <h5>📋 修正檔案</h5>
            <p>本次修正的檔案：</p>
            <ul class="mb-0">
                <li><strong>product_setting_form.js</strong>：修正 <code>addDiscountRow</code> 函數中的移除按鈕樣式</li>
            </ul>
        </div>

        <div class="demo-section">
            <h4>使用者體驗改善</h4>
            <div class="card">
                <div class="card-body">
                    <ul>
                        <li><strong>視覺一致性：</strong>與系統中其他移除按鈕保持相同的外觀</li>
                        <li><strong>國際化友好：</strong>圖示比文字更容易理解，不受語言限制</li>
                        <li><strong>空間效率：</strong>圖示比文字佔用更少空間</li>
                        <li><strong>提示功能：</strong>滑鼠懸停時顯示「移除」提示文字</li>
                        <li><strong>操作直覺：</strong>垃圾桶圖示是刪除/移除操作的通用符號</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 為測試頁面的按鈕添加提示功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
