<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>派工商品訂單查詢 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dispatch_product_orders_list.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>

        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            
            <div class="page-content">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                        <li class="breadcrumb-item"><a>訂單管理</a></li>
                        <li class="breadcrumb-item active" aria-current="page">派工訂單作業</li>
                    </ol>
                </nav>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2>派工商品訂單查詢</h2>
                    <a href="dispatch_product_order_form.html" class="btn btn-primary">
                        <i class="bi bi-plus-circle-fill me-2"></i>新增派工商品訂單
                    </a>
                </div>

                <!-- Search Filters -->
                <div class="card mb-3">
                    <div class="card-header">
                        查詢條件
                    </div>
                    <div class="card-body">
                        <form id="search-orders-form" class="row g-3">
                            <div class="col-md-3">
                                <label for="searchStore" class="form-label">開單門市</label>
                                <select class="form-select form-select-sm" id="searchStore">
                                    <option value="">所有門市</option>
                                    <!-- Options populated by JS -->
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="searchOrderNumber" class="form-label">訂單號碼</label>
                                <input type="text" class="form-control form-control-sm" id="searchOrderNumber">
                            </div>
                            <div class="col-md-3">
                                <label for="searchCustomerKeyword" class="form-label">客戶(姓名/電話)</label>
                                <input type="text" class="form-control form-control-sm" id="searchCustomerKeyword">
                            </div>
                            <div class="col-md-3">
                                <label for="searchOrderStatus" class="form-label">訂單狀態</label>
                                <select class="form-select form-select-sm" id="searchOrderStatus">
                                    <option value="">全部狀態</option>
                                    <!-- Options populated by JS -->
                                </select>
                            </div>
                             <div class="col-md-3">
                                <label for="searchDispatchStatus" class="form-label">派工狀態</label>
                                <select class="form-select form-select-sm" id="searchDispatchStatus">
                                    <option value="">全部狀態</option>
                                    <!-- Options populated by JS -->
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="searchOrderDateFrom" class="form-label">訂單日期 (起)</label>
                                <input type="date" class="form-control form-control-sm" id="searchOrderDateFrom">
                            </div>
                            <div class="col-md-3">
                                <label for="searchOrderDateTo" class="form-label">訂單日期 (迄)</label>
                                <input type="date" class="form-control form-control-sm" id="searchOrderDateTo">
                            </div>
                             <div class="col-md-3">
                                <label for="searchTechnician" class="form-label">指派技師</label>
                                <input type="text" class="form-control form-control-sm" id="searchTechnician" placeholder="輸入技師姓名或員工編號">
                                <!-- TODO: Could be a select dropdown populated from users with technician role -->
                            </div>
                            <div class="col-md-12 text-end">
                                <button type="button" class="btn btn-sm btn-secondary me-2" id="resetSearchBtn">清除條件</button>
                                <button type="submit" class="btn btn-sm btn-primary" id="execSearchBtn"><i class="bi bi-search"></i> 查詢</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="table-responsive">
                    <table class="table table-hover app-table" id="orders-table">
                        <thead>
                            <tr>
                                <th>訂單號碼</th>
                                <th>訂單日期</th>
                                <th>客戶姓名</th> <!-- Or Contact Person for dispatch -->
                                <th>安裝地址</th>
                                <th>指派技師</th>
                                <th>總金額</th>
                                <th>訂單狀態</th>
                                <th>派工狀態</th>
                                <th>最後更新時間</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table-body">
                            <tr>
                                <td colspan="10" class="text-center p-4">載入中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center" id="pagination-controls">
                        <!-- Pagination controls will be populated here by JS -->
                    </ul>
                </nav>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/dispatch_product_orders_list.js"></script>
</body>
</html> 