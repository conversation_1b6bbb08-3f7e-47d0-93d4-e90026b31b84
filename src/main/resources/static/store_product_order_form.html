<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增門市商品訂單 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/store_product_order_form.css"> 
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>

        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            
            <div class="page-content">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                        <li class="breadcrumb-item"><a href="store_product_orders_list.html">門市商品訂單查詢</a></li>
                        <li class="breadcrumb-item active" aria-current="page" id="form-title">新增門市商品訂單</li>
                    </ol>
                </nav>
                
                <h2 id="page-main-title">新增門市商品訂單</h2>

                <form id="store-order-form" class="mt-3">
                    
                    <!-- 訂單資訊 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            訂單資訊
                            <span id="orderNumberDisplay" class="text-muted ms-3 fw-normal"></span>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="companyDivision" class="form-label">公司別 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="companyDivision" required disabled></select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="store" class="form-label">門市 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="store" required></select>
                                </div>
                                 <div class="col-md-4 mb-3">
                                    <label for="orderDate" class="form-label">訂單日期 <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="orderDate" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="orderStatus" class="form-label">訂單狀態</label>
                                    <input type="text" class="form-control" id="orderStatus" value="草稿" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 顧客資訊 -->
                    <div class="card mb-3">
                        <div class="card-header">顧客資訊</div>
                        <div class="card-body">
                            <div class="row g-3 align-items-center">
                                <div class="col-md-4">
                                    <label for="customerPhoneSearch" class="form-label">會員電話查詢</label>
                                        <input type="text" class="form-control" id="customerPhoneSearch" placeholder="輸入電話號碼">
                                    </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <button class="btn btn-primary w-100" type="button" id="searchCustomerBtn">會員查詢</button>
                                </div>
                                <div class="col-md-3">
                                    <label for="customerName" class="form-label">會員姓名</label>
                                    <input type="text" class="form-control" id="customerName" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label for="customerPhoneDisplay" class="form-label">會員電話</label>
                                    <input type="text" class="form-control" id="customerPhoneDisplay" readonly>
                                </div>
                                <input type="hidden" id="customerId">
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                     <button type="button" class="btn btn-sm btn-outline-success" id="createNewCustomerBtn" style="display: none;" data-bs-toggle="modal" data-bs-target="#quickCreateCustomerModal">
                                        <i class="bi bi-person-plus-fill"></i> 快速新增會員
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 搜尋與篩選 -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="productKeyword" class="form-label">商品關鍵字</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="productKeyword" placeholder="請先選擇門市">
                                        <button class="btn btn-outline-secondary" type="button" id="product-search-btn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="promotionSelect" class="form-label">選擇優惠活動</label>
                                    <select class="form-select" id="promotionSelect">
                                        <option value="" selected>無</option>
                                        <!-- Options to be populated by JS -->
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="benefitSelect" class="form-label">選擇會員福利</label>
                                    <select class="form-select" id="benefitSelect">
                                        <option value="" selected>無</option>
                                        <!-- Options to be populated by JS -->
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 商品選擇區 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            商品選擇
                            <button type="button" class="btn btn-sm btn-primary float-end" id="addProductItemBtn" style="display: none;"><i class="bi bi-plus-circle"></i> 新增商品</button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table app-table" id="order-items-table">
                                    <thead>
                                        <tr>
                                            <th>商品名稱</th>
                                            <th>商品條碼</th>
                                            <th>原定價</th>
                                            <th>活動價</th>
                                            <th>數量</th>
                                            <th>小計</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Order items will be dynamically added here -->
                                    </tbody>
                                </table>
                            </div>
                            <div id="no-items-message" class="text-center p-3" style="display: none;">請點選「新增商品」按鈕來加入品項</div>
                        </div>
                    </div>

                    <!-- 金額總計區 -->
                    <div class="card mb-3">
                        <div class="card-header">金額總計</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 offset-md-6">
                                    <dl class="row text-end">
                                        <dt class="col-sm-6">商品總金額:</dt>
                                        <dd class="col-sm-6" id="productsTotalAmount">NT$ 0</dd>
                                        
                                        <dt class="col-sm-6">折扣總金額:</dt>
                                        <dd class="col-sm-6" id="discountAmount">NT$ 0</dd>

                                        <dt class="col-sm-6">小計 (未稅):</dt>
                                        <dd class="col-sm-6" id="netAmount">NT$ 0</dd>

                                        <dt class="col-sm-6">營業稅 (5%):</dt>
                                        <dd class="col-sm-6" id="taxAmount">NT$ 0</dd>

                                        <dt class="col-sm-6 fs-5">應付總金額:</dt>
                                        <dd class="col-sm-6 fs-5 fw-bold" id="grandTotalAmount">NT$ 0</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 發票開立區 -->
                    <div class="card mb-3">
                        <div class="card-header">發票資訊</div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label d-block">開立發票</label>
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="invoiceTypeBtn" id="invoiceBtnTwoPart" value="2" autocomplete="off" checked>
                                    <label class="btn btn-outline-primary" for="invoiceBtnTwoPart">開立二聯發票</label>

                                    <input type="radio" class="btn-check" name="invoiceTypeBtn" id="invoiceBtnThreePart" value="3" autocomplete="off">
                                    <label class="btn btn-outline-primary" for="invoiceBtnThreePart">開立三聯發票</label>

                                    <input type="radio" class="btn-check" name="invoiceTypeBtn" id="invoiceBtnNone" value="0" autocomplete="off">
                                    <label class="btn btn-outline-primary" for="invoiceBtnNone">不開立發票</label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label d-block">稅務類型</label>
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="taxTypeBtn" id="taxTypeExclusive" value="exclusive" autocomplete="off" checked>
                                    <label class="btn btn-outline-secondary" for="taxTypeExclusive">稅外</label>

                                    <input type="radio" class="btn-check" name="taxTypeBtn" id="taxTypeInclusive" value="inclusive" autocomplete="off">
                                    <label class="btn btn-outline-secondary" for="taxTypeInclusive">稅內</label>
                                </div>
                            </div>
                            <div id="invoice-details-container" class="row g-3">
                                <div class="col-md-4">
                                    <label for="taxIdNumber" class="form-label">統一編號</label>
                                    <input type="text" class="form-control" id="taxIdNumber" disabled>
                                </div>
                                <div class="col-md-4">
                                    <label for="invoiceCompanyTitle" class="form-label">發票抬頭</label>
                                    <input type="text" class="form-control" id="invoiceCompanyTitle" disabled>
                                </div>
                                 <div class="col-md-4">
                                    <label for="invoiceDate" class="form-label">發票日期</label>
                                    <input type="date" class="form-control" id="invoiceDate">
                                </div>
                                 <div class="col-md-4">
                                    <label for="invoiceNumber" class="form-label">發票號碼</label>
                                    <input type="text" class="form-control" id="invoiceNumber">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 付款方式區 -->
                    <div class="card mb-3">
                        <div class="card-header">付款方式</div>
                        <div class="card-body">
                             <div class="mb-3">
                                <label class="form-label">新增付款方式</label>
                                <div id="add-payment-buttons">
                                    <button type="button" class="btn btn-success me-2" data-payment-type="cash">現金</button>
                                    <button type="button" class="btn btn-success me-2" data-payment-type="credit_card">信用卡</button>
                                    <button type="button" class="btn btn-success" data-payment-type="bank_transfer">匯款</button>
                                </div>
                            </div>
                            <div id="payment-methods-container" class="mb-3">
                                <!-- Payment methods added by JS -->
                            </div>
                            <div class="row mt-3">
                                 <div class="col-md-6 offset-md-6 text-end">
                                    <p class="mb-1">已付金額: <span id="totalPaidAmountDisplay" class="fw-bold">NT$ 0</span></p>
                                    <p class="mb-0">待付差額: <span id="amountDueDisplay" class="fw-bold text-danger">NT$ 0</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 訂單備註 -->
                    <div class="card mb-3">
                        <div class="card-header">訂單備註</div>
                        <div class="card-body">
                            <textarea class="form-control" id="orderRemarks" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="text-center mt-4 mb-5" id="form-action-buttons">
                        <button type="button" class="btn btn-danger me-2" id="delete-btn">刪除</button>
                        <button type="button" class="btn btn-secondary me-2" id="save-draft-btn">存檔</button>
                        <button type="submit" class="btn btn-primary" id="submit-order-btn" disabled>送出</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div class="modal fade" id="productSearchModal" tabindex="-1" aria-labelledby="productSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productSearchModalLabel">搜尋並選擇商品</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="productSearchKeywordModal" placeholder="輸入商品條碼或名稱">
                        <button class="btn btn-outline-secondary" type="button" id="execProductSearchModalBtn"><i class="bi bi-search"></i></button>
                    </div>
                    <div class="list-group" id="productSearchResultsModal">
                        <!-- Search results here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Create Customer Modal -->
    <div class="modal fade" id="quickCreateCustomerModal" tabindex="-1" aria-labelledby="quickCreateCustomerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quickCreateCustomerModalLabel">快速新增會員</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="quick-customer-form">
                        <div class="mb-3 row">
                            <label for="modalCustomerName" class="col-sm-3 col-form-label">會員姓名 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="modalCustomerName" required>
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="modalCustomerPhone" class="col-sm-3 col-form-label">會員電話 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="tel" class="form-control" id="modalCustomerPhone" required>
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="modalCustomerAddress" class="col-sm-3 col-form-label">地址</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="modalCustomerAddress">
                            </div>
                        </div>
                         <div class="mb-3 row">
                            <label for="modalCustomerEmail" class="col-sm-3 col-form-label">Email</label>
                            <div class="col-sm-9">
                                <input type="email" class="form-control" id="modalCustomerEmail">
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <label for="modalCustomerRemarks" class="col-sm-3 col-form-label">備註</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="modalCustomerRemarks" rows="2"></textarea>
                            </div>
                        </div>
                        <!-- companyDivisionCode will be taken from userContext -->
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveQuickCustomerBtn">儲存會員</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Select Modal -->
    <div class="modal fade" id="customerSelectModal" tabindex="-1" aria-labelledby="customerSelectModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="customerSelectModalLabel">選擇會員</h5>
                    <p class="ms-3 mb-0 text-muted">查到多筆符合條件的會員，請選擇一位</p>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="list-group" id="customerSelectModalBody">
                        <!-- Customer list will be populated here by JS -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/store_product_order_form.js"></script>
</body>
</html> 