<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>門市詳情 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div> 
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a href="store_settings.html">門市查詢</a></li>
                            <li class="breadcrumb-item active" aria-current="page" id="form-mode-breadcrumb">新增門市</li>
                        </ol>
                    </nav>

                    <h3 id="form-title" class="mb-4">新增門市</h3>
                    <div id="store-form-error-message" class="alert alert-danger d-none" role="alert"></div>

                    <form id="store-form">
                        <input type="hidden" id="storeId">

                        <!-- 基本資料 -->
                        <div class="card mb-4">
                            <div class="card-header">基本資料</div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="storeName" class="form-label">門市名稱 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="storeName" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="storeCode" class="form-label">門市代碼</label>
                                        <input type="text" class="form-control" id="storeCode">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="erpCompanyDivision" class="form-label">歸屬公司別 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="erpCompanyDivision" required>
                                            <!-- Options populated by JS: EASTKING, QUEYOU -->
                                            <option value="">請選擇公司別...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="regionNameDisplay" class="form-label">地區 <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="regionNameDisplay" readonly placeholder="請選擇地區">
                                            <input type="hidden" id="regionId">
                                            <button class="btn btn-outline-secondary btn-sm" type="button" id="manageRegionsBtn" data-bs-toggle="modal" data-bs-target="#regionManagementModal">管理地區</button>
                                            <button class="btn btn-outline-primary btn-sm" type="button" id="selectRegionBtn" data-bs-toggle="modal" data-bs-target="#regionSelectionModal">選擇地區</button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">是否啟用 <span class="text-danger">*</span></label>
                                        <div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="isActive" id="isActiveYes" value="true" checked>
                                                <label class="form-check-label" for="isActiveYes">是</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="isActive" id="isActiveNo" value="false">
                                                <label class="form-check-label" for="isActiveNo">否</label>
                                            </div>
                                        </div>
                                    </div>
                                     <div class="col-md-6">
                                        <label for="storeAddress" class="form-label">門市地址</label>
                                        <input type="text" class="form-control" id="storeAddress">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="storePhone" class="form-label">門市電話</label>
                                        <input type="text" class="form-control" id="storePhone">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 門市人員 -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                門市人員
                                <button type="button" class="btn btn-sm btn-success" id="addStoreStaffBtn" data-bs-toggle="modal" data-bs-target="#staffSelectionModal" data-staff-type="REGULAR_STAFF">新增門市人員</button>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm table-hover">
                                    <thead><tr><th>員工編號</th><th>員工姓名</th><th>操作</th></tr></thead>
                                    <tbody id="storeStaffTableBody"></tbody>
                                </table>
                                <p id="noStoreStaffMessage" class="text-muted d-none">尚無門市人員</p>
                            </div>
                        </div>

                        <!-- 代班人員 -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                代班人員
                                <button type="button" class="btn btn-sm btn-success" id="addSubstituteStaffBtn" data-bs-toggle="modal" data-bs-target="#staffSelectionModal" data-staff-type="SUBSTITUTE_STAFF">新增代班人員</button>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm table-hover">
                                    <thead><tr><th>員工編號</th><th>員工姓名</th><th>操作</th></tr></thead>
                                    <tbody id="substituteStaffTableBody"></tbody>
                                </table>
                                <p id="noSubstituteStaffMessage" class="text-muted d-none">尚無代班人員</p>
                            </div>
                        </div>

                        <!-- 統編抬頭 -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                統編抬頭
                                <div>
                                    <button type="button" class="btn btn-sm btn-info me-2" id="manageTaxIdsBtn" data-bs-toggle="modal" data-bs-target="#taxIdManagementModal">統編抬頭管理</button>
                                    <button type="button" class="btn btn-sm btn-success" id="selectTaxIdsBtn" data-bs-toggle="modal" data-bs-target="#taxIdSelectionModal">統編抬頭選擇</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm table-hover">
                                    <thead><tr><th>統一編號</th><th>公司抬頭</th><th>操作</th></tr></thead>
                                    <tbody id="storeTaxIdsTableBody"></tbody>
                                </table>
                                <p id="noStoreTaxIdsMessage" class="text-muted d-none">尚無統編抬頭</p>
                            </div>
                        </div>

                        <div class="mt-4 d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" id="cancelStoreFormBtn">取消</button>
                            <button type="submit" class="btn btn-primary" id="saveStoreFormBtn">確認儲存</button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <!-- Region Management Modal -->
    <div class="modal fade" id="regionManagementModal" tabindex="-1" aria-labelledby="regionManagementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="regionManagementModalLabel">區域管理</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="newRegionForm" class="row g-3 mb-3 align-items-end">
                        <div class="col">
                            <label for="newRegionNameInput" class="form-label">地區名稱</label>
                            <input type="text" class="form-control" id="newRegionNameInput" placeholder="輸入新地區名稱">
                             <input type="hidden" id="editingRegionId">
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="btn btn-success">新增/更新地區</button>
                        </div>
                    </form>
                    <h6>現有地區:</h6>
                    <ul class="list-group" id="existingRegionsList"></ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Region Selection Modal -->
    <div class="modal fade" id="regionSelectionModal" tabindex="-1" aria-labelledby="regionSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="regionSelectionModalLabel">選擇區域</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="regionRadioGroupContainer"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmRegionSelectionBtn">確認</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Staff Selection Modal -->
    <div class="modal fade" id="staffSelectionModal" tabindex="-1" aria-labelledby="staffSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="staffSelectionModalLabel">選擇人員</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="text" class="form-control mb-3" id="staffSearchKeywordInput" placeholder="關鍵字查詢 (員工編號/姓名)">
                    <div id="staffListContainer" style="max-height: 300px; overflow-y: auto;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmStaffSelectionBtn">確認加入</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tax ID Management Modal -->
    <div class="modal fade" id="taxIdManagementModal" tabindex="-1" aria-labelledby="taxIdManagementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="taxIdManagementModalLabel">統編抬頭管理</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="newTaxIdForm" class="row g-3 mb-3 align-items-end">
                        <input type="hidden" id="editingTaxIdEntryId">
                        <div class="col-md-4">
                            <label for="newTaxIdNumberInput" class="form-label">統一編號</label>
                            <input type="text" class="form-control" id="newTaxIdNumberInput" required>
                        </div>
                        <div class="col-md-6">
                            <label for="newCompanyNameInput" class="form-label">公司抬頭</label>
                            <input type="text" class="form-control" id="newCompanyNameInput" required>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-success w-100">新增/更新統編</button>
                        </div>
                    </form>
                    <h6>現有統編抬頭:</h6>
                     <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead><tr><th>統一編號</th><th>公司抬頭</th><th>操作</th></tr></thead>
                            <tbody id="existingTaxIdsTableBody"></tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tax ID Selection Modal -->
    <div class="modal fade" id="taxIdSelectionModal" tabindex="-1" aria-labelledby="taxIdSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="taxIdSelectionModalLabel">選擇統編抬頭</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                     <input type="text" class="form-control mb-3" id="taxIdSearchKeywordInput" placeholder="關鍵字查詢 (統一編號/公司抬頭)">
                    <div id="taxIdListContainer" style="max-height: 300px; overflow-y: auto;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmSelectTaxIdModalBtn">確認加入</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/store_form.js"></script> 
</body>
</html> 