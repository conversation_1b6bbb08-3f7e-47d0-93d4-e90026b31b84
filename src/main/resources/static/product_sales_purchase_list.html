<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品進銷列表 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css"> 
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                        <li class="breadcrumb-item"><a>庫存管理</a></li>
                        <li class="breadcrumb-item active" aria-current="page">門市商品進銷列表</li>
                    </ol>
                </nav>
                <h2 class="mb-3">商品進銷列表</h2>

                <!-- Filters -->
                <div class="card mb-3">
                    <div class="card-header">查詢條件</div>
                    <div class="card-body">
                        <form id="search-form" class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label for="productCategoryFilter" class="form-label">商品分類</label>
                                <select class="form-select" id="productCategoryFilter">
                                    <option value="" selected>全部分類</option>
                                    <!-- Options will be populated by JS -->
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="dateFromFilter" class="form-label">日期範圍 (起)</label>
                                <input type="date" class="form-control" id="dateFromFilter">
                            </div>
                            <div class="col-md-3">
                                <label for="dateToFilter" class="form-label">日期範圍 (迄)</label>
                                <input type="date" class="form-control" id="dateToFilter">
                            </div>
                            <div class="col-md-3">
                                <label for="keywordFilter" class="form-label">商品關鍵字</label>
                                <input type="text" class="form-control" id="keywordFilter" placeholder="條碼/名稱">
                            </div>
                            <div class="col-md-12 text-end mt-3">
                                <button type="button" class="btn btn-secondary" id="resetButton"><i class="bi bi-arrow-clockwise"></i> 重設</button>
                                <button type="submit" class="btn btn-primary" id="searchButton"><i class="bi bi-search"></i> 查詢</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Table for results -->
                <div class="table-responsive">
                    <table class="table table-hover app-table">
                        <thead>
                            <tr>
                                <th>商品分類</th>
                                <th>國際條碼</th>
                                <th>商品名稱</th>
                                <th>總進貨數量</th>
                                <th>總銷貨數量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="salesPurchaseTableBody">
                            <!-- Rows will be populated by JS -->
                        </tbody>
                    </table>
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center" id="paginationControls">
                        <!-- Pagination will be populated by JS -->
                    </ul>
                </nav>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/product_sales_purchase_list.js"></script> 
</body>
</html> 