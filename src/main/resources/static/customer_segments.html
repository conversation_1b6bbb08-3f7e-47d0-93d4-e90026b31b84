<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客群管道管理 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/customer_segments.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a>系統管理</a></li>
                            <li class="breadcrumb-item active" aria-current="page">客群管道管理</li>
                        </ol>
                    </nav>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>客群管道管理</h3>
                        <button class="btn btn-primary" id="addSegmentBtn" data-bs-toggle="modal" data-bs-target="#segmentFormModal">新增客群管道</button>
                    </div>

                    <div class="row">
                        <!-- Active Segments Column -->
                        <div class="col-md-6">
                            <div class="card segment-list-card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5>啟用中的管道</h5>
                                    <button class="btn btn-sm btn-success" id="saveOrderBtn">確認排序修改</button>
                                </div>
                                <div class="card-body">
                                    <ul id="activeSegmentsList" class="list-group sortable-list">
                                        <!-- Active segments will be populated here -->
                                    </ul>
                                    <p id="noActiveSegmentsMessage" class="text-muted mt-2 d-none">目前沒有啟用中的客群管道。</p>
                                </div>
                            </div>
                        </div>

                        <!-- Inactive Segments Column -->
                        <div class="col-md-6">
                            <div class="card segment-list-card">
                                <div class="card-header">
                                    <h5>禁用中的管道</h5>
                                </div>
                                <div class="card-body">
                                    <ul id="inactiveSegmentsList" class="list-group">
                                        <!-- Inactive segments will be populated here -->
                                    </ul>
                                    <p id="noInactiveSegmentsMessage" class="text-muted mt-2 d-none">目前沒有禁用中的客群管道。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Segment Form Modal (Add/Edit) -->
    <div class="modal fade" id="segmentFormModal" tabindex="-1" aria-labelledby="segmentFormModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="segmentFormModalLabel">新增客群管道</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="segmentForm">
                    <div class="modal-body">
                        <input type="hidden" id="segmentId">
                        <div class="mb-3">
                            <label for="segmentName" class="form-label">管道名稱 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="segmentName" required maxlength="255">
                        </div>
                        <div id="segmentFormErrorMessage" class="alert alert-danger d-none"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary" id="saveSegmentBtn">儲存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">確認刪除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="deleteConfirmMessage">您確定要刪除此客群管道嗎？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">確定刪除</button>
                </div>
            </div>
        </div>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/customer_segments.js"></script>
</body>
</html> 