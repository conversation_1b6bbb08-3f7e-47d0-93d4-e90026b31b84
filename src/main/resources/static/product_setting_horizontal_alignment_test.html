<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>職權折扣設定水平對齊修正測試 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        body { padding: 20px; }
        .demo-section { margin-bottom: 30px; }
        .discount-row {
            padding-bottom: 15px;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .discount-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .comparison-bg {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
        }
        /* 用於視覺化對齊的輔助線 */
        .debug-border {
            border: 1px dashed #dc3545 !important;
        }
        .debug-bg {
            background-color: rgba(220, 53, 69, 0.1) !important;
        }
        /* 水平對齊輔助線 */
        .horizontal-guide {
            position: relative;
        }
        .horizontal-guide::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #007bff;
            z-index: 10;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>職權折扣設定水平對齊修正測試</h2>
        <p class="text-muted">調整移除按鈕與輸入欄位的水平對齊</p>

        <div class="demo-section">
            <h4>目標：按鈕與輸入欄位水平對齊</h4>
            <div class="alert alert-info">
                <ul class="mb-0">
                    <li><strong>目標：</strong>移除按鈕應該與輸入欄位（select 和 input）處於相同的水平線上</li>
                    <li><strong>問題：</strong><code>align-self-end</code> 讓按鈕位置太低</li>
                    <li><strong>解決方案：</strong>使用 <code>align-items-end</code> + <code>margin-bottom</code> 微調位置</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h4>方法 1：align-self-end（位置太低）</h4>
            <div class="comparison-bg">
                <div class="row g-3 align-items-center mb-3 horizontal-guide">
                    <div class="col-md-5">
                        <label class="form-label">角色</label>
                        <select class="form-select">
                            <option selected>系統管理員</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">折扣金額 (NT $)</label>
                        <div class="input-group">
                            <span class="input-group-text">NT $</span>
                            <input type="number" class="form-control" value="500">
                        </div>
                    </div>
                    <div class="col-md-2 align-self-end debug-border debug-bg">
                        <button type="button" class="btn btn-danger btn-sm" title="移除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <p class="text-danger small mb-0">
                    <i class="bi bi-exclamation-triangle"></i> 
                    問題：按鈕位置太低，沒有與輸入欄位對齊
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>方法 2：align-items-end + margin-bottom（修正版本）</h4>
            <div class="comparison-bg">
                <div class="row g-3 align-items-center mb-3 horizontal-guide">
                    <div class="col-md-5">
                        <label class="form-label">角色</label>
                        <select class="form-select">
                            <option selected>系統管理員</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">折扣金額 (NT $)</label>
                        <div class="input-group">
                            <span class="input-group-text">NT $</span>
                            <input type="number" class="form-control" value="500">
                        </div>
                    </div>
                    <div class="col-md-2 d-flex align-items-end debug-border debug-bg">
                        <button type="button" class="btn btn-danger btn-sm" title="移除" style="margin-bottom: 0.375rem;">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <p class="text-success small mb-0">
                    <i class="bi bi-check-circle"></i> 
                    解決：按鈕與輸入欄位水平對齊
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>方法 3：align-self-center（替代方案）</h4>
            <div class="comparison-bg">
                <div class="row g-3 align-items-center mb-3 horizontal-guide">
                    <div class="col-md-5">
                        <label class="form-label">角色</label>
                        <select class="form-select">
                            <option selected>系統管理員</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label class="form-label">折扣金額 (NT $)</label>
                        <div class="input-group">
                            <span class="input-group-text">NT $</span>
                            <input type="number" class="form-control" value="500">
                        </div>
                    </div>
                    <div class="col-md-2 align-self-center debug-border debug-bg">
                        <button type="button" class="btn btn-danger btn-sm" title="移除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <p class="text-warning small mb-0">
                    <i class="bi bi-info-circle"></i> 
                    替代方案：按鈕置中對齊，但可能不夠精確
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>方法 4：使用 CSS Grid（進階方案）</h4>
            <div class="comparison-bg">
                <div style="display: grid; grid-template-columns: 5fr 5fr 2fr; gap: 1rem; align-items: end;" class="mb-3">
                    <div>
                        <label class="form-label">角色</label>
                        <select class="form-select">
                            <option selected>系統管理員</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label">折扣金額 (NT $)</label>
                        <div class="input-group">
                            <span class="input-group-text">NT $</span>
                            <input type="number" class="form-control" value="500">
                        </div>
                    </div>
                    <div class="debug-border debug-bg">
                        <button type="button" class="btn btn-danger btn-sm" title="移除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <p class="text-info small mb-0">
                    <i class="bi bi-lightbulb"></i> 
                    進階方案：使用 CSS Grid 的 <code>align-items: end</code>
                </p>
            </div>
        </div>

        <div class="demo-section">
            <h4>完整的職權折扣設定區塊（最終版本）</h4>
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    職權折扣設定
                    <button type="button" class="btn btn-sm btn-outline-success">
                        <i class="bi bi-plus-circle"></i> 新增角色折扣
                    </button>
                </div>
                <div class="card-body">
                    <!-- 第一個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option selected>系統管理員</option>
                                <option>部門主管</option>
                                <option>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="500" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-danger btn-sm" title="移除" style="margin-bottom: 0.375rem;">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第二個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option>系統管理員</option>
                                <option selected>部門主管</option>
                                <option>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="300" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-danger btn-sm" title="移除" style="margin-bottom: 0.375rem;">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 第三個折扣行 -->
                    <div class="row g-3 align-items-center mb-3 discount-row">
                        <div class="col-md-5">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option>系統管理員</option>
                                <option>部門主管</option>
                                <option selected>一般員工</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">折扣金額 (NT $)</label>
                            <div class="input-group">
                                <span class="input-group-text">NT $</span>
                                <input type="number" class="form-control" value="100" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-danger btn-sm" title="移除" style="margin-bottom: 0.375rem;">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>技術解釋</h4>
            <div class="card">
                <div class="card-body">
                    <h6>為什麼使用 margin-bottom: 0.375rem？</h6>
                    <ul>
                        <li>Bootstrap 的 <code>.form-label</code> 預設有 <code>margin-bottom: 0.5rem</code></li>
                        <li>但實際的視覺間距約為 <code>0.375rem</code>（6px）</li>
                        <li>這個值讓按鈕與輸入欄位的中心線對齊</li>
                    </ul>
                    
                    <h6>對齊原理：</h6>
                    <ul>
                        <li><code>align-items-end</code> 讓按鈕對齊到容器底部</li>
                        <li><code>margin-bottom</code> 將按鈕向上推移到正確位置</li>
                        <li>結果是按鈕與輸入欄位處於同一水平線</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h4>程式碼修正對比</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header text-bg-warning">修正前（位置太低）</div>
                        <div class="card-body">
                            <pre><code>&lt;div class="col-md-2 align-self-end"&gt;
    &lt;button type="button" class="btn btn-danger btn-sm"&gt;
        &lt;i class="bi bi-trash"&gt;&lt;/i&gt;
    &lt;/button&gt;
&lt;/div&gt;</code></pre>
                            <p class="text-warning small mb-0">⚠️ 按鈕位置太低</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header text-bg-success">修正後（水平對齊）</div>
                        <div class="card-body">
                            <pre><code>&lt;div class="col-md-2 d-flex align-items-end"&gt;
    &lt;button type="button" class="btn btn-danger btn-sm" 
            style="margin-bottom: 0.375rem;"&gt;
        &lt;i class="bi bi-trash"&gt;&lt;/i&gt;
    &lt;/button&gt;
&lt;/div&gt;</code></pre>
                            <p class="text-success small mb-0">✅ 按鈕與輸入欄位水平對齊</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-success">
            <h5>✅ 修正完成</h5>
            <p class="mb-0">職權折扣設定區塊的移除按鈕現在：</p>
            <ul class="mb-0">
                <li>與左側角色選擇下拉選單和右側折扣金額輸入框在同一水平線上</li>
                <li>使用 <code>align-items-end</code> + <code>margin-bottom: 0.375rem</code> 精確對齊</li>
                <li>保持正方形外觀（<code>btn btn-danger btn-sm</code>）和垃圾桶圖示</li>
                <li>在有標籤的兩層結構中與輸入欄位而非標籤對齊</li>
            </ul>
        </div>

        <div class="alert alert-info">
            <h5>📋 修正檔案</h5>
            <p>本次修正的檔案：</p>
            <ul class="mb-0">
                <li><strong>product_setting_form.js</strong>：使用 <code>align-items-end</code> + <code>margin-bottom</code> 實現水平對齊</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
