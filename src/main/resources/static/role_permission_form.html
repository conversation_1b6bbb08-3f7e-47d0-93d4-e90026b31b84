<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色權限表單 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/role_permissions.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a href="role_permissions_list.html">角色權限列表</a></li>
                            <li class="breadcrumb-item active" aria-current="page" id="form-mode-breadcrumb">新增角色</li>
                        </ol>
                    </nav>

                    <h3 id="form-title" class="mb-3">新增角色</h3>

                    <div class="card">
                        <div class="card-body">
                            <form id="role-permission-form">
                                <input type="hidden" id="roleId">
                                
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="roleCode" class="form-label">角色代碼 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="roleCode" required maxlength="50">
                                    </div>
                                    <div class="col-md-8">
                                        <label for="roleName" class="form-label">角色名稱 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="roleName" required maxlength="100">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="roleDescription" class="form-label">角色描述</label>
                                    <textarea class="form-control" id="roleDescription" rows="3" maxlength="1000"></textarea>
                                </div>

                                <hr>
                                <h4>功能權限設定</h4>
                                <div id="function-permissions-container" class="mb-3">
                                    <!-- Function permissions will be rendered here by JS -->
                                    <p class="text-muted">正在載入功能列表...</p>
                                </div>

                                <hr>
                                <h4>欄位權限設定</h4>
                                <div id="field-permissions-container" class="mb-3">
                                    <!-- Field permissions will be rendered here by JS based on selected functions -->
                                    <p class="text-muted">請先設定功能權限以載入相關欄位。</p>
                                </div>

                                <hr>
                                <h4>區域限制</h4>
                                <div id="region-permissions-container" class="mb-3">
                                    <!-- Region selections (N, C, S, All) will be rendered here by JS -->
                                     <div class="form-check form-check-inline">
                                        <input class="form-check-input region-checkbox" type="checkbox" id="regionNorth" value="N">
                                        <label class="form-check-label" for="regionNorth">北區</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input region-checkbox" type="checkbox" id="regionCentral" value="C">
                                        <label class="form-check-label" for="regionCentral">中區</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input region-checkbox" type="checkbox" id="regionSouth" value="S">
                                        <label class="form-check-label" for="regionSouth">南區</label>
                                    </div>
                                     <div class="form-check form-check-inline">
                                        <input class="form-check-input region-checkbox" type="checkbox" id="regionAll" value="A">
                                        <label class="form-check-label" for="regionAll">全區</label>
                                    </div>
                                </div>

                                <div class="mt-4 d-flex justify-content-end">
                                    <a href="role_permissions_list.html" class="btn btn-outline-secondary me-2">返回列表</a>
                                    <button type="submit" class="btn btn-primary" id="submit-btn">儲存設定</button>
                                </div>
                                <div id="form-error-message" class="alert alert-danger mt-3 d-none"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/role_permission_form.js"></script>
</body>
</html> 