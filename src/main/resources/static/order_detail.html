<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>訂單詳情 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/order_detail.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>

        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            
            <div class="page-content">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                        <!-- Breadcrumb will be dynamically updated based on source page -->
                        <li class="breadcrumb-item"><a id="sourceOrderListPageLink" href="store_product_orders_list.html">門市商品訂單查詢</a></li>
                        <li class="breadcrumb-item active" aria-current="page">訂單詳情</li>
                    </ol>
                </nav>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2 id="page-main-title">訂單詳情 <span id="detailOrderNumber" class="text-muted fs-5"></span></h2>
                    <div id="order-actions-placeholder" class="d-flex gap-2">
                        <!-- Action buttons will be populated here by JS -->
                    </div>
                </div>

                <!-- Order Information Sections -->
                <div id="order-detail-content">
                    <!-- Main Order Info -->
                    <div class="card mb-3">
                        <div class="card-header">訂單基本資訊</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>訂單號碼:</strong> <span id="orderNumber"></span></p>
                                    <p><strong>訂單日期:</strong> <span id="orderDate"></span></p>
                                    <p><strong>訂單類型:</strong> <span id="orderTypeDescription"></span></p>
                                    <p id="promotion-display-row" style="display: none;"><strong>適用活動:</strong> <span id="promotionName" class="text-success"></span></p>
                                    <p><strong>門市:</strong> <span id="storeName"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>訂單狀態:</strong> <span id="orderStatusDescription" class="fw-bold"></span></p>
                                    <p><strong>付款狀態:</strong> <span id="paymentStatusDescription"></span></p>
                                    <p><strong>派工狀態:</strong> <span id="dispatchStatusDescription"></span></p>
                                    <p><strong>建立人員:</strong> <span id="createdByName"></span> (<span id="createTime"></span>)</p>
                                    <p><strong>最後更新:</strong> <span id="updatedByName"></span> (<span id="updateTime"></span>)</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Info -->
                    <div class="card mb-3">
                        <div class="card-header">客戶資訊</div>
                        <div class="card-body">
                             <div class="row">
                                <div class="col-md-6">
                                    <p><strong>客戶姓名:</strong> <span id="customerName"></span></p>
                                    <p><strong>客戶電話:</strong> <span id="customerPhone"></span></p>
                                    <p><strong>會員等級:</strong> <span id="memberLevelName"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>送貨/安裝姓名:</strong> <span id="contactName"></span></p>
                                    <p><strong>送貨/安裝電話:</strong> <span id="contactPhone"></span></p>
                                    <p><strong>送貨/安裝地址:</strong> <span id="installationAddress"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Items Info -->
                    <div class="card mb-3">
                        <div class="card-header">主商品資訊</div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table app-table" id="main-items-table">
                                    <thead class="table-header-white">
                                        <tr>
                                            <th>名稱</th>
                                            <th class="dispatch-only">出貨方式</th>
                                            <th>原價</th>
                                            <th>活動價</th>
                                            <th class="dispatch-only">機身號碼</th>
                                            <th>數量</th>
                                            <th>小計</th>
                                            <th class="dispatch-only">出貨倉庫</th>
                                        </tr>
                                    </thead>
                                    <tbody id="main-items-table-body">
                                        <!-- Main product items will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Gifts Info -->
                    <div class="card mb-3" id="gifts-info-section" style="display: none;">
                        <div class="card-header">贈品資訊</div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table app-table" id="gifts-table">
                                    <thead class="table-header-white">
                                        <tr>
                                            <th>名稱</th>
                                            <th>出貨方式</th>
                                            <th>原價</th>
                                            <th>活動價</th>
                                            <th>數量</th>
                                            <th>小計</th>
                                            <th>出貨倉庫</th>
                                        </tr>
                                    </thead>
                                    <tbody id="gifts-table-body">
                                        <!-- Gift items will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-end mt-2 gift-summary" style="display: none;">
                            </div>
                        </div>
                    </div>

                    <!-- Add-ons Info -->
                    <div class="card mb-3" id="addons-info-section" style="display: none;">
                        <div class="card-header">加購品資訊</div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table app-table" id="addons-table">
                                    <thead class="table-header-white">
                                        <tr>
                                            <th>名稱</th>
                                            <th>出貨方式</th>
                                            <th>原價</th>
                                            <th>活動價</th>
                                            <th>數量</th>
                                            <th>小計</th>
                                            <th>出貨倉庫</th>
                                        </tr>
                                    </thead>
                                    <tbody id="addons-table-body">
                                        <!-- Add-on items will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Amounts Info -->
                    <div class="card mb-3">
                        <div class="card-header">金額資訊</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>商品總額:</strong> <span id="productsTotalAmount"></span></p>
                                    <p><strong>整體折扣:</strong> <span id="discountAmount"></span></p>
                                    <p><strong>稅前金額:</strong> <span id="netAmount"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>稅額 (5%):</strong> <span id="taxAmount"></span></p>
                                    <dl class="row">
                                        <dt class="col-sm-6">應付總額:</dt>
                                        <dd class="col-sm-6 fs-5 fw-bold" id="grandTotalAmount">NT$ 0</dd>
                                        
                                        <dt class="col-sm-6">已付金額:</dt>
                                        <dd class="col-sm-6" id="paidAmount">NT$ 0</dd>
                                    </dl>
                                    <div id="payment-details-list" class="text-end border-top pt-2 mt-2">
                                        <!-- Payment details will be populated here by JS -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Invoice Info -->
                    <div class="card mb-3">
                        <div class="card-header">發票資訊</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>發票類型:</strong> <span id="invoiceTypeDescription"></span></p>
                                    <p><strong>發票號碼:</strong> <span id="invoiceNumber"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>發票日期:</strong> <span id="invoiceDate"></span></p>
                                    <p><strong>統一編號:</strong> <span id="taxIdNumber"></span></p>
                                    <p><strong>發票抬頭:</strong> <span id="invoiceCompanyTitle"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dispatch Info (if applicable) -->
                    <div class="card mb-3" id="dispatch-info-section" style="display: none;">
                        <div class="card-header">派工資訊</div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>預計安裝日:</strong> <span id="installationDate"></span> (<span id="installationTimeSlot"></span>)</p>
                                    <p><strong>實際完工日:</strong> <span id="actualCompletionDate"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>指派技師:</strong> <span id="technicianName"></span></p>
                                    <p><strong>派工備註:</strong> <span id="dispatchNotes"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Remarks -->
                    <div class="card mb-3">
                        <div class="card-header">訂單備註</div>
                        <div class="card-body">
                            <p id="orderRemarks"></p>
                        </div>
                    </div>

                </div> 
                <!-- End Order Detail Content -->
                <div id="loading-indicator" class="text-center p-5" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">載入訂單詳情中...</p>
                </div>
                <div id="error-display" class="alert alert-danger" style="display: none;"></div>

                <!-- Dynamic Actions Container -->
                <div id="dynamic-actions-container" class="text-center my-4">
                    <!-- Action buttons will be populated here by JS -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modals for Actions (e.g., Cancellation Reason) -->
    <div class="modal fade" id="reasonModal" tabindex="-1" aria-labelledby="reasonModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reasonModalLabel">提供原因</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="reasonForm">
                        <div class="mb-3">
                            <label for="actionReason" class="form-label">原因:</label>
                            <textarea class="form-control" id="actionReason" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="submitReasonBtn">確定</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/order_detail.js"></script>
</body>
</html> 