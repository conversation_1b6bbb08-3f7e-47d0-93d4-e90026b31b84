<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品相關設定 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css"> 
    <link rel="stylesheet" href="css/product_settings.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a>系統管理</a></li>
                            <li class="breadcrumb-item active" aria-current="page">商品設定管理</li>
                        </ol>
                    </nav>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>商品相關設定列表</h3>
                        <a href="product_setting_form.html?mode=add" class="btn btn-primary">新增商品設定</a> 
                    </div>

                    <div class="card mb-3">
                        <div class="card-body">
                            <form id="filter-form" class="row g-3 align-items-end">
                                <div class="col-md-3">
                                    <label for="filterProductBarcode" class="form-label">國際條碼</label>
                                    <input type="text" id="filterProductBarcode" class="form-control">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterProductName" class="form-label">商品名稱</label>
                                    <input type="text" id="filterProductName" class="form-control">
                                </div>
                                <div class="col-md-2">
                                    <label for="filterStartDate" class="form-label">最後編輯時間(起)</label>
                                    <input type="date" id="filterStartDate" class="form-control">
                                </div>
                                <div class="col-md-2">
                                    <label for="filterEndDate" class="form-label">最後編輯時間(迄)</label>
                                    <input type="date" id="filterEndDate" class="form-control">
                                </div>
                                <div class="col-md-1">
                                    <button type="submit" class="btn btn-primary w-100">查詢</button>
                                </div>
                                <div class="col-md-1">
                                    <button type="button" id="clear-filters-btn" class="btn btn-outline-secondary w-100">清除</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover app-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>國際條碼</th>
                                    <th>商品名稱</th>
                                    <th class="erp-company-col">ERP公司別</th>
                                    <th>保固</th>
                                    <th class="eastking-price-col">銷售價格</th>
                                    <th>是否啟用</th>
                                    <th>最後編輯時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="settings-table-body">
                                <!-- Rows will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    <div id="no-data-message" class="text-center mt-3 d-none">
                        <p>查無商品設定資料</p>
                    </div>

                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination-controls">
                        </ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>

    <div id="delete-confirm-modal" class="modal fade" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">確認刪除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您確定要刪除此商品設定嗎？此操作無法復原。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">確定刪除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script> 
    <script src="js/product_settings.js"></script> 
</body>
</html> 