<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>盤點紀錄查詢 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <div class="page-content p-4">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                        <li class="breadcrumb-item">門市管理</li>
                        <li class="breadcrumb-item active" aria-current="page">盤點紀錄列表</li>
                    </ol>
                </nav>
                
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0">盤點紀錄查詢</h2>
                    <a href="inventory_count_form.html" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i> 新增盤點
                    </a>
                </div>

                <div class="card mb-4">
                    <div class="card-body">
                        <form id="search-form" class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label for="searchStore" class="form-label">盤點門市</label>
                                <select class="form-select" id="searchStore">
                                    <option value="" selected>選擇門市</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="searchMonth" class="form-label">盤點月份</label>
                                <input type="month" class="form-select" id="searchMonth">
                            </div>
                            <div class="col-md-3">
                                <label for="searchApprovalStatus" class="form-label">審核狀態</label>
                                <select class="form-select" id="searchApprovalStatus">
                                    <option value="" selected>全部審核狀態</option>
                                </select>
                            </div>
                            <div class="col-md-2 align-self-end">
                                <button type="submit" class="btn btn-primary w-100">查詢</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover app-table">
                        <thead>
                            <tr>
                                <th>盤點單號</th>
                                <th>盤點門市</th>
                                <th>盤點表編號</th>
                                <th>盤點月份</th>
                                <th>盤點日期</th>
                                <th>盤點人員</th>
                                <th>原盤點表編號</th>
                                <th>盤點表狀態</th>
                                <th>審核狀態</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="records-table-body">
                            <tr><td colspan="10" class="text-center">載入中...</td></tr>
                        </tbody>
                    </table>
                </div>

                <nav id="pagination-nav" aria-label="Page navigation">
                    <ul class="pagination justify-content-center" id="pagination-controls">
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/inventory_count_list.js"></script>
</body>
</html> 