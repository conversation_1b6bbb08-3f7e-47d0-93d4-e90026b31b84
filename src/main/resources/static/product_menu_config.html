<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品選單結構設定 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/product_menu_config.css"> 
    <!-- SortableJS for drag and drop -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a>系統管理</a></li>
                            <li class="breadcrumb-item active" aria-current="page">商品選單設定</li>
                        </ol>
                    </nav>

                    <h3 class="mb-3">商品選單結構設定</h3>

                    <div class="row mb-3 align-items-center">
                        <div class="col-md-3">
                            <label for="menuTypeSelect" class="form-label">選擇選單類型：</label>
                            <select id="menuTypeSelect" class="form-select">
                                <option value="DISPATCH">派工商品清單</option>
                                <option value="STORE">門市商品清單</option>
                                <!-- Add other menu types as needed -->
                            </select>
                        </div>
                        <div class="col-md-9 text-end">
                            <button class="btn btn-success me-2" id="save-menu-order-btn"><i class="bi bi-check-lg"></i> 確認排序修改</button>
                            <button class="btn btn-outline-secondary" id="cancel-menu-order-btn"><i class="bi bi-x-lg"></i> 取消排序修改</button>
                        </div>
                    </div>

                    <div class="menu-config-container row g-0 p-3 border rounded">
                        <!-- Columns will be populated by JavaScript -->
                        <!-- Example Structure for one column (level 1) -->
                        <!-- 
                        <div class="menu-column col border-end" id="menu-level-0-col">
                            <div class="menu-column-header p-2 bg-light border-bottom">
                                <h5>第一層分類</h5> 
                                <button class="btn btn-sm btn-outline-primary add-category-btn" data-parent-id="" data-level="0">新增分類</button>
                            </div>
                            <ul class="list-group list-group-flush sortable-list" id="menu-level-0">
                                <li class="list-group-item menu-item d-flex justify-content-between align-items-center" data-id="cat-uuid-1" data-type="CATEGORY">
                                    <span><i class="bi bi-grip-vertical me-2 handle"></i>電動桌</span>
                                    <button class="btn btn-sm btn-danger remove-item-btn"><i class="bi bi-trash"></i></button>
                                </li>
                            </ul>
                        </div> 
                        -->
                    </div>

                </div>
            </main>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="add-category-modal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCategoryModalLabel">新增分類</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="add-category-form">
                        <input type="hidden" id="parentCategoryId">
                        <input type="hidden" id="currentMenuType">
                        <div class="mb-3">
                            <label for="categoryName" class="form-label">分類名稱 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="categoryName" required>
                            <div class="invalid-feedback">請輸入分類名稱。</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="save-category-btn">儲存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Item (Product) Modal -->
    <div class="modal fade" id="add-item-modal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addItemModalLabel">新增商品至分類</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="targetCategoryIdForItem">
                    <div class="mb-3">
                        <label for="productSearchInput" class="form-label">搜尋商品 (條碼或名稱)</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="productSearchInput" placeholder="輸入關鍵字...">
                            <button class="btn btn-outline-secondary" type="button" id="product-search-btn"><i class="bi bi-search"></i></button>
                        </div>
                    </div>
                    <div id="product-search-results" class="list-group" style="max-height: 300px; overflow-y: auto;">
                        <!-- Search results will be populated here -->
                    </div>
                    <p id="no-product-results-message" class="text-muted d-none">查無商品。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <!-- Add selected items button would be dynamically handled or this modal changed to select one by one -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal (shared or specific) -->
    <div class="modal fade" id="delete-menu-item-confirm-modal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">確認刪除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="delete-menu-item-message">您確定要刪除此項目嗎？如果這是分類，其下所有子項目也會被刪除。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-menu-item-btn">確定刪除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/product_menu_config.js"></script>
</body>
</html> 