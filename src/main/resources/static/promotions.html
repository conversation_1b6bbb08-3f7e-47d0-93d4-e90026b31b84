<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品促銷管理 - 東方不敗</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/promotions.css">
</head>
<body>
    <div class="app-wrapper">
        <div id="header-placeholder"></div>
        <div class="main-container">
            <div id="sidebar-placeholder"></div>
            <main class="page-content">
                <div class="container-fluid p-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="home.html">首頁</a></li>
                            <li class="breadcrumb-item"><a>系統管理</a></li>
                            <li class="breadcrumb-item active" aria-current="page">促銷價格設定</li>
                        </ol>
                    </nav>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>商品促銷列表</h3>
                        <a href="promotion_form.html?mode=add" class="btn btn-primary">新增促銷活動</a>
                    </div>

                    <div class="card mb-3">
                        <div class="card-body">
                            <form id="filter-form" class="row g-3 align-items-end">
                                <div class="col-md-3">
                                    <label for="filterKeyword" class="form-label">關鍵字 (活動名稱)</label>
                                    <input type="text" id="filterKeyword" class="form-control">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterActivityDateFrom" class="form-label">活動日期 (起)</label>
                                    <input type="date" id="filterActivityDateFrom" class="form-control">
                                </div>
                                <div class="col-md-3">
                                    <label for="filterActivityDateTo" class="form-label">活動日期 (迄)</label>
                                    <input type="date" id="filterActivityDateTo" class="form-control">
                                </div>
                                <div class="col-md-2">
                                    <label for="filterIsActive" class="form-label">狀態</label>
                                    <select id="filterIsActive" class="form-select">
                                        <option value="">全部</option>
                                        <option value="true">啟用中</option>
                                        <option value="false">未啟用</option>
                                    </select>
                                </div>
                                <div class="col-md-3 align-self-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="bi bi-search"></i> 查詢
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover app-table">
                            <thead>
                                <tr>
                                    <th>排序</th>
                                    <th>促銷活動名稱</th>
                                    <th>活動開始時間 <i class="bi bi-arrow-down-up sortable-header" data-sort-key="startTime"></i></th>
                                    <th>活動結束時間 <i class="bi bi-arrow-down-up sortable-header" data-sort-key="endTime"></i></th>
                                    <th>狀態</th>
                                    <th>編輯人員</th>
                                    <th>最後編輯時間 <i class="bi bi-arrow-down-up sortable-header" data-sort-key="updateTime"></i></th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="promotions-table-body"></tbody>
                        </table>
                    </div>
                    <div id="no-data-message" class="text-center mt-3 d-none"><p>查無促銷活動資料</p></div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination-controls"></ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">確認刪除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="deleteConfirmMessage">您確定要刪除此促銷活動嗎？</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">確定刪除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/promotions.js"></script>
</body>
</html> 