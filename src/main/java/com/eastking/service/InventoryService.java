package com.eastking.service;

import com.eastking.model.entity.Order;

public interface InventoryService {

    /**
     * 為指定訂單檢查庫存並創建預留記錄。
     * 此方法將遍歷訂單中的所有品項，計算可銷售庫存（實體庫存 - 已預留庫存），
     * 如果所有品項庫存都充足，則為每個品項創建一筆有效的庫存預留記錄。
     * 這是一個交易性操作，任何品項失敗將導致整個預留回滾。
     * @param order 欲檢查與預留庫存的訂單實體。
     * @return 如果所有品項庫存充足且預留成功，返回 true；否則返回 false。
     */
    boolean checkAndReserveStock(Order order);

    /**
     * 取消指定訂單的所有有效庫存預留。
     * 用於訂單被取消或駁回的場景。
     * @param order 欲取消預留的訂單實體。
     */
    void cancelReservation(Order order);

    /**
     * 消耗指定訂單的庫存預留。
     * TODO: 待辦事項 - 用於訂單完成出貨的場景，應將預留狀態更新為"已消耗"並觸發實際的庫存交易日誌。
     * @param order 欲消耗預留的訂單實體。
     */
    void consumeReservation(Order order);

} 