package com.eastking.service;

import com.eastking.enums.OrderStatusEnum;
import com.eastking.model.entity.Order;
import com.eastking.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class OrderScheduledTasks {

    private static final Logger logger = LoggerFactory.getLogger(OrderScheduledTasks.class);
    private final OrderRepository orderRepository;
    private final InventoryService inventoryService;

    @Scheduled(cron = "0 0 2 * * ?") // Run every day at 2 AM
    @Transactional
    public void checkInsufficientStockOrders() {
        logger.info("Running scheduled task: Checking orders with insufficient stock...");
        List<Order> ordersToCheck = orderRepository.findByOrderStatusCode(OrderStatusEnum.HQ_STOCK_INSUFFICIENT.getCode());

        for (Order order : ordersToCheck) {
            // Use the new inventory service to check if stock is now available (including reservations)
            boolean stockIsSufficient = inventoryService.checkAndReserveStock(order);
            if (stockIsSufficient) {
                logger.info("Stock is now available for order {}. Updating status to HQ_STOCK_FULL.", order.getOrderNumber());
                // The checkAndReserveStock method already creates the reservation.
                // We just need to update the order status.
                order.setOrderStatusCode(OrderStatusEnum.HQ_STOCK_FULL.getCode());
                orderRepository.save(order);
            } else {
                // If stock is still not sufficient, we need to cancel the reservations that checkAndReserveStock might have created in a failed transaction.
                // However, since checkAndReserveStock is @Transactional, it should have rolled back itself.
                // So, no action is needed here, just log it.
                logger.debug("Stock is still insufficient for order {}. No status change.", order.getOrderNumber());
            }
        }
        logger.info("Finished checking {} orders with insufficient stock.", ordersToCheck.size());
    }
} 