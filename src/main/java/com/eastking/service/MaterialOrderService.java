package com.eastking.service;

import com.eastking.enums.MaterialOrderStatusEnum;
import com.eastking.enums.MaterialTypeStatusEnum;
import com.eastking.model.dto.request.MaterialOrderRequestDto;
import com.eastking.model.dto.response.MaterialOrderSummaryDto;
import com.eastking.model.entity.DispatchMaterialOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.eastking.model.dto.request.MaterialOrderFilterRequest;

import java.util.UUID;
import com.eastking.model.dto.response.MaterialOrderDetailDto;

// Import DTOs and other necessary classes later

public interface MaterialOrderService {

    // Define service methods for material orders
    // e.g., createMaterialOrder, getMaterialOrders, getMaterialOrderDetail, etc.

    Page<MaterialOrderSummaryDto> searchMaterialOrders(MaterialOrderFilterRequest filter, MaterialTypeStatusEnum materialType, Pageable pageable);
    
    Page<MaterialOrderSummaryDto> searchForManagerView(MaterialOrderFilterRequest filter, Pageable pageable);

    MaterialOrderDetailDto createMaterialOrder(MaterialOrderRequestDto requestDto, MaterialTypeStatusEnum materialType);
    
    MaterialOrderDetailDto getMaterialOrderDetailById(UUID id);

    void completeMaterialCollection(UUID materialOrderId, MaterialOrderStatusEnum status);
} 