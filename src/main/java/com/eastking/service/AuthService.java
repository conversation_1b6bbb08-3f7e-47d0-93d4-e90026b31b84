package com.eastking.service;

import com.eastking.model.dto.request.LoginRequest;
import com.eastking.model.dto.response.LoginResponse;
import java.util.List;

/**
 * 認證服務介面
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
public interface AuthService {
    /**
     * 使用者登入
     *
     * @param loginRequest 登入請求參數
     * @return 登入回應，包含 JWT token 和使用者資訊
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 獲取當前登入使用者可操作的門市列表
     * @return 可操作門市列表
     */
    List<LoginResponse.StoreInfo> getOperableStores();
} 