package com.eastking.service;

import com.eastking.model.dto.TaxIdentificationDto;
import com.eastking.model.entity.TaxIdentificationEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 統編抬頭資料服務介面
 */
public interface TaxIdentificationService {

    TaxIdentificationDto createTaxIdentification(TaxIdentificationDto taxIdentificationDto);

    Optional<TaxIdentificationDto> getTaxIdentificationById(UUID id);

    Optional<TaxIdentificationDto> getByTaxIdNumber(String taxIdNumber);

    Page<TaxIdentificationDto> getAllTaxIdentifications(Pageable pageable, String keyword); // keyword can search taxIdNumber or companyName
    
    List<TaxIdentificationDto> getAllTaxIdentificationsList(); // For selection modals

    TaxIdentificationDto updateTaxIdentification(UUID id, TaxIdentificationDto taxIdentificationDto);

    void deleteTaxIdentification(UUID id);

    TaxIdentificationEntity findTaxEntityById(UUID id); // Helper
} 