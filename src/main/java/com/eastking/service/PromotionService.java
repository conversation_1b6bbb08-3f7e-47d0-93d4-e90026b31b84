package com.eastking.service;

import com.eastking.model.dto.PromotionDto;
import com.eastking.model.dto.request.PromotionQueryRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.UUID;
import java.util.List;

public interface PromotionService {
    PromotionDto createPromotion(PromotionDto promotionDto);
    PromotionDto updatePromotion(UUID promotionId, PromotionDto promotionDto);
    void deletePromotion(UUID promotionId);
    PromotionDto getPromotionById(UUID promotionId);
    Page<PromotionDto> getAllPromotions(PromotionQueryRequest queryRequest, Pageable pageable);
    List<PromotionDto> getSelectablePromotions(UUID storeId, UUID distributorId);
    List<PromotionDto> searchAvailablePromotions(UUID storeId);
} 