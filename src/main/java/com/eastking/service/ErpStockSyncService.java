package com.eastking.service;

import com.eastking.model.entity.ErpMahjongTableStockLog;
import com.eastking.repository.ErpMahjongTableStockLogRepository;
import com.eastking.repository.ProductSettingRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * ERP庫存同步服務 (示意)
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */

//todo: ERP 同步 --> 庫存：每天 12:00 24:00 各自動同步一次(另加手動同步)
//              --> 員工資訊、產品資訊：每天 24:00 後自動同步一次(另加手動同步)

@Service
@RequiredArgsConstructor
public class ErpStockSyncService {

    //todo:需補ERP庫存同步邏輯

    private static final Logger logger = LoggerFactory.getLogger(ErpStockSyncService.class);

    private final ErpMahjongTableStockLogRepository erpLogRepository;
    private final ProductSettingRepository productSettingRepository; // To identify mahjong tables

    // Example: cron = "0 0 1 * * ?" means 1 AM daily
    // @Scheduled(cron = "${app.erp.sync.cron:0 0 1 * * ?}")
    @Transactional
    public void syncErpMahjongTableStock() {
        logger.info("Starting ERP Mahjong Table Stock synchronization...");

        // TODO: 1. Identify Mahjong Table product barcodes from ProductSetting
        // List<String> mahjongTableBarcodes = productSettingRepository.findMahjongTableBarcodes(); // Custom query needed
        List<String> mahjongTableBarcodes = List.of("PROD001", "PROD002"); // Placeholder

        if (mahjongTableBarcodes.isEmpty()) {
            logger.info("No Mahjong Table products defined for ERP sync. Skipping.");
            return;
        }

        // TODO: 2. Connect to "正航 ERP" and fetch stock data for these barcodes.
        // This part is highly dependent on the ERP's integration capabilities (API, DB link, file).
        // For demonstration, let's simulate fetching some data:
        List<SimulatedErpStockData> erpData = fetchSimulatedErpData(mahjongTableBarcodes);

        for (SimulatedErpStockData data : erpData) {
            // TODO: 3. For each product/warehouse, mark existing logs as not latest
            // A more efficient way might be a single update query if possible, or careful handling in loop.
            // This part needs careful implementation to avoid race conditions if syncs are frequent.
            // Example (could be optimized):
            List<ErpMahjongTableStockLog> oldLogs = erpLogRepository
                .findByProductBarcodeAndErpWarehouseCodeAndIsLatestForProductWarehouseTrueOrderBySyncTimeDesc(
                    data.getProductBarcode(), data.getErpWarehouseCode());
            for (ErpMahjongTableStockLog oldLog : oldLogs) {
                oldLog.setIsLatestForProductWarehouse(false);
                erpLogRepository.save(oldLog); 
            }

            // TODO: 4. Insert new log record
            ErpMahjongTableStockLog newLog = new ErpMahjongTableStockLog();
            newLog.setProductBarcode(data.getProductBarcode());
            newLog.setProductName(data.getProductName()); // Ideally fetched from ProductSetting or ERP
            newLog.setErpWarehouseCode(data.getErpWarehouseCode());
            newLog.setErpWarehouseName(data.getErpWarehouseName());
            newLog.setQuantity(data.getQuantity());
            newLog.setSyncTime(OffsetDateTime.now());
            newLog.setErpLastUpdateTime(data.getErpLastUpdateTime()); // If available from ERP
            newLog.setIsLatestForProductWarehouse(true);
            erpLogRepository.save(newLog);
            logger.debug("Synced stock for {} in warehouse {}: {}", data.getProductBarcode(), data.getErpWarehouseCode(), data.getQuantity());
        }
        logger.info("ERP Mahjong Table Stock synchronization finished.");
    }

    // Placeholder for simulated ERP data fetching
    private List<SimulatedErpStockData> fetchSimulatedErpData(List<String> barcodes) {
        List<SimulatedErpStockData> data = new ArrayList<>();
        // Example: 
        if(barcodes.contains("PROD001")){
            data.add(new SimulatedErpStockData("PROD001", "斜行高手灰 (ERP)", "ERP-WH-NRT", "ERP台北倉", 10, OffsetDateTime.now().minusHours(1)));
        }
        if(barcodes.contains("PROD002")){
             data.add(new SimulatedErpStockData("PROD002", "雀友T180香檳 (ERP)", "ERP-WH-NRT", "ERP台北倉", 5, OffsetDateTime.now().minusHours(1)));
        }
        return data;
    }

    // Placeholder DTO for simulated ERP data
    @lombok.Data @lombok.AllArgsConstructor
    private static class SimulatedErpStockData {
        String productBarcode;
        String productName;
        String erpWarehouseCode;
        String erpWarehouseName;
        Integer quantity;
        OffsetDateTime erpLastUpdateTime;
    }
} 