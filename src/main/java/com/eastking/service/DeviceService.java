package com.eastking.service;

import com.eastking.model.dto.CustomerDeviceDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.UUID;

public interface DeviceService {
    
    /**
     * 根據關鍵字搜尋設備
     * @param keyword 關鍵字 (機身號、產品名稱、客戶姓名)
     * @param pageable 分頁資訊
     * @return 分頁後的設備列表
     */
    Page<CustomerDeviceDto> searchDevices(String keyword, Pageable pageable);

    Optional<CustomerDeviceDto> findBySerialNumber(String serialNumber);
} 