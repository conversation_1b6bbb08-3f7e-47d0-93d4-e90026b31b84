package com.eastking.service;

import com.eastking.model.dto.ProductSalesPurchaseSummaryDto;
import com.eastking.model.dto.ProductTransactionDetailDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID; // Assuming product ID might be UUID if not just barcode

/**
 * 商品進銷列表服務介面
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
public interface ProductSalesPurchaseService {

    /**
     * 查詢商品進銷匯總列表 (分頁)
     *
     * @param productCategoryId 商品分類ID (可選)
     * @param dateFrom 日期範圍起 (可選)
     * @param dateTo 日期範圍迄 (可選)
     * @param keyword 商品關鍵字 (條碼或名稱, 可選)
     * @param pageable 分頁參數
     * @return 商品進銷匯總分頁數據
     */
    Page<ProductSalesPurchaseSummaryDto> searchProductSalesPurchaseSummary(
            UUID productCategoryId, OffsetDateTime dateFrom, OffsetDateTime dateTo, String keyword, Pageable pageable);

    /**
     * 查詢特定商品的交易明細
     *
     * @param productBarcode 商品條碼
     * @param dateFrom 日期範圍起 (可選)
     * @param dateTo 日期範圍迄 (可選)
     * @param pageable 分頁參數
     * @return 特定商品的交易明細分頁數據
     */
    Page<ProductTransactionDetailDto> getProductTransactionDetails(
            String productBarcode, OffsetDateTime dateFrom, OffsetDateTime dateTo, Pageable pageable);
} 