package com.eastking.service;

import com.eastking.model.dto.SmsTemplateDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 簡訊模板服務介面
 */
public interface SmsTemplateService {

    SmsTemplateDto createSmsTemplate(SmsTemplateDto smsTemplateDto);

    Optional<SmsTemplateDto> getSmsTemplateById(UUID id);

    Optional<SmsTemplateDto> getSmsTemplateByName(String templateName);

    Page<SmsTemplateDto> getAllSmsTemplates(Pageable pageable, String templateType, String keyword);
    
    List<SmsTemplateDto> getSmsTemplatesByType(String templateType); // For specific uses like birthday gift SMS

    SmsTemplateDto updateSmsTemplate(UUID id, SmsTemplateDto smsTemplateDto);

    void deleteSmsTemplate(UUID id);
} 