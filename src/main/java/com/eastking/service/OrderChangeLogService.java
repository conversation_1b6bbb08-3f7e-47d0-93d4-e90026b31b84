package com.eastking.service;

import com.eastking.model.entity.Order;
import com.eastking.model.entity.OrderRefund;
import com.eastking.model.entity.UserAccount;

public interface OrderChangeLogService {

    void logChange(Order order, OrderRefund refund, Short previousStatus, Short newStatus, UserAccount changedByUser, String reason, Object requestDto);
    
    void logOrderChange(Order order, Short previousStatus, Short newStatus, UserAccount changedByUser, String reason, Object requestDto);

    void logRefundChange(OrderRefund refund, Short previousStatus, Short newStatus, UserAccount changedByUser, String reason, Object requestDto);
} 