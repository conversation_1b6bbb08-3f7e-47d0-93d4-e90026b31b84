package com.eastking.service;

import com.eastking.model.dto.CustomerDto;
import com.eastking.model.dto.CustomerDeviceDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;
import java.util.List;

public interface CustomerService {
    Page<CustomerDto> searchCustomers(String keyword, String phoneNumber, Short companyDivisionCode, Pageable pageable);
    CustomerDto createCustomer(CustomerDto customerDto);
    CustomerDto updateCustomer(UUID customerId, CustomerDto customerDto);
    CustomerDto getCustomerById(UUID customerId);
    List<CustomerDto> findByPhoneNumber(String phoneNumber, Short companyDivisionCode);
    void deleteCustomer(UUID customerId);
    CustomerDeviceDto addDeviceToCustomer(UUID customerId, CustomerDeviceDto deviceDto);
} 