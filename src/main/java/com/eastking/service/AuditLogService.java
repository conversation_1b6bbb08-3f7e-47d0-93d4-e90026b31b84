package com.eastking.service;

import com.eastking.model.dto.AuditLogDto;
import com.eastking.model.dto.AuditLogQueryDto;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface AuditLogService {

    void logAction(
        UUID userAccountId, 
        String employeeId, 
        String userName, 
        String userDepartmentName,
        AuditActionTypeEnum actionType, 
        AuditDataTypeEnum dataType, // Use Enum for type safety
        String entityIdStr, 
        String entityDescription,
        String detailsJson, // JSON string of changes
        String clientIpAddress,
        String traceId
    );
    
    // Overloaded method for convenience when user details are fetched from context
     void logAction( 
        AuditActionTypeEnum actionType, 
        AuditDataTypeEnum dataType, 
        String entityIdStr, 
        String entityDescription,
        String detailsJson
    );


    Page<AuditLogDto> getAuditLogs(AuditLogQueryDto queryDto, Pageable pageable);
    
    List<String> getDistinctDataTypes();
    
    List<String> getDistinctUserDepartmentNames(); // Or from a dedicated department source
} 