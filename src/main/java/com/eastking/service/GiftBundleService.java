package com.eastking.service;

import com.eastking.model.dto.GiftBundleDto;
import com.eastking.model.dto.request.GiftBundleQueryRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.UUID;

public interface GiftBundleService {
    GiftBundleDto createGiftBundle(GiftBundleDto giftBundleDto);
    GiftBundleDto updateGiftBundle(UUID bundleId, GiftBundleDto giftBundleDto);
    void deleteGiftBundle(UUID bundleId);
    GiftBundleDto getGiftBundleById(UUID bundleId);
    Page<GiftBundleDto> getAllGiftBundles(GiftBundleQueryRequest queryRequest, Pageable pageable);
} 