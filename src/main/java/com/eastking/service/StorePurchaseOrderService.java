package com.eastking.service;

import com.eastking.model.dto.StorePurchaseOrderDto;
import com.eastking.model.dto.request.PurchaseOrderDiscrepancyRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.OffsetDateTime;
import java.util.UUID;

public interface StorePurchaseOrderService {

    Page<StorePurchaseOrderDto> searchStorePurchaseOrders(
        UUID storeId,
        OffsetDateTime shipmentDateFrom,
        OffsetDateTime shipmentDateTo,
        Short status,
        String keyword,
        String companyContext,
        Pageable pageable
    );

    StorePurchaseOrderDto getStorePurchaseOrderById(UUID id);

    StorePurchaseOrderDto reportDiscrepancy(UUID orderId, PurchaseOrderDiscrepancyRequest discrepancyRequest);

    StorePurchaseOrderDto confirmReceipt(UUID orderId);

    // Potentially a method to create a StorePurchaseOrder when a wholesale order is completed (system-triggered)
    // StorePurchaseOrderDto createFromWholesaleOrder(WholesaleOrderDto wholesaleOrderDto);
} 