package com.eastking.service;

import com.eastking.model.dto.StoreTransferOrderDto;
import com.eastking.model.dto.request.TransferActionRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.OffsetDateTime;
import java.util.UUID;

public interface StoreTransferOrderService {

    StoreTransferOrderDto createTransferOrder(StoreTransferOrderDto transferOrderDto);

    Page<StoreTransferOrderDto> searchTransferOrders(
        String transferType, // "ALL", "INCOMING", "OUTGOING"
        OffsetDateTime dateFrom,
        OffsetDateTime dateTo,
        Short status,
        String keyword,
        Pageable pageable
    );

    StoreTransferOrderDto getTransferOrderById(UUID id);

    StoreTransferOrderDto dispatchOrder(UUID orderId, TransferActionRequest actionRequest);

    StoreTransferOrderDto receiveOrder(UUID orderId, TransferActionRequest actionRequest);
    
    StoreTransferOrderDto cancelOrder(UUID orderId);

    StoreTransferOrderDto approveTransferOrder(UUID orderId);

    StoreTransferOrderDto rejectTransferOrder(UUID orderId, String rejectionReason);
} 