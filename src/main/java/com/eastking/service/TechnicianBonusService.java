package com.eastking.service;

import com.eastking.model.dto.BonusItemDto;
import com.eastking.model.dto.TechnicianBonusSetupDto;
import com.eastking.model.dto.TechnicianRoleConfigDto;
import java.util.List;
import java.util.UUID;

public interface TechnicianBonusService {
    // Technician Role Configuration
    TechnicianRoleConfigDto addTechnicianRole(UUID roleId);
    void removeTechnicianRole(UUID technicianRoleConfigId); // Uses the ID of the config entry
    List<TechnicianRoleConfigDto> getActiveTechnicianRoles();

    // Bonus Items
    BonusItemDto saveBonusItem(BonusItemDto bonusItemDto); // Create or Update
    void deleteBonusItem(UUID bonusItemId);
    BonusItemDto getBonusItemById(UUID bonusItemId); // Includes its current settings
    List<BonusItemDto> getAllBonusItemsWithSettings();

    // Combined Setup (for saving a potentially complex page state)
    void saveTechnicianBonusSetup(TechnicianBonusSetupDto setupDto);
} 