package com.eastking.service;

import com.eastking.enums.InventoryTransactionTypeEnum;
import com.eastking.model.dto.StoreInventoryDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;
import java.util.List;

public interface WarehouseStoreInventoryService {

    Page<StoreInventoryDto> getStoreInventory(UUID storeId, String keyword, Pageable pageable);

    StoreInventoryDto getSingleStoreInventoryItem(UUID storeId, String productBarcode);
    
    List<StoreInventoryDto> getAllStoreInventory(UUID storeId);

    // Method to update inventory, called by other services (Purchase Order, Transfer, Sales, Adjustments)
    // This should be robust and handle concurrent updates if necessary.
    void updateInventory(UUID storeId, String productBarcode, String productName, int quantityChange, String transactionType, String transactionId);

    // Overload for HQ/Warehouse-specific inventory updates (not tied to a specific store entity)
    void updateInventoryForWarehouse(UUID warehouseId, String productBarcode, String productName, int quantityChange, String transactionType, String transactionId);

    Integer getStockQuantity(UUID warehouseId, String productBarcode);

    List<StoreInventoryDto> searchTechnicianStock(UUID technicianId, String keyword);

    void updateTechnicianInventory(UUID technicianId, String productBarcode, String productName, int quantityChange, InventoryTransactionTypeEnum transactionType, String referenceNumber);
} 