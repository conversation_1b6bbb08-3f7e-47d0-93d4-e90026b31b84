package com.eastking.service;

import com.eastking.model.dto.ErpMahjongTableStockDto;
import com.eastking.model.dto.ErpWarehouseDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.UUID;
import java.util.List;

/**
 * 電動麻將桌庫存(ERP)服務介面
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
public interface ElectricMahjongTableStockService {

    /**
     * 查詢從ERP同步的電動麻將桌庫存列表 (分頁)
     *
     * @param erpWarehouseCode ERP倉庫代碼 (可選)
     * @param productCategoryId 商品分類ID (可選, 用於篩選電動桌相關分類)
     * @param productKeyword 商品關鍵字 (條碼或名稱, 可選)
     * @param pageable 分頁參數
     * @return ERP電動麻將桌庫存分頁數據
     */
    Page<ErpMahjongTableStockDto> getErpMahjongTableStock(
            String erpWarehouseCode, 
            UUID productCategoryId, // Added for filtering by mahjong table categories
            String productKeyword, 
            Pageable pageable);

    List<ErpWarehouseDto> getDistinctErpWarehouses(); // New method
} 