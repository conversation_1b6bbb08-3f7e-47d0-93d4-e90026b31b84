package com.eastking.service;

import com.eastking.model.dto.DocumentClauseDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface DocumentClauseService {

    DocumentClauseDto createDocumentClause(DocumentClauseDto dto);

    DocumentClauseDto getDocumentClauseById(UUID id);

    Page<DocumentClauseDto> searchDocumentClauses(
        Short companyDivisionCode,
        String keyword,
        Boolean isActive,
        Boolean isDefault,
        String dateType, // e.g., "effectiveDate", "createDate"
        String dateFrom,
        String dateTo,
        Pageable pageable
    );

    List<DocumentClauseDto> getActiveDefaultClauses(Short companyDivisionCode); // For use in orders

    DocumentClauseDto updateDocumentClause(UUID id, DocumentClauseDto dto);

    void deleteDocumentClause(UUID id);
} 