package com.eastking.service;

import com.eastking.enums.OrderActionTypeEnum;
import com.eastking.model.dto.request.*;
import com.eastking.model.dto.response.OrderDetailDto;
import com.eastking.model.dto.response.OrderRefundDto;
import com.eastking.model.dto.response.OrderSummaryDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.OffsetDateTime; // Added import
import java.util.List;          // Added import
import java.util.UUID;

// Forward declaration for DTOs to be created later, remove comments once created
// import com.eastking.model.dto.request.OrderItemReturnRequestDto;
// import com.eastking.model.dto.request.OrderItemReceivedDto;
// import com.eastking.model.dto.request.DispatchUpdateRequestDto;

public interface OrderService {

    OrderDetailDto createOrder(BaseOrderRequestDto orderRequestDto);

    OrderDetailDto getOrderById(UUID orderId);
    
    OrderDetailDto getDispatchOrderDetailById(UUID orderId);
    OrderDetailDto getWholesaleOrderDetailById(UUID orderId);
    
    OrderSummaryDto getOrderSummaryById(UUID orderId);

    Page<OrderSummaryDto> searchOrders(
        Short companyDivisionCode,
        Short orderTypeCode,
        String orderNumber,
        String customerKeyword,
        OffsetDateTime orderDateFrom,
        OffsetDateTime orderDateTo,
        List<Short> orderStatusCodes,
        UUID storeId,
        Pageable pageable
    );

    OrderDetailDto submitOrder(UUID orderId);

    OrderDetailDto checkoutStoreOrder(UUID orderId);

    OrderDetailDto processCheckout(UUID orderId);

    OrderDetailDto confirmHqApproval(UUID orderId);

    OrderDetailDto storeApprove(UUID orderId);
    OrderDetailDto storeReturnForCorrection(UUID orderId, String reason);
    OrderDetailDto submitHqFromStockFull(UUID orderId);
    OrderDetailDto hqReturnForCorrection(UUID orderId, String reason);
    OrderDetailDto hqReject(UUID orderId, String reason);

    OrderDetailDto initiateCancellation(UUID orderId, String reason);

    OrderDetailDto hqApprove(UUID orderId);
    OrderDetailDto copyOrderToNew(UUID orderId);

    OrderDetailDto markOrderShippedOrDispatchCompleted(UUID orderId);
    
    OrderDetailDto requestOrderCancellation(UUID orderId, String reason);
    OrderDetailDto approveOrderCancellation(UUID orderId, UUID approverId);
    OrderDetailDto rejectOrderCancellation(UUID orderId, UUID approverId, String reason);

    OrderDetailDto requestOrderReturn(OrderRefundRequestDto refundRequest);
    OrderDetailDto approveOrderReturn(UUID refundId);
    OrderDetailDto rejectOrderReturn(UUID refundId, String reason);
    // OrderDetailDto confirmReturnItemsReceived(UUID orderId, List<OrderItemReceivedDto> itemsReceived);
    // OrderDetailDto processReturnRefund(UUID orderId, List<PaymentDetailDto> refundPaymentDetails);

    void deleteDraftOrder(UUID orderId);

    OrderDetailDto updateOrderForCorrection(UUID orderId, BaseOrderRequestDto orderRequestDto);
    OrderDetailDto updateDispatchOrder(UUID orderId, DispatchProductOrderRequestDto dispatchDto);
    // OrderDetailDto updateDispatchInfo(UUID orderId, DispatchUpdateRequestDto dispatchUpdateRequestDto);

    OrderRefundDto initiateOrderReturnOrChange(UUID orderId, OrderActionTypeEnum actionType);
    OrderRefundDto initiateDispatchOrderReturnOrChange(UUID orderId, OrderActionTypeEnum actionType);
    
    Object completeStoreReturn(UUID refundId, StoreReturnRequestDto requestDto);
    Object completeDispatchReturn(UUID refundId, StoreReturnRequestDto requestDto);

    /**
     * Processes the checkout for a dispatch order, checking stock and transitioning its status.
     * @param orderId The ID of the dispatch order to process.
     * @return The updated order details.
     */
    OrderDetailDto processDispatchOrderCheckout(UUID orderId);
    OrderDetailDto processWholesaleOrderCheckout(UUID orderId);

    /**
     * Submits an order for HQ approval.
     * @param orderId The ID of the order to submit.
     * @return The updated order details.
     */
    OrderDetailDto submitForHqApproval(UUID orderId);

    /**
     * Submits a dispatch order from the form, updating its data and transitioning its status.
     * @param orderId The ID of the dispatch order to submit.
     * @param requestDto The DTO containing the order data.
     * @return The updated order details.
     */
    OrderDetailDto submitDispatchOrderFromForm(UUID orderId, DispatchProductOrderRequestDto requestDto);
    OrderDetailDto submitWholesaleOrderFromForm(UUID orderId, WholesaleProductOrderRequestDto requestDto) ;

    OrderDetailDto submitForHqReview(UUID orderId);

    void setItemAwaitingMaterial(UUID orderItemGroupId, boolean isAwaiting);

    OrderDetailDto partialUpdateDispatchOrder(UUID orderId, DispatchProductOrderRequestDto requestDto);
    OrderDetailDto partialUpdateWholesaleOrder(UUID orderId, WholesaleProductOrderRequestDto requestDto);
} 