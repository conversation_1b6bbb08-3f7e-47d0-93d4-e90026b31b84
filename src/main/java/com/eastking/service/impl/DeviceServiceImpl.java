package com.eastking.service.impl;

import com.eastking.model.dto.CustomerDeviceDto;
import com.eastking.model.entity.CustomerDevice;
import com.eastking.repository.CustomerDeviceRepository;
import com.eastking.service.DeviceService;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class DeviceServiceImpl implements DeviceService {

    private final CustomerDeviceRepository customerDeviceRepository;

    @Override
    public Page<CustomerDeviceDto> searchDevices(String keyword, Pageable pageable) {
        Specification<CustomerDevice> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));

            if (StringUtils.hasText(keyword)) {
                // To avoid N+1 queries, we use a JOIN FETCH
                root.fetch("customer", JoinType.LEFT);
                root.fetch("productSetting", JoinType.LEFT);

                Predicate p1 = cb.like(root.get("deviceSerialNumber"), "%" + keyword + "%");
                Predicate p2 = cb.like(root.get("productSetting").get("productName"), "%" + keyword + "%");
                Predicate p3 = cb.like(root.get("customer").get("customerName"), "%" + keyword + "%");
                predicates.add(cb.or(p1, p2, p3));
            }
            
            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<CustomerDevice> devicePage = customerDeviceRepository.findAll(spec, pageable);
        List<CustomerDeviceDto> dtoList = devicePage.getContent().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageable, devicePage.getTotalElements());
    }

    @Override
    public Optional<CustomerDeviceDto> findBySerialNumber(String serialNumber) {
        return customerDeviceRepository.findByDeviceSerialNumberAndIsDeleted(serialNumber, (short) 0)
                .map(this::convertToDto);
    }

    private CustomerDeviceDto convertToDto(CustomerDevice entity) {
        return CustomerDeviceDto.builder()
                .customerDeviceId(entity.getCustomerOwnedDeviceId())
                .customerId(entity.getCustomer() != null ? entity.getCustomer().getCustomerId() : null)
                .customerName(entity.getCustomer() != null ? entity.getCustomer().getCustomerName() : null)
                .customerPhone(entity.getCustomer() != null ? entity.getCustomer().getPhoneNumber() : null)
                .deviceSerialNumber(entity.getDeviceSerialNumber())
                .productName(entity.getProductSetting() != null ? entity.getProductSetting().getProductName() : null)
                .installationAddress(entity.getInstallationAddress())
                .warrantyDate(entity.getWarrantyDate())
                .lastRepairDate(entity.getLastRepairDate())
                .createTime(entity.getCreateTime())
                .build();
    }
} 