package com.eastking.service.impl;

import com.eastking.model.dto.ErpMahjongTableStockDto;
import com.eastking.model.dto.ErpWarehouseDto;
import com.eastking.model.entity.ErpMahjongTableStockLog;
import com.eastking.model.entity.ProductMenuItem;
import com.eastking.model.entity.ProductMenuCategory;
import com.eastking.model.entity.ProductSetting;
import com.eastking.repository.ErpMahjongTableStockLogRepository;
import com.eastking.repository.ProductMenuItemRepository;
import com.eastking.repository.ProductSettingRepository;
import com.eastking.service.ElectricMahjongTableStockService;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 電動麻將桌庫存(ERP)服務實作
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ElectricMahjongTableStockServiceImpl implements ElectricMahjongTableStockService {

    private static final Logger logger = LoggerFactory.getLogger(ElectricMahjongTableStockServiceImpl.class);

    private final ErpMahjongTableStockLogRepository erpMahjongTableStockLogRepository;
    private final ProductSettingRepository productSettingRepository;
    private final ProductMenuItemRepository productMenuItemRepository;

    @Override
    public Page<ErpMahjongTableStockDto> getErpMahjongTableStock(
            String erpWarehouseCode, UUID productCategoryId, String productKeyword, Pageable pageable) {
        logger.info("Fetching ERP Mahjong table stock for warehouse: {}, categoryId: {}, keyword: '{}', page: {}, size: {}",
            erpWarehouseCode, productCategoryId, productKeyword, pageable.getPageNumber(), pageable.getPageSize());

        Specification<ErpMahjongTableStockLog> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isLatestForProductWarehouse"), true));

            if (productCategoryId != null) {
                List<ProductMenuItem> menuItemsInCategory = productMenuItemRepository.findByProductMenuCategory_ProductMenuCategoryIdAndIsDeleted(productCategoryId, (short) 0);
                if (menuItemsInCategory.isEmpty()) {
                    // If category is specified but no products found in it, return empty
                    predicates.add(cb.disjunction()); // effectively a "WHERE FALSE" condition
                } else {
                    List<String> barcodesInCategory = menuItemsInCategory.stream()
                                                        .map(ProductMenuItem::getProductBarcode)
                                                        .distinct()
                                                        .collect(Collectors.toList());
                    if(barcodesInCategory.isEmpty()){
                         predicates.add(cb.disjunction()); // No barcodes, so no results
                    } else {
                        predicates.add(root.get("productBarcode").in(barcodesInCategory));
                    }
                }
            }
            
            if (StringUtils.hasText(erpWarehouseCode)) {
                predicates.add(cb.equal(root.get("erpWarehouseCode"), erpWarehouseCode));
            }

            if (StringUtils.hasText(productKeyword)) {
                String lowerKeyword = "%" + productKeyword.toLowerCase() + "%";
                predicates.add(cb.or(
                    cb.like(cb.lower(root.get("productBarcode")), lowerKeyword),
                    cb.like(cb.lower(root.get("productName")), lowerKeyword) 
                ));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        
        Page<ErpMahjongTableStockLog> stockLogPage = erpMahjongTableStockLogRepository.findAll(spec, pageable);

        List<String> barcodesFromPage = stockLogPage.getContent().stream()
                                        .map(ErpMahjongTableStockLog::getProductBarcode)
                                        .distinct().collect(Collectors.toList());
        
        Map<String, String> productCategoryMap = Collections.emptyMap();
        if (!barcodesFromPage.isEmpty()) {
             productCategoryMap = productMenuItemRepository.findByProductBarcodeInAndIsDeleted(barcodesFromPage, (short)0)
                .stream()
                .filter(pmi -> pmi.getProductMenuCategory() != null)
                .collect(Collectors.toMap(
                        ProductMenuItem::getProductBarcode, 
                        pmi -> pmi.getProductMenuCategory().getCategoryName(),
                        (existing, replacement) -> existing // In case of multiple categories for a barcode, take first
                ));
        }

        Map<String, String> finalProductCategoryMap = productCategoryMap; // effectively final for lambda

        List<ErpMahjongTableStockDto> dtoList = stockLogPage.getContent().stream()
            .map(log -> {
                // Product name from log is a snapshot, could also fetch from ProductSetting if fresher name is needed
                String categoryName = finalProductCategoryMap.getOrDefault(log.getProductBarcode(), "未分類");

                return new ErpMahjongTableStockDto(
                    log.getProductBarcode(),
                    log.getProductName(), 
                    categoryName,
                    log.getErpWarehouseCode(),
                    log.getErpWarehouseName(),
                    log.getQuantity(),
                    log.getSyncTime()
                );
            })
            .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageable, stockLogPage.getTotalElements());
    }

    @Override
    public List<ErpWarehouseDto> getDistinctErpWarehouses() {
        logger.info("Fetching distinct ERP warehouses.");
        return erpMahjongTableStockLogRepository.findDistinctErpWarehouses();
    }
} 