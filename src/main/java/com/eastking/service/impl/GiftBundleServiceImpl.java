package com.eastking.service.impl;

import com.eastking.enums.ActivationStatusEnum;
import com.eastking.enums.DeleteStatusEnum;
import com.eastking.exception.DataConflictException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.GiftBundleDto;
import com.eastking.model.dto.GiftBundleItemDto;
import com.eastking.model.dto.request.GiftBundleQueryRequest;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.service.GiftBundleService;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class GiftBundleServiceImpl implements GiftBundleService {
    private static final Logger logger = LoggerFactory.getLogger(GiftBundleServiceImpl.class);

    private final GiftBundleRepository bundleRepository;
    private final GiftBundleItemRepository itemRepository;
    private final UserAccountRepository userAccountRepository; // For audit names
    private final ProductSettingRepository productSettingRepository; // To fetch product names

    @Override
    @Transactional
    public GiftBundleDto createGiftBundle(GiftBundleDto dto) {
        if (dto.getStartTime() == null || dto.getEndTime() == null || dto.getEndTime().isBefore(dto.getStartTime())) {
            throw new DataConflictException("套裝結束時間必須晚於開始時間。");
        }
        // Optional: Check for duplicate bundle name or main product if needed

        GiftBundle entity = new GiftBundle();
        mapDtoToEntity(dto, entity);
        entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        GiftBundle savedBundle = bundleRepository.save(entity);

        updateGiftBundleItems(savedBundle, dto.getItems());
        return getGiftBundleById(savedBundle.getBundleId());
    }

    @Override
    @Transactional
    public GiftBundleDto updateGiftBundle(UUID bundleId, GiftBundleDto dto) {
        GiftBundle entity = bundleRepository.findById(bundleId)
            .orElseThrow(() -> new ResourceNotFoundException("GiftBundle", "ID", bundleId));
        
        if (dto.getStartTime() == null || dto.getEndTime() == null || dto.getEndTime().isBefore(dto.getStartTime())) {
            throw new DataConflictException("套裝結束時間必須晚於開始時間。");
        }

        mapDtoToEntity(dto, entity);
        GiftBundle savedBundle = bundleRepository.save(entity);
        updateGiftBundleItems(savedBundle, dto.getItems());
        return getGiftBundleById(savedBundle.getBundleId());
    }

    private void mapDtoToEntity(GiftBundleDto dto, GiftBundle entity) {
        entity.setBundleName(dto.getBundleName());
        entity.setMainProductBarcode(dto.getMainProductBarcode());
        // Fetch and set main product name from ProductSetting if not provided or to ensure consistency
        productSettingRepository.findByProductBarcodeAndIsDeleted(dto.getMainProductBarcode(), DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(ps -> entity.setMainProductName(ps.getProductName()));
        entity.setStartTime(dto.getStartTime());
        entity.setEndTime(dto.getEndTime());
        entity.setIsActive(dto.getIsActive() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode());
    }

    private void updateGiftBundleItems(GiftBundle bundle, List<GiftBundleItemDto> itemDtos) {
        itemRepository.deleteByGiftBundle(bundle); // Simple delete and re-add
        if (!CollectionUtils.isEmpty(itemDtos)) {
            List<GiftBundleItem> items = itemDtos.stream().map(dto -> {
                GiftBundleItem item = new GiftBundleItem();
                item.setGiftBundle(bundle);
                item.setGiftProductBarcode(dto.getGiftProductBarcode());
                productSettingRepository.findByProductBarcodeAndIsDeleted(dto.getGiftProductBarcode(), DeleteStatusEnum.NOT_DELETED.getCode())
                    .ifPresent(ps -> item.setGiftProductName(ps.getProductName()));
                item.setQuantity(dto.getQuantity());
                item.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                return item;
            }).collect(Collectors.toList());
            itemRepository.saveAll(items);
        }
    }

    @Override
    @Transactional
    public void deleteGiftBundle(UUID bundleId) {
        GiftBundle entity = bundleRepository.findById(bundleId)
            .orElseThrow(() -> new ResourceNotFoundException("GiftBundle", "ID", bundleId));
        entity.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        entity.setIsActive(ActivationStatusEnum.NO.getCode());
        bundleRepository.save(entity);
        // Associated items are not hard-deleted
    }

    @Override
    @Transactional(readOnly = true)
    public GiftBundleDto getGiftBundleById(UUID bundleId) {
        GiftBundle entity = bundleRepository.findById(bundleId)
            .filter(b -> DeleteStatusEnum.NOT_DELETED.getCode().equals(b.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("GiftBundle", "ID", bundleId));
        return convertToDto(entity);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GiftBundleDto> getAllGiftBundles(GiftBundleQueryRequest queryRequest, Pageable pageable) {
        Specification<GiftBundle> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));

            if (StringUtils.hasText(queryRequest.getKeyword())) {
                Predicate nameLike = cb.like(cb.lower(root.get("bundleName")), "%" + queryRequest.getKeyword().toLowerCase() + "%");
                Predicate barcodeLike = cb.like(cb.lower(root.get("mainProductBarcode")), "%" + queryRequest.getKeyword().toLowerCase() + "%");
                Predicate mainProductNameLike = cb.like(cb.lower(root.get("mainProductName")), "%" + queryRequest.getKeyword().toLowerCase() + "%");
                predicates.add(cb.or(nameLike, barcodeLike, mainProductNameLike));
            }
            if (queryRequest.getEffectiveDateFrom() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("endTime"), queryRequest.getEffectiveDateFrom()));
            }
            if (queryRequest.getEffectiveDateTo() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("startTime"), queryRequest.getEffectiveDateTo()));
            }
            if (queryRequest.getIsActive() != null) {
                predicates.add(cb.equal(root.get("isActive"), queryRequest.getIsActive() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode()));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return bundleRepository.findAll(spec, pageable).map(this::convertToDtoForList);
    }

    private GiftBundleDto convertToDto(GiftBundle entity) {
        GiftBundleDto dto = new GiftBundleDto();
        BeanUtils.copyProperties(entity, dto, "items");
        dto.setIsActive(ActivationStatusEnum.YES.getCode().equals(entity.getIsActive()));

        List<GiftBundleItem> items = itemRepository.findByGiftBundleAndIsDeleted(entity, DeleteStatusEnum.NOT_DELETED.getCode());
        dto.setItems(items.stream().map(item -> {
            GiftBundleItemDto itemDto = new GiftBundleItemDto();
            BeanUtils.copyProperties(item, itemDto);
            return itemDto;
        }).collect(Collectors.toList()));

        if (entity.getCreateBy() != null) {
            userAccountRepository.findById(entity.getCreateBy()).ifPresent(u -> dto.setCreateByName(u.getUserName()));
        }
        if (entity.getUpdateBy() != null) {
            userAccountRepository.findById(entity.getUpdateBy()).ifPresent(u -> dto.setUpdateByName(u.getUserName()));
        }
        return dto;
    }
    
    private GiftBundleDto convertToDtoForList(GiftBundle entity) {
        GiftBundleDto dto = new GiftBundleDto();
        BeanUtils.copyProperties(entity, dto, "items"); // Exclude items for list view for brevity
        dto.setIsActive(ActivationStatusEnum.YES.getCode().equals(entity.getIsActive()));
         if (entity.getCreateBy() != null) {
            userAccountRepository.findById(entity.getCreateBy()).ifPresent(u -> dto.setCreateByName(u.getUserName()));
        }
        if (entity.getUpdateBy() != null) {
            userAccountRepository.findById(entity.getUpdateBy()).ifPresent(u -> dto.setUpdateByName(u.getUserName()));
        }
        dto.setItems(List.of()); // Explicitly empty or null for list view
        return dto;
    }
} 