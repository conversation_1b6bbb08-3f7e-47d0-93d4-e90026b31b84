package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.model.dto.SmsTemplateDto;
import com.eastking.model.entity.SmsTemplateEntity;
import com.eastking.repository.SmsTemplateRepository;
import com.eastking.service.SmsTemplateService;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.exception.DataConflictException;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class SmsTemplateServiceImpl implements SmsTemplateService {

    private final SmsTemplateRepository smsTemplateRepository;

    public SmsTemplateServiceImpl(SmsTemplateRepository smsTemplateRepository) {
        this.smsTemplateRepository = smsTemplateRepository;
    }

    @Override
    @Transactional
    public SmsTemplateDto createSmsTemplate(SmsTemplateDto dto) {
        smsTemplateRepository.findByTemplateNameAndIsDeleted(dto.getTemplateName(), DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(e -> { 
                throw new DataConflictException("簡訊模板名稱已存在: " + dto.getTemplateName()); 
            });
        SmsTemplateEntity entity = convertToEntity(dto);
        entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        SmsTemplateEntity savedEntity = smsTemplateRepository.save(entity);
        return convertToDto(savedEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SmsTemplateDto> getSmsTemplateById(UUID id) {
        return smsTemplateRepository.findById(id)
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SmsTemplateDto> getSmsTemplateByName(String templateName) {
        return smsTemplateRepository.findByTemplateNameAndIsDeleted(templateName, DeleteStatusEnum.NOT_DELETED.getCode())
            .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<SmsTemplateDto> getAllSmsTemplates(Pageable pageable, String templateType, String keyword) {
        Specification<SmsTemplateEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if (StringUtils.hasText(templateType)) {
                predicates.add(cb.equal(root.get("templateType"), templateType));
            }
            if (StringUtils.hasText(keyword)) {
                predicates.add(cb.like(cb.lower(root.get("templateName")), "%" + keyword.toLowerCase() + "%"));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return smsTemplateRepository.findAll(spec, pageable).map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SmsTemplateDto> getSmsTemplatesByType(String templateType) {
         Specification<SmsTemplateEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if (StringUtils.hasText(templateType)) {
                predicates.add(cb.equal(root.get("templateType"), templateType));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return smsTemplateRepository.findAll(spec).stream().map(this::convertToDto).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public SmsTemplateDto updateSmsTemplate(UUID id, SmsTemplateDto dto) {
        SmsTemplateEntity entity = smsTemplateRepository.findById(id)
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("SmsTemplate not found with id: " + id));

        if (!entity.getTemplateName().equals(dto.getTemplateName())) {
            smsTemplateRepository.findByTemplateNameAndIsDeleted(dto.getTemplateName(), DeleteStatusEnum.NOT_DELETED.getCode())
                .filter(e -> !e.getSmsTemplateId().equals(id))
                .ifPresent(e -> { 
                    throw new DataConflictException("簡訊模板名稱已存在: " + dto.getTemplateName()); 
                });
        }
        
        UUID createBy = entity.getCreateBy();
        java.time.OffsetDateTime createTime = entity.getCreateTime();

        BeanUtils.copyProperties(dto, entity, "smsTemplateId", "createTime", "updateTime", "createBy", "updateBy", "isDeleted");
        
        entity.setCreateBy(createBy);
        entity.setCreateTime(createTime);
        
        SmsTemplateEntity updatedEntity = smsTemplateRepository.save(entity);
        return convertToDto(updatedEntity);
    }

    @Override
    @Transactional
    public void deleteSmsTemplate(UUID id) {
        SmsTemplateEntity entity = smsTemplateRepository.findById(id)
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("SmsTemplate not found with id: " + id));
        entity.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        smsTemplateRepository.save(entity);
    }

    private SmsTemplateDto convertToDto(SmsTemplateEntity entity) {
        SmsTemplateDto dto = new SmsTemplateDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    private SmsTemplateEntity convertToEntity(SmsTemplateDto dto) {
        SmsTemplateEntity entity = new SmsTemplateEntity();
        BeanUtils.copyProperties(dto, entity, "isDeleted"); // isDeleted handled separately
        return entity;
    }
} 