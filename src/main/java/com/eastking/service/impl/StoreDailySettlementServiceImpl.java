package com.eastking.service.impl;

import com.eastking.enums.StoreDailySettlementStatusEnum;
import com.eastking.enums.StoreJournalTransactionTypeEnum;
import com.eastking.exception.DataConflictException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.request.DailySettlementRequestDto;
import com.eastking.model.dto.response.ExpenditureDetailDto;
import com.eastking.model.dto.response.StoreDailySettlementDto;
import com.eastking.model.dto.response.StoreJournalEntryDto;
import com.eastking.model.entity.StoreDailyJournal;
import com.eastking.model.entity.StoreDailySettlement;
import com.eastking.model.entity.StoreEntity;
import com.eastking.model.entity.UserAccount;
import com.eastking.repository.StoreDailyJournalRepository;
import com.eastking.repository.StoreDailySettlementRepository;
import com.eastking.repository.StoreRepository;
import com.eastking.repository.UserAccountRepository;
import com.eastking.service.StoreDailySettlementService;
import com.eastking.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StoreDailySettlementServiceImpl implements StoreDailySettlementService {

    private final StoreRepository storeRepository;
    private final UserAccountRepository userAccountRepository;
    private final StoreDailyJournalRepository storeDailyJournalRepository;
    private final StoreDailySettlementRepository storeDailySettlementRepository;

    @Override
    @Transactional(readOnly = true)
    public StoreDailySettlementDto getDailySettlementData(UUID storeId, LocalDate date) {
        storeDailySettlementRepository.findTopByStore_StoreIdAndSettlementDateBeforeOrderBySettlementDateDesc(storeId, date)
            .ifPresent(lastSettlement -> {
                if (lastSettlement.getSettlementDate().plusDays(1).isBefore(date)) {
                    throw new DataConflictException("尚有未完成的日結：" + lastSettlement.getSettlementDate().plusDays(1));
                }
            });
        
        if (storeDailySettlementRepository.existsByStore_StoreIdAndSettlementDate(storeId, date)) {
            throw new DataConflictException("本日已完成結算，不可重複操作");
        }

        StoreEntity store = storeRepository.findById(storeId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到門市: " + storeId));

        OffsetDateTime startOfDay = date.atStartOfDay().atOffset(ZoneOffset.UTC);
        OffsetDateTime endOfDay = date.atTime(LocalTime.MAX).atOffset(ZoneOffset.UTC);

        List<StoreDailyJournal> journalEntries = storeDailyJournalRepository
                .findByStore_StoreIdAndCreateTimeBetween(storeId, startOfDay, endOfDay);

        StoreDailySettlementDto dto = new StoreDailySettlementDto();
        dto.setStoreId(storeId);
        dto.setStoreName(store.getStoreName());
        dto.setSettlementDate(date);
        
        List<StoreJournalEntryDto> salesDetails = filterAndMapJournalEntries(journalEntries, StoreJournalTransactionTypeEnum.SALE_RECEIPT);
        List<StoreJournalEntryDto> returnDetails = filterAndMapJournalEntries(journalEntries, StoreJournalTransactionTypeEnum.REFUND_PAYOUT);
        
        dto.setSalesDetails(salesDetails);
        dto.setReturnDetails(returnDetails);

        dto.setTotalSalesAmount(calculateTotal(salesDetails));
        dto.setTotalReturnsAmount(calculateTotal(returnDetails).abs());
        dto.setTotalRevenueAmount(dto.getTotalSalesAmount().subtract(dto.getTotalReturnsAmount()));
        
        // This part needs adjustment based on PaymentMethodEnum on journal entries
        // For now, let's assume a simplified calculation
        BigDecimal cashIn = calculateJournalTotalByPaymentMethod(journalEntries, (short)1); // Assuming 1 is Cash
        BigDecimal cashOutRefunds = calculateJournalTotalByPaymentMethodForRefunds(journalEntries, (short)1);
        dto.setCashIncomeAmount(cashIn.subtract(cashOutRefunds));

        BigDecimal creditCardIn = calculateJournalTotalByPaymentMethod(journalEntries, (short)2); // Assuming 2 is Credit Card
        dto.setCreditCardIncomeAmount(creditCardIn);
        
        BigDecimal remittanceIn = calculateJournalTotalByPaymentMethod(journalEntries, (short)3); // Assuming 3 is Remittance
        dto.setRemittanceIncomeAmount(remittanceIn);

        dto.setAccountsReceivableAmount(BigDecimal.ZERO); // This logic needs to be derived from order status, not journal
        dto.setReceivableRepaymentAmount(BigDecimal.ZERO); // This logic needs to be derived from payments against old orders

        return dto;
    }
    
    @Override
    @Transactional
    public StoreDailySettlementDto performDailySettlement(DailySettlementRequestDto requestDto) {
        LocalDate date = requestDto.getSettlementDate();
        UUID storeId = requestDto.getStoreId();

        if (storeDailySettlementRepository.existsByStore_StoreIdAndSettlementDate(storeId, date)) {
            throw new DataConflictException("本日已完成結算，不可重複操作");
        }
        
        UUID currentUserId = SecurityUtil.getCurrentUserId()
                .orElseThrow(() -> new ResourceNotFoundException("無法獲取當前登入用戶ID"));
        UserAccount currentUser = userAccountRepository.findById(currentUserId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到用戶: " + currentUserId));

        StoreDailySettlementDto summaryData = getDailySettlementData(storeId, date);

        StoreDailySettlement settlement = new StoreDailySettlement();
        settlement.setStore(storeRepository.getReferenceById(storeId));
        settlement.setSettlementDate(date);
        settlement.setSettlementStatusCode(StoreDailySettlementStatusEnum.SETTLED.getCode());
        settlement.setSettlementBy(currentUser);
        settlement.setRemarks(requestDto.getRemarks());
        
        settlement.setTotalSalesAmount(summaryData.getTotalSalesAmount());
        settlement.setTotalReturnsAmount(summaryData.getTotalReturnsAmount());
        settlement.setTotalRevenueAmount(summaryData.getTotalRevenueAmount());
        settlement.setCashIncomeAmount(summaryData.getCashIncomeAmount());
        settlement.setCreditCardIncomeAmount(summaryData.getCreditCardIncomeAmount());
        settlement.setRemittanceIncomeAmount(summaryData.getRemittanceIncomeAmount());
        settlement.setAccountsReceivableAmount(summaryData.getAccountsReceivableAmount());
        settlement.setReceivableRepaymentAmount(summaryData.getReceivableRepaymentAmount());

        BigDecimal actualCash = requestDto.getActualCashIncomeAmount() != null ? requestDto.getActualCashIncomeAmount() : BigDecimal.ZERO;
        settlement.setActualCashIncomeAmount(actualCash);
        
        BigDecimal totalExpenditure = requestDto.getExpenditureDetails() == null ? BigDecimal.ZERO :
                requestDto.getExpenditureDetails().stream()
                        .map(ExpenditureDetailDto::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        settlement.setTotalExpenditureAmount(totalExpenditure);
        
        BigDecimal cashOnBook = summaryData.getCashIncomeAmount();
        settlement.setCashDiscrepancyAmount(actualCash.subtract(cashOnBook));
        settlement.setTotalRepaymentAmount(actualCash.subtract(totalExpenditure));

        StoreDailySettlement savedSettlement = storeDailySettlementRepository.save(settlement);
        
        return mapToSettlementDto(savedSettlement);
    }

    private List<StoreJournalEntryDto> filterAndMapJournalEntries(List<StoreDailyJournal> entries, StoreJournalTransactionTypeEnum type) {
        return entries.stream()
                .filter(entry -> Objects.equals(type.getCode(), entry.getTransactionTypeCode()))
                .map(this::mapToJournalEntryDto)
                .collect(Collectors.toList());
    }

    private StoreJournalEntryDto mapToJournalEntryDto(StoreDailyJournal entry) {
        StoreJournalEntryDto dto = new StoreJournalEntryDto();
        dto.setStoreDailyJournalId(entry.getJournalEntryId());
        if (entry.getTransactionTypeCode() != null) {
            dto.setTransactionTypeName(StoreJournalTransactionTypeEnum.fromCode(entry.getTransactionTypeCode()).getDescription());
        }
        dto.setSourceDocumentNumber(entry.getRelatedDocumentNumber());
        dto.setCustomerName(entry.getDescription());
        dto.setProductName(""); 
        dto.setAmount(entry.getAmountIn() != null ? entry.getAmountIn() : (entry.getAmountOut() != null ? entry.getAmountOut().negate() : BigDecimal.ZERO));
        dto.setCreateTime(entry.getCreateTime());
        return dto;
    }

    private BigDecimal calculateTotal(List<StoreJournalEntryDto> entries) {
        return entries.stream()
                .map(StoreJournalEntryDto::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    private BigDecimal calculateJournalTotalByPaymentMethod(List<StoreDailyJournal> entries, Short paymentMethodCode) {
        return entries.stream()
            .filter(e -> e.getTransactionTypeCode().equals(StoreJournalTransactionTypeEnum.SALE_RECEIPT.getCode()) && paymentMethodCode.equals(e.getPaymentMethodCode()))
            .map(StoreDailyJournal::getAmountIn)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateJournalTotalByPaymentMethodForRefunds(List<StoreDailyJournal> entries, Short paymentMethodCode) {
        return entries.stream()
            .filter(e -> e.getTransactionTypeCode().equals(StoreJournalTransactionTypeEnum.REFUND_PAYOUT.getCode()) && paymentMethodCode.equals(e.getPaymentMethodCode()))
            .map(StoreDailyJournal::getAmountOut)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    private StoreDailySettlementDto mapToSettlementDto(StoreDailySettlement entity) {
        StoreDailySettlementDto dto = new StoreDailySettlementDto();
        dto.setStoreDailySettlementId(entity.getStoreDailySettlementId());
        if (entity.getStore() != null) {
            dto.setStoreId(entity.getStore().getStoreId());
            dto.setStoreName(entity.getStore().getStoreName());
        }
        dto.setSettlementDate(entity.getSettlementDate());
        dto.setSettlementStatusCode(entity.getSettlementStatusCode());
        if (entity.getSettlementStatusCode() != null) {
            dto.setSettlementStatus(StoreDailySettlementStatusEnum.fromCode(entity.getSettlementStatusCode()).getDescription());
        }
        dto.setTotalSalesAmount(entity.getTotalSalesAmount());
        dto.setTotalReturnsAmount(entity.getTotalReturnsAmount());
        dto.setTotalRevenueAmount(entity.getTotalRevenueAmount());
        dto.setTotalExpenditureAmount(entity.getTotalExpenditureAmount());
        dto.setCashIncomeAmount(entity.getCashIncomeAmount());
        dto.setCreditCardIncomeAmount(entity.getCreditCardIncomeAmount());
        dto.setRemittanceIncomeAmount(entity.getRemittanceIncomeAmount());
        dto.setAccountsReceivableAmount(entity.getAccountsReceivableAmount());
        dto.setReceivableRepaymentAmount(entity.getReceivableRepaymentAmount());
        dto.setActualCashIncomeAmount(entity.getActualCashIncomeAmount());
        dto.setCashDiscrepancyAmount(entity.getCashDiscrepancyAmount());
        dto.setTotalRepaymentAmount(entity.getTotalRepaymentAmount());
        if (entity.getSettlementBy() != null) {
            dto.setSettlementById(entity.getSettlementBy().getUserAccountId());
            dto.setSettlementByName(entity.getSettlementBy().getUserName());
        }
        dto.setRemarks(entity.getRemarks());
        dto.setSalesDetails(new ArrayList<>());
        dto.setReturnDetails(new ArrayList<>());
        dto.setExpenditureDetails(new ArrayList<>());
        return dto;
    }
} 