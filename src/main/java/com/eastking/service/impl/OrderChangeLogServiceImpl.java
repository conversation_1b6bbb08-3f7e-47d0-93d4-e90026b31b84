package com.eastking.service.impl;

import com.eastking.model.entity.Order;
import com.eastking.model.entity.OrderChangeLog;
import com.eastking.model.entity.OrderRefund;
import com.eastking.model.entity.UserAccount;
import com.eastking.repository.OrderChangeLogRepository;
import com.eastking.service.OrderChangeLogService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class OrderChangeLogServiceImpl implements OrderChangeLogService {

    private static final Logger logger = LoggerFactory.getLogger(OrderChangeLogServiceImpl.class);
    private final OrderChangeLogRepository orderChangeLogRepository;
    private final ObjectMapper objectMapper;

    @Override
    public void logChange(Order order, OrderRefund refund, Short previousStatus, Short newStatus, UserAccount changedByUser, String reason, Object requestDto) {
        try {
            OrderChangeLog log = OrderChangeLog.builder()
                    .orderId(order != null ? order.getOrderId() : (refund != null ? refund.getOriginalOrder().getOrderId() : null))
                    .refundId(refund != null ? refund.getOrderRefundId() : null)
                    .previousStatus(previousStatus)
                    .newStatus(newStatus)
                    .changedByUserId(changedByUser.getUserAccountId())
                    .changeReason(reason)
                    .requestJson(objectMapper.writeValueAsString(requestDto))
                    .build();
            orderChangeLogRepository.save(log);
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize request DTO for order change log", e);
        }
    }
    
    @Override
    public void logOrderChange(Order order, Short previousStatus, Short newStatus, UserAccount changedByUser, String reason, Object requestDto) {
        logChange(order, null, previousStatus, newStatus, changedByUser, reason, requestDto);
    }

    @Override
    public void logRefundChange(OrderRefund refund, Short previousStatus, Short newStatus, UserAccount changedByUser, String reason, Object requestDto) {
        logChange(refund.getOriginalOrder(), refund, previousStatus, newStatus, changedByUser, reason, requestDto);
    }
} 