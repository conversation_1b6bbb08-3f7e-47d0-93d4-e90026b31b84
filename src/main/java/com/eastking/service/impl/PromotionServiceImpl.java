package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.exception.DataConflictException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.PromotionChannelDto;
import com.eastking.model.dto.PromotionDto;
import com.eastking.model.dto.PromotionProductDto;
import com.eastking.model.dto.request.PromotionQueryRequest;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.service.PromotionService;
import com.eastking.enums.ActivationStatusEnum;
import com.eastking.enums.PromotionChannelTypeEnum;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.SortedSet;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PromotionServiceImpl implements PromotionService {
    private static final Logger logger = LoggerFactory.getLogger(PromotionServiceImpl.class);

    private final PromotionRepository promotionRepository;
    private final PromotionChannelRepository channelRepository;
    private final PromotionProductRepository productRepository;
    private final UserAccountRepository userAccountRepository; // For audit names
    private final ProductSettingRepository productSettingRepository; // To get original price for display

    @Override
    @Transactional
    public PromotionDto createPromotion(PromotionDto dto) {
        // Basic validation on dates
        if (dto.getStartTime() == null || dto.getEndTime() == null || dto.getEndTime().isBefore(dto.getStartTime())) {
            throw new DataConflictException("活動結束時間必須晚於開始時間。");
        }

        Promotion entity = new Promotion();
        mapDtoToEntity(dto, entity);
        entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        Promotion savedPromotion = promotionRepository.save(entity);

        updatePromotionChannels(savedPromotion, dto.getChannels());
        updatePromotionProducts(savedPromotion, dto.getProducts());

        return getPromotionById(savedPromotion.getPromotionId()); // Fetch again to get populated DTO
    }

    @Override
    @Transactional
    public PromotionDto updatePromotion(UUID promotionId, PromotionDto dto) {
        Promotion entity = promotionRepository.findById(promotionId)
            .orElseThrow(() -> new ResourceNotFoundException("Promotion", "ID", promotionId));
        
        if (dto.getStartTime() == null || dto.getEndTime() == null || dto.getEndTime().isBefore(dto.getStartTime())) {
            throw new DataConflictException("活動結束時間必須晚於開始時間。");
        }

        mapDtoToEntity(dto, entity);
        Promotion savedPromotion = promotionRepository.save(entity);

        updatePromotionChannels(savedPromotion, dto.getChannels());
        updatePromotionProducts(savedPromotion, dto.getProducts());
        
        return getPromotionById(savedPromotion.getPromotionId());
    }

    private void mapDtoToEntity(PromotionDto dto, Promotion entity) {
        entity.setPromotionName(dto.getPromotionName());
        entity.setStartTime(dto.getStartTime());
        entity.setEndTime(dto.getEndTime());
        entity.setIsActive(dto.getIsActive() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode());
    }

    private void updatePromotionChannels(Promotion promotion, List<PromotionChannelDto> channelDtos) {
        channelRepository.deleteByPromotion(promotion); // Simple delete and re-add
        if (!CollectionUtils.isEmpty(channelDtos)) {
            List<PromotionChannel> channels = channelDtos.stream().map(dto -> {
                PromotionChannel pc = new PromotionChannel();
                pc.setPromotion(promotion);
                pc.setChannelType(dto.getChannelType());
                pc.setChannelTargetId(dto.getChannelTargetId());
                pc.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                return pc;
            }).collect(Collectors.toList());
            channelRepository.saveAll(channels);
        }
    }

    private void updatePromotionProducts(Promotion promotion, List<PromotionProductDto> productDtos) {
        productRepository.deleteByPromotion(promotion); // Simple delete and re-add
        if (!CollectionUtils.isEmpty(productDtos)) {
            List<PromotionProduct> products = productDtos.stream().map(dto -> {
                PromotionProduct pp = new PromotionProduct();
                pp.setPromotion(promotion);
                pp.setProductBarcode(dto.getProductBarcode());
                // productName can be fetched from product_setting if needed, or stored as-is from DTO
                ProductSetting productSetting = productSettingRepository.findByProductBarcodeAndIsDeleted(dto.getProductBarcode(), DeleteStatusEnum.NOT_DELETED.getCode()).orElse(null);
                pp.setProductName(productSetting != null ? productSetting.getProductName() : dto.getProductName());
                pp.setPromoPrice(dto.getPromoPrice());
                pp.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                return pp;
            }).collect(Collectors.toList());
            productRepository.saveAll(products);
        }
    }

    @Override
    @Transactional
    public void deletePromotion(UUID promotionId) {
        Promotion entity = promotionRepository.findById(promotionId)
            .orElseThrow(() -> new ResourceNotFoundException("Promotion", "ID", promotionId));
        entity.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        entity.setIsActive(ActivationStatusEnum.NO.getCode());
        promotionRepository.save(entity);
        // Associated channels and products are not hard-deleted but become inactive due to promotion being deleted.
    }

    @Override
    @Transactional(readOnly = true)
    public PromotionDto getPromotionById(UUID promotionId) {
        Promotion entity = promotionRepository.findById(promotionId)
            .filter(p -> DeleteStatusEnum.NOT_DELETED.getCode().equals(p.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("Promotion", "ID", promotionId));
        return convertToDto(entity);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PromotionDto> getAllPromotions(PromotionQueryRequest queryRequest, Pageable pageable) {
        Specification<Promotion> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));

            if (StringUtils.hasText(queryRequest.getKeyword())) {
                predicates.add(cb.like(cb.lower(root.get("promotionName")), "%" + queryRequest.getKeyword().toLowerCase() + "%"));
            }
            if (queryRequest.getActivityDateFrom() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("endTime"), queryRequest.getActivityDateFrom()));
            }
            if (queryRequest.getActivityDateTo() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("startTime"), queryRequest.getActivityDateTo()));
            }
            if (queryRequest.getIsActive() != null) {
                predicates.add(cb.equal(root.get("isActive"), queryRequest.getIsActive() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode()));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return promotionRepository.findAll(spec, pageable).map(this::convertToDtoForList);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PromotionDto> searchAvailablePromotions(UUID storeId) {
        //門市商品訂單的優惠活動
        Specification<Promotion> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Filter by active status
            predicates.add(cb.equal(root.get("isActive"), ActivationStatusEnum.YES.getCode()));

            // Filter by date validity
            OffsetDateTime now = OffsetDateTime.now();
            predicates.add(cb.lessThanOrEqualTo(root.get("startTime"), now));
            predicates.add(cb.greaterThanOrEqualTo(root.get("endTime"), now));

            // Filter by channel (Store specific or Head Office)
            if (storeId != null) {
                Subquery<UUID> subquery = query.subquery(UUID.class);
                Root<PromotionChannel> subRoot = subquery.from(PromotionChannel.class);
                
                Predicate storePredicate = cb.equal(subRoot.get("channelTargetId"), storeId.toString());
                Predicate headOfficePredicate = cb.equal(subRoot.get("channelType"), PromotionChannelTypeEnum.HEAD_OFFICE.name());

                subquery.select(subRoot.get("promotion").get("promotionId"))
                        .where(cb.or(storePredicate, headOfficePredicate));
                        
                predicates.add(root.get("promotionId").in(subquery));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return promotionRepository.findAll(spec).stream()
            .map(this::convertToDto) // Use the full DTO converter
            .collect(Collectors.toList());
    }

    @Override
    public List<PromotionDto> getSelectablePromotions(UUID storeId, UUID distributorId) {
        //派工商品訂單的優惠活動
        Specification<Promotion> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            predicates.add(cb.equal(root.get("isActive"), ActivationStatusEnum.YES.getCode()));

            OffsetDateTime now = OffsetDateTime.now();
            predicates.add(cb.lessThanOrEqualTo(root.get("startTime"), now));
            predicates.add(cb.greaterThanOrEqualTo(root.get("endTime"), now));

            // Subquery for channels
            Subquery<UUID> subquery = query.subquery(UUID.class);
            Root<PromotionChannel> subRoot = subquery.from(PromotionChannel.class);
            
            List<Predicate> channelPredicates = new ArrayList<>();
            // Always include Head Office promotions
            channelPredicates.add(cb.equal(subRoot.get("channelType"), PromotionChannelTypeEnum.HEAD_OFFICE.name()));

            // Include store-specific promotions if storeId is provided
            if (storeId != null) {
                Predicate storePredicate = cb.and(
                    //cb.equal(subRoot.get("channelType"), PromotionChannelTypeEnum.DIRECT_STORE.name()),
                    cb.equal(subRoot.get("channelTargetId"), storeId.toString())
                );
                channelPredicates.add(storePredicate);
            }
            
            // Include distributor-specific promotions if distributorId is provided
            if (distributorId != null) {
                Predicate distributorPredicate = cb.and(
                    //cb.equal(subRoot.get("channelType"), PromotionChannelTypeEnum.FRANCHISE_STORE.name()),
                    cb.equal(subRoot.get("channelTargetId"), distributorId.toString())
                );
                channelPredicates.add(distributorPredicate);
            }

            subquery.select(subRoot.get("promotion").get("promotionId"))
                    .where(cb.or(channelPredicates.toArray(new Predicate[0])));
            
            predicates.add(root.get("promotionId").in(subquery));
            
            return cb.and(predicates.toArray(new Predicate[0]));
        };

        return promotionRepository.findAll(spec).stream()
            .map(this::convertToDto) // Use the full DTO converter to include products
            .collect(Collectors.toList());
    }

    private PromotionDto convertToDto(Promotion entity) {
        PromotionDto dto = new PromotionDto();
        BeanUtils.copyProperties(entity, dto, "channels", "products");
        dto.setIsActive(ActivationStatusEnum.YES.getCode().equals(entity.getIsActive()));

        List<PromotionChannel> channels = channelRepository.findByPromotionAndIsDeleted(entity, DeleteStatusEnum.NOT_DELETED.getCode());
        dto.setChannels(channels.stream().map(pc -> {
            PromotionChannelDto cdto = new PromotionChannelDto();
            cdto.setPromoChannelId(pc.getPromoChannelId());
            cdto.setChannelType(pc.getChannelType());
            cdto.setChannelTargetId(pc.getChannelTargetId());
            // Populate channelTargetName if applicable, e.g., by fetching store name if type is DIRECT_STORE
            cdto.setChannelTargetName(pc.getChannelType().getDescription() + (StringUtils.hasText(pc.getChannelTargetId()) ? ": " + pc.getChannelTargetId() : ""));
            return cdto;
        }).collect(Collectors.toList()));

        List<PromotionProduct> products = productRepository.findByPromotionAndIsDeleted(entity, DeleteStatusEnum.NOT_DELETED.getCode());
        dto.setProducts(products.stream().map(pp -> {
            PromotionProductDto pdto = new PromotionProductDto();
            pdto.setPromoProductId(pp.getPromoProductId());
            pdto.setProductBarcode(pp.getProductBarcode());
            pdto.setProductName(pp.getProductName());
            pdto.setPromoPrice(pp.getPromoPrice());
            productSettingRepository.findByProductBarcodeAndIsDeleted(pp.getProductBarcode(), DeleteStatusEnum.NOT_DELETED.getCode())
                .ifPresent(ps -> pdto.setOriginalPrice(ps.getSalePrice())); 
            return pdto;
        }).collect(Collectors.toList()));

        if (entity.getCreateBy() != null) {
            userAccountRepository.findById(entity.getCreateBy()).ifPresent(u -> dto.setCreateByName(u.getUserName()));
        }
        if (entity.getUpdateBy() != null) {
            userAccountRepository.findById(entity.getUpdateBy()).ifPresent(u -> dto.setUpdateByName(u.getUserName()));
        }
        return dto;
    }
    
    private PromotionDto convertToDtoForList(Promotion entity) {
        PromotionDto dto = new PromotionDto();
        dto.setPromotionId(entity.getPromotionId());
        dto.setPromotionName(entity.getPromotionName());
        dto.setStartTime(entity.getStartTime());
        dto.setEndTime(entity.getEndTime());
        dto.setIsActive(ActivationStatusEnum.YES.getCode().equals(entity.getIsActive()));
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        if (entity.getCreateBy() != null) {
            userAccountRepository.findById(entity.getCreateBy()).ifPresent(u -> dto.setCreateByName(u.getUserName()));
        }
        if (entity.getUpdateBy() != null) {
            userAccountRepository.findById(entity.getUpdateBy()).ifPresent(u -> dto.setUpdateByName(u.getUserName()));
        }
        // For list view, channels and products are not typically populated to keep payload small
        dto.setChannels(List.of()); // Empty list or null
        dto.setProducts(List.of()); // Empty list or null
        return dto;
    }
} 