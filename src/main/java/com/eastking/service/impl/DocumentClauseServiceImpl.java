package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.DocumentClauseDto;
import com.eastking.model.entity.DocumentClause;
import com.eastking.model.entity.UserAccount;
import com.eastking.repository.DocumentClauseRepository;
import com.eastking.repository.UserAccountRepository;
import com.eastking.service.AuditLogService;
import com.eastking.service.DocumentClauseService;
import com.eastking.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;
import java.time.OffsetDateTime;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class DocumentClauseServiceImpl implements DocumentClauseService {

    private static final Logger logger = LoggerFactory.getLogger(DocumentClauseServiceImpl.class);
    private final DocumentClauseRepository documentClauseRepository;
    private final UserAccountRepository userAccountRepository; // For createdBy/updatedBy names
    private final AuditLogService auditLogService; // For logging actions

    @Override
    public DocumentClauseDto createDocumentClause(DocumentClauseDto dto) {
        logger.info("Creating new document clause with title: {}", dto.getClauseTitle());
        DocumentClause entity = new DocumentClause();
        BeanUtils.copyProperties(dto, entity, "documentClauseId", "createTime", "updateTime", "createdByName", "updatedByName", "isDeleted");
        // Ensure UUID is generated if not set by DTO (though @PrePersist should handle)
        if (entity.getDocumentClauseId() == null) {
            entity.setDocumentClauseId(UUID.randomUUID());
        }
        entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        DocumentClause savedEntity = documentClauseRepository.save(entity);
        // TODO: Audit Log
        return convertToDto(savedEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public DocumentClauseDto getDocumentClauseById(UUID id) {
        return documentClauseRepository.findById(id)
                .filter(clause -> !DeleteStatusEnum.DELETED.getCode().equals(clause.getIsDeleted()))
                .map(this::convertToDto)
                .orElseThrow(() -> new ResourceNotFoundException("表尾條文找不到，ID: " + id));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DocumentClauseDto> searchDocumentClauses(
            Short companyDivisionCode,
            String keyword, Boolean isActive, Boolean isDefault, 
            String dateType, String dateFromString, String dateToString, 
            Pageable pageable) {
        logger.info("Searching document clauses for company: {} with keyword: {}, isActive: {}, isDefault: {}", 
            companyDivisionCode, keyword, isActive, isDefault);
            
        Specification<DocumentClause> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));

            // Filter by company division
            predicates.add(cb.equal(root.get("companyDivisionCode"), companyDivisionCode));

            if (StringUtils.hasText(keyword)) {
                predicates.add(cb.or(
                    cb.like(cb.lower(root.get("clauseTitle")), "%" + keyword.toLowerCase() + "%"),
                    cb.like(cb.lower(root.get("clauseContent")), "%" + keyword.toLowerCase() + "%")
                ));
            }
            if (isActive != null) {
                predicates.add(cb.equal(root.get("isActive"), isActive));
            }
            if (isDefault != null) {
                predicates.add(cb.equal(root.get("isDefault"), isDefault));
            }

            // Date range filtering
            String dateFieldToFilter = null;
            if ("effectiveDate".equalsIgnoreCase(dateType)) {
                // This requires careful thought: do we filter by start_time, end_time or active within range?
                // For simplicity, let's filter by start_time for now if dateType is effectiveDate.
                dateFieldToFilter = "startTime"; 
            } else if ("createDate".equalsIgnoreCase(dateType)) {
                dateFieldToFilter = "createTime";
            }

            if (StringUtils.hasText(dateFieldToFilter)) {
                try {
                    if (StringUtils.hasText(dateFromString)) {
                        OffsetDateTime fromDate = LocalDate.parse(dateFromString).atStartOfDay(ZoneId.systemDefault()).toOffsetDateTime();
                        predicates.add(cb.greaterThanOrEqualTo(root.get(dateFieldToFilter), fromDate));
                    }
                    if (StringUtils.hasText(dateToString)) {
                        OffsetDateTime toDate = LocalDate.parse(dateToString).atTime(23, 59, 59, 999999999).atZone(ZoneId.systemDefault()).toOffsetDateTime();
                        predicates.add(cb.lessThanOrEqualTo(root.get(dateFieldToFilter), toDate));
                    }
                } catch (DateTimeParseException e) {
                    logger.warn("Invalid date format for clause search: {}", e.getMessage());
                }
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        
        Page<DocumentClause> page = documentClauseRepository.findAll(spec, pageable);
        return page.map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DocumentClauseDto> getActiveDefaultClauses(Short companyDivisionCode) {
        OffsetDateTime now = OffsetDateTime.now();
        logger.info("Fetching active default clauses for company: {}", companyDivisionCode);
        
        Specification<DocumentClause> spec = (root, query, cb) -> {
            Predicate companyPredicate = cb.equal(root.get("companyDivisionCode"), companyDivisionCode);
            Predicate defaultPredicate = cb.isTrue(root.get("isDefault"));
            Predicate activePredicate = cb.isTrue(root.get("isActive"));
            Predicate notDeletedPredicate = cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode());

            Predicate effectiveNowPredicate = cb.and(
                cb.lessThanOrEqualTo(root.get("startTime"), now),
                cb.or(
                    cb.greaterThanOrEqualTo(root.get("endTime"), now),
                    cb.isNull(root.get("endTime"))
                )
            );
            
            return cb.and(companyPredicate, defaultPredicate, activePredicate, notDeletedPredicate, effectiveNowPredicate);
        };

        List<DocumentClause> clauses = documentClauseRepository.findAll(spec);
        
        return clauses.stream()
                .sorted((c1, c2) -> Integer.compare(
                    c1.getSequenceOrder() != null ? c1.getSequenceOrder() : 0, 
                    c2.getSequenceOrder() != null ? c2.getSequenceOrder() : 0))
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentClauseDto updateDocumentClause(UUID id, DocumentClauseDto dto) {
        logger.info("Updating document clause ID: {}", id);
        DocumentClause entity = documentClauseRepository.findById(id)
                .filter(clause -> !DeleteStatusEnum.DELETED.getCode().equals(clause.getIsDeleted()))
                .orElseThrow(() -> new ResourceNotFoundException("表尾條文找不到，無法更新，ID: " + id));
        
        // Preserve createBy and createTime
        UUID createBy = entity.getCreateBy();
        OffsetDateTime createTime = entity.getCreateTime();

        BeanUtils.copyProperties(dto, entity, "documentClauseId", "createTime", "updateTime", "createdByName", "updatedByName", "isDeleted", "createBy");
        
        entity.setCreateBy(createBy);
        entity.setCreateTime(createTime);

        DocumentClause updatedEntity = documentClauseRepository.save(entity);
        // TODO: Audit Log
        return convertToDto(updatedEntity);
    }

    @Override
    public void deleteDocumentClause(UUID id) {
        logger.info("Deleting document clause ID: {}", id);
        DocumentClause entity = documentClauseRepository.findById(id)
                .filter(clause -> !DeleteStatusEnum.DELETED.getCode().equals(clause.getIsDeleted()))
                .orElseThrow(() -> new ResourceNotFoundException("表尾條文找不到，無法刪除，ID: " + id));
        entity.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        documentClauseRepository.save(entity);
        // TODO: Audit Log
    }

    private DocumentClauseDto convertToDto(DocumentClause entity) {
        if (entity == null) return null;
        DocumentClauseDto dto = DocumentClauseDto.builder()
                .documentClauseId(entity.getDocumentClauseId())
                .companyDivisionCode(entity.getCompanyDivisionCode())
                .clauseTitle(entity.getClauseTitle())
                .clauseContent(entity.getClauseContent())
                .isDefault(entity.getIsDefault())
                .startTime(entity.getStartTime())
                .endTime(entity.getEndTime())
                .isActive(entity.getIsActive())
                .sequenceOrder(entity.getSequenceOrder())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .isDeleted(DeleteStatusEnum.DELETED.getCode().equals(entity.getIsDeleted()))
                .build();
        Optional.ofNullable(entity.getCreateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> dto.setCreatedByName(user.getUserName()));
        Optional.ofNullable(entity.getUpdateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> dto.setUpdatedByName(user.getUserName()));
        return dto;
    }
} 