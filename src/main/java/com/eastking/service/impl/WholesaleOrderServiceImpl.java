package com.eastking.service.impl;

import com.eastking.enums.*;
import com.eastking.exception.BadRequestException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.request.OrderItemRequestDto;
import com.eastking.model.dto.request.WholesaleOrderRequestDto;
import com.eastking.model.dto.response.OrderItemDto;
import com.eastking.model.dto.response.WholesaleOrderDetailDto;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.service.AuditLogService;
import com.eastking.service.WholesaleOrderService;
import com.eastking.util.OrderNumberUtil;
import com.eastking.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class WholesaleOrderServiceImpl implements WholesaleOrderService {

    private static final Logger logger = LoggerFactory.getLogger(WholesaleOrderServiceImpl.class);

    private final OrderRepository orderRepository;
    private final OrderItemRepository orderItemRepository;
    private final WholesaleCustomerRepository wholesaleCustomerRepository;
    private final ProductSettingRepository productSettingRepository;
    private final UserAccountRepository userAccountRepository;
    private final StoreRepository storeRepository;
    private final AuditLogService auditLogService;
    private final WarehouseRepository warehouseRepository;

    @Override
    public WholesaleOrderDetailDto createWholesaleOrder(WholesaleOrderRequestDto requestDto) {
        logger.info("Creating wholesale order for wholesale customer ID: {}", requestDto.getWholesaleCustomerId());

        WholesaleCustomer wholesaleCustomer = wholesaleCustomerRepository.findById(requestDto.getWholesaleCustomerId())
                .filter(wc -> DeleteStatusEnum.NOT_DELETED.getCode().equals(wc.getIsDeleted()) && CustomerStatusEnum.ACTIVE.getCode().equals(wc.getCustomerStatusCode()))
                .orElseThrow(() -> new ResourceNotFoundException("批發客戶不存在或非啟用狀態，ID: " + requestDto.getWholesaleCustomerId()));

        StoreEntity store = storeRepository.findById(requestDto.getStoreId())
                .filter(s -> DeleteStatusEnum.NOT_DELETED.getCode().equals(s.getIsDeleted()) && StoreStatusEnum.ENABLED.getCode().equals(s.getIsActive()))
                .orElseThrow(() -> new ResourceNotFoundException("開單門市不存在或非啟用狀態，ID: " + requestDto.getStoreId()));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Order order = new Order();
        order.setOrderId(UUID.randomUUID());
        order.setOrderNumber(OrderNumberUtil.generateNumber("W"));

        order.setCompanyDivisionCode(requestDto.getCompanyDivisionCode());
        order.setOrderTypeCode(OrderTypeEnum.WHOLESALE_ORDER.getCode());
        order.setOperatorTypeCode(OrderOperatorTypeEnum.SALES_PERSON.getCode());
        order.setStore(store);
        order.setUserAccount(currentUser);
        order.setWholesaleCustomer(wholesaleCustomer);
        order.setCustomerName(wholesaleCustomer.getCustomerName());
        order.setCustomerPhone(wholesaleCustomer.getPhoneNumber());
        
        order.setOrderDate(requestDto.getOrderDate() != null ? requestDto.getOrderDate() : OffsetDateTime.now());

        order.setShipmentMethodCode(requestDto.getShipmentMethodCode());
        order.setLogisticsProviderName(requestDto.getLogisticsProviderName());
        order.setLogisticsShipmentCount(requestDto.getLogisticsShipmentCount());
        order.setTruckLicensePlate(requestDto.getTruckLicensePlate());
        order.setLogisticsTrackingNumber(requestDto.getLogisticsTrackingNumber());

        order.setRemarks(requestDto.getRemarks());
        order.setInvoiceTypeCode(requestDto.getInvoiceTypeCode());
        order.setInvoiceAddress(requestDto.getInvoiceAddress() != null ? requestDto.getInvoiceAddress() : wholesaleCustomer.getCompanyAddress());
        order.setTaxIdNumber(wholesaleCustomer.getTaxIdNumber()); 
        order.setInvoiceCompanyTitle(wholesaleCustomer.getCustomerName());

        if (CollectionUtils.isEmpty(requestDto.getItems())) {
            throw new BadRequestException("訂單品項不得為空");
        }
        List<OrderItem> orderItems = new ArrayList<>();
        BigDecimal itemsSubtotalNet = BigDecimal.ZERO;
        BigDecimal itemsSubtotalGross = BigDecimal.ZERO;

        for (OrderItemRequestDto itemDto : requestDto.getItems()) {
            ProductSetting productSetting = productSettingRepository.findByProductBarcodeAndIsDeleted(itemDto.getProductBarcode(), DeleteStatusEnum.NOT_DELETED.getCode())
                .orElseThrow(() -> new ResourceNotFoundException("找不到商品設定: " + itemDto.getProductBarcode()));

            OrderItem item = new OrderItem();
            item.setOrderItemId(UUID.randomUUID());
            item.setOrder(order);
            item.setProductBarcode(itemDto.getProductBarcode());
            item.setProductName(productSetting.getProductName()); 
            item.setQuantity(itemDto.getQuantity());
            
            BigDecimal unitPrice = itemDto.getUnitPrice(); 
            if (unitPrice == null) {
                throw new BadRequestException("商品 " + itemDto.getProductBarcode() + " 未提供批發單價。");
            }
            item.setUnitPrice(unitPrice);
            itemsSubtotalGross = itemsSubtotalGross.add(unitPrice.multiply(BigDecimal.valueOf(item.getQuantity())));
            
            BigDecimal discountRate = itemDto.getDiscountRate() != null ? itemDto.getDiscountRate() : BigDecimal.ZERO;
            BigDecimal discountAmountPerItem = itemDto.getDiscountAmountPerItem() != null ? itemDto.getDiscountAmountPerItem() : BigDecimal.ZERO;
            item.setDiscountRate(discountRate);
            item.setDiscountAmountPerItem(discountAmountPerItem);

            BigDecimal priceAfterRateDiscount = unitPrice.multiply(BigDecimal.ONE.subtract(discountRate));
            BigDecimal finalPricePerItem = priceAfterRateDiscount.subtract(discountAmountPerItem);
            item.setFinalPricePerItem(finalPricePerItem.max(BigDecimal.ZERO));
            item.setSubtotalAmount(item.getFinalPricePerItem().multiply(BigDecimal.valueOf(item.getQuantity())));

            item.setItemTypeCode(itemDto.getItemTypeCode() != null ? itemDto.getItemTypeCode() : OrderItemTypeEnum.MAIN_PRODUCT.getCode());
            item.setItemNotes(itemDto.getItemNotes());
            item.setItemRemark(itemDto.getItemRemark());
            item.setWarehouseCode(itemDto.getWarehouseCode());
            orderItems.add(item);
            itemsSubtotalNet = itemsSubtotalNet.add(item.getSubtotalAmount());
        }
        order.setItems(orderItems);
        
        order.setProductsTotalAmount(itemsSubtotalGross);
        order.setNetAmount(itemsSubtotalNet);
        order.setDiscountAmount(itemsSubtotalGross.subtract(itemsSubtotalNet));

        BigDecimal taxRate = TaxCategoryEnum.TAXABLE.getCode().equals(wholesaleCustomer.getTaxCategoryCode()) ? new BigDecimal("0.05") : BigDecimal.ZERO;
        order.setTaxAmount(order.getNetAmount().multiply(taxRate).setScale(2, RoundingMode.HALF_UP));
        order.setGrandTotalAmount(order.getNetAmount().add(order.getTaxAmount()));
        
        order.setPaymentStatusCode(PaymentStatusEnum.UNPAID.getCode());
        order.setPaidAmount(BigDecimal.ZERO);
        order.setOrderStatusCode(OrderStatusEnum.PENDING_HQ_APPROVAL.getCode());

        Order savedOrder = orderRepository.save(order);
        auditLogService.logAction(AuditActionTypeEnum.CREATE, AuditDataTypeEnum.ORDER, savedOrder.getOrderId().toString(), "批發訂單建立: " + savedOrder.getOrderNumber(), null);
        logger.info("Wholesale order {} created successfully.", savedOrder.getOrderNumber());
        return convertToWholesaleOrderDetailDto(savedOrder);
    }

    @Override
    @Transactional(readOnly = true)
    public WholesaleOrderDetailDto getWholesaleOrderById(UUID orderId) {
        Order order = orderRepository.findByOrderIdAndOrderTypeCodeAndIsDeleted(orderId, OrderTypeEnum.WHOLESALE_ORDER.getCode(), DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("批發訂單找不到，ID: " + orderId));
        return convertToWholesaleOrderDetailDto(order);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WholesaleOrderDetailDto> searchWholesaleOrders(
            Short companyDivisionCode, String orderNumber, UUID storeId, Short orderStatusCode, String dateFromString, String dateToString, Pageable pageable) {
        logger.info("Searching wholesale orders for company: {} with number: {}, storeId: {}, status: {}", 
            companyDivisionCode, orderNumber, storeId, orderStatusCode);
        
        Specification<Order> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("orderTypeCode"), OrderTypeEnum.WHOLESALE_ORDER.getCode()));
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));

            predicates.add(cb.equal(root.get("companyDivisionCode"), companyDivisionCode));

            if (StringUtils.hasText(orderNumber)) {
                predicates.add(cb.like(cb.lower(root.get("orderNumber")), "%" + orderNumber.toLowerCase() + "%"));
            }
            if (storeId != null) {
                predicates.add(cb.equal(root.get("store").get("storeId"), storeId));
            }
            if (orderStatusCode != null) {
                predicates.add(cb.equal(root.get("orderStatusCode"), orderStatusCode));
            }
            try {
                if (StringUtils.hasText(dateFromString)) {
                    LocalDate fromDate = LocalDate.parse(dateFromString);
                    predicates.add(cb.greaterThanOrEqualTo(root.get("orderDate"), fromDate.atStartOfDay(ZoneId.systemDefault()).toOffsetDateTime()));
                }
                if (StringUtils.hasText(dateToString)) {
                    LocalDate toDate = LocalDate.parse(dateToString);
                    predicates.add(cb.lessThanOrEqualTo(root.get("orderDate"), toDate.atTime(23, 59, 59, 999999999).atZone(ZoneId.systemDefault()).toOffsetDateTime()));
                }
            } catch (DateTimeParseException e) {
                logger.warn("Invalid date format received for search: from='{}', to='{}'. Ignoring date filter.", dateFromString, dateToString, e);
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        Page<Order> orderPage = orderRepository.findAll(spec, pageable);
        List<WholesaleOrderDetailDto> dtoList = orderPage.getContent().stream()
                .map(this::convertToWholesaleOrderDetailDto)
                .collect(Collectors.toList());
        return new PageImpl<>(dtoList, pageable, orderPage.getTotalElements());
    }

    @Override
    public WholesaleOrderDetailDto updateWholesaleOrder(UUID orderId, WholesaleOrderRequestDto requestDto) {
        logger.info("Updating wholesale order ID: {}", orderId);
        Order order = orderRepository.findByOrderIdAndOrderTypeCodeAndIsDeleted(orderId, OrderTypeEnum.WHOLESALE_ORDER.getCode(), DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("批發訂單找不到，無法更新，ID: " + orderId));
        
        if (order.getOrderStatusCode() >= OrderStatusEnum.PENDING_HQ_APPROVAL.getCode() && 
            !(OrderStatusEnum.DRAFT.getCode().equals(order.getOrderStatusCode()) || 
              OrderStatusEnum.AWAITING_CORRECTION_HQ.getCode().equals(order.getOrderStatusCode()) ||
              OrderStatusEnum.PENDING_HQ_APPROVAL.getCode().equals(order.getOrderStatusCode()) )) {
            throw new BadRequestException("批發訂單狀態為 '" + OrderStatusEnum.getDescriptionByCode(order.getOrderStatusCode()) + "'，不可編輯。");
        }
        
        WholesaleCustomer wholesaleCustomer = wholesaleCustomerRepository.findById(requestDto.getWholesaleCustomerId())
            .filter(wc -> DeleteStatusEnum.NOT_DELETED.getCode().equals(wc.getIsDeleted()) && CustomerStatusEnum.ACTIVE.getCode().equals(wc.getCustomerStatusCode()))
            .orElseThrow(() -> new ResourceNotFoundException("批發客戶不存在或非啟用狀態，ID: " + requestDto.getWholesaleCustomerId()));
        
        order.setWholesaleCustomer(wholesaleCustomer);
        order.setCustomerName(wholesaleCustomer.getCustomerName());
        order.setCustomerPhone(wholesaleCustomer.getPhoneNumber());

        order.setShipmentMethodCode(requestDto.getShipmentMethodCode());
        order.setLogisticsProviderName(requestDto.getLogisticsProviderName());
        order.setLogisticsShipmentCount(requestDto.getLogisticsShipmentCount());
        order.setTruckLicensePlate(requestDto.getTruckLicensePlate());
        order.setLogisticsTrackingNumber(requestDto.getLogisticsTrackingNumber());
        order.setRemarks(requestDto.getRemarks());
        order.setInvoiceTypeCode(requestDto.getInvoiceTypeCode());
        order.setInvoiceAddress(requestDto.getInvoiceAddress() != null ? requestDto.getInvoiceAddress() : wholesaleCustomer.getCompanyAddress());
        order.setTaxIdNumber(wholesaleCustomer.getTaxIdNumber());
        order.setInvoiceCompanyTitle(wholesaleCustomer.getCustomerName());

        if (order.getItems() != null) {
            orderItemRepository.deleteAllInBatch(order.getItems());
            order.getItems().clear();
        }
        
        List<OrderItem> newOrderItems = new ArrayList<>();
        BigDecimal itemsSubtotalNet = BigDecimal.ZERO;
        BigDecimal itemsSubtotalGross = BigDecimal.ZERO;

        if (!CollectionUtils.isEmpty(requestDto.getItems())) {
            for (OrderItemRequestDto itemDto : requestDto.getItems()) {
                ProductSetting productSetting = productSettingRepository.findByProductBarcodeAndIsDeleted(itemDto.getProductBarcode(), DeleteStatusEnum.NOT_DELETED.getCode())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到商品設定: " + itemDto.getProductBarcode()));
                OrderItem item = new OrderItem();
                item.setOrderItemId(UUID.randomUUID());
                item.setOrder(order);
                item.setProductBarcode(itemDto.getProductBarcode());
                item.setProductName(productSetting.getProductName());
                item.setQuantity(itemDto.getQuantity());
                BigDecimal unitPrice = itemDto.getUnitPrice();
                if (unitPrice == null) {
                    throw new BadRequestException("商品 " + itemDto.getProductBarcode() + " 更新時未提供批發單價。");
                }
                item.setUnitPrice(unitPrice);
                itemsSubtotalGross = itemsSubtotalGross.add(unitPrice.multiply(BigDecimal.valueOf(item.getQuantity())));
                BigDecimal discountRate = itemDto.getDiscountRate() != null ? itemDto.getDiscountRate() : BigDecimal.ZERO;
                BigDecimal discountAmountPerItem = itemDto.getDiscountAmountPerItem() != null ? itemDto.getDiscountAmountPerItem() : BigDecimal.ZERO;
                item.setDiscountRate(discountRate);
                item.setDiscountAmountPerItem(discountAmountPerItem);
                BigDecimal priceAfterRateDiscount = unitPrice.multiply(BigDecimal.ONE.subtract(discountRate));
                BigDecimal finalPricePerItem = priceAfterRateDiscount.subtract(discountAmountPerItem);
                item.setFinalPricePerItem(finalPricePerItem.max(BigDecimal.ZERO));
                item.setSubtotalAmount(item.getFinalPricePerItem().multiply(BigDecimal.valueOf(item.getQuantity())));
                item.setItemTypeCode(itemDto.getItemTypeCode() != null ? itemDto.getItemTypeCode() : OrderItemTypeEnum.MAIN_PRODUCT.getCode());
                item.setItemNotes(itemDto.getItemNotes());
                item.setItemRemark(itemDto.getItemRemark());
                item.setWarehouseCode(itemDto.getWarehouseCode());
                newOrderItems.add(item);
                itemsSubtotalNet = itemsSubtotalNet.add(item.getSubtotalAmount());
            }
        }
        order.setItems(newOrderItems); 
        order.setProductsTotalAmount(itemsSubtotalGross);
        order.setNetAmount(itemsSubtotalNet);
        order.setDiscountAmount(itemsSubtotalGross.subtract(itemsSubtotalNet));
        BigDecimal taxRate = TaxCategoryEnum.TAXABLE.getCode().equals(wholesaleCustomer.getTaxCategoryCode()) ? new BigDecimal("0.05") : BigDecimal.ZERO;
        order.setTaxAmount(order.getNetAmount().multiply(taxRate).setScale(2, RoundingMode.HALF_UP));
        order.setGrandTotalAmount(order.getNetAmount().add(order.getTaxAmount()));
        
        Order updatedOrder = orderRepository.save(order);
        auditLogService.logAction(AuditActionTypeEnum.UPDATE, AuditDataTypeEnum.ORDER, updatedOrder.getOrderId().toString(), "批發訂單更新: " + updatedOrder.getOrderNumber(), null);
        return convertToWholesaleOrderDetailDto(updatedOrder);
    }

    @Override
    public WholesaleOrderDetailDto cancelWholesaleOrder(UUID orderId) {
        logger.info("Cancelling wholesale order ID: {}", orderId);
        Order order = orderRepository.findByOrderIdAndOrderTypeCodeAndIsDeleted(orderId, OrderTypeEnum.WHOLESALE_ORDER.getCode(), DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("批發訂單找不到，無法取消，ID: " + orderId));
        
        if (order.getOrderStatusCode() < OrderStatusEnum.HQ_APPROVED.getCode() && 
            !OrderStatusEnum.CANCEL_PENDING_HQ_APPROVAL.getCode().equals(order.getOrderStatusCode()) &&
            !OrderStatusEnum.CANCEL_APPROVED_BY_HQ.getCode().equals(order.getOrderStatusCode())) {
             order.setOrderStatusCode(OrderStatusEnum.CANCEL_PENDING_HQ_APPROVAL.getCode());
        } else if (OrderStatusEnum.HQ_REJECTED.getCode().equals(order.getOrderStatusCode())) {
            order.setOrderStatusCode(OrderStatusEnum.CANCEL_APPROVED_BY_HQ.getCode());
        } else if (order.getOrderStatusCode() >= OrderStatusEnum.SHIPPED_CLOSED.getCode()){
            throw new BadRequestException("批發訂單已出貨或結案，無法取消，請執行退貨流程。");
        } else if (OrderStatusEnum.CANCEL_PENDING_HQ_APPROVAL.getCode().equals(order.getOrderStatusCode()) || OrderStatusEnum.CANCEL_APPROVED_BY_HQ.getCode().equals(order.getOrderStatusCode())){
            logger.info("Wholesale order {} is already in a cancellation process or cancelled.", order.getOrderNumber());
        } else {
            order.setOrderStatusCode(OrderStatusEnum.CANCEL_PENDING_HQ_APPROVAL.getCode());
        }

        Order cancelledOrder = orderRepository.save(order);
        auditLogService.logAction(AuditActionTypeEnum.UPDATE, AuditDataTypeEnum.ORDER, cancelledOrder.getOrderId().toString(), "批發訂單狀態變更 (取消相關): " + cancelledOrder.getOrderNumber() + " -> " + OrderStatusEnum.getDescriptionByCode(cancelledOrder.getOrderStatusCode()), null);
        return convertToWholesaleOrderDetailDto(cancelledOrder);
    }

    @Override
    public String getPrintableOrderData(UUID orderId) {
        logger.info("Fetching printable data for wholesale order ID: {}", orderId);
        Order order = orderRepository.findByOrderIdAndOrderTypeCodeAndIsDeleted(orderId, OrderTypeEnum.WHOLESALE_ORDER.getCode(), DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("批發訂單找不到，無法列印，ID: " + orderId));
        String customerInfo = order.getWholesaleCustomer() != null ? order.getWholesaleCustomer().getCustomerName() : order.getCustomerName();
        return String.format("批發訂單列印資料:\n  訂單號碼: %s\n  客戶: %s\n  總金額: %s\n  (詳細品項列表...)", 
            order.getOrderNumber(), customerInfo, order.getGrandTotalAmount());
    }

    private WholesaleOrderDetailDto convertToWholesaleOrderDetailDto(Order order) {
        if (order == null) return null;

        WholesaleOrderDetailDto.WholesaleOrderDetailDtoBuilder builder = WholesaleOrderDetailDto.builder();
        
        builder.orderId(order.getOrderId());
        builder.orderNumber(order.getOrderNumber());
        builder.companyDivisionCode(order.getCompanyDivisionCode());
        if(order.getCompanyDivisionCode() != null) builder.companyDivisionDescription(ErpCompanyDivisionEnum.getDescriptionByCode(order.getCompanyDivisionCode()));
        builder.orderTypeCode(order.getOrderTypeCode());
        if(order.getOrderTypeCode() != null) builder.orderTypeDescription(OrderTypeEnum.getDescriptionByCode(order.getOrderTypeCode()));
        builder.operatorTypeCode(order.getOperatorTypeCode());
        if(order.getOperatorTypeCode() != null) builder.operatorTypeDescription(OrderOperatorTypeEnum.getDescriptionByCode(order.getOperatorTypeCode()));
        
        if (order.getStore() != null) {
            builder.storeId(order.getStore().getStoreId());
            builder.storeName(order.getStore().getStoreName());
        }
        if (order.getUserAccount() != null) {
            builder.userAccountId(order.getUserAccount().getUserAccountId());
            builder.userEmployeeId(order.getUserAccount().getEmployeeId());
            builder.userName(order.getUserAccount().getUserName());
        }
        
        if(order.getMember() != null){ 
            builder.memberId(order.getMember().getCustomerId());
            if(order.getMember().getMemberLevel() != null){
                builder.memberLevelName(order.getMember().getMemberLevel().getLevelName());
            }
        }
        builder.customerName(order.getCustomerName()); 
        builder.customerPhone(order.getCustomerPhone());

        builder.orderDate(order.getOrderDate());
        builder.orderStatusCode(order.getOrderStatusCode());
        if(order.getOrderStatusCode() != null) builder.orderStatusDescription(OrderStatusEnum.getDescriptionByCode(order.getOrderStatusCode()));
        
        builder.productsTotalAmount(order.getProductsTotalAmount());
        builder.discountAmount(order.getDiscountAmount());
        builder.netAmount(order.getNetAmount());
        builder.taxAmount(order.getTaxAmount());
        builder.grandTotalAmount(order.getGrandTotalAmount());
        builder.paidAmount(order.getPaidAmount());
        builder.paymentStatusCode(order.getPaymentStatusCode());
        if(order.getPaymentStatusCode() != null) builder.paymentStatusDescription(PaymentStatusEnum.getDescriptionByCode(order.getPaymentStatusCode()));
        
        builder.invoiceTypeCode(order.getInvoiceTypeCode());
        if(order.getInvoiceTypeCode() != null) builder.invoiceTypeDescription(InvoiceTypeEnum.getDescriptionByCode(order.getInvoiceTypeCode()));
        builder.invoiceNumber(order.getInvoiceNumber());
        builder.taxIdNumber(order.getTaxIdNumber());
        builder.invoiceCompanyTitle(order.getInvoiceCompanyTitle());
        builder.invoiceDate(order.getInvoiceDate());
        builder.invoiceAmount(order.getInvoiceAmount());
        builder.invoiceAddress(order.getInvoiceAddress());
        builder.isInvoiceSent(order.getIsInvoiceSent());
        
        builder.contactName(order.getContactName()); 
        builder.contactPhone(order.getContactPhone());
        builder.installationAddress(order.getInstallationAddress());
        builder.installationDate(order.getInstallationDate());
        builder.installationTimeSlot(order.getInstallationTimeSlot());
        if(order.getTechnician() != null) {
            builder.technicianId(order.getTechnician().getUserAccountId());
            builder.technicianName(order.getTechnician().getUserName());
        }
        builder.actualCompletionDate(order.getActualCompletionDate());

        builder.remarks(order.getRemarks());
        if(order.getSourceOrder() != null) {
            builder.sourceOrderId(order.getSourceOrder().getOrderId());
            builder.sourceOrderNumber(order.getSourceOrder().getOrderNumber());
        }
        if (!CollectionUtils.isEmpty(order.getItems())) {
            builder.items(order.getItems().stream().map(this::convertToOrderItemDto).collect(Collectors.toList()));
        }
        
        builder.createTime(order.getCreateTime());
        builder.updateTime(order.getUpdateTime());
        Optional.ofNullable(order.getCreateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> builder.createdByName(user.getUserName()));
        Optional.ofNullable(order.getUpdateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> builder.updatedByName(user.getUserName()));
        builder.isDeleted(Objects.equals(order.getIsDeleted(), DeleteStatusEnum.DELETED.getCode()));

        if (order.getWholesaleCustomer() != null) {
            WholesaleCustomer wc = order.getWholesaleCustomer();
            builder.wholesaleCustomerId(wc.getWholesaleCustomerId());
            builder.wholesaleCustomerErpCode(wc.getErpCustomerCode());
            builder.wholesaleCustomerName(wc.getCustomerName());
            builder.wholesaleCustomerContactPerson(wc.getContactPerson());
            builder.wholesaleCustomerPhone(wc.getPhoneNumber());
            builder.wholesaleCustomerAddress(wc.getShippingAddress());
        }

        builder.expectedShipmentDate(order.getInstallationDate());
        builder.shipmentMethodCode(order.getShipmentMethodCode());
        if (order.getShipmentMethodCode() != null) {
            builder.shipmentMethodDescription(ShipmentMethodEnum.getDescriptionByCode(order.getShipmentMethodCode()));
        }
        builder.logisticsProviderName(order.getLogisticsProviderName());
        builder.logisticsShipmentCount(order.getLogisticsShipmentCount());
        builder.truckLicensePlate(order.getTruckLicensePlate());
        builder.logisticsTrackingNumber(order.getLogisticsTrackingNumber());

        return builder.build();
    }
    
    private OrderItemDto convertToOrderItemDto(OrderItem item) {
        if (item == null) return null;
        OrderItemDto.OrderItemDtoBuilder builder = OrderItemDto.builder(); 
        
        builder.orderItemId(item.getOrderItemId());
        builder.productBarcode(item.getProductBarcode());
        builder.productName(item.getProductName());
        builder.quantity(item.getQuantity());
        builder.unitPrice(item.getUnitPrice());
        builder.discountRate(item.getDiscountRate());
        builder.discountAmountPerItem(item.getDiscountAmountPerItem());
        builder.finalPricePerItem(item.getFinalPricePerItem());
        builder.subtotalAmount(item.getSubtotalAmount());
        builder.itemTypeCode(item.getItemTypeCode());
        if (item.getItemTypeCode() != null) {
            builder.itemTypeDescription(OrderItemTypeEnum.getDescriptionByCode(item.getItemTypeCode()));
        }
        builder.itemNotes(item.getItemNotes());
        builder.itemRemark(item.getItemRemark());
        builder.warehouseCode(item.getWarehouseCode());
        builder.mahjongTableSerialNumber(item.getMahjongTableSerialNumber());
        builder.createTime(item.getCreateTime());
        builder.updateTime(item.getUpdateTime());
        Optional.ofNullable(item.getCreateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> builder.createdByName(user.getUserName()));
        Optional.ofNullable(item.getUpdateBy()).flatMap(userAccountRepository::findById).ifPresent(user -> builder.updatedByName(user.getUserName()));
        return builder.build();
    }
} 