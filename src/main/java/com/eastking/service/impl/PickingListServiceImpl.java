package com.eastking.service.impl;

import com.eastking.enums.MaterialOrderStatusEnum;
import com.eastking.enums.MaterialTypeStatusEnum;
import com.eastking.exception.InvalidDataException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.request.CompletePickingRequestDto;
import com.eastking.model.dto.request.PickingListQueryDto;
import com.eastking.model.dto.response.PickingListDetailDto;
import com.eastking.model.dto.response.PickingListItemDto;
import com.eastking.model.dto.response.PickingListSummaryDto;
import com.eastking.model.entity.DispatchMaterialOrder;
import com.eastking.model.entity.DispatchMaterialOrderItem;
import com.eastking.repository.DispatchMaterialOrderItemRepository;
import com.eastking.repository.DispatchMaterialOrderRepository;
import com.eastking.service.PickingListService;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PickingListServiceImpl implements PickingListService {

    private final DispatchMaterialOrderRepository materialOrderRepository;
    private final DispatchMaterialOrderItemRepository materialOrderItemRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<PickingListSummaryDto> searchPickingLists(PickingListQueryDto queryDto, MaterialTypeStatusEnum materialType, Pageable pageable) {
        Specification<DispatchMaterialOrder> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));

            boolean isFiltered = false;

            if (queryDto.getWarehouseId() != null) {
                predicates.add(cb.equal(root.get("targetWarehouse").get("warehouseId"), queryDto.getWarehouseId()));
                isFiltered = true;
            }
            if (queryDto.getTechnicianId() != null) {
                predicates.add(cb.equal(root.get("requestingTechnician").get("userAccountId"), queryDto.getTechnicianId()));
                isFiltered = true;
            }
            if (queryDto.getStatus() != null) {
                predicates.add(cb.equal(root.get("materialOrderStatusCode"), queryDto.getStatus()));
                isFiltered = true;
            }
            if (queryDto.getDateFrom() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("createTime"), queryDto.getDateFrom()));
                isFiltered = true;
            }
            if (queryDto.getDateTo() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("createTime"), queryDto.getDateTo()));
                isFiltered = true;
            }
            if (StringUtils.hasText(queryDto.getKeyword())) {
                String keywordPattern = "%" + queryDto.getKeyword().toLowerCase() + "%";
                predicates.add(cb.or(
                        cb.like(cb.lower(root.get("materialOrderNumber")), keywordPattern),
                        cb.like(cb.lower(root.get("requestingTechnician").get("userName")), keywordPattern)
                ));
                isFiltered = true;
            }

            // Add materialType to the specification
            if (materialType != null) {
                predicates.add(cb.equal(root.get("materialType"), materialType.getCode()));
            }

            // Default condition for initial load: only show orders with unpicked items.
            if (!isFiltered) {
                query.distinct(true);
                Join<DispatchMaterialOrder, DispatchMaterialOrderItem> items = root.join("items", JoinType.INNER);
                //predicates.add(cb.equal(items.get("isPicked"), (short) 0));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<DispatchMaterialOrder> page = materialOrderRepository.findAll(spec, pageable);
        List<PickingListSummaryDto> dtos = page.getContent().stream()
                .map(this::mapToSummaryDto)
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, page.getTotalElements());
    }

    @Override
    @Transactional(readOnly = true)
    public PickingListDetailDto getPickingListDetailById(UUID id) {
        return materialOrderRepository.findById(id)
                .map(this::mapToDetailDto)
                .orElseThrow(() -> new ResourceNotFoundException("Picking List not found with id: " + id));
    }

    @Override
    @Transactional
    public void completePicking(UUID orderId, CompletePickingRequestDto requestDto) {
        DispatchMaterialOrder order = materialOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("Picking List not found with id: " + orderId));

        Map<UUID, DispatchMaterialOrderItem> itemsFromDb = order.getItems().stream()
                .collect(Collectors.toMap(DispatchMaterialOrderItem::getMaterialOrderItemId, item -> item));

        for (CompletePickingRequestDto.PickedItemDto itemDto : requestDto.getItems()) {
            DispatchMaterialOrderItem dbItem = itemsFromDb.get(itemDto.getItemId());
            if (dbItem == null) {
                throw new InvalidDataException("Item with id " + itemDto.getItemId() + " not found in this order.");
            }

            if (dbItem.getPickedQuantity()==null) dbItem.setPickedQuantity(0);
            if (itemDto.getPickedQuantity()==null) itemDto.setPickedQuantity(0);
            log.debug("dbItem.getPickedQuantity(): {}", dbItem.getPickedQuantity()); //原領數量
            log.debug("itemDto.getPickedQuantity(): {}", itemDto.getPickedQuantity()); //新領數量
            dbItem.setPickedQuantity(dbItem.getPickedQuantity()+itemDto.getPickedQuantity());

            if (dbItem.getRequestedQuantity().equals(dbItem.getPickedQuantity())) {
                dbItem.setIsPicked((short) 1);
            }
        }
        
        // Final check if all items in DB are now marked as picked
        boolean allItemsPicked = order.getItems().stream().allMatch(item -> item.getIsPicked() == 1);
        if (!allItemsPicked) {
            throw new InvalidDataException("Not all items were marked as picked.");
        }

        order.setMaterialOrderStatusCode(MaterialOrderStatusEnum.PICKING_COMPLETED.getCode());
        materialOrderRepository.save(order);
    }

    @Override
    @Transactional
    public void completeRefundPicking(UUID orderId, CompletePickingRequestDto requestDto) {
        DispatchMaterialOrder order = materialOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("Picking Refund List not found with id: " + orderId));

        Map<UUID, DispatchMaterialOrderItem> itemsFromDb = order.getItems().stream()
                .collect(Collectors.toMap(DispatchMaterialOrderItem::getMaterialOrderItemId, item -> item));

        for (CompletePickingRequestDto.PickedItemDto itemDto : requestDto.getItems()) {
            DispatchMaterialOrderItem dbItem = itemsFromDb.get(itemDto.getItemId());
            if (dbItem == null) {
                throw new InvalidDataException("Item with id " + itemDto.getItemId() + " not found in this order.");
            }

            if (dbItem.getPickedQuantity()==null) dbItem.setPickedQuantity(0);
            if (itemDto.getPickedQuantity()==null) itemDto.setPickedQuantity(0);
            log.debug("dbItem.getPickedQuantity(): {}", dbItem.getPickedQuantity()); //原領數量
            log.debug("itemDto.getPickedQuantity(): {}", itemDto.getPickedQuantity()); //新領數量
            dbItem.setPickedQuantity(dbItem.getPickedQuantity()+itemDto.getPickedQuantity());

            if (dbItem.getRequestedQuantity().equals(dbItem.getPickedQuantity())) {
                dbItem.setIsPicked((short) 1);
            }
        }

        // Final check if all items in DB are now marked as picked
        boolean allItemsPicked = order.getItems().stream().allMatch(item -> item.getIsPicked() == 1);
        if (!allItemsPicked) {
            throw new InvalidDataException("Not all items were marked as picked.");
        }

        order.setMaterialOrderStatusCode(MaterialOrderStatusEnum.REFUND_PICKING_COMPLETED.getCode());
        materialOrderRepository.save(order);
    }

    private PickingListSummaryDto mapToSummaryDto(DispatchMaterialOrder order) {
        PickingListSummaryDto dto = new PickingListSummaryDto();
        dto.setId(order.getMaterialOrderId());
        dto.setPickingOrderNumber(order.getMaterialOrderNumber());
        dto.setRequestDate(order.getCreateTime());
        if (order.getRequestingTechnician() != null) {
            dto.setRequesterName(order.getRequestingTechnician().getUserName());
        }
        if (order.getRequestingTechnician() != null) {
            dto.setTechnicianName(order.getRequestingTechnician().getUserName());
        }
        dto.setTotalItemTypes(order.getItems() != null ? order.getItems().size() : 0);
        dto.setTotalItemQuantity(order.getItems() != null ? order.getItems().stream().mapToInt(DispatchMaterialOrderItem::getRequestedQuantity).sum() : 0);
        dto.setStatusCode(order.getMaterialOrderStatusCode());
        dto.setStatusDescription(MaterialOrderStatusEnum.fromCode(order.getMaterialOrderStatusCode()).getDescription());
        return dto;
    }

    private PickingListDetailDto mapToDetailDto(DispatchMaterialOrder order) {
        PickingListDetailDto detailDto = new PickingListDetailDto();
        
        // Map summary fields
        PickingListSummaryDto summaryDto = mapToSummaryDto(order);
        detailDto.setId(summaryDto.getId());
        detailDto.setPickingOrderNumber(summaryDto.getPickingOrderNumber());
        detailDto.setRequestDate(summaryDto.getRequestDate());
        detailDto.setRequesterName(summaryDto.getRequesterName());
        detailDto.setTechnicianName(summaryDto.getTechnicianName());
        detailDto.setTotalItemTypes(summaryDto.getTotalItemTypes());
        detailDto.setTotalItemQuantity(summaryDto.getTotalItemQuantity());
        detailDto.setStatusCode(summaryDto.getStatusCode());
        detailDto.setStatusDescription(summaryDto.getStatusDescription());

        // Map detail fields
        if (order.getTargetWarehouse() != null) {
            detailDto.setWarehouseName(order.getTargetWarehouse().getWarehouseName());
        }
        detailDto.setNotes(order.getRemarks());
        
        List<PickingListItemDto> itemDtos = order.getItems().stream()
                .map(this::mapToItemDto)
                .collect(Collectors.toList());
        detailDto.setItems(itemDtos);
        
        return detailDto;
    }

    private PickingListItemDto mapToItemDto(DispatchMaterialOrderItem item) {
        PickingListItemDto itemDto = new PickingListItemDto();
        itemDto.setItemId(item.getMaterialOrderItemId());
        itemDto.setProductBarcode(item.getProductBarcode());
        itemDto.setProductName(item.getProductName());
        itemDto.setRequestedQuantity(item.getRequestedQuantity());
        itemDto.setPicked(item.getIsPicked() != null && item.getIsPicked() == 1);
        return itemDto;
    }
} 