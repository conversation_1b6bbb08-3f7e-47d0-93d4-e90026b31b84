package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.enums.PermissionStatusEnum;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.enums.StoreStatusEnum;
import com.eastking.model.dto.request.LoginRequest;
import com.eastking.model.dto.response.LoginResponse;
import com.eastking.model.entity.Role;
import com.eastking.model.entity.RoleFunctionPermission;
import com.eastking.model.entity.UserAccount;
import com.eastking.model.entity.UserSessionPermission;
import com.eastking.model.entity.UserRoleMap;
import com.eastking.model.entity.StoreEntity;
import com.eastking.model.entity.StoreStaffMapEntity;
import com.eastking.repository.RoleFunctionPermissionRepository;
import com.eastking.repository.UserAccountRepository;
import com.eastking.repository.UserRoleMapRepository;
import com.eastking.repository.StoreRepository;
import com.eastking.repository.StoreStaffMapRepository;
import com.eastking.security.JwtTokenProvider;
import com.eastking.security.UserDetailsImpl;
import com.eastking.service.AuthService;
import com.eastking.service.UserSessionPermissionService;
import com.eastking.service.AuditLogService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.Optional;
import com.eastking.util.SecurityUtil;
import com.eastking.exception.ResourceNotFoundException;

/**
 * 認證服務實作
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Service
public class AuthServiceImpl implements AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthServiceImpl.class);

    private final AuthenticationManager authenticationManager;
    private final UserAccountRepository userAccountRepository;
    private final UserRoleMapRepository userRoleMapRepository;
    private final RoleFunctionPermissionRepository roleFunctionPermissionRepository;
    private final UserSessionPermissionService userSessionPermissionService;
    private final JwtTokenProvider jwtTokenProvider;
    private final PasswordEncoder passwordEncoder; // Used for initial password hashing if needed, not for login check via AuthenticationManager
    private final AuditLogService auditLogService;
    private final ObjectMapper objectMapper;
    private final StoreRepository storeRepository;
    private final StoreStaffMapRepository storeStaffMapRepository;

    @Autowired
    public AuthServiceImpl(AuthenticationManager authenticationManager,
                           UserAccountRepository userAccountRepository,
                           UserRoleMapRepository userRoleMapRepository,
                           RoleFunctionPermissionRepository roleFunctionPermissionRepository,
                           UserSessionPermissionService userSessionPermissionService,
                           JwtTokenProvider jwtTokenProvider,
                           PasswordEncoder passwordEncoder,
                           @Lazy AuditLogService auditLogService,
                           ObjectMapper objectMapper,
                           StoreRepository storeRepository,
                           StoreStaffMapRepository storeStaffMapRepository) {
        this.authenticationManager = authenticationManager;
        this.userAccountRepository = userAccountRepository;
        this.userRoleMapRepository = userRoleMapRepository;
        this.roleFunctionPermissionRepository = roleFunctionPermissionRepository;
        this.userSessionPermissionService = userSessionPermissionService;
        this.jwtTokenProvider = jwtTokenProvider;
        this.passwordEncoder = passwordEncoder;
        this.auditLogService = auditLogService;
        this.objectMapper = objectMapper;
        this.storeRepository = storeRepository;
        this.storeStaffMapRepository = storeStaffMapRepository;
    }

    private String toJson(Object obj) {
        try {
            if (obj == null) return null;
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            return "{\"error\":\"Error serializing details for audit log\"}";
        }
    }

    @Override
    @Transactional
    public LoginResponse login(LoginRequest loginRequest) {
        logger.info("Processing login request for employeeId: {} with company context: {}", 
                    loginRequest.getEmployeeId(), loginRequest.getCompanyContext());
        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getEmployeeId(), loginRequest.getPassword()));

            SecurityContextHolder.getContext().setAuthentication(authentication);
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();

            UserAccount userAccount = userAccountRepository.findById(userDetails.getId())
                    .orElseThrow(() -> new BadCredentialsException("User account not found after authentication: " + userDetails.getUsername()));

            // Check company context
            Short expectedCompanyCode = null;
            if ("EASTKING".equalsIgnoreCase(loginRequest.getCompanyContext())) {
                expectedCompanyCode = ErpCompanyDivisionEnum.EASTKING.getCode();
            } else if ("QUEYOU".equalsIgnoreCase(loginRequest.getCompanyContext())) {
                expectedCompanyCode = ErpCompanyDivisionEnum.QUEYOU.getCode();
            }

            if (expectedCompanyCode == null) {
                logger.warn("Invalid company context provided during login: {}", loginRequest.getCompanyContext());
                throw new BadCredentialsException("登入公司別無效。");
            }

            if (userAccount.getErpCompanyDivision() == null || !userAccount.getErpCompanyDivision().equals(expectedCompanyCode)) {
                logger.warn("User {} (Account Division: {}) attempted to login to company {} (Expected Division: {}). Access denied.", 
                            userAccount.getEmployeeId(), userAccount.getErpCompanyDivision(), 
                            loginRequest.getCompanyContext(), expectedCompanyCode);
                throw new BadCredentialsException("您沒有權限登入所選的公司別。");
            }

            logger.info("User {} successfully authenticated for company context: {}. User ERP Division: {}", 
                        userAccount.getEmployeeId(), loginRequest.getCompanyContext(), userAccount.getErpCompanyDivision());

            userSessionPermissionService.clearUserSessionPermissions(userAccount);

            List<Role> roles = userRoleMapRepository.findByUserAccountAndIsDeleted(userAccount, DeleteStatusEnum.NOT_DELETED.getCode())
                .stream()
                .map(UserRoleMap::getRole)
                .distinct()
                .collect(Collectors.toList());

            List<String> effectiveFunctionAuthorities = new ArrayList<>();

            //todo:如需加非 CRUD 的權限需再調整
            if (!roles.isEmpty()) {
                List<RoleFunctionPermission> permissions = roleFunctionPermissionRepository.findByRoleInAndIsDeleted(roles, DeleteStatusEnum.NOT_DELETED.getCode());
                for (RoleFunctionPermission p : permissions) {
                    String funcCode = p.getSystemFunction().getFunctionCode();
                    if (PermissionStatusEnum.HAS_PERMISSION.getCode().equals(p.getCanCreate())) effectiveFunctionAuthorities.add(funcCode + "_CREATE");
                    if (PermissionStatusEnum.HAS_PERMISSION.getCode().equals(p.getCanRead())) effectiveFunctionAuthorities.add(funcCode + "_READ");
                    if (PermissionStatusEnum.HAS_PERMISSION.getCode().equals(p.getCanUpdate())) effectiveFunctionAuthorities.add(funcCode + "_UPDATE");
                    if (PermissionStatusEnum.HAS_PERMISSION.getCode().equals(p.getCanDelete())) effectiveFunctionAuthorities.add(funcCode + "_DELETE");
                    if (PermissionStatusEnum.HAS_PERMISSION.getCode().equals(p.getCanApprove())) effectiveFunctionAuthorities.add(funcCode + "_APPROVE");
                    if (PermissionStatusEnum.HAS_PERMISSION.getCode().equals(p.getCanChangePrice())) effectiveFunctionAuthorities.add(funcCode + "_CHANGEPRICE");
                    if (PermissionStatusEnum.HAS_PERMISSION.getCode().equals(p.getCanPrint())) effectiveFunctionAuthorities.add(funcCode + "_PRINT");
                    if (PermissionStatusEnum.HAS_PERMISSION.getCode().equals(p.getCanStoreApprove())) effectiveFunctionAuthorities.add(funcCode + "_STOREAPPROVE");
                    if (PermissionStatusEnum.HAS_PERMISSION.getCode().equals(p.getCanDispatchApprove())) effectiveFunctionAuthorities.add(funcCode + "_DISPATCHAPPROVE");
                }
            }

            List<String> distinctFunctionAuthorities = effectiveFunctionAuthorities.stream().distinct().collect(Collectors.toList());
            
            // Log the distinct function authorities
            logger.info("User {} ({}) successfully logged in. Effective function authorities: {}", 
                        userAccount.getUserName(), userAccount.getEmployeeId(), distinctFunctionAuthorities);

            UserSessionPermission sessionPermission = userSessionPermissionService.createOrUpdateUserSessionPermission(userAccount, distinctFunctionAuthorities);
            UUID sessionUuid = sessionPermission.getUserSessionPermissionId();
            
            // 記錄開始生成 JWT token
            logger.info("Starting JWT token generation for login - Employee ID: {}, User Name: {}, Session UUID: {}", 
                userAccount.getEmployeeId(), userAccount.getUserName(), sessionUuid);
            
            String jwt = jwtTokenProvider.generateToken(authentication, sessionUuid);
            
            // 記錄 JWT token 生成結果
            if (jwt != null) {
                int tokenLength = jwt.length();
                String tokenSnippet = tokenLength > 20 ? 
                    jwt.substring(0, 10) + "..." + jwt.substring(tokenLength - 10) : jwt;
                logger.info("JWT token generated for login - Employee ID: {}, Token length: {}, Token snippet: {}", 
                    userAccount.getEmployeeId(), tokenLength, tokenSnippet);
            } else {
                logger.error("Failed to generate JWT token for employee: {}", userAccount.getEmployeeId());
            }

            auditLogService.logAction(
                AuditActionTypeEnum.LOGIN_SUCCESS, 
                AuditDataTypeEnum.AUTHENTICATION, 
                userAccount.getEmployeeId(), 
                "User Login Success for company: " + loginRequest.getCompanyContext(), 
                toJson(Map.of("employeeId", userAccount.getEmployeeId(), "sessionUuid", sessionUuid, "companyContext", loginRequest.getCompanyContext(), "authoritiesCount", distinctFunctionAuthorities.size()))
            );

            boolean requiresStoreSelection = false; 
            List<LoginResponse.StoreInfo> availableStores = Collections.emptyList();
            LoginResponse.StoreInfo selectedStore = null;

            return LoginResponse.builder()
                    .token(jwt)
                    .sessionUuid(sessionUuid)
                    .userName(userDetails.getUserDisplayName())
                    .roles(userDetails.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toList()))
                    .functionPermissions(distinctFunctionAuthorities)
                    .requiresStoreSelection(requiresStoreSelection)
                    .availableStores(requiresStoreSelection ? availableStores : null)
                    .selectedStore(!requiresStoreSelection ? selectedStore : null) 
                    .message("登入成功")
                    .build();

        } catch (BadCredentialsException e) {
            logger.warn("Login failed for employeeId {}: {}", loginRequest.getEmployeeId(), e.getMessage());
            // Log company context if available in loginRequest
            String attemptedCompany = loginRequest.getCompanyContext() != null ? loginRequest.getCompanyContext() : "N/A";
            auditLogService.logAction(AuditActionTypeEnum.LOGIN_FAILURE, AuditDataTypeEnum.AUTHENTICATION, loginRequest.getEmployeeId(), 
                                    "User Login Failed: " + e.getMessage(), 
                                    toJson(Map.of("employeeId", loginRequest.getEmployeeId(), "companyContext", attemptedCompany, "reason", e.getMessage())));
            // Return specific message if it's our custom company check failure
            if (e.getMessage().contains("公司別")) { // Check if the exception message is from our company check
                return LoginResponse.builder().message(e.getMessage()).build();
            }
            return LoginResponse.builder().message("員工編號或密碼錯誤，或無權限登入所選公司。").build(); // General message
        } catch (Exception e) {
            logger.error("Login error for employeeId {}: {}", loginRequest.getEmployeeId(), e.getMessage(), e);
            String attemptedCompany = loginRequest.getCompanyContext() != null ? loginRequest.getCompanyContext() : "N/A";
             auditLogService.logAction(AuditActionTypeEnum.LOGIN_FAILURE, AuditDataTypeEnum.AUTHENTICATION, loginRequest.getEmployeeId(), 
                                    "User Login Failed: System Error", 
                                    toJson(Map.of("employeeId", loginRequest.getEmployeeId(), "companyContext", attemptedCompany, "error", e.getMessage())));
            return LoginResponse.builder().message("登入時發生內部錯誤，請稍後再試").build();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<LoginResponse.StoreInfo> getOperableStores() {
        Optional<UserDetailsImpl> currentUserDetailsOpt = SecurityUtil.getCurrentUserDetails();
        if (currentUserDetailsOpt.isEmpty()) {
            logger.warn("No authenticated user found for getOperableStores.");
            return Collections.emptyList();
        }
        UserDetailsImpl currentUserDetails = currentUserDetailsOpt.get();
        UUID currentUserId = currentUserDetails.getId();
        
        // Determine the company context for filtering stores, based on the user's own erpCompanyDivision
        UserAccount currentUserAccount = userAccountRepository.findById(currentUserId)
            .orElseThrow(() -> new ResourceNotFoundException("Authenticated user account not found: " + currentUserId));
        Short userCompanyDivision = currentUserAccount.getErpCompanyDivision();

        boolean hasMasterStoreAccess = currentUserDetails.getAuthorities().stream()
                .anyMatch(grantedAuthority -> "HO_ADMIN".equals(grantedAuthority.getAuthority()) || "ALL_STORE_ADMIN".equals(grantedAuthority.getAuthority()) || "SYS_ADMIN".equals(grantedAuthority.getAuthority()));

        List<StoreEntity> stores;
        if (hasMasterStoreAccess) {
            logger.info("User {} has master store access. Fetching all active stores, then filtering by user company {}.", 
                        currentUserDetails.getUsername(), userCompanyDivision);
            // Master access might still be implicitly scoped to their primary company context if not truly global.
            // For now, let's assume master access means all stores of THEIR company, or ALL companies if userCompanyDivision is null (though unlikely for store ops).
            stores = storeRepository.findByIsActiveAndIsDeletedOrderByStoreNameAsc(
                    StoreStatusEnum.ENABLED.getCode(), 
                    DeleteStatusEnum.NOT_DELETED.getCode()
            );
            // If master access should be restricted to their company division, filter here:
            if (userCompanyDivision != null) {
                stores = stores.stream().filter(s -> userCompanyDivision.equals(s.getErpCompanyDivision())).collect(Collectors.toList());
            }
        } else {
            logger.info("User {} (Company Division: {}) does not have master store access. Fetching stores from StoreStaffMap.", 
                        currentUserDetails.getUsername(), userCompanyDivision);
            List<StoreStaffMapEntity> staffMaps = storeStaffMapRepository
                .findByUserAccountUserAccountIdAndIsDeletedAndStoreIsActiveAndStoreIsDeletedOrderByStoreStoreNameAsc(
                    currentUserId, 
                    DeleteStatusEnum.NOT_DELETED.getCode(), 
                    StoreStatusEnum.ENABLED.getCode(), 
                    DeleteStatusEnum.NOT_DELETED.getCode()
            );
            stores = staffMaps.stream().map(StoreStaffMapEntity::getStore)
                // Filter mapped stores by the user's company division
                .filter(store -> userCompanyDivision != null && userCompanyDivision.equals(store.getErpCompanyDivision()))
                .distinct()
                .collect(Collectors.toList());
        }

        return stores.stream()
                .map(storeEntity -> LoginResponse.StoreInfo.builder()
                        .storeId(storeEntity.getStoreId().toString())
                        .storeName(storeEntity.getStoreName())
                        .regionId(storeEntity.getRegion() != null ? storeEntity.getRegion().getRegionId() : null)
                        .regionName(storeEntity.getRegion() != null ? storeEntity.getRegion().getRegionName() : null)
                        .companyDivisionCode(storeEntity.getErpCompanyDivision())
                        .companyDivisionName(ErpCompanyDivisionEnum.fromCode(storeEntity.getErpCompanyDivision()).name())
                        .build())
                .collect(Collectors.toList());
    }
} 