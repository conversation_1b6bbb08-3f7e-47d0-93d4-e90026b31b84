package com.eastking.service.impl;

import com.eastking.model.dto.DistributorDto;
import com.eastking.model.entity.Distributor;
import com.eastking.repository.DistributorRepository;
import com.eastking.service.DistributorService;
import com.eastking.enums.DeleteStatusEnum;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class DistributorServiceImpl implements DistributorService {

    private static final Logger logger = LoggerFactory.getLogger(DistributorServiceImpl.class);
    private final DistributorRepository distributorRepository;

    @Override
    public List<DistributorDto> getAllActiveSelectableDistributors() {
        return distributorRepository.findByIsDeletedOrderByDistributorNameAsc((short) 0).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<DistributorDto> getDistributorById(UUID id) {
        return distributorRepository.findById(id)
                .filter(d -> d.getIsDeleted() == (short) 0)
                .map(this::convertToDto);
    }

    private DistributorDto convertToDto(Distributor distributor) {
        if (distributor == null) {
            return null;
        }
        DistributorDto dto = new DistributorDto();
        dto.setDistributorId(distributor.getDistributorId());
        dto.setDistributorName(distributor.getDistributorName());
        dto.setErpCode(distributor.getErpCode());
        dto.setRemarks(distributor.getRemarks());
        dto.setCreateTime(distributor.getCreateTime());
        dto.setUpdateTime(distributor.getUpdateTime());
        dto.setIsDeleted(distributor.getIsDeleted());
        return dto;
    }

    // Placeholder for sync logic if needed in the future
    /*
    @Transactional
    public void syncDistributors(List<DistributorDto> externalDistributors) {
        logger.info("Starting distributor sync with {} external records.", externalDistributors.size());
        for (DistributorDto dto : externalDistributors) {
            Optional<Distributor> existingOpt = distributorRepository.findByErpCodeAndIsDeleted(dto.getErpCode(), DeleteStatusEnum.NOT_DELETED.getCode());
            Distributor entity;
            if (existingOpt.isPresent()) {
                entity = existingOpt.get();
                logger.debug("Updating existing distributor: {} (ERP: {})", entity.getDistributorName(), entity.getErpCode());
            } else {
                entity = new Distributor();
                entity.setDistributorId(UUID.randomUUID());
                entity.setErpCode(dto.getErpCode());
                entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                logger.debug("Creating new distributor with ERP code: {}", dto.getErpCode());
            }
            entity.setDistributorName(dto.getDistributorName());
            entity.setRemarks(dto.getRemarks());
            // Set audit fields if BaseEntity listener isn't fully handling it for sync or if specific user needed
            distributorRepository.save(entity);
        }
        logger.info("Distributor sync completed.");
    }
    */
} 