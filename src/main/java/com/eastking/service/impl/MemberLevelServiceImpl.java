package com.eastking.service.impl;

import com.eastking.enums.ActivationStatusEnum;
import com.eastking.enums.DeleteStatusEnum;
import com.eastking.model.dto.MemberLevelDto;
import com.eastking.model.entity.MemberLevelEntity;
import com.eastking.repository.MemberLevelRepository;
// import com.eastking.repository.UserAccountRepository; // If needed for checking member assignments
import com.eastking.service.MemberLevelService;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.exception.DataConflictException;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class MemberLevelServiceImpl implements MemberLevelService {

    private final MemberLevelRepository memberLevelRepository;
    // private final UserAccountRepository userAccountRepository; // For checking active members in a level

    public MemberLevelServiceImpl(MemberLevelRepository memberLevelRepository) {
        this.memberLevelRepository = memberLevelRepository;
    }

    @Override
    @Transactional
    public MemberLevelDto createMemberLevel(MemberLevelDto dto) {
        memberLevelRepository.findByLevelNameAndIsDeleted(dto.getLevelName(), DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(e -> { 
                throw new DataConflictException("會員等級名稱已存在: " + dto.getLevelName()); 
            });
        MemberLevelEntity entity = convertToEntity(dto);
        entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        MemberLevelEntity savedEntity = memberLevelRepository.save(entity);
        return convertToDto(savedEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MemberLevelDto> getMemberLevelById(UUID id) {
        return memberLevelRepository.findById(id)
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MemberLevelDto> getMemberLevelByName(String levelName) {
        return memberLevelRepository.findByLevelNameAndIsDeleted(levelName, DeleteStatusEnum.NOT_DELETED.getCode())
            .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MemberLevelDto> getAllMemberLevels() {
        return memberLevelRepository.findAll(Sort.by(Sort.Direction.ASC, "sequenceOrder"))
            .stream()
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public MemberLevelDto updateMemberLevel(UUID id, MemberLevelDto dto) {
        MemberLevelEntity entity = memberLevelRepository.findById(id)
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("MemberLevel not found with id: " + id));

        if (!entity.getLevelName().equals(dto.getLevelName())) {
            memberLevelRepository.findByLevelNameAndIsDeleted(dto.getLevelName(), DeleteStatusEnum.NOT_DELETED.getCode())
                .filter(e -> !e.getMemberLevelId().equals(id))
                .ifPresent(e -> { 
                    throw new DataConflictException("會員等級名稱已存在: " + dto.getLevelName()); 
                });
        }
        
        // Preserve createBy and createTime
        UUID createBy = entity.getCreateBy();
        java.time.OffsetDateTime createTime = entity.getCreateTime();

        BeanUtils.copyProperties(dto, entity, "memberLevelId", "createTime", "updateTime", "createBy", "updateBy", "isDeleted");
        // Map boolean DTO fields to Short entity fields for status
        entity.setDiscountCouponEnabled(dto.getDiscountCouponEnabled() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode());
        entity.setBirthdayGiftEnabled(dto.getBirthdayGiftEnabled() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode());
        entity.setAdditionalPerkEnabled(dto.getAdditionalPerkEnabled() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode());
        
        entity.setCreateBy(createBy);
        entity.setCreateTime(createTime);

        MemberLevelEntity updatedEntity = memberLevelRepository.save(entity);
        return convertToDto(updatedEntity);
    }

    @Override
    @Transactional
    public void deleteMemberLevel(UUID id) {
        if (!memberLevelRepository.existsById(id)) {
            throw new ResourceNotFoundException("MemberLevel", "id", id);
        }
        memberLevelRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MemberLevelDto> getAllActiveMemberLevels() {
        return memberLevelRepository.findByIsDeletedOrderBySequenceOrder((short) 0)
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private MemberLevelDto convertToDto(MemberLevelEntity entity) {
        MemberLevelDto dto = new MemberLevelDto();
        BeanUtils.copyProperties(entity, dto);
        // Map Short status fields from entity to Boolean DTO fields
        dto.setDiscountCouponEnabled(ActivationStatusEnum.YES.getCode().equals(entity.getDiscountCouponEnabled()));
        dto.setBirthdayGiftEnabled(ActivationStatusEnum.YES.getCode().equals(entity.getBirthdayGiftEnabled()));
        dto.setAdditionalPerkEnabled(ActivationStatusEnum.YES.getCode().equals(entity.getAdditionalPerkEnabled()));
        return dto;
    }

    private MemberLevelEntity convertToEntity(MemberLevelDto dto) {
        MemberLevelEntity entity = new MemberLevelEntity();
        BeanUtils.copyProperties(dto, entity, "isDeleted"); // isDeleted handled separately
        // Map boolean DTO fields to Short entity fields for status
        entity.setDiscountCouponEnabled(dto.getDiscountCouponEnabled() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode());
        entity.setBirthdayGiftEnabled(dto.getBirthdayGiftEnabled() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode());
        entity.setAdditionalPerkEnabled(dto.getAdditionalPerkEnabled() ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode());
        return entity;
    }
} 