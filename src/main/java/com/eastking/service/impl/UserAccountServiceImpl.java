package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.enums.UserAccountStatusEnum;
import com.eastking.enums.UserAccountTypeEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.enums.BooleanStatusEnum;
import com.eastking.enums.InvitationStatusEnum;
import com.eastking.model.dto.UserAccountDto;
import com.eastking.model.dto.UserAccountSlimDto;
import com.eastking.model.dto.ServiceAreaDto;
import com.eastking.model.entity.Role;
import com.eastking.model.entity.UserAccount;
import com.eastking.model.entity.UserRoleMap;
import com.eastking.model.entity.DispatchCollaborator;
import com.eastking.model.entity.DispatchRepair;
import com.eastking.model.entity.TechnicianAreaConfig;
import com.eastking.repository.RoleRepository;
import com.eastking.repository.UserAccountRepository;
import com.eastking.repository.UserRoleMapRepository;
import com.eastking.repository.StoreStaffMapRepository;
import com.eastking.repository.TechnicianRoleConfigRepository;
import com.eastking.repository.DispatchRepairRepository;
import com.eastking.repository.TechnicianAreaConfigRepository;
import com.eastking.repository.DispatchCollaboratorRepository;
import com.eastking.security.AuthSecurityService;
import com.eastking.service.AuditLogService;
import com.eastking.service.UserAccountService;
import com.eastking.util.SecurityUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import com.eastking.exception.DataConflictException;
import com.eastking.exception.InvalidInputException;
import com.eastking.exception.ResourceNotFoundException;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserAccountServiceImpl implements UserAccountService {

    private final UserAccountRepository userAccountRepository;
    private final RoleRepository roleRepository;
    private final UserRoleMapRepository userRoleMapRepository;
    private final PasswordEncoder passwordEncoder;
    private final StoreStaffMapRepository storeStaffMapRepository;
    private final AuditLogService auditLogService;
    private final ObjectMapper objectMapper;
    private final TechnicianRoleConfigRepository technicianRoleConfigRepository;
    private final DispatchRepairRepository dispatchRepairRepository;
    private final TechnicianAreaConfigRepository technicianAreaConfigRepository;
    private final DispatchCollaboratorRepository dispatchCollaboratorRepository;

    @Override
    @Transactional
    public UserAccountDto createUserAccount(UserAccountDto dto) {
        userAccountRepository.findByEmployeeIdAndIsDeleted(dto.getEmployeeId(), DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(e -> { throw new DataConflictException("員工編號已存在: " + dto.getEmployeeId()); });

        UserAccount entity = new UserAccount();
        BeanUtils.copyProperties(dto, entity, "userAccountId", "password", "roleNames", "createdByName", "updatedByName", "createTime", "updateTime", "isDeleted", "accountTypeDescription", "erpCompanyDivisionDescription");
        entity.setUserPassword(passwordEncoder.encode(dto.getPassword()));
        entity.setIsActive(dto.getIsActiveCode());
        entity.setAccountType(dto.getAccountType() != null ? dto.getAccountType() : UserAccountTypeEnum.GENERAL_STAFF.getCode());
        entity.setErpCompanyDivision(dto.getErpCompanyDivision());
        entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        
        UserAccount savedUser = userAccountRepository.save(entity);
        updateUserRoleMappings(savedUser, dto.getRoleIds());
        return convertToDto(savedUser);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserAccountDto> getUserAccountById(UUID userId) {
        return userAccountRepository.findByUserAccountIdAndIsDeleted(userId, DeleteStatusEnum.NOT_DELETED.getCode())
            .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserAccountDto> getUserByEmployeeId(String employeeId) {
        return userAccountRepository.findByEmployeeIdAndIsDeleted(employeeId, DeleteStatusEnum.NOT_DELETED.getCode())
            .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserAccountDto> getAllUserAccounts(Pageable pageable, String employeeId, String userName, Boolean isActive, Short accountType, String companyContext) {
        Specification<UserAccount> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if (StringUtils.hasText(employeeId)) {
                predicates.add(cb.like(cb.lower(root.get("employeeId")), "%" + employeeId.toLowerCase() + "%"));
            }
            if (StringUtils.hasText(userName)) {
                predicates.add(cb.like(cb.lower(root.get("userName")), "%" + userName.toLowerCase() + "%"));
            }
            if (isActive != null) {
                predicates.add(cb.equal(root.get("isActive"), isActive ? UserAccountStatusEnum.ACTIVE.getCode() : UserAccountStatusEnum.INACTIVE.getCode()));
            }
            if (accountType != null) {
                predicates.add(cb.equal(root.get("accountType"), accountType));
            }
            if (StringUtils.hasText(companyContext)) {
                if ("EASTKING".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.EASTKING.getCode()));
                } else if ("QUEYOU".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.QUEYOU.getCode()));
                }
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        Page<UserAccount> userPage = userAccountRepository.findAll(spec, pageable);
        List<UserAccountDto> dtoList = userPage.getContent().stream()
                                       .map(this::convertToDto)
                                       .collect(Collectors.toList());
        return new PageImpl<>(dtoList, pageable, userPage.getTotalElements());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<UserAccountDto> getAllActiveUsersForSelection() {
        List<UserAccount> activeUsers = userAccountRepository.findByIsActiveAndIsDeleted(UserAccountStatusEnum.ACTIVE.getCode(), (short) 0);
        return activeUsers.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserAccountDto> findActiveUsersByRoleCode(String roleCode) {
        if (!StringUtils.hasText(roleCode)) {
            return Collections.emptyList();
        }
        Optional<Role> roleOpt = roleRepository.findByRoleCodeAndIsDeleted(roleCode, (short) 0);
        if (roleOpt.isEmpty()) {
            System.err.println("Role with code '" + roleCode + "' not found.");
            return Collections.emptyList();
        }
        Role targetRole = roleOpt.get();
        List<UserRoleMap> userRoleMaps = userRoleMapRepository.findByRoleAndIsDeleted(targetRole, (short) 0);
        
        return userRoleMaps.stream()
                .map(UserRoleMap::getUserAccount)
                .filter(userAccount -> userAccount.getIsActive() != null && userAccount.getIsActive().equals(UserAccountStatusEnum.ACTIVE.getCode()) && userAccount.getIsDeleted() == 0)
                .map(this::convertToDto)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public UserAccountDto updateUserAccount(UUID userId, UserAccountDto dto) {
        UserAccount entity = userAccountRepository.findByUserAccountIdAndIsDeleted(userId, DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("UserAccount not found with id: " + userId));

        if (!entity.getEmployeeId().equals(dto.getEmployeeId())) {
             userAccountRepository.findByEmployeeIdAndIsDeleted(dto.getEmployeeId(), DeleteStatusEnum.NOT_DELETED.getCode())
                .filter(e -> !e.getUserAccountId().equals(userId))
                .ifPresent(e -> { throw new DataConflictException("員工編號已存在: " + dto.getEmployeeId()); });
        }

        BeanUtils.copyProperties(dto, entity, "userAccountId", "password", "roleNames", "createdByName", "updatedByName", "createTime", "updateTime", "isDeleted", "createBy", "accountTypeDescription", "erpCompanyDivisionDescription");
        if (StringUtils.hasText(dto.getPassword())) {
            entity.setUserPassword(passwordEncoder.encode(dto.getPassword()));
        }
        entity.setIsActive(dto.getIsActiveCode());
        if (dto.getAccountType() != null) {
            entity.setAccountType(dto.getAccountType());
        }
        entity.setErpCompanyDivision(dto.getErpCompanyDivision());
        
        UserAccount updatedUser = userAccountRepository.save(entity);
        updateUserRoleMappings(updatedUser, dto.getRoleIds());
        return convertToDto(updatedUser);
    }

    @Override
    @Transactional
    public UserAccountDto updateUserRoles(UUID userId, List<UUID> roleIds) {
        UserAccount entity = userAccountRepository.findByUserAccountIdAndIsDeleted(userId, DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("UserAccount not found with id: " + userId));
        updateUserRoleMappings(entity, roleIds);
        return convertToDto(entity);
    }

    @Override
    @Transactional
    public void deleteUserAccount(UUID userId) {
        UserAccount entity = userAccountRepository.findByUserAccountIdAndIsDeleted(userId, DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("UserAccount not found with id: " + userId));
        entity.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        userAccountRepository.save(entity);
        // Note: UserRoleMap entries will remain but are effectively orphaned for this user. Can be cleaned if needed.
    }

    @Override
    @Transactional
    public void changePassword(UUID userId, String oldPassword, String newPassword) {
        UserAccount entity = userAccountRepository.findByUserAccountIdAndIsDeleted(userId, DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("UserAccount not found with id: " + userId));
        if (!passwordEncoder.matches(oldPassword, entity.getUserPassword())) {
            throw new InvalidInputException("舊密碼不正確。");
        }
        entity.setUserPassword(passwordEncoder.encode(newPassword));
        userAccountRepository.save(entity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserAccountDto> getSalespersons(UUID storeId) {
        // 查找所有包含 "SALES" 的角色
        List<Role> salesRoles = roleRepository.findByRoleCodeContainingIgnoreCaseAndIsDeleted("SALES", (short) 0);
        if (salesRoles.isEmpty()) {
            return Collections.emptyList();
        }

        List<UUID> salesRoleIds = salesRoles.stream().map(Role::getRoleId).collect(Collectors.toList());

        // 查找所有擁有這些銷售角色的使用者
        List<UserAccount> salesUsers = userRoleMapRepository.findDistinctByUserAccount_IsActiveAndUserAccount_IsDeletedAndRole_RoleIdIn(
            UserAccountStatusEnum.ACTIVE.getCode(),
            DeleteStatusEnum.NOT_DELETED.getCode(),
            salesRoleIds
        ).stream().map(UserRoleMap::getUserAccount).distinct().collect(Collectors.toList());

        // 如果提供了 storeId，則進一步篩選
        if (storeId != null) {
            // 這裡需要一個方法來獲取某個門市的所有員工
            // 假設我們有一個 storeStaffMapRepository (需要注入)
            // List<UUID> staffIdsInStore = storeStaffMapRepository.findByStoreId(storeId).stream().map(map -> map.getUserAccount().getUserAccountId()).collect(Collectors.toList());
            // salesUsers = salesUsers.stream().filter(user -> staffIdsInStore.contains(user.getUserAccountId())).collect(Collectors.toList());
            // 暫時，我們先不過濾，因為 StoreStaffMapRepository 未注入
        }

        return salesUsers.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserAccountSlimDto> getTechnicians() {
        // 1. Find all role IDs configured as technician roles
        List<UUID> technicianRoleIds = technicianRoleConfigRepository.findAllByIsActive(BooleanStatusEnum.TRUE.getCode())
                .stream()
                .map(config -> config.getRole().getRoleId())
                .collect(Collectors.toList());

        if (technicianRoleIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. Find all UserRoleMap entities linked to these technician roles
        List<UserRoleMap> userRoleMaps = userRoleMapRepository.findAllByRole_RoleIdInAndIsDeleted(
                technicianRoleIds,
                DeleteStatusEnum.NOT_DELETED.getCode()
        );

        // 3. Extract unique, active users from the maps
        if (userRoleMaps!=null) log.debug("getTechnicians count:{}",userRoleMaps.size());
        return userRoleMaps.stream()
                .map(UserRoleMap::getUserAccount)
                .filter(user -> user != null && user.getIsActive().equals(BooleanStatusEnum.TRUE.getCode()))
                .distinct() 
                .map(this::convertToSlimDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserAccountSlimDto> findSelectableCollaborators(UUID dispatchRepairId) {
        // 1. Get the main dispatch repair and technician
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));

        UUID mainTechnicianId = dispatchRepair.getAssignedTechnicianId();
        if (mainTechnicianId == null) {
            throw new InvalidInputException("此派工單尚未指派主要技師。");
        }

        // 2. Get the current month for area config lookup (e.g., "2023-10")
        String currentMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));

        // 3. Find the service areas for the main technician for the current month
        Set<String> mainTechnicianAreaCodes = technicianAreaConfigRepository.findByTechnicianIdAndConfigMonth(mainTechnicianId, currentMonth)
                .map(TechnicianAreaConfig::getServiceAreas)
                .map(serviceAreas -> serviceAreas.stream()
                        .flatMap(areaDto -> areaDto.getDistrictCodes().stream())
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());

        // 4. Get all active technicians
        List<UserAccount> allTechnicians = getTechnicians().stream()
                .map(slimDto -> {
                    UserAccount user = new UserAccount();
                    user.setUserAccountId(slimDto.getUserAccountId());
                    user.setUserName(slimDto.getUserName());
                    return user;
                })
                .collect(Collectors.toList());

        // 5. Get all area configs for the current month and map them by technician ID
        Map<UUID, Set<String>> allTechnicianAreasMap = technicianAreaConfigRepository.findByConfigMonth(currentMonth).stream()
                .collect(Collectors.toMap(
                        TechnicianAreaConfig::getTechnicianId,
                        config -> config.getServiceAreas().stream()
                                .flatMap(areaDto -> areaDto.getDistrictCodes().stream())
                                .collect(Collectors.toSet())
                ));

        // 6. Get collaborators for this dispatch repair who are not rejected
        Set<UUID> excludedCollaboratorIds = dispatchCollaboratorRepository.findByDispatchRepair(dispatchRepair).stream()
                .filter(c -> !InvitationStatusEnum.REJECTED.getCode().equals(c.getInvitationStatus()))
                .map(c -> c.getTechnician().getUserAccountId())
                .collect(Collectors.toSet());

        // 7. Filter the list of all technicians
        return allTechnicians.stream()
                // Filter out the main technician
                .filter(tech -> !tech.getUserAccountId().equals(mainTechnicianId))
                // Filter out already invited (and not rejected) collaborators
                .filter(tech -> !excludedCollaboratorIds.contains(tech.getUserAccountId()))
                // Filter by service area
                .filter(tech -> {
                    Set<String> currentTechAreas = allTechnicianAreasMap.get(tech.getUserAccountId());
                    // If the main technician has no areas, any other technician is selectable.
                    if (mainTechnicianAreaCodes.isEmpty()) {
                        return true;
                    }
                    // If the current technician has no areas, they are selectable.
                    if (currentTechAreas == null || currentTechAreas.isEmpty()) {
                        return true;
                    }
                    // Check for intersection
                    return !Collections.disjoint(mainTechnicianAreaCodes, currentTechAreas);
                })
                .map(this::convertToSlimDto)
            .collect(Collectors.toList());
    }

    private void updateUserRoleMappings(UserAccount user, List<UUID> newRoleIds) {
        if (newRoleIds == null) newRoleIds = new ArrayList<>();
        
        List<UserRoleMap> existingMaps = userRoleMapRepository.findByUserAccountUserAccountIdAndIsDeleted(user.getUserAccountId(), DeleteStatusEnum.NOT_DELETED.getCode());
        Set<UUID> existingRoleIds = existingMaps.stream().map(map -> map.getRole().getRoleId()).collect(Collectors.toSet());
        Set<UUID> targetRoleIds = new HashSet<>(newRoleIds);

        // Remove old roles not in new list
        existingMaps.stream()
            .filter(map -> !targetRoleIds.contains(map.getRole().getRoleId()))
            .forEach(map -> {
                map.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
                userRoleMapRepository.save(map);
            });

        // Add new roles
        targetRoleIds.stream()
            .filter(roleId -> !existingRoleIds.contains(roleId))
            .forEach(roleId -> {
                Role role = roleRepository.findById(roleId)
                    .orElseThrow(() -> new ResourceNotFoundException("Role not found with id: " + roleId));
                UserRoleMap newMap = new UserRoleMap();
                newMap.setUserAccount(user);
                newMap.setRole(role);
                newMap.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                userRoleMapRepository.save(newMap);
            });
    }

    private UserAccountSlimDto convertToSlimDto(UserAccount user) {
        if (user == null) return null;
        return UserAccountSlimDto.fromEntity(user);
    }

    private UserAccountDto convertToDto(UserAccount entity) {
        if (entity == null) {
            return null;
        }
        UserAccountDto dto = new UserAccountDto();
        BeanUtils.copyProperties(entity, dto, "userPassword");
        dto.setIsActive(UserAccountStatusEnum.ACTIVE.getCode().equals(entity.getIsActive()));
        dto.setAccountTypeFields(entity.getAccountType());
        dto.setErpCompanyDivisionFields(entity.getErpCompanyDivision());
        
        List<String> roleNames = userRoleMapRepository.findByUserAccountAndIsDeleted(entity, (short) 0)
            .stream()
            .map(userRoleMap -> userRoleMap.getRole().getRoleName())
            .distinct()
            .collect(Collectors.toList());
        dto.setRoleNames(roleNames);
        
        return dto;
    }
} 