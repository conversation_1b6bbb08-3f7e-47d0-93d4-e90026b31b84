package com.eastking.service.impl;

import com.eastking.model.dto.ProductSettingDto;
import com.eastking.model.dto.ProductSearchResultDto;
import com.eastking.model.dto.ProductSearchResultWithGiftDto;
import com.eastking.model.dto.response.GiftBundleItemDto;
import com.eastking.model.entity.GiftBundle;
import com.eastking.model.entity.GiftBundleItem;
import com.eastking.model.entity.ProductSetting;
import com.eastking.model.entity.StoreInventory;
import com.eastking.model.entity.StoreEntity;
import com.eastking.repository.ProductSettingRepository;
import com.eastking.repository.UserAccountRepository; 
import com.eastking.repository.StoreInventoryRepository;
import com.eastking.repository.StoreRepository;
import com.eastking.repository.WarehouseRepository;
import com.eastking.repository.WarehouseInventoryRepository;
import com.eastking.repository.GiftBundleRepository;
import com.eastking.service.ProductSettingService;
import com.eastking.enums.DeleteStatusEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.enums.WarrantyPeriodEnum;
import com.eastking.exception.ResourceNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.criteria.Predicate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Collections;
import java.util.HashMap;
import java.time.OffsetDateTime;

@Service
public class ProductSettingServiceImpl implements ProductSettingService {

    private static final Logger logger = LoggerFactory.getLogger(ProductSettingServiceImpl.class);
    private final ProductSettingRepository productSettingRepository;
    private final UserAccountRepository userAccountRepository; 
    private final StoreInventoryRepository storeInventoryRepository;
    private final StoreRepository storeRepository;
    private final WarehouseRepository warehouseRepository;
    private final WarehouseInventoryRepository warehouseInventoryRepository;
    private final GiftBundleRepository giftBundleRepository;

    @Autowired
    public ProductSettingServiceImpl(ProductSettingRepository productSettingRepository, UserAccountRepository userAccountRepository, StoreInventoryRepository storeInventoryRepository,
                                   StoreRepository storeRepository, WarehouseRepository warehouseRepository, WarehouseInventoryRepository warehouseInventoryRepository, GiftBundleRepository giftBundleRepository) {
        this.productSettingRepository = productSettingRepository;
        this.userAccountRepository = userAccountRepository;
        this.storeInventoryRepository = storeInventoryRepository;
        this.storeRepository = storeRepository;
        this.warehouseRepository = warehouseRepository;
        this.warehouseInventoryRepository = warehouseInventoryRepository;
        this.giftBundleRepository = giftBundleRepository;
    }

    @Override
    @Transactional
    public ProductSettingDto createProductSetting(ProductSettingDto dto) {
        logger.info("Creating new product setting for barcode: {}", dto.getProductBarcode());
        ProductSetting entity = convertToEntity(dto);
        entity.setProductSettingId(UUID.randomUUID()); 
        entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        ProductSetting savedEntity = productSettingRepository.save(entity);
        logger.info("Successfully created product setting with ID: {}", savedEntity.getProductSettingId());
        return convertToDtoWithAuditNames(savedEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ProductSettingDto> getProductSettingById(UUID id, String companyContext) {
        logger.debug("Fetching product setting by ID: {} with company context: {}", id, companyContext);
        return productSettingRepository.findByProductSettingIdAndIsDeleted(id, DeleteStatusEnum.NOT_DELETED.getCode())
                .map(this::convertToDtoWithAuditNames);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<ProductSettingDto> getProductSettingByBarcode(String productBarcode, String companyContext) {
        logger.debug("Fetching product setting by barcode: {} with company context: {}", productBarcode, companyContext);
        return productSettingRepository.findByProductBarcodeAndIsDeleted(productBarcode, DeleteStatusEnum.NOT_DELETED.getCode())
                .map(this::convertToDtoWithAuditNames);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProductSettingDto> getAllProductSettings(Pageable pageable, String productBarcode, String productName, Boolean isActive, String companyContext) {
        logger.debug("Fetching all product settings with filters - barcode: {}, name: {}, active: {}, company: {}", productBarcode, productName, isActive, companyContext);
        Specification<ProductSetting> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if (StringUtils.hasText(productBarcode)) {
                predicates.add(cb.like(cb.lower(root.get("productBarcode")), "%" + productBarcode.toLowerCase() + "%"));
            }
            if (StringUtils.hasText(productName)) {
                predicates.add(cb.like(cb.lower(root.get("productName")), "%" + productName.toLowerCase() + "%"));
            }
            if (isActive != null) {
                predicates.add(cb.equal(root.get("isActive"), isActive ? (short)1 : (short)0));
            }

            if (StringUtils.hasText(companyContext)) {
                Predicate allCompaniesPredicate = cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.ALL.getCode());
                if ("EASTKING".equalsIgnoreCase(companyContext)) {
                    Predicate eastkingPredicate = cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.EASTKING.getCode());
                    predicates.add(cb.or(eastkingPredicate, allCompaniesPredicate));
                } else if ("QUEYOU".equalsIgnoreCase(companyContext)) {
                    Predicate queyouPredicate = cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.QUEYOU.getCode());
                    predicates.add(cb.or(queyouPredicate, allCompaniesPredicate));
                }
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return productSettingRepository.findAll(spec, pageable).map(this::convertToDtoWithAuditNames);
    }

    @Override
    @Transactional
    public ProductSettingDto updateProductSetting(UUID id, ProductSettingDto dto, String companyContext) {
        logger.info("Updating product setting with ID: {}, companyContext: {}", id, companyContext);
        ProductSetting existingSetting = productSettingRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("ProductSetting not found with id: " + id));

        // Manually map fields that need logic or are not on the DTO
        // Use BeanUtils for the rest, but be careful about nulls if needed
        BeanUtils.copyProperties(dto, existingSetting, "productSettingId", "createTime", "createBy");
        
        // Explicitly set the product name
        existingSetting.setProductName(dto.getProductName());

        existingSetting.setCurrency(dto.getCurrency());
        
        existingSetting.setListPrice(dto.getListPrice());
        existingSetting.setSalePrice(dto.getSalePrice());
        existingSetting.setCostPrice(dto.getCostPrice());
        existingSetting.setSaleUnit(dto.getSaleUnit());
        existingSetting.setSaleEffectiveStartDate(dto.getSaleEffectiveStartDate());
        existingSetting.setSaleEffectiveEndDate(dto.getSaleEffectiveEndDate());
        
        existingSetting.setQueyouListPrice(dto.getQueyouListPrice());
        existingSetting.setQueyouSalePrice(dto.getQueyouSalePrice());
        existingSetting.setQueyouCostPrice(dto.getQueyouCostPrice());
        existingSetting.setQueyouSaleEffectiveStartDate(dto.getQueyouSaleEffectiveStartDate());
        existingSetting.setQueyouSaleEffectiveEndDate(dto.getQueyouSaleEffectiveEndDate());

        existingSetting.setIsDispatchProduct(dto.getIsDispatchProduct() != null && dto.getIsDispatchProduct() ? (short)1 : (short)0);
        existingSetting.setIsActive(dto.getIsActive() != null && dto.getIsActive() ? (short)1 : (short)0);
        existingSetting.setWarrantyMonths(dto.getWarrantyMonths());

        if (dto.getWarrantyMonths() != null) {
            WarrantyPeriodEnum anEnum = WarrantyPeriodEnum.fromCode(dto.getWarrantyMonths().shortValue());
            if (anEnum != null) {
                dto.setWarrantyPeriodDescription(anEnum.getDescription());
            } else {
                dto.setWarrantyPeriodDescription(dto.getWarrantyMonths() + " 個月");
            }
        }

        ProductSetting updatedEntity = productSettingRepository.save(existingSetting);
        logger.info("Product setting updated for ID: {}", updatedEntity.getProductSettingId());
        return convertToDtoWithAuditNames(updatedEntity);
    }

    @Override
    @Transactional
    public void deleteProductSetting(UUID id) {
        logger.info("Attempting to delete product setting with ID: {}", id);
        ProductSetting entity = productSettingRepository.findByProductSettingIdAndIsDeleted(id, DeleteStatusEnum.NOT_DELETED.getCode())
                .orElseThrow(() -> new ResourceNotFoundException("ProductSetting not found with id: " + id));
        
        if (!CollectionUtils.isEmpty(entity.getDiscounts())) {
            logger.debug("Soft deleting associated discounts for product setting ID: {}", id);
            entity.getDiscounts().forEach(discount -> discount.setIsDeleted(DeleteStatusEnum.DELETED.getCode()));
        }

        entity.setIsDeleted(DeleteStatusEnum.DELETED.getCode()); 
        productSettingRepository.save(entity);
        logger.info("Soft deleted product setting with ID: {}", id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ProductSettingDto> findByProductBarcodeIn(List<String> barcodes, String companyContext) {
        logger.debug("Fetching product settings for barcodes: {} with company context: {}", barcodes, companyContext);
        List<ProductSetting> entities = productSettingRepository.findByProductBarcodeInAndIsDeleted(barcodes, DeleteStatusEnum.NOT_DELETED.getCode());
        return entities.stream().map(this::convertToDtoWithAuditNames).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductSettingDto> findByKeyword(String keyword) {
        Specification<ProductSetting> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));
            if (StringUtils.hasText(keyword)) {
                Predicate nameLike = cb.like(cb.lower(root.get("productName")), "%" + keyword.toLowerCase() + "%");
                Predicate barcodeLike = cb.like(cb.lower(root.get("productBarcode")), "%" + keyword.toLowerCase() + "%");
                predicates.add(cb.or(nameLike, barcodeLike));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return productSettingRepository.findAll(spec).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProductSettingDto> searchDispatchProducts(String productBarcode, String productName, Boolean isActive, OffsetDateTime startDate, OffsetDateTime endDate, Pageable pageable) {
        Specification<ProductSetting> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDispatchProduct"), (short) 1)); // Filter for dispatch products
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));

            if (StringUtils.hasText(productBarcode)) {
                predicates.add(cb.like(cb.lower(root.get("productBarcode")), "%" + productBarcode.toLowerCase() + "%"));
            }
            if (StringUtils.hasText(productName)) {
                predicates.add(cb.like(cb.lower(root.get("productName")), "%" + productName.toLowerCase() + "%"));
            }

            if (isActive != null) {
                predicates.add(cb.equal(root.get("isActive"), isActive ? (short) 1 : (short) 0));
            }
            
            if (startDate != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("updateTime"), startDate));
            }
            if (endDate != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("updateTime"), endDate));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return productSettingRepository.findAll(spec, pageable).map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductSearchResultDto> searchStoreProducts(String keyword, UUID storeId, String companyContext) {
        // Step 1: Find all inventory items in the given store that match the product name keyword.
        List<StoreInventory> inventories = storeInventoryRepository.findByStore_StoreIdAndProductNameContainingIgnoreCase(storeId, keyword);

        if (inventories.isEmpty()) {
            return Collections.emptyList();
        }

        // Step 2: Get the barcodes from the found inventory items.
        List<String> barcodes = inventories.stream()
                .map(StoreInventory::getProductBarcode)
                .collect(Collectors.toList());
        
        // Create a map for quick lookup of stock quantity
        Map<String, Integer> stockMap = inventories.stream()
                .collect(Collectors.toMap(StoreInventory::getProductBarcode, StoreInventory::getQuantityOnHand));

        // Step 3: Find all product settings for these barcodes that are NOT dispatch products.
        List<ProductSetting> settings = productSettingRepository.findByProductBarcodeInAndIsDispatchProductAndIsDeleted(barcodes, (short) 0, (short) 0);

        // Step 4: Convert to DTOs and return
        return settings.stream()
                .map(s -> convertToProductSearchResultDto(s, companyContext, stockMap.get(s.getProductBarcode()), null))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductSearchResultWithGiftDto> searchDispatchableProducts(String keyword, String companyContext, UUID storeId, UUID distributorId) {
        
        // 1. 決定要查詢的倉庫 ID 列表
        List<UUID> warehouseIds = new ArrayList<>();
        if (storeId != null) {
            // 有門市：查詢該門市所在區域的所有倉庫
            StoreEntity store = storeRepository.findById(storeId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到門市，ID: " + storeId));
            if (store.getRegion() != null) {
                warehouseRepository.findByRegion_RegionIdAndIsDeleted(store.getRegion().getRegionId(), (short) 0)
                    .forEach(wh -> warehouseIds.add(wh.getWarehouseId()));
            }
        } else {
            // 無門市：查詢總倉庫
            Short companyCode = "QUEYOU".equalsIgnoreCase(companyContext) ? 
                                ErpCompanyDivisionEnum.QUEYOU.getCode() : 
                                ErpCompanyDivisionEnum.EASTKING.getCode();
            warehouseRepository.findByIsMainAndErpCompanyDivision((short) 1, companyCode)
                .forEach(wh -> warehouseIds.add(wh.getWarehouseId()));
        }

        if (warehouseIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 根據倉庫ID和關鍵字，從庫存中反查有庫存(>0)的商品條碼
        List<String> barcodesInStock = warehouseInventoryRepository.findDistinctProductBarcodesByWarehouseIdsAndKeywordAndInStock(warehouseIds, keyword);

        if (barcodesInStock.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 3. 根據商品條碼，查詢是「派工商品」的設定
        List<ProductSetting> settings = productSettingRepository.findByProductBarcodeInAndIsDispatchProduct((short) 1, barcodesInStock);

        // 4. (可選優化) 批量獲取庫存數量以用於顯示
        Map<String, Integer> stockMap = warehouseInventoryRepository.findTotalStockByBarcodesAndWarehouses(barcodesInStock, warehouseIds)
                .stream()
                .collect(Collectors.toMap(
                    item -> (String) item[0], // barcode
                    item -> ((Number) item[1]).intValue()  // total stock
                ));

        // --- Start of New Gift Bundle Logic ---
        
        // 5. Get all barcodes from the found settings
        List<String> mainProductBarcodes = settings.stream()
            .map(ProductSetting::getProductBarcode)
            .collect(Collectors.toList());
        logger.debug("Found {} main products to check for gift bundles. Barcodes: {}", mainProductBarcodes.size(), mainProductBarcodes);

        // 6. Batch fetch active gift bundles for these products
        List<GiftBundle> activeBundles = giftBundleRepository.findActiveBundlesByMainProductBarcodes(mainProductBarcodes, OffsetDateTime.now());
//        if (!activeBundles.isEmpty()) {
//            logger.debug("Found {} active gift bundles for the given barcodes. Bundle Names: {}", activeBundles.size(), activeBundles.stream().map(GiftBundle::getBundleName).collect(Collectors.joining(", ")));
//        } else {
//            logger.debug("No active gift bundles found for the given barcodes.");
//        }

        // 7. Batch fetch prices for all gift items from the bundles
        Map<String, ProductSetting> giftProductSettings = new HashMap<>();
        if (!activeBundles.isEmpty()) {
            List<String> giftBarcodes = activeBundles.stream()
                .flatMap(bundle -> bundle.getItems().stream())
                .map(GiftBundleItem::getGiftProductBarcode)
                .distinct()
                .collect(Collectors.toList());
            
            if (!giftBarcodes.isEmpty()) {
                giftProductSettings.putAll(productSettingRepository.findByProductBarcodeInAndIsDeleted(giftBarcodes, (short)0)
                    .stream()
                    .collect(Collectors.toMap(ProductSetting::getProductBarcode, ps -> ps)));
            }
        }
        
        // 8. Group bundles by their main product barcode for easy lookup
        Map<String, List<GiftBundle>> bundlesByMainBarcode = activeBundles.stream()
            .collect(Collectors.groupingBy(GiftBundle::getMainProductBarcode));

        // 9. Convert to DTO, now including gift bundle information
        return settings.stream().map(s -> {
            ProductSearchResultWithGiftDto dto = new ProductSearchResultWithGiftDto();
            ProductSearchResultDto originalDto = convertToProductSearchResultDto(s, companyContext, stockMap.get(s.getProductBarcode()), "總倉/區域倉");
            BeanUtils.copyProperties(originalDto, dto);
            
            if (bundlesByMainBarcode.containsKey(s.getProductBarcode())) {
                List<GiftBundleItemDto> giftItemsDto = bundlesByMainBarcode.get(s.getProductBarcode()).stream()
                    .flatMap(bundle -> bundle.getItems().stream())
                    .map(item -> {
                        ProductSetting giftPs = giftProductSettings.get(item.getGiftProductBarcode());
                        
                        Short companyCode = "QUEYOU".equalsIgnoreCase(companyContext) 
                                            ? ErpCompanyDivisionEnum.QUEYOU.getCode() 
                                            : ErpCompanyDivisionEnum.EASTKING.getCode();
                        
                        BigDecimal listPrice = giftPs != null ? giftPs.determinePriceForCompany(companyCode) : BigDecimal.ZERO;
                        BigDecimal salePrice = listPrice;

                        return GiftBundleItemDto.builder()
                            .giftBundleItemId(item.getBundleItemId())
                            .giftProductBarcode(item.getGiftProductBarcode())
                            .giftProductName(item.getGiftProductName())
                            .quantity(item.getQuantity())
                            .listPrice(listPrice)
                            .salePrice(salePrice)
                            .build();
                    })
                    .collect(Collectors.toList());
                dto.setGiftBundleItems(giftItemsDto);
            }
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductSearchResultDto> searchAccessories(String keyword, String companyContext, UUID storeId) {
        // 1. 決定要查詢的倉庫 ID 列表 (與 searchDispatchableProducts 邏輯相同)
        List<UUID> warehouseIds = new ArrayList<>();
        if (storeId != null) {
            StoreEntity store = storeRepository.findById(storeId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到門市，ID: " + storeId));
            if (store.getRegion() != null) {
                warehouseRepository.findByRegion_RegionIdAndIsDeleted(store.getRegion().getRegionId(), (short) 0)
                    .forEach(wh -> warehouseIds.add(wh.getWarehouseId()));
            }
        } else {
            Short companyCode = "QUEYOU".equalsIgnoreCase(companyContext) ? 
                                ErpCompanyDivisionEnum.QUEYOU.getCode() : 
                                ErpCompanyDivisionEnum.EASTKING.getCode();
            warehouseRepository.findByIsMainAndErpCompanyDivision((short) 1, companyCode)
                .forEach(wh -> warehouseIds.add(wh.getWarehouseId()));
        }

        if (warehouseIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 根據倉庫ID和關鍵字，從庫存中反查有庫存(>0)的商品條碼
        List<String> barcodesInStock = warehouseInventoryRepository.findDistinctProductBarcodesByWarehouseIdsAndKeywordAndInStock(warehouseIds, keyword);

        if (barcodesInStock.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. 根據商品條碼，查詢是「非主商品」的設定
        List<ProductSetting> settings = productSettingRepository.findByProductBarcodeInAndIsMain((short) 0, barcodesInStock);

        // 4. (可選優化) 批量獲取庫存數量以用於顯示
        Map<String, Integer> stockMap = warehouseInventoryRepository.findTotalStockByBarcodesAndWarehouses(barcodesInStock, warehouseIds)
                .stream()
                .collect(Collectors.toMap(
                    item -> (String) item[0], // barcode
                    item -> ((Number) item[1]).intValue()  // total stock
                ));

        // 5. 轉換為 DTO 並返回
        return settings.stream()
            .map(s -> convertToProductSearchResultDto(s, companyContext, stockMap.get(s.getProductBarcode()), "總倉/區域倉"))
               .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductSearchResultDto> searchProductsByMainFlag(String keyword, boolean isMain) {
        Specification<ProductSetting> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isMain"), isMain ? (short)1 : (short)0));
            predicates.add(cb.equal(root.get("isDeleted"), (short)0));
            if (StringUtils.hasText(keyword)) {
                Predicate nameLike = cb.like(cb.lower(root.get("productName")), "%" + keyword.toLowerCase() + "%");
                Predicate barcodeLike = cb.like(cb.lower(root.get("productBarcode")), "%" + keyword.toLowerCase() + "%");
                predicates.add(cb.or(nameLike, barcodeLike));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return productSettingRepository.findAll(spec).stream()
            .map(s -> convertToProductSearchResultDto(s, null, null, null)) // companyContext, stock info are not relevant here
            .collect(Collectors.toList());
    }

    private ProductSettingDto convertToDto(ProductSetting entity) {
        if (entity == null) return null;
        ProductSettingDto dto = new ProductSettingDto();
        dto.setProductSettingId(entity.getProductSettingId());
        dto.setProductBarcode(entity.getProductBarcode());
        dto.setProductName(entity.getProductName());
        dto.setProductShortName(entity.getProductShortName());
        dto.setCurrency(entity.getCurrency());
        
        dto.setListPrice(entity.getListPrice());
        dto.setSalePrice(entity.getSalePrice());
        dto.setCostPrice(entity.getCostPrice());
        dto.setSaleUnit(entity.getSaleUnit());
        dto.setSaleEffectiveStartDate(entity.getSaleEffectiveStartDate());
        dto.setSaleEffectiveEndDate(entity.getSaleEffectiveEndDate());
        
        dto.setQueyouListPrice(entity.getQueyouListPrice());
        dto.setQueyouSalePrice(entity.getQueyouSalePrice());
        dto.setQueyouCostPrice(entity.getQueyouCostPrice());
        dto.setQueyouSaleEffectiveStartDate(entity.getQueyouSaleEffectiveStartDate());
        dto.setQueyouSaleEffectiveEndDate(entity.getQueyouSaleEffectiveEndDate());

        dto.setIsDispatchProduct(entity.getIsDispatchProduct() != null && entity.getIsDispatchProduct() == 1);
        dto.setIsActive(entity.getIsActive() != null && entity.getIsActive() == 1);
        dto.setWarrantyMonths(entity.getWarrantyMonths());

        if (entity.getWarrantyMonths() != null) {
            WarrantyPeriodEnum anEnum = WarrantyPeriodEnum.fromCode(entity.getWarrantyMonths().shortValue());
            if (anEnum != null) {
                dto.setWarrantyPeriodDescription(anEnum.getDescription());
            } else {
                dto.setWarrantyPeriodDescription(entity.getWarrantyMonths() + " 個月");
            }
        }

        dto.setErpCompanyDivisionFields(entity.getErpCompanyDivision());
        dto.setErpProductCode(entity.getErpProductCode());
        dto.setErpProductCategory(entity.getErpProductCategory());
        dto.setErpEastkingPrice(entity.getErpEastkingPrice());
        dto.setErpQueyouPrice(entity.getErpQueyouPrice());

        dto.setCreateBy(entity.getCreateBy());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateBy(entity.getUpdateBy());
        dto.setUpdateTime(entity.getUpdateTime());

        if (!CollectionUtils.isEmpty(entity.getDiscounts())) {
            dto.setDiscounts(entity.getDiscounts().stream()
                .filter(d -> d.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode()) 
                .map(this::convertToDiscountDto) 
                .collect(Collectors.toList()));
        }
        return dto;
    }

    private com.eastking.model.dto.ProductSettingDiscountDto convertToDiscountDto(com.eastking.model.entity.ProductSettingDiscount discountEntity) {
        if (discountEntity == null) return null;
        com.eastking.model.dto.ProductSettingDiscountDto discountDto = new com.eastking.model.dto.ProductSettingDiscountDto();
        discountDto.setProductSettingDiscountId(discountEntity.getProductSettingDiscountId());
        if (discountEntity.getRole() != null) {
            discountDto.setRoleId(discountEntity.getRole().getRoleId());
            discountDto.setRoleName(discountEntity.getRole().getRoleName()); // Assuming Role entity has getRoleName()
        }
        discountDto.setDiscountAmount(discountEntity.getDiscountAmount());
        // isDeleted is part of BaseEntity, isActive is not directly on ProductSettingDiscount entity according to its definition.
        // If ProductSettingDiscountDto needs an isActive field, it should be added to the DTO and mapped appropriately.
        // For now, commenting out isActive as it's not on the entity.
        // discountDto.setIsActive(discountEntity.getIsActive() != null && discountEntity.getIsActive() == 1);
        return discountDto;
    }

    private ProductSetting convertToEntity(ProductSettingDto dto) {
        if (dto == null) return null;
        ProductSetting entity = new ProductSetting();
        
        if (dto.getProductSettingId() != null) { 
            entity.setProductSettingId(dto.getProductSettingId());
        }
        entity.setProductBarcode(dto.getProductBarcode());
        entity.setProductShortName(dto.getProductShortName());
        entity.setCurrency(dto.getCurrency());
        
        entity.setListPrice(dto.getListPrice());
        entity.setSalePrice(dto.getSalePrice());
        entity.setCostPrice(dto.getCostPrice());
        entity.setSaleUnit(dto.getSaleUnit());
        entity.setSaleEffectiveStartDate(dto.getSaleEffectiveStartDate());
        entity.setSaleEffectiveEndDate(dto.getSaleEffectiveEndDate());

        entity.setQueyouListPrice(dto.getQueyouListPrice());
        entity.setQueyouSalePrice(dto.getQueyouSalePrice());
        entity.setQueyouCostPrice(dto.getQueyouCostPrice());
        entity.setQueyouSaleEffectiveStartDate(dto.getQueyouSaleEffectiveStartDate());
        entity.setQueyouSaleEffectiveEndDate(dto.getQueyouSaleEffectiveEndDate());

        entity.setIsDispatchProduct(dto.getIsDispatchProduct() != null && dto.getIsDispatchProduct() ? (short)1 : (short)0);
        entity.setIsActive(dto.getIsActive() != null && dto.getIsActive() ? (short)1 : (short)0);
        entity.setWarrantyMonths(dto.getWarrantyMonths());
        
        // productName and ERP fields are not mapped from DTO to Entity here as they are considered read-only from user input perspective
        // or managed by a separate ERP sync process.
        
        // Handling discounts requires more complex logic (fetching existing, updating, creating new ones, removing old ones)
        // This is typically done based on a list of discount DTOs in the ProductSettingDto.
        // For simplicity, assuming this is handled by cascade if ProductSettingDiscount entities are fully managed via the list in ProductSettingDto.
        // If not, this part would need specific logic to manage the lifecycle of discount entities.

        return entity;
    }

    private ProductSettingDto convertToDtoWithAuditNames(ProductSetting entity) {
        ProductSettingDto dto = convertToDto(entity);
        if (dto != null) {
            if (entity.getCreateBy() != null) {
                userAccountRepository.findById(entity.getCreateBy()).ifPresent(user -> dto.setCreateByName(user.getUserName()));
            }
            if (entity.getUpdateBy() != null) {
                userAccountRepository.findById(entity.getUpdateBy()).ifPresent(user -> dto.setUpdateByName(user.getUserName()));
            }
        }
        return dto;
    }

    // == Methods moved from ExternalProductServiceImpl ==

    private Page<ProductSearchResultDto> searchProductSettingsInternalForMenu(String keyword, Pageable pageable, String companyContext, UUID storeId) {
        logger.info("Internal search for product menu items - keyword: '{}', companyContext: '{}', storeId: {}, pageable: {}", 
            keyword, companyContext, storeId, pageable);
        
        Specification<ProductSetting> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            predicates.add(cb.equal(root.get("isActive"), (short)1)); 

            if (StringUtils.hasText(keyword)) {
                Predicate nameLike = cb.like(cb.lower(root.get("productName")), "%" + keyword.toLowerCase() + "%");
                Predicate barcodeLike = cb.like(cb.lower(root.get("productBarcode")), "%" + keyword.toLowerCase() + "%");
                Predicate erpCodeLike = cb.like(cb.lower(root.get("erpProductCode")), "%" + keyword.toLowerCase() + "%"); 
                predicates.add(cb.or(nameLike, barcodeLike, erpCodeLike));
            }
            
            if (StringUtils.hasText(companyContext)) {
                Predicate allCompaniesPredicate = cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.ALL.getCode());
                if ("EASTKING".equalsIgnoreCase(companyContext)) {
                    Predicate eastkingPredicate = cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.EASTKING.getCode());
                    predicates.add(cb.or(eastkingPredicate, allCompaniesPredicate));
                } else if ("QUEYOU".equalsIgnoreCase(companyContext)) {
                    Predicate queyouPredicate = cb.equal(root.get("erpCompanyDivision"), ErpCompanyDivisionEnum.QUEYOU.getCode());
                    predicates.add(cb.or(queyouPredicate, allCompaniesPredicate));
                }
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<ProductSetting> productSettingPage = productSettingRepository.findAll(spec, pageable);
        
        List<ProductSearchResultDto> dtoList = productSettingPage.getContent().stream()
            .map(ps -> {
                Integer stockQuantity = null;
                String stockLocation = "總倉庫存"; // Default location

                if (storeId != null) {
                    // If a storeId is provided, query its specific inventory
                    Optional<StoreInventory> inventoryOpt = storeInventoryRepository.findByStore_StoreIdAndProductBarcode(storeId, ps.getProductBarcode());
                    if (inventoryOpt.isPresent()) {
                        stockQuantity = inventoryOpt.get().getQuantityOnHand();
                        stockLocation = inventoryOpt.get().getStore().getStoreName() + "庫存";
                    } else {
                        stockQuantity = 0; // If no inventory record for that store, assume 0
                        stockLocation = "門市庫存"; 
                    }
                } else {
                    stockQuantity = null; 
                }

                return convertToProductSearchResultDto(ps, companyContext, stockQuantity, stockLocation);
            })
            .collect(Collectors.toList());
        
        return new PageImpl<>(dtoList, pageable, productSettingPage.getTotalElements());
    }

    private ProductSearchResultDto convertToProductSearchResultDto(ProductSetting ps, String companyContext, Integer stockQuantity, String stockLocation) {

        String erpCompanyDivisionDescription = null;
        if (ps.getErpCompanyDivision() != null) {
            ErpCompanyDivisionEnum anEnum = ErpCompanyDivisionEnum.fromCode(ps.getErpCompanyDivision());
            if (anEnum != null) {
                erpCompanyDivisionDescription = anEnum.getDescription();
            }
        }

        BigDecimal listPriceToUse = ps.getListPrice();
        BigDecimal salePriceToUse = ps.getSalePrice();

        if ("QUEYOU".equalsIgnoreCase(companyContext)) {
            listPriceToUse = ps.getQueyouListPrice();
            salePriceToUse = ps.getQueyouSalePrice();
        }

        return new ProductSearchResultDto(
            ps.getProductBarcode(),
            ps.getProductName(),
            ps.getErpProductCategory(),      
            ps.getIsDispatchProduct() != null && ps.getIsDispatchProduct() == 1,
            ps.getIsMain() != null && ps.getIsMain() == 1,
            erpCompanyDivisionDescription,   
            listPriceToUse,
            salePriceToUse,
            stockQuantity,
            stockLocation
        );
    }
    
    @Override
    public List<ProductSearchResultDto> searchProductsForMenu(String keyword, String companyContext, UUID storeId) {
        Page<ProductSearchResultDto> pagedResult = searchProductSettingsInternalForMenu(keyword, Pageable.unpaged(), companyContext, storeId);
        return pagedResult.getContent();
    }

    @Override
    public Page<ProductSearchResultDto> searchProductsForMenu(String keyword, Pageable pageable, String companyContext, UUID storeId) {
        return searchProductSettingsInternalForMenu(keyword, pageable, companyContext, storeId);
    }
}
