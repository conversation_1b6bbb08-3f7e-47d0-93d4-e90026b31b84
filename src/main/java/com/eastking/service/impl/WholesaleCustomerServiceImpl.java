package com.eastking.service.impl;

import com.eastking.enums.CustomerStatusEnum;
import com.eastking.enums.DeleteStatusEnum;
import com.eastking.model.dto.response.WholesaleCustomerSelectDto;
import com.eastking.model.entity.WholesaleCustomer;
import com.eastking.repository.WholesaleCustomerRepository;
import com.eastking.service.WholesaleCustomerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WholesaleCustomerServiceImpl implements WholesaleCustomerService {

    private final WholesaleCustomerRepository wholesaleCustomerRepository;

    @Override
    public List<WholesaleCustomerSelectDto> getAllActiveSelectableCustomers() {
        List<WholesaleCustomer> customers = wholesaleCustomerRepository.findAllByIsDeletedAndCustomerStatusCode(
                DeleteStatusEnum.NOT_DELETED.getCode(),
                CustomerStatusEnum.ACTIVE.getCode()
        );
        return customers.stream()
                .map(this::convertToSelectDto)
                .collect(Collectors.toList());
    }

    private WholesaleCustomerSelectDto convertToSelectDto(WholesaleCustomer customer) {
        WholesaleCustomerSelectDto dto = new WholesaleCustomerSelectDto();
        dto.setWholesaleCustomerId(customer.getWholesaleCustomerId());
        dto.setCustomerName(customer.getCustomerName());
        dto.setErpCustomerCode(customer.getErpCustomerCode());
        return dto;
    }
} 