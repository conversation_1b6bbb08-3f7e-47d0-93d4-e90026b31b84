package com.eastking.service.impl;

import com.eastking.enums.*;
import com.eastking.model.dto.StoreTransferOrderDto;
import com.eastking.model.dto.StoreTransferOrderItemDto;
import com.eastking.model.dto.request.TransferActionRequest;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.service.AuditLogService;
import com.eastking.service.WarehouseStoreInventoryService;
import com.eastking.service.StoreTransferOrderService;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.exception.InvalidOperationException;
import com.eastking.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicLong;

@Service
@RequiredArgsConstructor
public class StoreTransferOrderServiceImpl implements StoreTransferOrderService {
    private static final Logger logger = LoggerFactory.getLogger(StoreTransferOrderServiceImpl.class);

    private final StoreTransferOrderRepository transferOrderRepository;
    private final StoreTransferOrderItemRepository transferOrderItemRepository;
    private final StoreRepository storeRepository;
    private final UserAccountRepository userAccountRepository;
    private final ProductSettingRepository productSettingRepository; 
    private final StoreInventoryRepository storeInventoryRepository; 
    private final WarehouseStoreInventoryService warehouseStoreInventoryService;
    private final SalesOrderRepository salesOrderRepository;
    private final StorePurchaseOrderRepository storePurchaseOrderRepository;
    private final WarehouseRepository warehouseRepository;
    private final StoreStaffMapRepository storeStaffMapRepository;
    private final AuditLogService auditLogService;
    private static final String HQ_WAREHOUSE_CODE = "HQ_MAIN";
    private AtomicLong salesOrderCounter = new AtomicLong(System.currentTimeMillis() / 1000);
    private AtomicLong purchaseOrderCounter = new AtomicLong(System.currentTimeMillis() / 1000);

    @Override
    @Transactional
    public StoreTransferOrderDto createTransferOrder(StoreTransferOrderDto dto) {
        StoreEntity requestingStore = storeRepository.findById(dto.getRequestingStoreId())
            .orElseThrow(() -> new ResourceNotFoundException("Requesting Store", "ID", dto.getRequestingStoreId()));
        UserAccount requestingUser = SecurityUtil.getCurrentUserId()
            .flatMap(userAccountRepository::findById)
            .orElseThrow(() -> new InvalidOperationException("無法獲取申請人資訊。"));
        StoreEntity supplyingStore = storeRepository.findById(dto.getSupplyingStoreId())
            .orElseThrow(() -> new ResourceNotFoundException("Supplying Store", "ID", dto.getSupplyingStoreId()));

        if (requestingStore.getStoreId().equals(supplyingStore.getStoreId())) {
            throw new InvalidOperationException("申請門市與調撥門市不能相同。");
        }

        StoreTransferOrder order = new StoreTransferOrder();
        order.setTransferOrderNumber(generateTransferOrderNumber()); // Implement this utility
        order.setRequestingStore(requestingStore);
        order.setRequestingUser(requestingUser);
        order.setSupplyingStore(supplyingStore);
        order.setTransferStatus(StoreTransferOrderStatusEnum.PENDING_APPROVAL.getCode());
        order.setRequestDate(OffsetDateTime.now());
        order.setNotes(dto.getNotes());
        order.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        
        StoreTransferOrder savedOrder = transferOrderRepository.save(order);

        if (CollectionUtils.isEmpty(dto.getItems())) {
            throw new InvalidOperationException("調撥單必須至少包含一個品項。");
        }
        List<StoreTransferOrderItem> items = dto.getItems().stream().map(itemDto -> {
            // Basic validation for product existence can be added here by checking productBarcode
            productSettingRepository.findByProductBarcodeAndIsDeleted(itemDto.getProductBarcode(), DeleteStatusEnum.NOT_DELETED.getCode())
                .orElseThrow(() -> new ResourceNotFoundException("Product", "barcode", itemDto.getProductBarcode()));
                
            StoreTransferOrderItem item = new StoreTransferOrderItem();
            item.setStoreTransferOrder(savedOrder);
            item.setProductBarcode(itemDto.getProductBarcode());
            item.setProductName(itemDto.getProductName()); // Or fetch from product master data
            item.setRequestedQuantity(itemDto.getRequestedQuantity());
            item.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
            return item;
        }).collect(Collectors.toList());
        transferOrderItemRepository.saveAll(items);
        savedOrder.setItems(items);
        
        // auditLogService.logAction(...);
        return convertToDtoWithItems(savedOrder);
    }
    
    private String generateTransferOrderNumber() {
        // Basic example: STO-YYYYMMDD-XXXX (sequential or random)
        // For a robust solution, use a sequence or a more collision-resistant method.
        return "STO-" + OffsetDateTime.now().getYear() + String.format("%02d", OffsetDateTime.now().getMonthValue()) + String.format("%02d", OffsetDateTime.now().getDayOfMonth()) + "-" + UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StoreTransferOrderDto> searchTransferOrders(String transferType, OffsetDateTime dateFrom, OffsetDateTime dateTo, Short status, String keyword, Pageable pageable) {
        UserAccount currentUser = SecurityUtil.getCurrentUserId()
            .flatMap(userAccountRepository::findById)
            .orElseThrow(() -> new InvalidOperationException("無法獲取當前使用者資訊"));
        StoreEntity currentUsersStore = null;
        List<StoreStaffMapEntity> staffMaps = storeStaffMapRepository.findByUserAccountUserAccountIdAndIsDeleted(currentUser.getUserAccountId(), DeleteStatusEnum.NOT_DELETED.getCode());
        if (!staffMaps.isEmpty()) {
            currentUsersStore = staffMaps.get(0).getStore(); 
        }
        final StoreEntity finalCurrentUsersStore = currentUsersStore;

        Specification<StoreTransferOrder> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if ("INCOMING".equalsIgnoreCase(transferType) && finalCurrentUsersStore != null) {
                predicates.add(criteriaBuilder.equal(root.get("requestingStore"), finalCurrentUsersStore));
            } else if ("OUTGOING".equalsIgnoreCase(transferType) && finalCurrentUsersStore != null) {
                predicates.add(criteriaBuilder.equal(root.get("supplyingStore"), finalCurrentUsersStore));
            }
            if (dateFrom != null) predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("requestDate"), dateFrom));
            if (dateTo != null) predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("requestDate"), dateTo));
            if (status != null) predicates.add(criteriaBuilder.equal(root.get("transferStatus"), status));
            if (StringUtils.hasText(keyword)) predicates.add(criteriaBuilder.like(root.get("transferOrderNumber"), "%" + keyword + "%"));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        Page<StoreTransferOrder> orderPage = transferOrderRepository.findAll(spec, pageable);
        return orderPage.map(this::convertToDtoWithItems);
    }

    @Override
    @Transactional(readOnly = true)
    public StoreTransferOrderDto getTransferOrderById(UUID id) {
        StoreTransferOrder order = transferOrderRepository.findById(id)
            .filter(o -> o.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("StoreTransferOrder", "ID", id));
        return convertToDtoWithItems(order);
    }

    @Override
    @Transactional
    public StoreTransferOrderDto dispatchOrder(UUID orderId, TransferActionRequest actionRequest) {
        //門市調撥單轉出時會呼叫這裡
        StoreTransferOrder transferOrder = transferOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("StoreTransferOrder", "id", orderId));

        if (transferOrder.getTransferStatus() != StoreTransferOrderStatusEnum.APPROVED.getCode() && 
            transferOrder.getTransferStatus() != StoreTransferOrderStatusEnum.PENDING_APPROVAL.getCode()) { 
            throw new InvalidOperationException("調撥單狀態不正確，無法執行轉出操作。當前狀態: " + StoreTransferOrderStatusEnum.fromCode(transferOrder.getTransferStatus()).getDescription());
        }

        UUID dispatchingUserId = SecurityUtil.getCurrentUserId().orElseThrow(() -> new InvalidOperationException("無法取得當前操作者ID"));
        UserAccount dispatchingUser = userAccountRepository.findById(dispatchingUserId)
            .orElseThrow(() -> new ResourceNotFoundException("UserAccount", "id", dispatchingUserId));

        transferOrder.setDispatchingUser(dispatchingUser);
        transferOrder.setDispatchDate(OffsetDateTime.now());
        transferOrder.setTransferStatus(StoreTransferOrderStatusEnum.DISPATCHED.getCode());

        SalesOrder storeReturnSalesOrder = new SalesOrder();
        storeReturnSalesOrder.setStore(transferOrder.getSupplyingStore());
        storeReturnSalesOrder.setSalesOrderNumber(generateInternalOrderNumber("SR")); 
        storeReturnSalesOrder.setOrderDate(OffsetDateTime.now());
        storeReturnSalesOrder.setSalesOrderStatus(SalesOrderStatusEnum.COMPLETED.getCode());
        storeReturnSalesOrder.setSalesOrderType(SalesOrderTypeEnum.STORE_RETURN_TO_HQ.getCode());
        storeReturnSalesOrder.setRelatedTransferOrderId(transferOrder.getStoreTransferOrderId());
        storeReturnSalesOrder.setCreateBy(dispatchingUserId);
        storeReturnSalesOrder.setUpdateBy(dispatchingUserId);
        storeReturnSalesOrder.setTotalAmount(BigDecimal.ZERO);
        storeReturnSalesOrder.setTaxAmount(BigDecimal.ZERO);
        storeReturnSalesOrder.setGrandTotalAmount(BigDecimal.ZERO);
        storeReturnSalesOrder.setOrderNotes("調撥單 " + transferOrder.getTransferOrderNumber() + " " + transferOrder.getSupplyingStore().getStoreName() + " 退回總倉");
        
        List<SalesOrderItem> salesOrderItems = new ArrayList<>();

        for (TransferActionRequest.ItemActionDto itemAction : actionRequest.getItems()) {
            StoreTransferOrderItem transferItem = transferOrder.getItems().stream()
                    .filter(i -> i.getStoreTransferOrderItemId().equals(itemAction.getStoreTransferOrderItemId()))
                    .findFirst()
                    .orElseThrow(() -> new ResourceNotFoundException("StoreTransferOrderItem", "id", itemAction.getStoreTransferOrderItemId()));

            transferItem.setDispatchedQuantity(itemAction.getQuantity());
            
            warehouseStoreInventoryService.updateInventory(
                    transferOrder.getSupplyingStore().getStoreId(),
                    transferItem.getProductBarcode(),
                    transferItem.getProductName(),
                    -itemAction.getQuantity(), 
                    InventoryTransactionTypeEnum.STORE_RETURN_TO_HQ.name(),
                    transferOrder.getStoreTransferOrderId().toString()
            );

            SalesOrderItem soItem = new SalesOrderItem();
            soItem.setProductBarcode(transferItem.getProductBarcode());
            soItem.setProductName(transferItem.getProductName());
            soItem.setQuantitySold(itemAction.getQuantity());
            soItem.setUnitPrice(BigDecimal.ZERO); 
            soItem.setItemSubtotal(BigDecimal.ZERO);
            soItem.setDiscountAmount(BigDecimal.ZERO);
            soItem.setFinalAmount(BigDecimal.ZERO);
            salesOrderItems.add(soItem);
            
            Warehouse hqWarehouse = getHqWarehouse(); 
            ((WarehouseStoreInventoryServiceImpl) warehouseStoreInventoryService).updateInventoryForWarehouse(
                hqWarehouse.getWarehouseId(), 
                transferItem.getProductBarcode(), 
                transferItem.getProductName(), 
                itemAction.getQuantity(), 
                InventoryTransactionTypeEnum.HQ_RECEIVE_FROM_STORE.name(), 
                transferOrder.getStoreTransferOrderId().toString()
            );
        }
        storeReturnSalesOrder.setItems(salesOrderItems);
        salesOrderItems.forEach(item -> item.setSalesOrder(storeReturnSalesOrder)); 
        salesOrderRepository.save(storeReturnSalesOrder);
        
        StoreTransferOrder updatedTransferOrder = transferOrderRepository.save(transferOrder);
        return convertToDtoWithItems(updatedTransferOrder);
    }

    @Override
    @Transactional
    public StoreTransferOrderDto receiveOrder(UUID orderId, TransferActionRequest actionRequest) {
        //門市調撥單轉入時會呼叫這裡
        StoreTransferOrder transferOrder = transferOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("StoreTransferOrder", "id", orderId));

        if (transferOrder.getTransferStatus() != StoreTransferOrderStatusEnum.DISPATCHED.getCode()) {
            throw new InvalidOperationException("調撥單狀態不正確，無法執行轉入操作。當前狀態: " + StoreTransferOrderStatusEnum.fromCode(transferOrder.getTransferStatus()).getDescription());
        }
        
        UUID receivingUserId = SecurityUtil.getCurrentUserId().orElseThrow(() -> new InvalidOperationException("無法取得當前操作者ID"));
        UserAccount receivingUser = userAccountRepository.findById(receivingUserId)
             .orElseThrow(() -> new ResourceNotFoundException("UserAccount", "id", receivingUserId));

        transferOrder.setReceivingUser(receivingUser);
        transferOrder.setReceivedDate(OffsetDateTime.now());

        StorePurchaseOrder hqShipmentPurchaseOrder = new StorePurchaseOrder();
        hqShipmentPurchaseOrder.setStore(transferOrder.getRequestingStore());
        hqShipmentPurchaseOrder.setPurchaseOrderNumber(generateInternalOrderNumber("HP")); 
        hqShipmentPurchaseOrder.setShipmentTime(transferOrder.getDispatchDate()); 
        // Default to PENDING_CONFIRMATION, will be updated based on discrepancy check
        hqShipmentPurchaseOrder.setPurchaseOrderStatus(StorePurchaseOrderStatusEnum.PENDING_CONFIRMATION.getCode()); 
        hqShipmentPurchaseOrder.setPurchaseOrderOrigin(PurchaseOrderOriginEnum.HQ_SHIPMENT_FOR_TRANSFER.getCode());
        hqShipmentPurchaseOrder.setRelatedTransferOrderId(transferOrder.getStoreTransferOrderId());
        hqShipmentPurchaseOrder.setCreateBy(receivingUserId); 
        hqShipmentPurchaseOrder.setUpdateBy(receivingUserId);
        hqShipmentPurchaseOrder.setTotalAmount(BigDecimal.ZERO);
        hqShipmentPurchaseOrder.setTaxAmount(BigDecimal.ZERO);
        hqShipmentPurchaseOrder.setGrandTotalAmount(BigDecimal.ZERO);
        hqShipmentPurchaseOrder.setOrderNotes("調撥單 " + transferOrder.getTransferOrderNumber() + " 總倉出貨至 " + transferOrder.getRequestingStore().getStoreName());

        List<StorePurchaseOrderItem> purchaseOrderItems = new ArrayList<>();
        boolean discrepancyFound = false;

        for (TransferActionRequest.ItemActionDto itemAction : actionRequest.getItems()) {
            StoreTransferOrderItem transferItem = transferOrder.getItems().stream()
                    .filter(i -> i.getStoreTransferOrderItemId().equals(itemAction.getStoreTransferOrderItemId()))
                    .findFirst()
                    .orElseThrow(() -> new ResourceNotFoundException("StoreTransferOrderItem", "id", itemAction.getStoreTransferOrderItemId()));

            transferItem.setReceivedQuantity(itemAction.getQuantity());
            if (transferItem.getDispatchedQuantity() == null || !transferItem.getDispatchedQuantity().equals(itemAction.getQuantity())) {
                discrepancyFound = true;
            }

            warehouseStoreInventoryService.updateInventory(
                    transferOrder.getRequestingStore().getStoreId(),
                    transferItem.getProductBarcode(),
                    transferItem.getProductName(),
                    itemAction.getQuantity(), 
                    InventoryTransactionTypeEnum.STORE_RECEIVE_FROM_HQ.name(),
                    transferOrder.getStoreTransferOrderId().toString()
            );
            
            StorePurchaseOrderItem poItem = new StorePurchaseOrderItem();
            poItem.setProductBarcode(transferItem.getProductBarcode());
            poItem.setProductName(transferItem.getProductName());
            poItem.setWarehouseCode(HQ_WAREHOUSE_CODE); 
            poItem.setOrderedQuantity(transferItem.getDispatchedQuantity() != null ? transferItem.getDispatchedQuantity() : 0); 
            poItem.setReceivedQuantity(itemAction.getQuantity()); 
            poItem.setUnitPrice(BigDecimal.ZERO); 
            poItem.setItemSubtotal(BigDecimal.ZERO);
            purchaseOrderItems.add(poItem);

            Warehouse hqWarehouse = getHqWarehouse();
            ((WarehouseStoreInventoryServiceImpl) warehouseStoreInventoryService).updateInventoryForWarehouse(
                hqWarehouse.getWarehouseId(), 
                transferItem.getProductBarcode(), 
                transferItem.getProductName(), 
                -itemAction.getQuantity(), 
                InventoryTransactionTypeEnum.HQ_SHIP_TO_STORE.name(), 
                transferOrder.getStoreTransferOrderId().toString()
            );
        }
        hqShipmentPurchaseOrder.setItems(purchaseOrderItems);
        purchaseOrderItems.forEach(item -> item.setStorePurchaseOrder(hqShipmentPurchaseOrder)); 

        if (discrepancyFound) {
            transferOrder.setTransferStatus(StoreTransferOrderStatusEnum.RECEIVED_DISCREPANCY.getCode());
            hqShipmentPurchaseOrder.setPurchaseOrderStatus(StorePurchaseOrderStatusEnum.DISCREPANCY.getCode());
        } else {
            transferOrder.setTransferStatus(StoreTransferOrderStatusEnum.RECEIVED_COMPLETE.getCode());
            hqShipmentPurchaseOrder.setPurchaseOrderStatus(StorePurchaseOrderStatusEnum.COMPLETED.getCode());
            hqShipmentPurchaseOrder.setConfirmedAt(OffsetDateTime.now());
            hqShipmentPurchaseOrder.setConfirmedByUser(receivingUser);
        }
        storePurchaseOrderRepository.save(hqShipmentPurchaseOrder);

        StoreTransferOrder updatedTransferOrder = transferOrderRepository.save(transferOrder);
        return convertToDtoWithItems(updatedTransferOrder);
    }

    @Override
    @Transactional
    public StoreTransferOrderDto cancelOrder(UUID orderId) {
        StoreTransferOrder order = transferOrderRepository.findById(orderId)
            .orElseThrow(() -> new ResourceNotFoundException("StoreTransferOrder", "ID", orderId));
        if (order.getTransferStatus() != StoreTransferOrderStatusEnum.PENDING_APPROVAL.getCode() && 
            order.getTransferStatus() != StoreTransferOrderStatusEnum.APPROVED.getCode()) {
            throw new InvalidOperationException("只有[待審核]或[待出庫]狀態的調撥單才能取消。");
        }
        order.setTransferStatus(StoreTransferOrderStatusEnum.CANCELLED.getCode());
        StoreTransferOrder savedOrder = transferOrderRepository.save(order);
        // auditLogService.logAction(...);
        return convertToDtoWithItems(savedOrder);
    }

    @Override
    @Transactional
    public StoreTransferOrderDto approveTransferOrder(UUID orderId) {
        StoreTransferOrder order = transferOrderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("StoreTransferOrder", "ID", orderId));

        if (order.getTransferStatus() != StoreTransferOrderStatusEnum.PENDING_APPROVAL.getCode()) {
            throw new InvalidOperationException("只有[待審核]狀態的調撥單才能被核准。");
        }

        UserAccount approver = SecurityUtil.getCurrentUserId()
            .flatMap(userAccountRepository::findById)
            .orElseThrow(() -> new InvalidOperationException("無法獲取當前審核者帳號資訊。"));

        order.setTransferStatus(StoreTransferOrderStatusEnum.APPROVED.getCode());
        
        StoreTransferOrder updatedOrder = transferOrderRepository.save(order);
        auditLogService.logAction(AuditActionTypeEnum.APPROVE, AuditDataTypeEnum.STORE_TRANSFER_ORDER, order.getTransferOrderNumber(), 
                                "調撥單核准 (ID: " + orderId + ") by " + approver.getUserName(), null);
        return convertToDtoWithItems(updatedOrder);
    }

    @Override
    @Transactional
    public StoreTransferOrderDto rejectTransferOrder(UUID orderId, String rejectionReason) {
        StoreTransferOrder order = transferOrderRepository.findById(orderId)
            .filter(o -> o.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("StoreTransferOrder", "ID", orderId));

        if (order.getTransferStatus() != StoreTransferOrderStatusEnum.PENDING_APPROVAL.getCode()) {
            throw new InvalidOperationException("只有[待審核]狀態的調撥單才能被駁回。");
        }
        UserAccount rejector = SecurityUtil.getCurrentUserId()
            .flatMap(userAccountRepository::findById)
            .orElseThrow(() -> new InvalidOperationException("無法獲取當前操作者帳號資訊。"));

        order.setTransferStatus(StoreTransferOrderStatusEnum.REJECTED.getCode());
        order.setRejectionReason(rejectionReason);
        
        StoreTransferOrder updatedOrder = transferOrderRepository.save(order);
        auditLogService.logAction(AuditActionTypeEnum.REJECT, AuditDataTypeEnum.STORE_TRANSFER_ORDER, order.getTransferOrderNumber(), 
                                "調撥單駁回 (ID: " + orderId + ") by " + rejector.getUserName() + ", 原因: " + rejectionReason, null);
        return convertToDtoWithItems(updatedOrder);
    }
    
    private void updateStoreInventory(StoreEntity store, String productBarcode, String productName, int quantityChange) {
        if (quantityChange == 0) return;
        StoreInventory inventory = storeInventoryRepository.findByStoreAndProductBarcodeAndIsDeleted(store, productBarcode, DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseGet(() -> {
                StoreInventory newInventory = new StoreInventory();
                newInventory.setStore(store);
                newInventory.setProductBarcode(productBarcode);
                newInventory.setProductName(productName != null ? productName :
                    productSettingRepository.findByProductBarcodeAndIsDeleted(productBarcode, DeleteStatusEnum.NOT_DELETED.getCode())
                        .map(ProductSetting::getProductName).orElse("未知商品"));
                newInventory.setQuantityOnHand(0);
                newInventory.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                return newInventory;
            });
        inventory.setQuantityOnHand(inventory.getQuantityOnHand() + quantityChange);
        inventory.setLastStockUpdateTime(OffsetDateTime.now());
        storeInventoryRepository.save(inventory);
        logger.info("Store inventory updated for store: {}, product: {}, change: {}, new quantity: {}", 
            store.getStoreId(), productBarcode, quantityChange, inventory.getQuantityOnHand());
    }

    private StoreTransferOrderDto convertToDto(StoreTransferOrder entity) {
        StoreTransferOrderDto dto = new StoreTransferOrderDto();
        BeanUtils.copyProperties(entity, dto, "items");
        if (entity.getRequestingStore() != null) dto.setRequestingStoreName(entity.getRequestingStore().getStoreName());
        if (entity.getRequestingUser() != null) dto.setRequestingUserName(entity.getRequestingUser().getUserName());
        if (entity.getSupplyingStore() != null) dto.setSupplyingStoreName(entity.getSupplyingStore().getStoreName());
        if (entity.getDispatchingUser() != null) dto.setDispatchingUserName(entity.getDispatchingUser().getUserName());
        if (entity.getReceivingUser() != null) dto.setReceivingUserName(entity.getReceivingUser().getUserName());
        StoreTransferOrderStatusEnum statusEnum = StoreTransferOrderStatusEnum.fromCode(entity.getTransferStatus());
        if(statusEnum != null) dto.setStatusDescription(statusEnum.getDescription());
        return dto;
    }

    private StoreTransferOrderDto convertToDtoWithItems(StoreTransferOrder entity) {
        StoreTransferOrderDto dto = convertToDto(entity);
        List<StoreTransferOrderItem> items = transferOrderItemRepository.findByStoreTransferOrder_StoreTransferOrderIdAndIsDeleted(entity.getStoreTransferOrderId(), DeleteStatusEnum.NOT_DELETED.getCode());
        dto.setItems(items.stream().map(this::convertItemToDto).collect(Collectors.toList()));
        return dto;
    }

    private StoreTransferOrderItemDto convertItemToDto(StoreTransferOrderItem entity) {
        StoreTransferOrderItemDto dto = new StoreTransferOrderItemDto();
        BeanUtils.copyProperties(entity, dto);
        // productName is already set during item creation/update from product master or DTO
        return dto;
    }

    private String generateInternalOrderNumber(String prefix) {
        return prefix + "-" + System.currentTimeMillis() + "-" + (prefix.equals("SR") ? salesOrderCounter.incrementAndGet() : purchaseOrderCounter.incrementAndGet());
    }
    
    private Warehouse getHqWarehouse() {
        return warehouseRepository.findByWarehouseCodeAndIsDeleted(HQ_WAREHOUSE_CODE, DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new InvalidOperationException("HQ Warehouse with code '" + HQ_WAREHOUSE_CODE + "' not found. Inventory operations for HQ cannot proceed."));
    }
} 