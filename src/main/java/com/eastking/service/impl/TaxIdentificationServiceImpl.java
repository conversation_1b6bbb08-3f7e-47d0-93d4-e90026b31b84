package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.model.dto.TaxIdentificationDto;
import com.eastking.model.entity.TaxIdentificationEntity;
import com.eastking.repository.TaxIdentificationRepository;
import com.eastking.repository.StoreTaxIdentificationMapRepository;
import com.eastking.service.TaxIdentificationService;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.exception.DataConflictException;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class TaxIdentificationServiceImpl implements TaxIdentificationService {

    private final TaxIdentificationRepository taxIdentificationRepository;
    private final StoreTaxIdentificationMapRepository storeTaxMapRepository;

    public TaxIdentificationServiceImpl(TaxIdentificationRepository taxIdentificationRepository, 
                                        StoreTaxIdentificationMapRepository storeTaxMapRepository) {
        this.taxIdentificationRepository = taxIdentificationRepository;
        this.storeTaxMapRepository = storeTaxMapRepository;
    }

    @Override
    @Transactional
    public TaxIdentificationDto createTaxIdentification(TaxIdentificationDto dto) {
        taxIdentificationRepository.findByTaxIdNumberAndIsDeleted(dto.getTaxIdNumber(), DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(e -> {
                throw new DataConflictException("統一編號已存在: " + dto.getTaxIdNumber());
            });
        TaxIdentificationEntity entity = new TaxIdentificationEntity();
        BeanUtils.copyProperties(dto, entity, "taxIdentificationId", "createTime", "updateTime", "createBy", "updateBy", "isDeleted");
        entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        TaxIdentificationEntity savedEntity = taxIdentificationRepository.save(entity);
        return convertToDto(savedEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TaxIdentificationDto> getTaxIdentificationById(UUID id) {
        return taxIdentificationRepository.findById(id)
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public TaxIdentificationEntity findTaxEntityById(UUID id) {
        return taxIdentificationRepository.findById(id)
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TaxIdentificationDto> getByTaxIdNumber(String taxIdNumber) {
        return taxIdentificationRepository.findByTaxIdNumberAndIsDeleted(taxIdNumber, DeleteStatusEnum.NOT_DELETED.getCode())
            .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TaxIdentificationDto> getAllTaxIdentifications(Pageable pageable, String keyword) {
        Specification<TaxIdentificationEntity> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if (StringUtils.hasText(keyword)) {
                Predicate taxIdMatch = criteriaBuilder.like(criteriaBuilder.lower(root.get("taxIdNumber")), "%" + keyword.toLowerCase() + "%");
                Predicate companyNameMatch = criteriaBuilder.like(criteriaBuilder.lower(root.get("companyName")), "%" + keyword.toLowerCase() + "%");
                predicates.add(criteriaBuilder.or(taxIdMatch, companyNameMatch));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        return taxIdentificationRepository.findAll(spec, pageable).map(this::convertToDto);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<TaxIdentificationDto> getAllTaxIdentificationsList() {
        Specification<TaxIdentificationEntity> spec = (root, query, cb) -> cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode());
        return taxIdentificationRepository.findAll(spec).stream().map(this::convertToDto).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TaxIdentificationDto updateTaxIdentification(UUID id, TaxIdentificationDto dto) {
        TaxIdentificationEntity entity = taxIdentificationRepository.findById(id)
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("TaxIdentification not found with id: " + id));

        if (!entity.getTaxIdNumber().equals(dto.getTaxIdNumber())) {
            taxIdentificationRepository.findByTaxIdNumberAndIsDeleted(dto.getTaxIdNumber(), DeleteStatusEnum.NOT_DELETED.getCode())
                .ifPresent(e -> {
                    if (!e.getTaxIdentificationId().equals(id)) {
                        throw new DataConflictException("統一編號已存在: " + dto.getTaxIdNumber());
                    }
                });
        }
        BeanUtils.copyProperties(dto, entity, "taxIdentificationId", "createTime", "updateTime", "createBy", "updateBy", "isDeleted");
        TaxIdentificationEntity updatedEntity = taxIdentificationRepository.save(entity);
        return convertToDto(updatedEntity);
    }

    @Override
    @Transactional
    public void deleteTaxIdentification(UUID id) {
        TaxIdentificationEntity entity = taxIdentificationRepository.findById(id)
            .filter(e -> !DeleteStatusEnum.DELETED.getCode().equals(e.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("TaxIdentification not found with id: " + id));
        
        long count = storeTaxMapRepository.countByTaxIdentification_TaxIdentificationIdAndIsDeleted(
            id, DeleteStatusEnum.NOT_DELETED.getCode()
        );
        if (count > 0) {
            throw new DataConflictException("無法刪除此統編，尚有 " + count + " 個門市正在使用。");
        }

        entity.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        taxIdentificationRepository.save(entity);
    }

    private TaxIdentificationDto convertToDto(TaxIdentificationEntity entity) {
        TaxIdentificationDto dto = new TaxIdentificationDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
} 