package com.eastking.service.impl;

import com.eastking.model.dto.ProductSalesPurchaseSummaryDto;
import com.eastking.model.dto.ProductTransactionDetailDto;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.service.ProductSalesPurchaseService;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 商品進銷列表服務實作
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ProductSalesPurchaseServiceImpl implements ProductSalesPurchaseService {

    private static final Logger logger = LoggerFactory.getLogger(ProductSalesPurchaseServiceImpl.class);

    private final StorePurchaseOrderItemRepository storePurchaseOrderItemRepository;
    private final SalesOrderItemRepository salesOrderItemRepository;
    private final ProductSettingRepository productSettingRepository;
    private final ProductMenuItemRepository productMenuItemRepository;
    private final ProductMenuCategoryRepository productMenuCategoryRepository;

    @Override
    public Page<ProductSalesPurchaseSummaryDto> searchProductSalesPurchaseSummary(
            UUID productCategoryId, OffsetDateTime dateFrom, OffsetDateTime dateTo, String keyword, Pageable pageable) {
        logger.info("Searching product sales/purchase summary for categoryId: {}, from: {}, to: {}, keyword: '{}', page: {}, size: {}",
            productCategoryId, dateFrom, dateTo, keyword, pageable.getPageNumber(), pageable.getPageSize());

        Specification<ProductSetting> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.hasText(keyword)) {
                String lowerKeyword = "%" + keyword.toLowerCase() + "%";
                predicates.add(cb.or(
                    cb.like(cb.lower(root.get("productBarcode")), lowerKeyword),
                    cb.like(cb.lower(root.get("productName")), lowerKeyword)
                ));
            }
            if (productCategoryId != null) {
                logger.debug("ProductCategoryId provided: {}. Post-filtering will be applied if needed.", productCategoryId);
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<ProductSetting> productSettingPage = productSettingRepository.findAll(spec, pageable);

        List<ProductSalesPurchaseSummaryDto> summaryList = new ArrayList<>();
        for (ProductSetting ps : productSettingPage.getContent()) {
            String categoryName = "未分類";
            List<ProductMenuItem> menuItems = productMenuItemRepository.findByProductBarcodeAndIsDeleted(ps.getProductBarcode(), (short)0);
            ProductMenuCategory finalCategory = null;
            if (!menuItems.isEmpty()) {
                if (productCategoryId != null) {
                    finalCategory = menuItems.stream()
                        .map(ProductMenuItem::getProductMenuCategory)
                        .filter(cat -> cat != null && productCategoryId.equals(cat.getProductMenuCategoryId()))
                        .findFirst().orElse(null);
                }
                if (finalCategory == null && !menuItems.isEmpty() && menuItems.get(0).getProductMenuCategory() != null) {
                    finalCategory = menuItems.get(0).getProductMenuCategory();
                }
            }
            if (finalCategory != null) {
                categoryName = finalCategory.getCategoryName();
            } else {
                if (productCategoryId != null) continue;
            }
            
            if (productCategoryId != null && (finalCategory == null || !productCategoryId.equals(finalCategory.getProductMenuCategoryId()))) {
                continue;
            }

            Integer totalPurchases = storePurchaseOrderItemRepository
                .sumQuantityByProductBarcodeAndDateRange(ps.getProductBarcode(), dateFrom, dateTo);
            totalPurchases = (totalPurchases == null) ? 0 : totalPurchases;

            Integer totalSales = salesOrderItemRepository
                .sumQuantitySoldByProductBarcodeAndDateRange(ps.getProductBarcode(), dateFrom, dateTo);
            totalSales = (totalSales == null) ? 0 : totalSales;
            
            if ((dateFrom == null && dateTo == null) || totalPurchases > 0 || totalSales > 0) {
                summaryList.add(new ProductSalesPurchaseSummaryDto(
                    ps.getProductBarcode(),
                    ps.getProductName(),
                    categoryName, 
                    totalPurchases,
                    totalSales
                ));
            }
        }
        int totalFilteredSummaries = summaryList.size();
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), totalFilteredSummaries);
        List<ProductSalesPurchaseSummaryDto> paginatedSummaryList = (start <= end && start < totalFilteredSummaries) ? summaryList.subList(start, end) : Collections.emptyList();

        return new PageImpl<>(paginatedSummaryList, pageable, totalFilteredSummaries); 
    }

    @Override
    public Page<ProductTransactionDetailDto> getProductTransactionDetails(
            String productBarcode, OffsetDateTime dateFrom, OffsetDateTime dateTo, Pageable pageable) {
        logger.info("Fetching transaction details for product barcode: {}, from: {}, to: {}, page: {}, size: {}", 
            productBarcode, dateFrom, dateTo, pageable.getPageNumber(), pageable.getPageSize());

        List<ProductTransactionDetailDto> transactions = new ArrayList<>();

        List<StorePurchaseOrderItem> purchaseItems = storePurchaseOrderItemRepository
            .findByProductBarcodeAndDateRange(productBarcode, dateFrom, dateTo);
        purchaseItems.forEach(item -> {
            OffsetDateTime transactionTime = item.getStorePurchaseOrder() != null && item.getStorePurchaseOrder().getCreateTime() != null 
                                           ? item.getStorePurchaseOrder().getCreateTime() 
                                           : item.getCreateTime(); 
            transactions.add(new ProductTransactionDetailDto(
                transactionTime,
                "進貨",
                item.getOrderedQuantity(),
                0,
                item.getStorePurchaseOrder() != null ? item.getStorePurchaseOrder().getPurchaseOrderNumber() : "N/A",
                "/store_purchase_order_detail.html?id=" + (item.getStorePurchaseOrder() != null ? item.getStorePurchaseOrder().getStorePurchaseOrderId() : "")
            ));
        });

        List<SalesOrderItem> salesItems = salesOrderItemRepository
            .findByProductBarcodeAndDateRange(productBarcode, dateFrom, dateTo);
        salesItems.forEach(item -> {
            OffsetDateTime transactionTime = item.getSalesOrder() != null && item.getSalesOrder().getOrderDate() != null
                                           ? item.getSalesOrder().getOrderDate()
                                           : item.getCreateTime(); 
            transactions.add(new ProductTransactionDetailDto(
                transactionTime,
                "銷貨",
                0,
                item.getQuantitySold(),
                item.getSalesOrder() != null ? item.getSalesOrder().getSalesOrderNumber() : "N/A",
                "/sales_order_detail.html?id=" + (item.getSalesOrder() != null ? item.getSalesOrder().getSalesOrderId() : "") 
            ));
        });

        transactions.sort((t1, t2) -> {
            if (t1.getTransactionDate() == null && t2.getTransactionDate() == null) return 0;
            if (t1.getTransactionDate() == null) return 1;
            if (t2.getTransactionDate() == null) return -1;
            return t2.getTransactionDate().compareTo(t1.getTransactionDate()); 
        });

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), transactions.size());
        List<ProductTransactionDetailDto> paginatedList = (start <= end && start < transactions.size()) ? transactions.subList(start, end) : Collections.emptyList();
        
        return new PageImpl<>(paginatedList, pageable, transactions.size());
    }
} 