package com.eastking.service.impl;

import com.eastking.enums.*;
import com.eastking.exception.BadRequestException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.OrderPaymentDto;
import com.eastking.model.dto.ProductSettingDto;
import com.eastking.model.dto.request.DispatchRepairFilterRequest;
import com.eastking.model.dto.response.DispatchRepairDetailDto;
import com.eastking.model.dto.response.DispatchRepairItemDto;
import com.eastking.model.dto.response.DispatchRepairItemSummaryDto;
import com.eastking.model.dto.response.DispatchRepairSummaryDto;
import com.eastking.model.dto.response.DispatchTechRecordDto;
import com.eastking.model.dto.response.OrderItemDto;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.security.UserDetailsImpl;
import com.eastking.service.DispatchRepairService;
import com.eastking.util.OrderNumberUtil;
import com.eastking.util.SecurityUtil;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

import com.eastking.enums.DispatchRepairItemTypeEnum;
import com.eastking.model.entity.OrderItem;
import com.eastking.model.entity.OrderItemGroup;
import com.eastking.enums.OrderStatusEnum;
import com.eastking.enums.OrderItemTypeEnum;
import com.eastking.service.DispatchRepairLogService;
import com.eastking.model.dto.request.DispatchRepairRequestDto;
import com.eastking.model.dto.request.DispatchOrderFilterRequest;
import com.eastking.repository.ProductSettingRepository;
import com.eastking.model.dto.request.CompleteContactRequestDto;
import com.eastking.repository.DispatchTechRecordRepository;
import com.eastking.model.dto.request.UpdateScheduleRequestDto;
import com.eastking.model.dto.request.CompleteWorkingRequestDto;
import com.eastking.model.dto.request.WorkingStepItemDto;
import com.eastking.model.dto.request.RepairedPartDto;
import com.eastking.model.dto.request.ImageDataDto;
import java.math.BigDecimal;
import java.math.RoundingMode;
import com.eastking.service.WarehouseStoreInventoryService;
import com.eastking.model.dto.request.CompletePaymentRequestDto;
import com.eastking.model.dto.request.PaymentDetailDto;
import java.time.OffsetDateTime;
import com.eastking.model.dto.request.CompleteSignatureRequestDto;
import com.eastking.model.dto.request.SubmitHandlingRequestDto;
import com.eastking.repository.CustomerDeviceRepository;
import com.eastking.service.MaterialOrderService;
import com.eastking.service.OrderService;
import com.eastking.model.dto.request.DispatchCollaboratorRequest;
import com.eastking.repository.DispatchCollaboratorRepository;
import com.eastking.model.dto.response.DispatchCollaboratorDto;

@Service
@RequiredArgsConstructor
@Transactional
public class DispatchRepairServiceImpl implements DispatchRepairService {

    private static final Logger logger = LoggerFactory.getLogger(DispatchRepairServiceImpl.class);

    private final DispatchRepairRepository dispatchRepairRepository;
    private final UserAccountRepository userAccountRepository;
    private final DispatchMaterialOrderItemRepository dispatchMaterialOrderItemRepository;
    private final WarehouseRepository warehouseRepository;
    private final OrderRepository orderRepository;
    private final DispatchRepairLogService dispatchRepairLogService;
    private final ProductSettingRepository productSettingRepository;
    private final DispatchTechRecordRepository dispatchTechRecordRepository;
    private final WarehouseStoreInventoryService warehouseStoreInventoryService;
    private final CustomerDeviceRepository customerDeviceRepository;
    private final DispatchCollaboratorRepository dispatchCollaboratorRepository;
    private static final List<String> ADMIN_ROLES = Arrays.asList("SYS_ADMIN", "HO_ADMIN", "TECH_ADMIN");

    @Override
    @Transactional(readOnly = true)
    public Page<DispatchRepairSummaryDto> searchForTechnicianView(DispatchOrderFilterRequest filter, Pageable pageable) {
        UserDetailsImpl currentUser = SecurityUtil.getCurrentUserDetails()
                .orElseThrow(() -> new ResourceNotFoundException("無法獲取當前用戶信息"));

        Specification<DispatchRepair> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), (short) 0));

            // --- START: MODIFICATION ---
            // This view should ALWAYS filter by the current user, regardless of admin status.
            // Find collaborations accepted by the current user.
            UserAccount technician = new UserAccount();
            technician.setUserAccountId(currentUser.getId());
            List<UUID> collaborationIds = dispatchCollaboratorRepository.findByTechnicianAndInvitationStatus(technician, InvitationStatusEnum.ACCEPTED.getCode())
                    .stream()
                    .map(c -> c.getDispatchRepair().getDispatchRepairId())
                    .collect(Collectors.toList());

            // Create a predicate for orders assigned to the current user.
            Predicate mainDispatchPredicate = criteriaBuilder.equal(root.get("assignedTechnicianId"), currentUser.getId());

            // Combine the predicates: main dispatch OR collaboration.
            if (!collaborationIds.isEmpty()) {
                Predicate collaborationPredicate = root.get("dispatchRepairId").in(collaborationIds);
                predicates.add(criteriaBuilder.or(mainDispatchPredicate, collaborationPredicate));
            } else {
                predicates.add(mainDispatchPredicate);
            }
            // --- END: MODIFICATION ---

            // Default status filter for technician view
            // (要大於等於 CONTACTED 且小於等於 COMPLETED) 或 (要小於等於 REFUND_CONTACTED 且大於等於 CANCEL_COMPLETED)
            Predicate normalStatusRange = criteriaBuilder.and(
                    criteriaBuilder.greaterThanOrEqualTo(root.get("statusCode"), DispatchStatusEnum.CONTACTED.getCode()),
                    criteriaBuilder.lessThanOrEqualTo(root.get("statusCode"), DispatchStatusEnum.COMPLETED.getCode())
            );
            Predicate refundStatusRange = criteriaBuilder.and(
                    criteriaBuilder.lessThanOrEqualTo(root.get("statusCode"), DispatchStatusEnum.REFUND_CONTACTED.getCode()),
                    criteriaBuilder.greaterThanOrEqualTo(root.get("statusCode"), DispatchStatusEnum.CANCEL_COMPLETED.getCode())
            );
            predicates.add(criteriaBuilder.or(normalStatusRange, refundStatusRange));

            // Apply additional filters from request
            if (filter.getDispatchTypeCode() != null) {
                predicates.add(criteriaBuilder.equal(root.get("typeCode"), filter.getDispatchTypeCode()));
            }
            logger.info("searchForTechnicianView filter.getDispatchStatusCode(): {}", filter.getDispatchStatusCode());
            if (filter.getDispatchStatusCode() != null) {
                predicates.add(criteriaBuilder.equal(root.get("statusCode"), filter.getDispatchStatusCode()));
            }
            if (filter.getStartDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), filter.getStartDate().atStartOfDay()));
            }
            if (filter.getEndDate() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), filter.getEndDate().plusDays(1).atStartOfDay()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<DispatchRepair> page = dispatchRepairRepository.findAll(spec, pageable);
        //logger.info("searchForTechnicianView page stream count: {}", page.stream().findFirst().stream().count());

        // Fetch all collaborator info for the retrieved page in one go to avoid N+1
        List<UUID> pageRepairIds = page.getContent().stream().map(DispatchRepair::getDispatchRepairId).collect(Collectors.toList());
        Map<UUID, List<DispatchCollaborator>> collaboratorsMap = dispatchCollaboratorRepository.findByDispatchRepair_DispatchRepairIdIn(pageRepairIds)
                .stream()
                .collect(Collectors.groupingBy(c -> c.getDispatchRepair().getDispatchRepairId()));

        List<DispatchRepairSummaryDto> dtoList = page.getContent().stream()
                .map(entity -> {
                    boolean isCollab = collaboratorsMap.getOrDefault(entity.getDispatchRepairId(), Collections.emptyList())
                                        .stream()
                                        .anyMatch(c -> c.getTechnician().getUserAccountId().equals(currentUser.getId()));
                    return convertToSummaryDto(entity, isCollab);
                })
                .collect(Collectors.toList());
        //logger.info("searchForTechnicianView dtoList size: {}", dtoList.size());

        return new PageImpl<>(dtoList, pageable, page.getTotalElements());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DispatchRepairSummaryDto> search(DispatchRepairFilterRequest filter, Pageable pageable) {
        Specification<DispatchRepair> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), (short) 0));

            // --- START: MODIFICATION FOR TECH_STAFF ROLE ---
            UserDetailsImpl currentUser = SecurityUtil.getCurrentUserDetails().orElse(null);
            if (currentUser != null) {
                boolean isTechnician = currentUser.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .anyMatch("TECH_STAFF"::equals);

                boolean isAdmin = currentUser.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .anyMatch(ADMIN_ROLES::contains);

                if (isTechnician && !isAdmin) {
                    // Find collaborations accepted by the current user
                    UserAccount technician = new UserAccount();
                    technician.setUserAccountId(currentUser.getId());
                    List<UUID> collaborationIds = dispatchCollaboratorRepository.findByTechnicianAndInvitationStatus(technician, InvitationStatusEnum.ACCEPTED.getCode())
                            .stream()
                            .map(c -> c.getDispatchRepair().getDispatchRepairId())
                            .collect(Collectors.toList());

                    Predicate mainDispatchPredicate = criteriaBuilder.equal(root.get("assignedTechnicianId"), currentUser.getId());

                    if (!collaborationIds.isEmpty()) {
                        Predicate collaborationPredicate = root.get("dispatchRepairId").in(collaborationIds);
                        predicates.add(criteriaBuilder.or(mainDispatchPredicate, collaborationPredicate));
                    } else {
                        predicates.add(mainDispatchPredicate);
                    }
                }
            }
            // --- END: MODIFICATION FOR TECH_STAFF ROLE ---

            logger.debug("DispatchRepairServiceImpl search filter.getTypeCode(): {}", filter.getTypeCode());
            if (filter.getTypeCode() != null) {
                predicates.add(criteriaBuilder.equal(root.get("typeCode"), filter.getTypeCode()));
            }
            if (filter.getStatusCode() != null) {
                predicates.add(criteriaBuilder.equal(root.get("statusCode"), filter.getStatusCode()));
            }
            if (StringUtils.hasText(filter.getNumber())) {
                predicates.add(criteriaBuilder.like(root.get("dispatchRepairNumber"), "%" + filter.getNumber() + "%"));
            }
            if (filter.getTechnicianId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("assignedTechnicianId"), filter.getTechnicianId()));
            }
            if (filter.getCustomerId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("customerId"), filter.getCustomerId()));
            }
            if (filter.getStartDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), filter.getStartDate().atStartOfDay()));
            }
            if (filter.getEndDate() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), filter.getEndDate().plusDays(1).atStartOfDay()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<DispatchRepair> page = dispatchRepairRepository.findAll(spec, pageable);
        List<DispatchRepairSummaryDto> dtoList = page.getContent().stream()
                .map(this::convertToSummaryDto)
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageable, page.getTotalElements());
    }

    @Override
    @Transactional(readOnly = true)
    public DispatchRepairDetailDto findById(UUID id) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("找不到單據，ID: " + id));
        return convertToDetailDto(dispatchRepair);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DispatchRepairSummaryDto> getPendingDispatchOrders() {
        UserDetailsImpl currentUser = SecurityUtil.getCurrentUserDetails()
                .orElseThrow(() -> new ResourceNotFoundException("無法獲取當前用戶信息"));

        // 查詢直接指派的派工單
        List<DispatchRepair> mainDispatchOrders = dispatchRepairRepository.findByAssignedTechnicianIdAndStatusCodeInAndIsDeleted(
                currentUser.getId(),
                Arrays.asList(DispatchStatusEnum.PENDING_ASSIGNMENT.getCode(), DispatchStatusEnum.REFUND_PENDING_ASSIGNMENT.getCode()),
                (short) 0
            );

        List<DispatchRepairSummaryDto> resultList = mainDispatchOrders.stream()
            .map(order -> convertToSummaryDto(order, false)) // isCollaboration = false
            .collect(Collectors.toList());

        // 查詢協同邀請的派工單
        UserAccount technician = new UserAccount();
        technician.setUserAccountId(currentUser.getId());

        List<DispatchCollaborator> collaborations = dispatchCollaboratorRepository.findByTechnicianAndInvitationStatus(
            technician,
            InvitationStatusEnum.PENDING.getCode()
        );

        List<DispatchRepairSummaryDto> collaborationDtos = collaborations.stream()
            .map(collab -> convertToSummaryDto(collab.getDispatchRepair(), true)) // isCollaboration = true
            .collect(Collectors.toList());

        // 合併結果
        resultList.addAll(collaborationDtos);

        return resultList;
    }

    @Override
    @Transactional
    public void acceptDispatchOrders(List<UUID> dispatchRepairIds, UUID technicianId) {
        List<DispatchRepair> orders = dispatchRepairRepository.findAllById(dispatchRepairIds);
        UserAccount currentUser = userAccountRepository.findById(technicianId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到執行接單操作的技師，ID: " + technicianId));

        for (DispatchRepair order : orders) {
//            if (!order.getAssignedTechnicianId().equals(technicianId)) {
//                throw new SecurityException("無權操作不屬於您的派工單: " + order.getDispatchRepairNumber());
//            }
            Short previousStatus = order.getStatusCode();
            //logger.info("previousStatus: {}", previousStatus);
            if (previousStatus>0) {
                order.setStatusCode(DispatchStatusEnum.CONTACTED.getCode());
            } else {
                order.setStatusCode(DispatchStatusEnum.REFUND_CONTACTED.getCode());
            }
            dispatchRepairLogService.logChange(order, previousStatus, order.getStatusCode(), currentUser, "技師接單", dispatchRepairIds);
            DispatchTechRecord record = new DispatchTechRecord();
            record.setDispatchRepairId(order.getDispatchRepairId());
            record.setStatusCode(previousStatus);
            record.setTechnicianId(currentUser.getUserAccountId());
            record.setRecordType(RecordTypeEnum.REMARKS.getCode());
            record.setRecord1("技師已接單");
            dispatchTechRecordRepository.save(record);
        }
        dispatchRepairRepository.saveAll(orders);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DispatchRepairItemSummaryDto> getPendingDispatchItems(UUID dispatchOrderId, DispatchStatusEnum status) {
        UserDetailsImpl currentUser = SecurityUtil.getCurrentUserDetails()
                .orElseThrow(() -> new ResourceNotFoundException("無法獲取當前用戶信息"));

        Specification<DispatchRepair> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("statusCode"), status.getCode()));
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));

            // --- START: SECURITY FIX ---
            // Filter by the current technician
            predicates.add(cb.equal(root.get("assignedTechnicianId"), currentUser.getId()));
            // --- END: SECURITY FIX ---

            if (dispatchOrderId != null) {
                predicates.add(cb.equal(root.get("dispatchRepairId"), dispatchOrderId));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };

        List<DispatchRepair> dispatchRepairs = dispatchRepairRepository.findAll(spec);

        //這裡的派工單要過濾掉已經產生領料單的商品數字，步驟如下：
        // 1. 檢查目前該技師已建立的領料單品項(sm_dispatch_material_order_item)和該領料單品項對應的派工單品項。
        // 2. 建立一個變數 hadMaterialQuantity 來記錄已經對應到該派工單品項的領料單品項的數量加總(可能多筆另料單品項相加)。
        // 3. 如果該派工單品項數量已經等於 hadMaterialQuantity，則該筆派工單品項應該要被搜尋過濾掉。
        // 4. 如果該派工單的所有品項都已經被過濾掉，則該筆派工單也應該被過濾掉。
        // 5. 最後回傳的 DispatchRepairItemSummaryDto 的 quantity 欄位應該要等於 item.getQuantity()-hadMaterialQuantity。

        List<Object[]> materialQuantities = dispatchMaterialOrderItemRepository
            .sumRequestedQuantityGroupByDispatchRepairItemIdForTechnician(currentUser.getId());

        // 建立派工單品項ID到已領料數量的對應表
        Map<UUID, Integer> hadMaterialQuantityMap = new HashMap<>();
        for (Object[] row : materialQuantities) {
            UUID dispatchRepairItemId = (UUID) row[0];
            Long sumQuantity = (Long) row[1];
            hadMaterialQuantityMap.put(dispatchRepairItemId, sumQuantity != null ? sumQuantity.intValue() : 0);
        }

        // --- N+1 Optimization for Warehouse Names ---
        // The `warehouseCode` in DispatchRepairItem actually stores the warehouseId (UUID)
        Set<UUID> warehouseIds = dispatchRepairs.stream()
            .flatMap(repair -> repair.getItems().stream())
            .filter(item -> item.getWarehouseCode() != null)
            .map(item -> {
                try {
                    return UUID.fromString(item.getWarehouseCode());
                } catch (IllegalArgumentException e) {
                    logger.warn("Invalid UUID format in DispatchRepairItem's warehouseCode field: {}", item.getWarehouseCode());
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Map<UUID, String> warehouseNameMap = warehouseIds.isEmpty() ? Collections.emptyMap() :
            warehouseRepository.findAllById(warehouseIds).stream()
                .collect(Collectors.toMap(Warehouse::getWarehouseId, Warehouse::getWarehouseName));
        // --- End Optimization ---

        // 過濾並轉換資料
        List<DispatchRepairItemSummaryDto> result = new ArrayList<>();
        Set<UUID> dispatchRepairIdsWithItems = new HashSet<>();

        for (DispatchRepair repair : dispatchRepairs) {
            List<DispatchRepairItemSummaryDto> validItems = repair.getItems().stream()
                .map(item -> {
                    // 取得該品項已建立的領料單數量
                    Integer hadMaterialQuantity = hadMaterialQuantityMap.getOrDefault(item.getDispatchRepairItemId(), 0);

                    // 如果已領料數量等於派工數量，則過濾掉
                    if (hadMaterialQuantity >= item.getQuantity()) {
                        return null;
                    }

                    Integer pickedQty = dispatchMaterialOrderItemRepository.sumPickedQuantityByDispatchRepairItemId(item.getDispatchRepairItemId());
                    Integer collected = dispatchMaterialOrderItemRepository.sumCollectedByDispatchRepairItemId(item.getDispatchRepairItemId());
                    if (pickedQty == null) pickedQty = 0;
                    if (collected == null) collected = 0;

                    // 計算剩餘待領數量
                    Integer remainingQuantity = item.getQuantity() - hadMaterialQuantity;

                    return DispatchRepairItemSummaryDto.builder()
                        .dispatchRepairItemId(item.getDispatchRepairItemId())
                        .productBarcode(item.getProductBarcode())
                        .productName(item.getProductName())
                        .quantity(remainingQuantity) // 使用剩餘數量
                        .dispatchRepairId(item.getDispatchRepair().getDispatchRepairId())
                        .dispatchRepairNumber(item.getDispatchRepair().getDispatchRepairNumber())
                        .scheduledDate(item.getDispatchRepair().getScheduledDate())
                        .warehouseName(getWarehouseNameFromIdString(item.getWarehouseCode(), warehouseNameMap))
                        .pickedQuantity(pickedQty)
                        .alreadyRequestedQuantity(collected)
                        .build();
                })
                .filter(Objects::nonNull) // 過濾掉 null（已完全領料的品項）
                .collect(Collectors.toList());

            // 只有當派工單還有未完全領料的品項時，才加入結果
            if (!validItems.isEmpty()) {
                result.addAll(validItems);
                dispatchRepairIdsWithItems.add(repair.getDispatchRepairId());
            }
        }

        return result;
    }

    private String getWarehouseNameFromIdString(String idString, Map<UUID, String> nameMap) {
        if (idString == null) {
            return "未指定倉庫";
        }
        try {
            UUID id = UUID.fromString(idString);
            return nameMap.getOrDefault(id, "倉庫ID不存在");
        } catch (IllegalArgumentException e) {
            return "無效的倉庫ID格式";
        }
    }

    @Override
    @Transactional
    public void createDispatchRepairsFromOrder(UUID orderId) {
        createDispatchRepairsFromOrder(orderId, null, null);
    }

    @Override
    @Transactional
    public void createDispatchRepairsFromOrder(UUID orderId, DispatchRepairTypeEnum dispatchRepairType, DispatchStatusEnum dispatchStatus) {
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到對應的商品訂單，ID: " + orderId));

        if (!OrderStatusEnum.HQ_APPROVED.getCode().equals(order.getOrderStatusCode())) {
            throw new BadRequestException("只有總公司審核通過(49)的訂單才能建立派工單。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        // --- Collect all dispatchable items first ---
        logger.info("order.getItems().get(0).getItemGroups() count: {}", order.getItems().get(0).getItemGroups().size());

        // Separate item groups into awaiting and non-awaiting lists
        List<OrderItemGroup> nonAwaitingSubItems = new ArrayList<>();
        List<OrderItemGroup> awaitingSubItems = new ArrayList<>();

        for (OrderItem mainItem : order.getItems()) {
            if (!CollectionUtils.isEmpty(mainItem.getItemGroups())) {
                for (OrderItemGroup group : mainItem.getItemGroups()) {
                    if (group.getRequiresDispatch() != null && RequireDispatchEnum.DISPATCH.getCode().equals(group.getRequiresDispatch()) && !StringUtils.hasText(group.getAwaitingMaterials())) {
                        if (group.getIsAwait() != null && group.getIsAwait() == 1) {
                            awaitingSubItems.add(group);
                        } else {
                            nonAwaitingSubItems.add(group);
                        }
                    }
                }
            }
        }
        logger.info("nonAwaitingSubItems count: {}", nonAwaitingSubItems.size());
        logger.info("awaitingSubItems count: {}", awaitingSubItems.size());

        // 1. Process main dispatch order (main products + non-awaiting sub-items)
        List<DispatchRepairItem> mainDispatchItems = new ArrayList<>();
        List<DispatchRepairItem> carrySelfDispatchItems = new ArrayList<>();

        for (OrderItem mainItem : order.getItems()) {

            //建立派工商品的派工單項目
            if (mainItem.getRequiresDispatch() != null && RequireDispatchEnum.DISPATCH.getCode().equals(mainItem.getRequiresDispatch())) {
                DispatchRepairItem mainRepairItem = new DispatchRepairItem();
                mainRepairItem.setOrderItemId(mainItem.getOrderItemId());
                mainRepairItem.setProductBarcode(mainItem.getProductBarcode());
                mainRepairItem.setProductName(mainItem.getProductName());
                mainRepairItem.setQuantity(mainItem.getQuantity());
                mainRepairItem.setUnitPrice(mainItem.getFinalPricePerItem());
                mainRepairItem.setSubtotalAmount(mainItem.getSubtotalAmount());
                mainRepairItem.setItemTypeCode(DispatchRepairItemTypeEnum.DISPATCH_ITEM.getCode());
                mainRepairItem.setWarehouseCode(mainItem.getWarehouseCode());
                mainRepairItem.setDeviceSerialNumber(mainItem.getMahjongTableSerialNumber());
                mainDispatchItems.add(mainRepairItem);
            }

            //建立自載機的派工單項目
            if (mainItem.getRequiresDispatch() != null && RequireDispatchEnum.CARRYSELF.getCode().equals(mainItem.getRequiresDispatch())) {
                DispatchRepairItem carrySelfDispatchItem = new DispatchRepairItem();
                carrySelfDispatchItem.setOrderItemId(mainItem.getOrderItemId());
                carrySelfDispatchItem.setProductBarcode(mainItem.getProductBarcode());
                carrySelfDispatchItem.setProductName(mainItem.getProductName());
                carrySelfDispatchItem.setQuantity(mainItem.getQuantity());
                carrySelfDispatchItem.setUnitPrice(mainItem.getFinalPricePerItem());
                carrySelfDispatchItem.setSubtotalAmount(mainItem.getSubtotalAmount());
                carrySelfDispatchItem.setItemTypeCode(DispatchRepairItemTypeEnum.DISPATCH_ITEM.getCode());
                carrySelfDispatchItem.setWarehouseCode(mainItem.getWarehouseCode());
                carrySelfDispatchItem.setDeviceSerialNumber(mainItem.getMahjongTableSerialNumber());
                carrySelfDispatchItems.add(carrySelfDispatchItem);
            }
        }

        for (OrderItemGroup group : nonAwaitingSubItems) {
            DispatchRepairItem subRepairItem = new DispatchRepairItem();
            subRepairItem.setOrderItemGroupId(group.getOrderItemGroupId());
            subRepairItem.setProductBarcode(group.getProductBarcode());
            subRepairItem.setProductName(group.getProductName());
            subRepairItem.setOrderItemId(group.getOrderItem().getOrderItemId());
            subRepairItem.setQuantity(group.getQuantity());
            subRepairItem.setWarehouseCode(group.getWarehouseId().toString());
            if (OrderItemTypeEnum.GIFT_ITEM.getCode().equals(group.getItemTypeCode())) {
                subRepairItem.setUnitPrice(BigDecimal.ZERO);
                subRepairItem.setSubtotalAmount(BigDecimal.ZERO);
                subRepairItem.setItemTypeCode(DispatchRepairItemTypeEnum.DISPATCH_ITEM.getCode());
            } else if (OrderItemTypeEnum.ADD_ON_ITEM.getCode().equals(group.getItemTypeCode())) {
                subRepairItem.setUnitPrice(group.getUnitPrice());
                subRepairItem.setSubtotalAmount(group.getSubtotalAmount());
                subRepairItem.setItemTypeCode(DispatchRepairItemTypeEnum.ADDITIONAL_PURCHASE.getCode());
            }
            mainDispatchItems.add(subRepairItem);
            //PS.自載機不會有贈品和加購品的派工, 所以可忽略子項目
        }

        //建立派工商品的派工單
        if (!mainDispatchItems.isEmpty()) {
            DispatchRepair dispatchRepair = new DispatchRepair();
            dispatchRepair.setDispatchRepairNumber(OrderNumberUtil.generateNumber("R"));
            dispatchRepair.setOrder(order);
            if (dispatchRepairType!=null) {
                dispatchRepair.setTypeCode(dispatchRepairType.getCode());
            } else {
                dispatchRepair.setTypeCode(DispatchRepairTypeEnum.DISPATCH.getCode());
            }
            if (dispatchStatus!=null) {
                dispatchRepair.setStatusCode(dispatchStatus.getCode());
            } else {
                dispatchRepair.setStatusCode(DispatchStatusEnum.PENDING_ASSIGNMENT.getCode());
            }
            dispatchRepair.setCustomerName(order.getCustomerName());
            dispatchRepair.setCustomerPhone(order.getContactPhone());
            if (order.getMember() != null) {
                dispatchRepair.setCustomerId(order.getMember().getCustomerId());
            }
            if (order.getTechnician() != null) {
                dispatchRepair.setAssignedTechnicianId(order.getTechnician().getUserAccountId());
            }
            dispatchRepair.setInstallationAddress(order.getInstallationAddress());
            dispatchRepair.setScheduledDate(order.getInstallationDate());
            dispatchRepair.setScheduledTimeSlot(order.getInstallationTimeSlot());
            dispatchRepair.setRemarks("來自訂單編號："+order.getOrderNumber());

            mainDispatchItems.forEach(item -> item.setDispatchRepair(dispatchRepair));
            dispatchRepair.setItems(mainDispatchItems);

            DispatchRepair savedDispatchRepair = dispatchRepairRepository.save(dispatchRepair);

            order.setOrderStatusCode(OrderStatusEnum.DISPATCH_PENDING_PICKUP.getCode());
            String existingRemarks = StringUtils.hasText(order.getRemarks()) ? order.getRemarks() + "\n" : "";
            order.setRemarks(existingRemarks + "已建立派工單：" + savedDispatchRepair.getDispatchRepairNumber());
            orderRepository.save(order);

            dispatchRepairLogService.logChange(savedDispatchRepair, null, savedDispatchRepair.getStatusCode(), currentUser, "從訂單建立", orderId);
        }

        //建立自載機的派工單(倉庫送到門市)
        if (!carrySelfDispatchItems.isEmpty()) {
            DispatchRepair dispatchRepair = new DispatchRepair();
            dispatchRepair.setDispatchRepairNumber(OrderNumberUtil.generateNumber("R"));
            dispatchRepair.setOrder(order);
            dispatchRepair.setTypeCode(DispatchRepairTypeEnum.STOREBYCARRY.getCode());
            dispatchRepair.setStatusCode(DispatchStatusEnum.DRAFT.getCode());
            dispatchRepair.setCustomerName(order.getCustomerName());
            dispatchRepair.setCustomerPhone(order.getContactPhone());
            if (order.getMember() != null) {
                dispatchRepair.setCustomerId(order.getMember().getCustomerId());
            }
            if (order.getTechnician() != null) {
                dispatchRepair.setAssignedTechnicianId(order.getTechnician().getUserAccountId());
            }
            dispatchRepair.setInstallationAddress(order.getInstallationAddress());
            dispatchRepair.setScheduledDate(order.getInstallationDate());
            dispatchRepair.setScheduledTimeSlot(order.getInstallationTimeSlot());
            dispatchRepair.setRemarks("來自訂單編號："+order.getOrderNumber());

            carrySelfDispatchItems.forEach(item -> item.setDispatchRepair(dispatchRepair));
            dispatchRepair.setItems(carrySelfDispatchItems);

            DispatchRepair savedDispatchRepair = dispatchRepairRepository.save(dispatchRepair);

            order.setOrderStatusCode(OrderStatusEnum.DISPATCH_PENDING_PICKUP.getCode());
            String existingRemarks = StringUtils.hasText(order.getRemarks()) ? order.getRemarks() + "\n" : "";
            order.setRemarks(existingRemarks + "已建立自載機門市派工單：" + savedDispatchRepair.getDispatchRepairNumber());
            orderRepository.save(order);

            dispatchRepairLogService.logChange(savedDispatchRepair, null, savedDispatchRepair.getStatusCode(), currentUser, "從訂單建立", orderId);
        }

        // 2. Process awaiting items into a separate repair order
        if (!awaitingSubItems.isEmpty()) {
            DispatchRepair awaitingRepair = new DispatchRepair();
            awaitingRepair.setDispatchRepairNumber(OrderNumberUtil.generateNumber("R"));
            awaitingRepair.setOrder(order);
            awaitingRepair.setTypeCode(DispatchRepairTypeEnum.REPAIR.getCode());
            awaitingRepair.setStatusCode(DispatchStatusEnum.PENDING_ASSIGNMENT.getCode());
            awaitingRepair.setCustomerName(order.getCustomerName());
            awaitingRepair.setCustomerPhone(order.getContactPhone());
             if (order.getMember() != null) {
                awaitingRepair.setCustomerId(order.getMember().getCustomerId());
            }
            if (order.getTechnician() != null) {
                awaitingRepair.setAssignedTechnicianId(order.getTechnician().getUserAccountId());
            }
            awaitingRepair.setInstallationAddress(order.getInstallationAddress());
            awaitingRepair.setScheduledDate(order.getInstallationDate());
            awaitingRepair.setScheduledTimeSlot(order.getInstallationTimeSlot());
            awaitingRepair.setRemarks("來自訂單編號：" + order.getOrderNumber() + " (待料補貨)");

            List<DispatchRepairItem> awaitingRepairItems = awaitingSubItems.stream().map(group -> {
                DispatchRepairItem subRepairItem = new DispatchRepairItem();
                subRepairItem.setOrderItemGroupId(group.getOrderItemGroupId());
                subRepairItem.setProductBarcode(group.getProductBarcode());
                subRepairItem.setProductName(group.getProductName());
                subRepairItem.setOrderItemId(group.getOrderItem().getOrderItemId());
                subRepairItem.setQuantity(group.getQuantity());
                subRepairItem.setItemTypeCode(DispatchRepairItemTypeEnum.AWAIT_MATERIAL.getCode());
                subRepairItem.setWarehouseCode(group.getWarehouseId().toString());
                subRepairItem.setDispatchRepair(awaitingRepair);
                return subRepairItem;
            }).collect(Collectors.toList());

            awaitingRepair.setItems(awaitingRepairItems);
            DispatchRepair savedAwaitingRepair = dispatchRepairRepository.save(awaitingRepair);

            for (OrderItemGroup group : awaitingSubItems) {
                group.setAwaitingMaterials(savedAwaitingRepair.getDispatchRepairId().toString());
            }

            String existingRemarks = StringUtils.hasText(order.getRemarks()) ? order.getRemarks() + "\n" : "";
            order.setRemarks(existingRemarks + "已建立待料維修單：" + savedAwaitingRepair.getDispatchRepairNumber());
            orderRepository.save(order);
        }
    }

    @Override
    @Transactional
    public DispatchRepairDetailDto createDispatchRepair(DispatchRepairRequestDto requestDto) {
        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        DispatchRepair dispatchRepair = new DispatchRepair();

        // Copy properties from DTO to Entity
        BeanUtils.copyProperties(requestDto, dispatchRepair, "items");

        dispatchRepair.setDispatchRepairNumber(OrderNumberUtil.generateNumber("R"));

        List<DispatchRepairItem> repairItems = requestDto.getItems().stream().map(itemDto -> {
            DispatchRepairItem item = new DispatchRepairItem();
            BeanUtils.copyProperties(itemDto, item);
            item.setDispatchRepair(dispatchRepair);
            return item;
        }).collect(Collectors.toList());

        dispatchRepair.setItems(repairItems);

        DispatchRepair savedOrder = dispatchRepairRepository.save(dispatchRepair);

        // Log the creation
        dispatchRepairLogService.logChange(savedOrder, null, savedOrder.getStatusCode(), currentUser, "建立派工單", requestDto);

        return convertToDetailDto(savedOrder);
    }

    @Override
    @Transactional
    public void completeContactStep(UUID dispatchRepairId, CompleteContactRequestDto requestDto) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));

        if (!DispatchStatusEnum.CONTACTED.getCode().equals(dispatchRepair.getStatusCode())) {
            throw new BadRequestException("只有'待聯繫'狀態的派工單才能執行此操作。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short previousStatus = dispatchRepair.getStatusCode();

        // Update DispatchRepair
        dispatchRepair.setScheduledDate(requestDto.getScheduledDate());
        dispatchRepair.setStatusCode(DispatchStatusEnum.SCHEDULED.getCode());
        dispatchRepairRepository.save(dispatchRepair);

        // Create Tech Record for contact content
        if (StringUtils.hasText(requestDto.getContactContent())) {
            DispatchTechRecord record = new DispatchTechRecord();
            record.setDispatchRepairId(dispatchRepairId);
            record.setStatusCode(previousStatus);
            record.setTechnicianId(currentUser.getUserAccountId());
            record.setRecordType(RecordTypeEnum.REMARKS.getCode());
            record.setRecord1(requestDto.getContactContent());
            dispatchTechRecordRepository.save(record);
        }

        // Log history
        dispatchRepairLogService.logChange(dispatchRepair, previousStatus, dispatchRepair.getStatusCode(), currentUser, "完成聯繫", requestDto);
    }

    @Override
    @Transactional
    public void updateSchedule(UUID dispatchRepairId, UpdateScheduleRequestDto requestDto) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));

        if (!DispatchStatusEnum.SCHEDULED.getCode().equals(dispatchRepair.getStatusCode())) {
            throw new BadRequestException("只有'待排單'狀態的派工單才能執行此操作。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short previousStatus = dispatchRepair.getStatusCode();

        dispatchRepair.setScheduledDate(requestDto.getScheduledDate());
        dispatchRepair.setStatusCode(DispatchStatusEnum.MATERIALS_COLLECTED.getCode());
        dispatchRepairRepository.save(dispatchRepair);

        dispatchRepairLogService.logChange(dispatchRepair, previousStatus, dispatchRepair.getStatusCode(), currentUser, "更新排程", requestDto);

        // Create Tech Record
        DispatchTechRecord record = new DispatchTechRecord();
        record.setDispatchRepairId(dispatchRepairId);
        record.setStatusCode(previousStatus);
        record.setTechnicianId(currentUser.getUserAccountId());
        record.setRecordType(RecordTypeEnum.REMARKS.getCode());
        record.setRecord1("排程已更新");
        dispatchTechRecordRepository.save(record);
    }

    @Override
    @Transactional
    public void completeMaterialCollection(UUID dispatchRepairId) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));

        if (!DispatchStatusEnum.MATERIALS_COLLECTED.getCode().equals(dispatchRepair.getStatusCode())) {
            throw new BadRequestException("只有'待領料'狀態的派工單才能執行此操作。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short previousStatus = dispatchRepair.getStatusCode();

        dispatchRepair.setStatusCode(DispatchStatusEnum.DEPARTED.getCode());
        dispatchRepairRepository.save(dispatchRepair);

        dispatchRepairLogService.logChange(dispatchRepair, previousStatus, dispatchRepair.getStatusCode(), currentUser, "完成領料", null);

        // Create Tech Record
        DispatchTechRecord record = new DispatchTechRecord();
        record.setDispatchRepairId(dispatchRepairId);
        record.setStatusCode(previousStatus);
        record.setTechnicianId(currentUser.getUserAccountId());
        record.setRecordType(RecordTypeEnum.REMARKS.getCode());
        record.setRecord1("已完成領料");
        dispatchTechRecordRepository.save(record);
    }

    @Override
    @Transactional
    public void completeDeparture(UUID dispatchRepairId) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));

        if (!DispatchStatusEnum.DEPARTED.getCode().equals(dispatchRepair.getStatusCode())) {
            throw new BadRequestException("只有'在途中'狀態的派工單才能執行此操作。");
        }

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        Short previousStatus = dispatchRepair.getStatusCode();

        dispatchRepair.setStatusCode(DispatchStatusEnum.WORKING.getCode());
        dispatchRepairRepository.save(dispatchRepair);

        dispatchRepairLogService.logChange(dispatchRepair, previousStatus, dispatchRepair.getStatusCode(), currentUser, "已到達現場", null);

        // Create Tech Record
        DispatchTechRecord record = new DispatchTechRecord();
        record.setDispatchRepairId(dispatchRepairId);
        record.setStatusCode(previousStatus);
        record.setTechnicianId(currentUser.getUserAccountId());
        record.setRecordType(RecordTypeEnum.REMARKS.getCode());
        record.setRecord1("已完成到達現場");
        dispatchTechRecordRepository.save(record);
    }

    @Override
    @Transactional
    public void deleteDispatchRepair(UUID dispatchRepairId) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到要刪除的派工單，ID: " + dispatchRepairId));

        if (!DispatchStatusEnum.DRAFT.getCode().equals(dispatchRepair.getStatusCode())) {
            throw new BadRequestException("只有'草稿'狀態的派工單才能被刪除。");
        }
        dispatchRepair.setIsDeleted((short) 1);
        dispatchRepairRepository.save(dispatchRepair);
    }

    @Override
    @Transactional
    public DispatchRepairDetailDto updateDispatchRepair(UUID id, DispatchRepairRequestDto requestDto) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("找不到要修改的派工單，ID: " + id));

        // You can add logic here to check if the order is in an editable state (e.g., DRAFT)

        // Copy editable properties from DTO to Entity
        BeanUtils.copyProperties(requestDto, dispatchRepair, "id", "dispatchRepairNumber", "items");

        // Clear and re-create items
        dispatchRepair.getItems().clear();
        List<DispatchRepairItem> repairItems = requestDto.getItems().stream().map(itemDto -> {
            DispatchRepairItem item = new DispatchRepairItem();
            BeanUtils.copyProperties(itemDto, item);
            item.setDispatchRepair(dispatchRepair);
            return item;
        }).collect(Collectors.toList());
        dispatchRepair.setItems(repairItems);

        DispatchRepair savedOrder = dispatchRepairRepository.save(dispatchRepair);

        // Optionally log this update action
        // logService.logChange(...);

        return convertToDetailDto(savedOrder);
    }

    @Override
    @Transactional
    public void completeWorkingStep(UUID dispatchRepairId, CompleteWorkingRequestDto requestDto) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));
        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        if (!DispatchStatusEnum.WORKING.getCode().equals(dispatchRepair.getStatusCode())) {
            throw new BadRequestException("只有'裝修中'狀態的派工單才能執行此操作。");
        }

        Short previousStatus = dispatchRepair.getStatusCode();

        // --- START of modification: Only process Order-related logic for DISPATCH type ---
        if (DispatchRepairTypeEnum.DISPATCH.getCode().equals(dispatchRepair.getTypeCode())) {
            Order order = orderRepository.findById(dispatchRepair.getOrder().getOrderId())
                    .orElseThrow(() -> new ResourceNotFoundException("找不到關聯的訂單，ID: " + dispatchRepair.getOrder().getOrderId()));

            StringBuilder allDispatchRemarks = new StringBuilder();

            // Process each main item from the request
            for (WorkingStepItemDto itemDto : requestDto.getItems()) {
                DispatchRepairItem repairItem = dispatchRepair.getItems().stream()
                    .filter(i -> itemDto.getOrderItemId().equals(i.getOrderItemId()))
                    .findFirst()
                    .orElseThrow(() -> new ResourceNotFoundException("找不到對應的派工品項"));

                OrderItem orderItem = order.getItems().stream()
                    .filter(i -> itemDto.getOrderItemId().equals(i.getOrderItemId()))
                    .findFirst()
                    .orElseThrow(() -> new ResourceNotFoundException("找不到對應的訂單品項"));

                // 2A. Update DispatchRepairItem and create tech records
                repairItem.setDeviceSerialNumber(itemDto.getDeviceSerialNumber());
                repairItem.setIssueDescription(itemDto.getDispatchRemarks());
                createTechRecord(dispatchRepair, previousStatus, currentUser, RecordTypeEnum.DEVICE_ID, itemDto.getDeviceSerialNumber(), repairItem.getProductBarcode(), null, null, null);
                createTechRecord(dispatchRepair, previousStatus, currentUser, RecordTypeEnum.DISPATCH_REMARKS, itemDto.getDeviceSerialNumber(), itemDto.isAbnormal() ? "有異常" : "無異常", itemDto.getDispatchRemarks(), null, null);

                // 2B. Handle repaired parts
                if (!CollectionUtils.isEmpty(itemDto.getRepairedParts())) {
                    for (RepairedPartDto partDto : itemDto.getRepairedParts()) {
                        ProductSetting partSetting = productSettingRepository.findByProductBarcodeAndIsDeleted(partDto.getProductBarcode(), (short) 0)
                            .orElseThrow(() -> new ResourceNotFoundException("找不到維修零件: " + partDto.getProductBarcode()));

                        UUID newGroupId = UUID.randomUUID();
                        DispatchRepairItem newPartItem = new DispatchRepairItem();
                        newPartItem.setDispatchRepair(dispatchRepair);
                        newPartItem.setProductBarcode(partDto.getProductBarcode());
                        newPartItem.setProductName(partSetting.getProductName());
                        newPartItem.setQuantity(partDto.getQuantity());
                        newPartItem.setUnitPrice(partDto.getUnitPrice());
                        newPartItem.setSubtotalAmount(partDto.getUnitPrice().multiply(BigDecimal.valueOf(partDto.getQuantity())));
                        newPartItem.setItemTypeCode(DispatchRepairItemTypeEnum.REPAIR_PART.getCode());
                        newPartItem.setWarehouseCode(order.getTechnician().getUserAccountId().toString()); //技師車存倉
                        newPartItem.setOrderItemId(itemDto.getOrderItemId());
                        newPartItem.setOrderItemGroupId(newGroupId);
                        dispatchRepair.getItems().add(newPartItem);

                        OrderItemGroup newGroup = new OrderItemGroup();
                        newGroup.setOrderItemGroupId(newGroupId);
                        newGroup.setOrderItem(orderItem);
                        newGroup.setWarehouseId(order.getTechnician().getUserAccountId()); //技師車存倉
                        newGroup.setProductBarcode(partDto.getProductBarcode());
                        newGroup.setProductName(partSetting.getProductName());
                        newGroup.setQuantity(partDto.getQuantity());
                        newGroup.setUnitPrice(partDto.getUnitPrice());
                        newGroup.setListPrice(partSetting.getListPrice());
                        newGroup.setSubtotalAmount(newGroup.getUnitPrice().multiply(BigDecimal.valueOf(newGroup.getQuantity())));
                        newGroup.setItemTypeCode(OrderItemTypeEnum.ADD_ON_ITEM.getCode());
                        newGroup.setRequiresDispatch((short) 1);
                        newGroup.setRemarks(DispatchRepairItemTypeEnum.REPAIR_PART.name());
                        orderItem.getItemGroups().add(newGroup);

                        warehouseStoreInventoryService.updateTechnicianInventory(
                            order.getTechnician().getUserAccountId(),
                            partDto.getProductBarcode(),
                            partSetting.getProductName(),
                            -partDto.getQuantity(),
                            InventoryTransactionTypeEnum.DISPATCH_USE,
                            dispatchRepair.getDispatchRepairNumber()
                        );

                        createTechRecord(dispatchRepair, previousStatus, currentUser, RecordTypeEnum.REPAIR_PRODUCT, itemDto.getDeviceSerialNumber(), partDto.getProductBarcode(), partSetting.getProductName(), partDto.getUnitPrice().toString(), partDto.getQuantity().toString());
                    }
                }

                // 2C. Handle images
                if (!CollectionUtils.isEmpty(itemDto.getImages())) {
                    for (ImageDataDto imageDto : itemDto.getImages()) {
                        createTechRecord(dispatchRepair, previousStatus, currentUser, RecordTypeEnum.IMAGE_DATA, itemDto.getDeviceSerialNumber(), imageDto.getFileName(), imageDto.getBase64Data(), imageDto.getDescription(), null);
                    }
                }

                if (itemDto.isAbnormal()) {
                    allDispatchRemarks.append("[商品: ").append(repairItem.getProductName()).append("] ")
                              .append(itemDto.getDispatchRemarks()).append("\n");
                }
            }

            dispatchRepair.setDispatchRemarks(allDispatchRemarks.toString());
            recalculateOrderTotals(order);
            orderRepository.save(order);
        }
        // --- END of modification ---

        // Update main DispatchRepair status (this happens for all types)
        dispatchRepair.setStatusCode(DispatchStatusEnum.PAYMENT_COLLECTED.getCode());
        dispatchRepairRepository.save(dispatchRepair);

        // Log history
        dispatchRepairLogService.logChange(dispatchRepair, previousStatus, dispatchRepair.getStatusCode(), currentUser, "完成裝修", requestDto);
    }

    @Override
    @Transactional
    public void completePaymentStep(UUID dispatchRepairId, CompletePaymentRequestDto requestDto) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("DispatchRepair", "ID", dispatchRepairId));
        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new BadRequestException("無法獲取當前用戶信息"));

        if (!DispatchStatusEnum.PAYMENT_COLLECTED.getCode().equals(dispatchRepair.getStatusCode())) {
            throw new BadRequestException("只有'收款中'狀態的派工單才能執行此操作。");
        }

        Short previousStatus = dispatchRepair.getStatusCode();
        BigDecimal totalPaid = requestDto.getPaymentDetails().stream()
                .map(PaymentDetailDto::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal otherDiscount = requestDto.getOtherDiscount() != null ? requestDto.getOtherDiscount() : BigDecimal.ZERO;

        // --- START of modification: Only process Order-related logic for DISPATCH type ---
        if (DispatchRepairTypeEnum.DISPATCH.getCode().equals(dispatchRepair.getTypeCode())) {
            Order order = orderRepository.findById(dispatchRepair.getOrder().getOrderId())
                    .orElseThrow(() -> new ResourceNotFoundException("Order", "ID", dispatchRepair.getOrder().getOrderId()));

            // --- Recalculate total due amount on the backend for data integrity ---
            BigDecimal repairCost = order.getItems().stream()
                .flatMap(item -> item.getItemGroups().stream())
                .filter(group -> DispatchRepairItemTypeEnum.REPAIR_PART.name().equals(group.getRemarks()))
                .map(OrderItemGroup::getSubtotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal oldTechCollectionAmount = order.getPayments().stream()
                .filter(p -> PaymentMethodEnum.TECHNICIAN_COLLECTION.name().equals(p.getPaymentMethodCode()) && p.getIsDeleted() == 0)
                .map(OrderPayment::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal subtotal = repairCost.subtract(otherDiscount);
            BigDecimal tax = BigDecimal.ZERO;
            if (requestDto.isOpenInvoice()) {
                tax = subtotal.multiply(new BigDecimal("0.05")).setScale(0, RoundingMode.HALF_UP);
            }
            BigDecimal totalDue = subtotal.add(tax).add(oldTechCollectionAmount);
            dispatchRepair.setTotalAmount(totalDue);

            // Handle sm_order_payment
            order.getPayments().stream()
                    .filter(p -> PaymentMethodEnum.TECHNICIAN_COLLECTION.name().equals(p.getPaymentMethodCode()))
                    .forEach(p -> p.setIsDeleted((short) 1));

            List<OrderPayment> newPayments = requestDto.getPaymentDetails().stream()
                    .map(dto -> {
                        OrderPayment payment = new OrderPayment();
                        BeanUtils.copyProperties(dto, payment, "paymentMethodCode");
                        payment.setPaymentMethodCode(dto.getPaymentMethodCode().name());
                        payment.setOrder(order);
                        payment.setPaymentTime(OffsetDateTime.now());
                        return payment;
                    }).collect(Collectors.toList());
            order.getPayments().addAll(newPayments);

            // Update sm_order
            order.setDiscountAmount((order.getDiscountAmount() != null ? order.getDiscountAmount() : BigDecimal.ZERO).add(requestDto.getOtherDiscount()));
            order.setPaidAmount(order.getPaidAmount().subtract(oldTechCollectionAmount).add(totalPaid));
            recalculateOrderTotals(order);
            if (requestDto.isOpenInvoice()) {
                order.setInvoiceAmount(order.getGrandTotalAmount());
            }
            orderRepository.save(order);
        }
        // --- END of modification ---

        // Update sm_dispatch_repair
        dispatchRepair.setStatusCode(DispatchStatusEnum.SIGNED.getCode());
        dispatchRepair.setOtherDiscount(otherDiscount);
        dispatchRepair.setPaidAmount(totalPaid);

        // Create sm_dispatch_tech_record
        String paymentRecord = requestDto.getPaymentDetails().stream()
            .map(p -> p.getPaymentMethodCode().name() + ": " + p.getAmount())
            .collect(Collectors.joining(", "));
        createTechRecord(dispatchRepair, previousStatus, currentUser, RecordTypeEnum.PAYMENT, paymentRecord, totalPaid.toString(), null, null, null);

        // Save and Log
        dispatchRepairRepository.save(dispatchRepair);
        dispatchRepairLogService.logChange(dispatchRepair, previousStatus, dispatchRepair.getStatusCode(), currentUser, "完成收款", requestDto);
    }

    @Override
    @Transactional
    public void completeSignatureStep(UUID dispatchRepairId, CompleteSignatureRequestDto requestDto) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前操作員信息"));

        if (!DispatchStatusEnum.SIGNED.getCode().equals(dispatchRepair.getStatusCode())) {
            throw new BadRequestException("只有'簽確中'狀態的派工單才能執行此操作。");
        }

        Short previousStatus = dispatchRepair.getStatusCode();

        // Update DispatchRepair status
        dispatchRepair.setStatusCode(DispatchStatusEnum.PROCESS_INFO.getCode());
        dispatchRepairRepository.save(dispatchRepair);

        // Create Tech Record for signature
        createTechRecord(dispatchRepair, previousStatus, currentUser, RecordTypeEnum.SIGNED_INFO, requestDto.getSignatureImage(), null, null, null, null);

        // Log history
        dispatchRepairLogService.logChange(dispatchRepair, previousStatus, dispatchRepair.getStatusCode(), currentUser, "完成簽名", null);
    }

    @Override
    @Transactional
    public void submitHandlingStep(UUID dispatchRepairId, SubmitHandlingRequestDto requestDto) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("DispatchRepair", "ID", dispatchRepairId));
        Order order = orderRepository.findById(dispatchRepair.getOrder().getOrderId())
                .orElseThrow(() -> new ResourceNotFoundException("Order", "ID", dispatchRepair.getOrder().getOrderId()));
        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElseThrow(() -> new BadRequestException("無法獲取當前用戶信息"));

        if (!DispatchStatusEnum.PROCESS_INFO.getCode().equals(dispatchRepair.getStatusCode())) {
            throw new BadRequestException("只有'處理方式'狀態的派工單才能執行此操作。");
        }

        Short previousStatus = dispatchRepair.getStatusCode();

        // Update DispatchRepair
        dispatchRepair.setHandlingMethod(requestDto.getHandlingMethod());
        dispatchRepair.setStatusCode(DispatchStatusEnum.ACCOUNT_CONFIRM.getCode());
        dispatchRepairRepository.save(dispatchRepair);

        // Update Order status
        order.setOrderStatusCode(OrderStatusEnum.DISPATCH_TECHNICIAN_LOADED.getCode());
        orderRepository.save(order);

        // Create Tech Record
        createTechRecord(dispatchRepair, previousStatus, currentUser, RecordTypeEnum.PROCESS_INFO, requestDto.getHandlingMethod(), null, null, null, null);

        // Update or create customer device record
        updateOrCreateCustomerDevice(dispatchRepair);

        // Log history
        dispatchRepairLogService.logChange(dispatchRepair, previousStatus, dispatchRepair.getStatusCode(), currentUser, "提交處理方式", requestDto);
    }

    private void updateOrCreateCustomerDevice(DispatchRepair dispatchRepair) {
        // Find the main dispatch item to get the serial number
        String serialNumber = dispatchRepair.getItems().stream()
            .filter(item -> DispatchRepairItemTypeEnum.DISPATCH_ITEM.getCode().equals(item.getItemTypeCode()) && StringUtils.hasText(item.getDeviceSerialNumber()))
            .map(DispatchRepairItem::getDeviceSerialNumber)
            .findFirst()
            .orElse(null);

        if (dispatchRepair.getCustomerId() == null || serialNumber == null) {
            return; // Cannot proceed without customer and serial number
        }

        Optional<CustomerDevice> existingDeviceOpt = customerDeviceRepository.findByDeviceSerialNumberAndIsDeleted(serialNumber, (short) 0);

        CustomerDevice device = existingDeviceOpt.orElse(new CustomerDevice());

        // Set or update fields
        device.setDeviceSerialNumber(serialNumber);
        device.setLastRepairDate(OffsetDateTime.now().toLocalDate());

        // Ensure the customer is set
        Customer customer = new Customer();
        customer.setCustomerId(dispatchRepair.getCustomerId());
        device.setCustomer(customer);

        // If it's a new device, set other relevant info
        if (device.getCustomerOwnedDeviceId() == null) {
            device.setInstallationAddress(dispatchRepair.getInstallationAddress());
            // Try to find the main product to link to product_setting
            dispatchRepair.getItems().stream()
                .filter(item -> DispatchRepairItemTypeEnum.DISPATCH_ITEM.getCode().equals(item.getItemTypeCode()))
                .findFirst()
                .ifPresent(mainItem -> {
                    productSettingRepository.findByProductBarcodeAndIsDeleted(mainItem.getProductBarcode(), (short)0)
                        .ifPresent(device::setProductSetting);
                });
        }

        customerDeviceRepository.save(device);
    }

    private void recalculateOrderTotals(Order order, Short taxType) {
        BigDecimal itemsSubtotalNet = BigDecimal.ZERO;
        BigDecimal itemsSubtotalGross = BigDecimal.ZERO;

        if (order.getItems() != null) {
            for (OrderItem item : order.getItems()) {
                BigDecimal itemSubtotal = BigDecimal.ZERO;
                // Add main item's subtotal
                itemSubtotal = itemSubtotal.add(item.getSubtotalAmount());
                // Add sub-items (groups) subtotal
                if (!CollectionUtils.isEmpty(item.getItemGroups())) {
                    //將維修品金額加入訂單
                    for (OrderItemGroup group : item.getItemGroups()) {
                        if (DispatchRepairItemTypeEnum.REPAIR_PART.name().equals(group.getRemarks())) {
                            itemSubtotal = itemSubtotal.add(group.getSubtotalAmount());
                        }
                    }
                }
                itemsSubtotalGross = itemsSubtotalGross.add(itemSubtotal); // Assuming gross = net for now in this context
                itemsSubtotalNet = itemsSubtotalNet.add(itemSubtotal);
            }
        }

        order.setProductsTotalAmount(itemsSubtotalGross);
        order.setNetAmount(itemsSubtotalNet);
        order.setDiscountAmount(itemsSubtotalGross.subtract(itemsSubtotalNet));

        BigDecimal taxAmount = BigDecimal.ZERO;
        BigDecimal grandTotal = itemsSubtotalNet;

        if (order.getInvoiceTypeCode() != null && 
            (order.getInvoiceTypeCode().equals(InvoiceTypeEnum.TWO_PART.getCode()) || 
             order.getInvoiceTypeCode().equals(InvoiceTypeEnum.THREE_PART.getCode()))) {

            BigDecimal taxRate = new BigDecimal("0.05");
            // 特殊處理：派工訂單使用門市發票金額作為稅基（如果有的話）
            BigDecimal taxBaseAmount = order.getStoreInvoiceAmount() != null && 
                                      order.getStoreInvoiceAmount().compareTo(BigDecimal.ZERO) > 0 
                                      ? order.getStoreInvoiceAmount() : order.getNetAmount();

            if (taxType != null && taxType.equals((short)2)) { // 稅內 (inclusive)
                // 稅內：稅基金額保持不變，反推淨額和稅額
                if (order.getStoreInvoiceAmount() != null && 
                    order.getStoreInvoiceAmount().compareTo(BigDecimal.ZERO) > 0) {
                    // 如果有門市發票金額，則以此為稅基
                    BigDecimal netAmountFromTotal = taxBaseAmount.divide(new BigDecimal("1.05"), 0, RoundingMode.HALF_UP);
                    taxAmount = taxBaseAmount.subtract(netAmountFromTotal);
                    // 總額 = 商品總額 + 稅額
                    grandTotal = itemsSubtotalNet.add(taxAmount);
                } else {
                    // 無門市發票金額，商品總額為稅基
                    grandTotal = itemsSubtotalNet; // 總額不變
                    BigDecimal netAmountFromTotal = grandTotal.divide(new BigDecimal("1.05"), 0, RoundingMode.HALF_UP);
                    taxAmount = grandTotal.subtract(netAmountFromTotal);
                    order.setNetAmount(netAmountFromTotal);
                }
            } else { // 稅外 (exclusive) 或 null (預設稅外)
                // 稅外：淨額不變，計算稅額和總額
                taxAmount = taxBaseAmount.multiply(taxRate).setScale(0, RoundingMode.HALF_UP);
                grandTotal = itemsSubtotalNet.add(taxAmount);
            }
        }

        order.setTaxAmount(taxAmount);
        order.setGrandTotalAmount(grandTotal);

        if (order.getInvoiceTypeCode() != null) {
            order.setInvoiceAmount(order.getGrandTotalAmount());
        }
    }

    // 為了向後相容，保留原方法，但內部調用新方法
    private void recalculateOrderTotals(Order order) {
        recalculateOrderTotals(order, (short)1); // 預設為稅外 (1)
    }

    private void createTechRecord(DispatchRepair dispatchRepair, Short statusCode, UserAccount user, RecordTypeEnum type, String r1, String r2, String r3, String r4, String r5) {
        DispatchTechRecord record = new DispatchTechRecord();
        record.setDispatchRepairId(dispatchRepair.getDispatchRepairId());
        record.setStatusCode(statusCode);
        record.setTechnicianId(user.getUserAccountId());
        record.setRecordType(type.getCode());
        record.setRecord1(r1);
        record.setRecord2(r2);
        record.setRecord3(r3);
        record.setRecord4(r4);
        record.setRecord5(r5);
        dispatchTechRecordRepository.save(record);
    }

    @Override
    @Transactional
    public void inviteCollaborators(UUID dispatchRepairId, DispatchCollaboratorRequest request) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));

        List<UserAccount> technicians = userAccountRepository.findAllById(request.getTechnicianIds());
        if (technicians.size() != request.getTechnicianIds().size()) {
            throw new BadRequestException("部分受邀的技師ID不存在。");
        }

        List<DispatchCollaborator> newCollaborators = new ArrayList<>();
        for (UserAccount technician : technicians) {
            DispatchCollaborator collaborator = new DispatchCollaborator();
            collaborator.setDispatchRepair(dispatchRepair);
            collaborator.setTechnician(technician);
            collaborator.setInvitationStatus(InvitationStatusEnum.PENDING.getCode());
            newCollaborators.add(collaborator);
        }

        dispatchCollaboratorRepository.saveAll(newCollaborators);
        // Optional: Log this action
    }

    @Override
    @Transactional
    public void acceptCollaboration(UUID dispatchRepairId, UUID technicianId) {
        DispatchCollaborator collaborator = dispatchCollaboratorRepository.findByDispatchRepair_DispatchRepairIdAndTechnician_UserAccountId(dispatchRepairId, technicianId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到對應的協同邀請。"));

        if (!InvitationStatusEnum.PENDING.getCode().equals(collaborator.getInvitationStatus())) {
            throw new BadRequestException("此協同邀請無法被接受 (可能已接受或已拒絕)。");
        }

        collaborator.setInvitationStatus(InvitationStatusEnum.ACCEPTED.getCode());
        dispatchCollaboratorRepository.save(collaborator);

        // Optional: Log this specific action if needed
        // For example: logService.logCollaborationAccepted(collaborator);
    }

    @Override
    @Transactional
    public void updateUrgentStatus(UUID dispatchRepairId, boolean isUrgent) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));

        // Check if status is editable (<= 29)
        if (dispatchRepair.getStatusCode() > 29) {
            throw new BadRequestException("此派工單狀態已無法變更急單設定。");
        }

        dispatchRepair.setIsUrgent(isUrgent ? (short) 1 : (short) 0);
        dispatchRepairRepository.save(dispatchRepair);
    }

    @Override
    @Transactional
    public void transferDispatchRepair(UUID dispatchRepairId, UUID targetTechnicianId, String reason, String materialHandling) {
        DispatchRepair dispatchRepair = dispatchRepairRepository.findById(dispatchRepairId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到派工單，ID: " + dispatchRepairId));

        if (dispatchRepair.getIsDeleted() != null && dispatchRepair.getIsDeleted() != 0) {
            throw new BadRequestException("此派工單已被刪除，無法轉單。");
        }

        // 僅允許在 29(待排單) 之前轉單，符合前端邏輯：狀態 >=29 隱藏轉單
        if (dispatchRepair.getStatusCode() == null || dispatchRepair.getStatusCode() >= DispatchStatusEnum.SCHEDULED.getCode()) {
            throw new BadRequestException("此派工單狀態已無法轉單。");
        }

        if (targetTechnicianId == null) {
            throw new BadRequestException("目標技師不可為空。");
        }

        UserAccount targetTech = userAccountRepository.findById(targetTechnicianId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到目標技師，ID: " + targetTechnicianId));

        UUID currentTechId = dispatchRepair.getAssignedTechnicianId();
        if (currentTechId != null && currentTechId.equals(targetTechnicianId)) {
            throw new BadRequestException("目標技師與現任技師相同，無需轉單。");
        }

        // 處理已領料物料（僅針對已完成領料的數量）
        String strategy = materialHandling != null ? materialHandling.trim().toUpperCase() : "";
        boolean transferToTech = "TRANSFER_TO_TECH".equals(strategy);
        boolean returnToWarehouse = "RETURN_TO_WAREHOUSE".equals(strategy);

        if (currentTechId != null && (transferToTech || returnToWarehouse)) {
            for (DispatchRepairItem item : Optional.ofNullable(dispatchRepair.getItems()).orElse(Collections.emptyList())) {
                UUID itemId = item.getDispatchRepairItemId();
                if (itemId == null) continue;
                Integer collectedQty = dispatchMaterialOrderItemRepository.sumCollectedByDispatchRepairItemId(itemId);
                int qty = collectedQty != null ? collectedQty : 0;
                if (qty <= 0) continue; // 無已領料數量

                String barcode = item.getProductBarcode();
                String name = item.getProductName();

                // 先由原技師庫存轉出
                try {
                    warehouseStoreInventoryService.updateTechnicianInventory(currentTechId, barcode, name, -qty, InventoryTransactionTypeEnum.TRANSFER_OUT, dispatchRepair.getDispatchRepairNumber());
                } catch (Exception e) {
                    logger.error("技師庫存轉出失敗: dispatchRepair={}, item={}, qty={}", dispatchRepair.getDispatchRepairNumber(), itemId, qty, e);
                    throw new BadRequestException("處理物料轉出時發生錯誤。");
                }

                if (transferToTech) {
                    // 轉到新技師庫存
                    warehouseStoreInventoryService.updateTechnicianInventory(targetTechnicianId, barcode, name, qty, InventoryTransactionTypeEnum.TRANSFER_IN, dispatchRepair.getDispatchRepairNumber());
                } else if (returnToWarehouse) {
                    // 退回原倉: 由派工品項的 warehouseCode 取回倉庫ID（該欄位儲存 UUID 字串）
                    UUID warehouseId = null;
                    try {
                        if (item.getWarehouseCode() != null) {
                            warehouseId = UUID.fromString(item.getWarehouseCode());
                        }
                    } catch (IllegalArgumentException iae) {
                        logger.warn("派工品項 warehouseCode 非有效 UUID: {}", item.getWarehouseCode());
                    }
                    if (warehouseId == null) {
                        // 若無法取得原倉，將物料轉入目標技師，避免遺失
                        warehouseStoreInventoryService.updateTechnicianInventory(targetTechnicianId, barcode, name, qty, InventoryTransactionTypeEnum.TRANSFER_IN, dispatchRepair.getDispatchRepairNumber());
                    } else {
                        warehouseStoreInventoryService.updateInventoryForWarehouse(warehouseId, barcode, name, qty, InventoryTransactionTypeEnum.TRANSFER_IN.name(), dispatchRepair.getDispatchRepairId().toString());
                    }
                }
            }
        }

        Short previousStatus = dispatchRepair.getStatusCode();
        // 更新派工單責任歸屬
        dispatchRepair.setAssignedTechnicianId(targetTechnicianId);
        dispatchRepairRepository.save(dispatchRepair);

        // 建立技師紀錄
        DispatchTechRecord record = new DispatchTechRecord();
        record.setDispatchRepairId(dispatchRepair.getDispatchRepairId());
        record.setStatusCode(previousStatus);
        record.setTechnicianId(targetTechnicianId);
        record.setRecordType(RecordTypeEnum.REMARKS.getCode());
        record.setRecord1("已轉單給技師：" + targetTech.getUserName() + (StringUtils.hasText(reason) ? ("，原因：" + reason) : ""));
        dispatchTechRecordRepository.save(record);

        // 紀錄歷程
        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
            .flatMap(ud -> userAccountRepository.findById(ud.getId()))
            .orElse(targetTech);
        dispatchRepairLogService.logChange(dispatchRepair, previousStatus, dispatchRepair.getStatusCode(), currentUser, "轉單", Map.of(
            "fromTechnicianId", currentTechId,
            "toTechnicianId", targetTechnicianId,
            "reason", reason,
            "materialHandling", strategy
        ));

        // TODO: 自動通知相關人員（此處以日誌代替，以保持最小改動）
        logger.info("Dispatch order {} transferred from {} to {}. Strategy: {}", dispatchRepair.getDispatchRepairNumber(), currentTechId, targetTechnicianId, strategy);
    }

    private DispatchRepairSummaryDto convertToSummaryDto(DispatchRepair entity) {
        return convertToSummaryDto(entity, false);
    }

    private DispatchRepairSummaryDto convertToSummaryDto(DispatchRepair entity, boolean isCollaboration) {
        // Get Type Description
        String typeDescription = "未知類型";
        DispatchRepairTypeEnum typeEnum = DispatchRepairTypeEnum.fromCode(entity.getTypeCode());
        if (typeEnum != null) {
            typeDescription = typeEnum.getDescription();
    }

        // Get Status Description
        String statusDescription = "未知狀態";
        DispatchStatusEnum statusEnum = DispatchStatusEnum.fromCode(entity.getStatusCode());
        if (statusEnum != null) {
            statusDescription = statusEnum.getDescription();
        }

        // Get Technician Name
        String technicianName = "未指派";
        if (entity.getAssignedTechnicianId() != null) {
            technicianName = userAccountRepository.findById(entity.getAssignedTechnicianId())
                    .map(UserAccount::getUserName)
                    .orElse("查無此人");
        }

        return new DispatchRepairSummaryDto(
                entity.getDispatchRepairId(),
                entity.getDispatchRepairNumber(),
                isCollaboration,
                technicianName, // For collaborations, this is the main tech's name
                entity.getTypeCode(), 
                typeDescription,
                entity.getStatusCode(), 
                statusDescription,
                entity.getIsUrgent(),
                entity.getCustomerName(),
                entity.getScheduledDate(), 
                technicianName // assignedTechnicianName
        );
    }

    private DispatchRepairDetailDto convertToDetailDto(DispatchRepair entity) {
        DispatchRepairDetailDto dto = new DispatchRepairDetailDto();
        BeanUtils.copyProperties(entity, dto, "items");

        // Direct Mappings from DispatchRepair header
        dto.setId(entity.getDispatchRepairId());
        dto.setNumber(entity.getDispatchRepairNumber());
        dto.setTypeCode(entity.getTypeCode());
        dto.setStatusCode(entity.getStatusCode());
        dto.setCustomerId(entity.getCustomerId());
        dto.setCustomerName(entity.getCustomerName());
        dto.setCustomerPhone(entity.getCustomerPhone());
        dto.setInstallationAddress(entity.getInstallationAddress());
        dto.setCustomerDeviceId(entity.getCustomerDeviceId());
        dto.setHandlingMethod(entity.getHandlingMethod());
        dto.setFollowUpAction(entity.getFollowUpAction());
        dto.setScheduledDate(entity.getScheduledDate());
        dto.setScheduledTimeSlot(entity.getScheduledTimeSlot());
        dto.setAssignedTechnicianId(entity.getAssignedTechnicianId());
        dto.setTotalAmount(entity.getTotalAmount());
        dto.setPaidAmount(entity.getPaidAmount());
        dto.setRefundMethodCode(entity.getRefundMethodCode());
        dto.setRemarks(entity.getRemarks());
        dto.setCreateTime(entity.getCreateTime());

        // Enum Description Mappings
        if (entity.getTypeCode() != null) {
            DispatchRepairTypeEnum typeEnum = DispatchRepairTypeEnum.fromCode(entity.getTypeCode());
            if (typeEnum != null) dto.setTypeDescription(typeEnum.getDescription());
        }
        if (entity.getStatusCode() != null) {
            DispatchStatusEnum statusEnum = DispatchStatusEnum.fromCode(entity.getStatusCode());
            if (statusEnum != null) dto.setStatusDescription(statusEnum.getDescription());
        }
        if (entity.getRefundMethodCode() != null) {
            RefundMethodEnum refundEnum = RefundMethodEnum.fromCode(entity.getRefundMethodCode());
            if (refundEnum != null) dto.setRefundMethodDescription(refundEnum.getDescription());
        }

        // Relational Data Mapping
        if (entity.getAssignedTechnicianId() != null) {
            userAccountRepository.findById(entity.getAssignedTechnicianId())
                .ifPresent(tech -> dto.setAssignedTechnicianName(tech.getUserName()));
        }

        // === START: ADD COLLABORATORS ===
        List<DispatchCollaborator> collaborators = dispatchCollaboratorRepository.findByDispatchRepair(entity);
        if (!CollectionUtils.isEmpty(collaborators)) {
            List<DispatchCollaboratorDto> collaboratorDtos = collaborators.stream().map(c -> {
                DispatchCollaboratorDto collaboratorDto = new DispatchCollaboratorDto();
                collaboratorDto.setTechnicianId(c.getTechnician().getUserAccountId());
                collaboratorDto.setTechnicianName(c.getTechnician().getUserName());
                collaboratorDto.setInvitationStatus(c.getInvitationStatus());
                if (c.getInvitationStatus() != null) {
                    InvitationStatusEnum statusEnum = InvitationStatusEnum.fromCode(c.getInvitationStatus());
                    if (statusEnum != null) {
                        collaboratorDto.setStatusDescription(statusEnum.getDescription());
                    }
                }
                return collaboratorDto;
            }).collect(Collectors.toList());
            dto.setCollaborators(collaboratorDtos);
        }
        // === END: ADD COLLABORATORS ===

        // Fetch and set remarks from the original order
        if (entity.getOrder() != null) {
            orderRepository.findById(entity.getOrder().getOrderId())
                .ifPresent(order -> {
                    dto.setOriginalOrderRemarks(order.getRemarks());
                    dto.setOriginalOrderTaxAmount(order.getTaxAmount());

                    // --- NEW LOGIC: Populate amount info from original order ---
                    dto.setProductsTotalAmount(order.getProductsTotalAmount());
                    dto.setDiscountAmount(order.getDiscountAmount());
                    dto.setNetAmount(order.getNetAmount());
                    dto.setTaxAmount(order.getTaxAmount());
                    dto.setGrandTotalAmount(order.getGrandTotalAmount());
                    dto.setPaidAmount(order.getPaidAmount());

                    // --- NEW LOGIC: Populate payment details ---
                    if (order.getPayments() != null && !order.getPayments().isEmpty()) {
                        dto.setPaymentDetails(order.getPayments().stream()
                            .filter(p -> p.getIsDeleted() == 0)
                            .map(this::convertOrderPaymentToDto)
                            .collect(Collectors.toList()));
                    }

                    // --- New Logic to fetch Repaired Parts and Technician Collection Amount ---
                    List<OrderItemDto> repairedParts = order.getItems().stream()
                        .flatMap(mainItem -> mainItem.getItemGroups().stream()
                            .filter(group -> DispatchRepairItemTypeEnum.REPAIR_PART.name().equals(group.getRemarks()))
                            .map(group -> {
                                OrderItemDto partDto = new OrderItemDto();
                                BeanUtils.copyProperties(group, partDto);
                                partDto.setMainProductName(mainItem.getProductName());
                                return partDto;
                            })
                        )
                        .collect(Collectors.toList());
                    dto.setRepairedParts(repairedParts);

                    BigDecimal techAmount = order.getPayments().stream()
                        .filter(p -> PaymentMethodEnum.TECHNICIAN_COLLECTION.name().equals(p.getPaymentMethodCode()) && p.getIsDeleted() == 0)
                        .map(OrderPayment::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    dto.setTechnicianCollectionAmount(techAmount);
                });
        }

        // Pre-fetch all product settings for the items to avoid N+1 queries
        List<String> barcodes = entity.getItems().stream()
                                    .map(DispatchRepairItem::getProductBarcode)
                                    .collect(Collectors.toList());
        Map<String, ProductSetting> productSettingMap = productSettingRepository.findByProductBarcodeInAndIsDeleted(barcodes, (short) 0)
                                    .stream()
                                    .collect(Collectors.toMap(ProductSetting::getProductBarcode, ps -> ps));

        // Child Collection Mapping
        if (entity.getItems() != null && !entity.getItems().isEmpty()) {
            dto.setItems(entity.getItems().stream()
                .map(item -> convertToItemDto(item, productSettingMap.get(item.getProductBarcode())))
                .collect(Collectors.toList()));
        } else {
            dto.setItems(Collections.emptyList());
        }

        // New Tech Records Mapping
        if (entity.getTechRecords() != null && !entity.getTechRecords().isEmpty()) {
            dto.setTechRecords(entity.getTechRecords().stream()
                .sorted(Comparator.comparing(DispatchTechRecord::getCreateTime))
                .map(this::convertToTechRecordDto)
                .collect(Collectors.toList()));
        }

        return dto;
    }

    private OrderPaymentDto convertOrderPaymentToDto(OrderPayment payment) {
        OrderPaymentDto dto = new OrderPaymentDto();
        BeanUtils.copyProperties(payment, dto);
        if (payment.getPaymentMethodCode() != null) {
            try {
                dto.setPaymentMethodCode(PaymentMethodEnum.valueOf(payment.getPaymentMethodCode()));
            } catch (IllegalArgumentException e) {
                // Handle cases where the string from DB doesn't match enum constant
                dto.setPaymentMethodCode(null);
            }
        }
        return dto;
    }

    private DispatchTechRecordDto convertToTechRecordDto(DispatchTechRecord record) {
        DispatchTechRecordDto dto = new DispatchTechRecordDto();
        BeanUtils.copyProperties(record, dto);

        if (record.getStatusCode() != null) {
            dto.setStatusDescription(DispatchStatusEnum.getDescriptionByCode(record.getStatusCode()));
        }
        if (record.getRecordType() != null) {
            dto.setRecordTypeDescription(RecordTypeEnum.getDescriptionByCode(record.getRecordType()));
        }
        if (record.getTechnicianId() != null) {
            userAccountRepository.findById(record.getTechnicianId())
                .ifPresent(tech -> dto.setTechnicianName(tech.getUserName()));
        }
        return dto;
    }

    private DispatchRepairItemDto convertToItemDto(DispatchRepairItem item, ProductSetting productSetting) {
        DispatchRepairItemDto itemDto = new DispatchRepairItemDto();
        BeanUtils.copyProperties(item, itemDto);

        if (productSetting != null) {
            itemDto.setIsMain(productSetting.getIsMain() != null && productSetting.getIsMain() == 1);
        } else {
            itemDto.setIsMain(false); // Default to false if product setting not found
        }

        itemDto.setIsAddon(item.getIsAddon());

        if (item.getWarrantyStatusCode() != null) {
            itemDto.setWarrantyStatusDescription(WarrantyStatusEnum.getDescriptionByCode(item.getWarrantyStatusCode()));
        }
        if (item.getItemTypeCode() != null) {
            itemDto.setItemTypeDescription(DispatchRepairItemTypeEnum.fromCode(item.getItemTypeCode()).getDescription());
        }
        return itemDto;
    }
} 
