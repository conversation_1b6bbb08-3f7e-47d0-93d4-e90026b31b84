package com.eastking.service.impl;

import com.eastking.enums.AnnouncementCategoryEnum;
import com.eastking.enums.AnnouncementTargetTypeEnum;
import com.eastking.enums.DeleteStatusEnum;
import com.eastking.model.dto.request.AnnouncementQueryRequest;
import com.eastking.model.dto.request.AnnouncementRequest;
import com.eastking.model.dto.request.AnnouncementTargetDto;
import com.eastking.model.dto.response.AnnouncementResponse;
import com.eastking.model.entity.Announcement;
import com.eastking.model.entity.AnnouncementTarget;
import com.eastking.model.entity.UserAccount; // For populating createByName/updateByName
import com.eastking.repository.AnnouncementRepository;
import com.eastking.repository.AnnouncementTargetRepository;
import com.eastking.repository.UserAccountRepository; // For populating createByName/updateByName
import com.eastking.service.AnnouncementService;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AnnouncementServiceImpl implements AnnouncementService {

    private final AnnouncementRepository announcementRepository;
    private final AnnouncementTargetRepository announcementTargetRepository;
    private final UserAccountRepository userAccountRepository; // For fetching user names

    @Override
    @Transactional
    public AnnouncementResponse createAnnouncement(AnnouncementRequest request) {
        Announcement announcement = new Announcement();
        mapRequestToEntity(request, announcement);
        announcement.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        // Auditing fields (createBy, createTime, updateBy, updateTime) will be set by JpaAuditingConfiguration

        Announcement savedAnnouncement = announcementRepository.save(announcement);

        if (!CollectionUtils.isEmpty(request.getTargets())) {
            List<AnnouncementTarget> targets = request.getTargets().stream()
                    .map(dto -> mapTargetDtoToEntity(dto, savedAnnouncement))
                    .collect(Collectors.toList());
            announcementTargetRepository.saveAll(targets);
            savedAnnouncement.setTargets(targets);
        }
        return mapEntityToResponse(savedAnnouncement, true); // Include targets in response
    }

    @Override
    @Transactional
    public AnnouncementResponse updateAnnouncement(UUID announcementId, AnnouncementRequest request) {
        Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new EntityNotFoundException("公告未找到，ID: " + announcementId));

        mapRequestToEntity(request, announcement);
        // Auditing fields for update will be handled by JPA

        // Handle targets: clear existing and add new ones
        announcementTargetRepository.deleteByAnnouncement(announcement);
        announcement.getTargets().clear(); // Important for JPA to remove from collection

        if (!CollectionUtils.isEmpty(request.getTargets())) {
            List<AnnouncementTarget> newTargets = request.getTargets().stream()
                    .map(dto -> mapTargetDtoToEntity(dto, announcement))
                    .collect(Collectors.toList());
            announcementTargetRepository.saveAll(newTargets);
            announcement.setTargets(newTargets);
        }
        
        Announcement updatedAnnouncement = announcementRepository.save(announcement);
        return mapEntityToResponse(updatedAnnouncement, true); // Include targets in response
    }

    @Override
    @Transactional
    public void deleteAnnouncement(UUID announcementId) {
        Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new EntityNotFoundException("公告未找到，ID: " + announcementId));
        announcement.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        announcement.setIsEnabled((short)0); // Also disable it
        announcementRepository.save(announcement);
        // Targets will remain but effectively inactive due to announcement being deleted
    }

    @Override
    @Transactional(readOnly = true)
    public AnnouncementResponse getAnnouncementById(UUID announcementId) {
        Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new EntityNotFoundException("公告未找到，ID: " + announcementId));
        if (announcement.getIsDeleted() == DeleteStatusEnum.DELETED.getCode()){
             throw new EntityNotFoundException("公告已被刪除，ID: " + announcementId);
        }
        return mapEntityToResponse(announcement, true); // Include targets in response
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AnnouncementResponse> getAllAnnouncements(AnnouncementQueryRequest queryRequest, Pageable pageable) {
        // Override sort from queryRequest if present, else use pageable's sort or default
        Sort sort = pageable.getSort();
        if (queryRequest.getSort() != null && queryRequest.getSort().length > 0) {
            List<Sort.Order> orders = new ArrayList<>();
            for (String sortOrder : queryRequest.getSort()) {
                String[] _sort = sortOrder.split(",");
                orders.add(new Sort.Order(_sort.length > 1 && "desc".equalsIgnoreCase(_sort[1]) ? Sort.Direction.DESC : Sort.Direction.ASC, _sort[0]));
            }
            sort = Sort.by(orders);
        }
        pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), sort);

        Specification<Announcement> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));

            if (queryRequest.getCategoryCode() != null) {
                predicates.add(criteriaBuilder.equal(root.get("categoryCode"), queryRequest.getCategoryCode()));
            }
            if (queryRequest.getKeyword() != null && !queryRequest.getKeyword().isBlank()) {
                predicates.add(criteriaBuilder.like(root.get("title"), "%" + queryRequest.getKeyword() + "%"));
            }
            if (queryRequest.getIsEnabled() != null) {
                predicates.add(criteriaBuilder.equal(root.get("isEnabled"), queryRequest.getIsEnabled() ? 1 : 0));
            }
             if (queryRequest.getIsImportant() != null) {
                predicates.add(criteriaBuilder.equal(root.get("isImportant"), queryRequest.getIsImportant() ? 1 : 0));
            }
            // Combined date range query: (start_time <= to_date AND end_time >= from_date)
            if (queryRequest.getAnnouncementDateFrom() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("startTime"), queryRequest.getAnnouncementDateFrom()));
            }
            if (queryRequest.getAnnouncementDateTo() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("endTime"), queryRequest.getAnnouncementDateTo()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<Announcement> announcementPage = announcementRepository.findAll(spec, pageable);
        return announcementPage.map(ann -> mapEntityToResponse(ann, false)); // Don't include targets in list view for brevity
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<AnnouncementResponse> getActivePublicAnnouncements() {
        List<Announcement> announcements = announcementRepository
            .findByIsEnabledAndIsDeletedAndStartTimeBeforeAndEndTimeAfterOrderByIsImportantDescCreateTimeDesc(
                (short)1, DeleteStatusEnum.NOT_DELETED.getCode(), OffsetDateTime.now(), OffsetDateTime.now());
        return announcements.stream()
                            .map(ann -> mapEntityToResponse(ann, false)) // Targets not needed for this view
                            .collect(Collectors.toList());
    }

    private void mapRequestToEntity(AnnouncementRequest request, Announcement announcement) {
        announcement.setCategoryCode(request.getCategoryCode().shortValue());
        announcement.setTitle(request.getTitle());
        announcement.setContent(request.getContent());
        announcement.setStartTime(request.getStartTime());
        announcement.setEndTime(request.getEndTime());
        announcement.setIsImportant(request.getIsImportant() ? (short) 1 : (short) 0);
        announcement.setIsEnabled(request.getIsEnabled() ? (short) 1 : (short) 0);
    }

    private AnnouncementTarget mapTargetDtoToEntity(AnnouncementTargetDto dto, Announcement announcement) {
        AnnouncementTarget target = new AnnouncementTarget();
        target.setAnnouncement(announcement);
        target.setTargetType(dto.getTargetType().shortValue());
        target.setTargetIdentifier(dto.getTargetIdentifier());
        target.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        // Auditing fields for target will also be set by JPA
        return target;
    }

    private AnnouncementResponse mapEntityToResponse(Announcement announcement, boolean includeTargets) {
        String createByName = announcement.getCreateBy() != null ? userAccountRepository.findById(announcement.getCreateBy()).map(UserAccount::getUserName).orElse(null) : null;
        String updateByName = announcement.getUpdateBy() != null ? userAccountRepository.findById(announcement.getUpdateBy()).map(UserAccount::getUserName).orElse(null) : null;
        AnnouncementCategoryEnum categoryEnum = AnnouncementCategoryEnum.fromCode(announcement.getCategoryCode() != null ? announcement.getCategoryCode() : null);

        List<AnnouncementTargetDto> targetDtos = null;
        if (includeTargets && !CollectionUtils.isEmpty(announcement.getTargets())) {
            targetDtos = announcement.getTargets().stream()
                .filter(t -> t.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode())
                .map(targetEntity -> {
                    AnnouncementTargetDto dto = new AnnouncementTargetDto();
                    dto.setTargetType(targetEntity.getTargetType().intValue());
                    dto.setTargetIdentifier(targetEntity.getTargetIdentifier());
                    // TODO: Optionally populate targetName here by fetching from external source if needed for display
                    AnnouncementTargetTypeEnum typeEnum = AnnouncementTargetTypeEnum.fromCode(targetEntity.getTargetType());
                    dto.setTargetName(typeEnum != null ? typeEnum.getDescription() + ": " + targetEntity.getTargetIdentifier() : targetEntity.getTargetIdentifier());
                    return dto;
                }).collect(Collectors.toList());
        }

        return AnnouncementResponse.builder()
                .announcementId(announcement.getAnnouncementId())
                .categoryCode(announcement.getCategoryCode() != null ? announcement.getCategoryCode().intValue() : null)
                .categoryDescription(categoryEnum != null ? categoryEnum.getDescription() : null)
                .title(announcement.getTitle())
                .content(announcement.getContent())
                .startTime(announcement.getStartTime())
                .endTime(announcement.getEndTime())
                .isImportant(announcement.getIsImportant() == 1)
                .isEnabled(announcement.getIsEnabled() == 1)
                .targets(targetDtos)
                .createBy(announcement.getCreateBy())
                .createByName(createByName)
                .createTime(announcement.getCreateTime())
                .updateBy(announcement.getUpdateBy())
                .updateByName(updateByName)
                .updateTime(announcement.getUpdateTime())
                .build();
    }
} 