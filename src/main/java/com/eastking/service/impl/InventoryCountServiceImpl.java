package com.eastking.service.impl;

import com.eastking.enums.*;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.InventoryCountItemDto;
import com.eastking.model.dto.InventoryCountSheetDto;
import com.eastking.model.dto.request.InventoryCountItemRequestDto;
import com.eastking.model.dto.request.InventoryCountSheetRequestDto;
import com.eastking.model.entity.InventoryCountItem;
import com.eastking.model.entity.InventoryCountSheet;
import com.eastking.model.entity.ProductSetting;
import com.eastking.model.entity.StoreEntity;
import com.eastking.model.entity.UserAccount;
import com.eastking.repository.InventoryCountSheetRepository;
import com.eastking.repository.ProductSettingRepository;
import com.eastking.repository.StoreRepository;
import com.eastking.repository.UserAccountRepository;
import com.eastking.service.InventoryCountService;
import com.eastking.util.OrderNumberUtil;
import com.eastking.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class InventoryCountServiceImpl implements InventoryCountService {

    private final InventoryCountSheetRepository inventoryCountSheetRepository;
    private final StoreRepository storeRepository;
    private final UserAccountRepository userAccountRepository;
    private final ProductSettingRepository productSettingRepository;

    @Override
    @Transactional
    public InventoryCountSheetDto createInventoryCountSheet(InventoryCountSheetRequestDto requestDto) {
        StoreEntity store = storeRepository.findById(requestDto.getStoreId())
                .orElseThrow(() -> new ResourceNotFoundException("找不到門市, ID: " + requestDto.getStoreId()));

        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .flatMap(ud -> userAccountRepository.findById(ud.getId()))
                .orElseThrow(() -> new ResourceNotFoundException("無法獲取當前使用者資訊"));
        
        InventoryCountSheet sheet = new InventoryCountSheet();
        sheet.setInventoryCountSheetId(UUID.randomUUID());
        sheet.setSheetNumber(OrderNumberUtil.generateNumber("S"));
        sheet.setStore(store);
        sheet.setCompanyDivisionCode(currentUser.getErpCompanyDivision());
        sheet.setCountDate(OffsetDateTime.parse(requestDto.getCountDate()));
        sheet.setCountMonth(requestDto.getCountMonth());
        sheet.setRemarks(requestDto.getRemarks());
        sheet.setCountedByUser(currentUser);
        
        if (requestDto.getIsSubmitted()) {
            sheet.setSheetStatusCode(InventoryCountSheetStatusEnum.SUBMITTED_INITIAL.getCode());
            sheet.setApprovalStatusCode(InventoryCountApprovalStatusEnum.PENDING_APPROVAL.getCode());
        } else {
            sheet.setSheetStatusCode(InventoryCountSheetStatusEnum.DRAFT_INITIAL.getCode());
            sheet.setApprovalStatusCode(InventoryCountApprovalStatusEnum.PENDING_SUBMISSION.getCode());
        }

        List<InventoryCountItem> items = requestDto.getItems().stream()
                .map(itemDto -> {
                    ProductSetting product = productSettingRepository.findByProductBarcodeAndIsDeleted(itemDto.getProductBarcode(), DeleteStatusEnum.NOT_DELETED.getCode())
                        .orElseThrow(() -> new ResourceNotFoundException("找不到商品: " + itemDto.getProductBarcode()));
                    
                    InventoryCountItem item = new InventoryCountItem();
                    item.setInventoryCountItemId(UUID.randomUUID());
                    item.setInventoryCountSheet(sheet);
                    item.setProductBarcode(itemDto.getProductBarcode());
                    item.setProductName(product.getProductName());
                    item.setCountedQuantity(itemDto.getCountedQuantity());
                    item.setRemarks(itemDto.getRemarks());
                    return item;
                }).collect(Collectors.toList());
        
        sheet.setItems(items);

        InventoryCountSheet savedSheet = inventoryCountSheetRepository.save(sheet);
        return convertToDto(savedSheet);
    }

    @Override
    public Page<InventoryCountSheetDto> searchInventoryCountSheets(
            Short companyDivisionCode, UUID storeId, String countMonth, Short approvalStatusCode, Pageable pageable) {

        Specification<InventoryCountSheet> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            
            predicates.add(cb.equal(root.get("companyDivisionCode"), companyDivisionCode));

            if (storeId != null) {
                predicates.add(cb.equal(root.get("store").get("storeId"), storeId));
            }
            if (StringUtils.hasText(countMonth)) {
                predicates.add(cb.equal(root.get("countMonth"), countMonth));
            }
            if (approvalStatusCode != null) {
                predicates.add(cb.equal(root.get("approvalStatusCode"), approvalStatusCode));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        return inventoryCountSheetRepository.findAll(spec, pageable).map(this::convertToDto);
    }

    @Override
    public InventoryCountSheetDto getInventoryCountSheetById(UUID sheetId) {
        return inventoryCountSheetRepository.findById(sheetId)
                .filter(sheet -> !DeleteStatusEnum.DELETED.getCode().equals(sheet.getIsDeleted()))
                .map(this::convertToDto)
                .orElseThrow(() -> new ResourceNotFoundException("找不到盤點單，ID: " + sheetId));
    }

    private InventoryCountSheetDto convertToDto(InventoryCountSheet entity) {
        if (entity == null) {
            return null;
        }

        List<InventoryCountItemDto> itemDtos = List.of();
        if (!CollectionUtils.isEmpty(entity.getItems())) {
            itemDtos = entity.getItems().stream()
                    .map(this::convertItemToDto)
                    .collect(Collectors.toList());
        }

        return InventoryCountSheetDto.builder()
                .inventoryCountSheetId(entity.getInventoryCountSheetId())
                .sheetNumber(entity.getSheetNumber())
                .storeId(entity.getStore() != null ? entity.getStore().getStoreId() : null)
                .storeName(entity.getStore() != null ? entity.getStore().getStoreName() : null)
                .companyDivisionCode(entity.getCompanyDivisionCode())
                .companyDivisionDescription(ErpCompanyDivisionEnum.getDescriptionByCode(entity.getCompanyDivisionCode()))
                .countDate(entity.getCountDate())
                .countMonth(entity.getCountMonth())
                .sheetStatusCode(entity.getSheetStatusCode())
                .sheetStatusDescription(InventoryCountSheetStatusEnum.getDescriptionByCode(entity.getSheetStatusCode()))
                .approvalStatusCode(entity.getApprovalStatusCode())
                .approvalStatusDescription(InventoryCountApprovalStatusEnum.getDescriptionByCode(entity.getApprovalStatusCode()))
                .originalSheetId(entity.getOriginalSheet() != null ? entity.getOriginalSheet().getInventoryCountSheetId() : null)
                .originalSheetNumber(entity.getOriginalSheet() != null ? entity.getOriginalSheet().getSheetNumber() : null)
                .countedByUserName(entity.getCountedByUser() != null ? entity.getCountedByUser().getUserName() : null)
                .recountByUserName(entity.getRecountByUser() != null ? entity.getRecountByUser().getUserName() : null)
                .approvedByUserName(entity.getApprovedByUser() != null ? entity.getApprovedByUser().getUserName() : null)
                .approvalTime(entity.getApprovalTime())
                .remarks(entity.getRemarks())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .items(itemDtos)
                .build();
    }

    private InventoryCountItemDto convertItemToDto(InventoryCountItem item) {
        if (item == null) {
            return null;
        }

        Integer variance = null;
        if (item.getFinalQuantity() != null && item.getSystemQuantity() != null) {
            variance = item.getFinalQuantity() - item.getSystemQuantity();
        }

        return InventoryCountItemDto.builder()
                .inventoryCountItemId(item.getInventoryCountItemId())
                .productBarcode(item.getProductBarcode())
                .productName(item.getProductName())
                .systemQuantity(item.getSystemQuantity())
                .countedQuantity(item.getCountedQuantity())
                .recountQuantity(item.getRecountQuantity())
                .finalQuantity(item.getFinalQuantity())
                .variance(variance)
                .requiresRecount(item.getRequiresRecount())
                .remarks(item.getRemarks())
                .build();
    }
} 