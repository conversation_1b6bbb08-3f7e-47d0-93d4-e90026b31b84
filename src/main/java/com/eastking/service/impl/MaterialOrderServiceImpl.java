package com.eastking.service.impl;

import com.eastking.enums.*;
import com.eastking.repository.*;
import com.eastking.service.MaterialOrderService;
import com.eastking.model.dto.request.MaterialOrderItemRequestDto;
import com.eastking.model.dto.request.MaterialOrderRequestDto;
import com.eastking.model.dto.request.MaterialOrderFilterRequest;
import com.eastking.model.dto.response.MaterialOrderSummaryDto;
import com.eastking.model.entity.*;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.util.OrderNumberUtil;
import com.eastking.util.SecurityUtil;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.security.core.GrantedAuthority;
import java.util.Set;
import java.util.Collections;
import java.util.UUID;
import com.eastking.model.dto.response.MaterialOrderItemDto;
import com.eastking.model.dto.response.MaterialOrderDetailDto;
import com.eastking.exception.InvalidOperationException;
import com.eastking.model.entity.DispatchMaterialOrder;
import com.eastking.model.entity.DispatchMaterialOrderItem;
import com.eastking.model.entity.DispatchRepair;
import com.eastking.repository.DispatchMaterialOrderItemRepository;
import com.eastking.repository.DispatchMaterialOrderRepository;
import com.eastking.repository.DispatchRepairRepository;
import com.eastking.repository.UserAccountRepository;
import com.eastking.repository.WarehouseRepository;
import com.eastking.service.MaterialOrderService;
import com.eastking.enums.MaterialOrderStatusEnum;
import com.eastking.exception.InvalidDataException;
import com.eastking.exception.InvalidOperationException;
import java.util.Map;
import java.util.Optional;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class MaterialOrderServiceImpl implements MaterialOrderService {

    private final DispatchMaterialOrderRepository dispatchMaterialOrderRepository;
    private final DispatchMaterialOrderItemRepository dispatchMaterialOrderItemRepository;
    private final DispatchRepairRepository dispatchRepairRepository;
    private final WarehouseRepository warehouseRepository;
    private final UserAccountRepository userAccountRepository;
    private final ProductSettingRepository productSettingRepository;
    private final DispatchRepairItemRepository dispatchRepairItemRepository;
    private final DispatchTechRecordRepository dispatchTechRecordRepository;

    @Override
    public MaterialOrderDetailDto createMaterialOrder(MaterialOrderRequestDto requestDto, MaterialTypeStatusEnum materialType) {
        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .map(userDetails -> userAccountRepository.findById(userDetails.getId()))
                .orElseThrow(() -> new ResourceNotFoundException("無法獲取當前用戶信息"))
                .orElseThrow(() -> new ResourceNotFoundException("找不到當前用戶信息"));

        Warehouse warehouse = warehouseRepository.findById(requestDto.getTargetWarehouseId())
                .orElseThrow(() -> new ResourceNotFoundException("找不到指定的倉庫，ID: " + requestDto.getTargetWarehouseId()));

        DispatchMaterialOrder newOrder = new DispatchMaterialOrder();
        newOrder.setRequestingTechnician(currentUser);
        newOrder.setTargetWarehouse(warehouse);
        if (MaterialTypeStatusEnum.PICKING==materialType) {
            newOrder.setMaterialOrderNumber(OrderNumberUtil.generateNumber("L")); // 'L' for 領料
            newOrder.setMaterialOrderStatusCode(MaterialOrderStatusEnum.PENDING_PICKING.getCode());
        } else if (MaterialTypeStatusEnum.REFUND_PICKING==materialType) {
            newOrder.setMaterialOrderNumber(OrderNumberUtil.generateNumber("R")); // 'L' for 領料
            newOrder.setMaterialOrderStatusCode(MaterialOrderStatusEnum.REFUND_PENDING_PICKING.getCode());
        }
        newOrder.setMaterialType(materialType.getCode());
        newOrder.setRemarks(requestDto.getRemarks());

        List<DispatchMaterialOrderItem> items = requestDto.getItems().stream().map(itemDto -> {
            // Find product to get its name
            ProductSetting product = productSettingRepository.findByProductBarcodeAndIsDeleted(itemDto.getProductBarcode(), (short) 0)
                    .orElseThrow(() -> new ResourceNotFoundException("找不到商品，條碼: " + itemDto.getProductBarcode()));
            
            DispatchMaterialOrderItem item = new DispatchMaterialOrderItem();
            item.setMaterialOrder(newOrder);
            item.setDispatchRepairItemId(itemDto.getDispatchRepairItemId());
            item.setProductBarcode(itemDto.getProductBarcode());
            item.setProductName(product.getProductName());
            item.setRequestedQuantity(itemDto.getRequestedQuantity());
            return item;
        }).collect(Collectors.toList());

        newOrder.setItems(items);
        
        DispatchMaterialOrder savedOrder = dispatchMaterialOrderRepository.save(newOrder);
        
        return convertToDetailDto(savedOrder);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MaterialOrderSummaryDto> searchMaterialOrders(MaterialOrderFilterRequest filter, MaterialTypeStatusEnum materialType, Pageable pageable) {
        UserAccount currentUser = SecurityUtil.getCurrentUserDetails()
                .map(userDetails -> userAccountRepository.findById(userDetails.getId()))
                .orElseThrow(() -> new ResourceNotFoundException("無法獲取當前用戶信息"))
                .orElseThrow(() -> new ResourceNotFoundException("找不到當前用戶信息"));
        
        Set<String> roles = SecurityUtil.getCurrentUserDetails()
                .map(ud -> ud.getAuthorities().stream().map(GrantedAuthority::getAuthority).collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
        
        //boolean isAdminView = roles.contains("SYS_ADMIN") || roles.contains("HO_ADMIN") || roles.contains("TECH_ADMIN");
        boolean isAdminView = false;

        log.debug("currentUser.getUserName: {}", currentUser.getUserName());
        filter.setTechnicianId(currentUser.getUserAccountId());

        Specification<DispatchMaterialOrder> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));
            predicates.add(cb.equal(root.get("requestingTechnician").get("userAccountId"), filter.getTechnicianId()));
            
            if (StringUtils.hasText(filter.getOrderNumber())) {
                predicates.add(cb.like(cb.lower(root.get("materialOrderNumber")), "%" + filter.getOrderNumber().toLowerCase() + "%"));
            }
            if (filter.getStatusCode() != null) {
                predicates.add(cb.equal(root.get("materialOrderStatusCode"), filter.getStatusCode()));
            }
            if (filter.getStartDate() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("createTime"), filter.getStartDate().atStartOfDay().atOffset(ZoneOffset.UTC)));
            }
            if (filter.getEndDate() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("createTime"), filter.getEndDate().atTime(23, 59, 59).atOffset(ZoneOffset.UTC)));
            }
            
            // Add materialType to the specification
            if (materialType != null) {
                predicates.add(cb.equal(root.get("materialType"), materialType.getCode()));
            }
            
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        
        return dispatchMaterialOrderRepository.findAll(spec, pageable).map(this::convertToSummaryDto);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<MaterialOrderSummaryDto> searchForManagerView(MaterialOrderFilterRequest filter, Pageable pageable) {
        Specification<DispatchMaterialOrder> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));

            // Role-based filtering
            SecurityUtil.getCurrentUserDetails().ifPresent(currentUser -> {
                boolean isAdmin = currentUser.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .anyMatch(role -> role.equals("SYS_ADMIN") || role.equals("HO_ADMIN") || role.equals("TECH_ADMIN"));

                if (!isAdmin) {
                    predicates.add(cb.equal(root.get("requestingTechnician").get("userAccountId"), currentUser.getId()));
                }
            });

            if (StringUtils.hasText(filter.getOrderNumber())) {
                predicates.add(cb.like(cb.lower(root.get("materialOrderNumber")), "%" + filter.getOrderNumber().toLowerCase() + "%"));
            }
            if (filter.getStatusCode() != null) {
                predicates.add(cb.equal(root.get("materialOrderStatusCode"), filter.getStatusCode()));
            }
            if (filter.getStartDate() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("createTime"), filter.getStartDate().atStartOfDay().atOffset(ZoneOffset.UTC)));
            }
            if (filter.getEndDate() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("createTime"), filter.getEndDate().atTime(23, 59, 59).atOffset(ZoneOffset.UTC)));
            }
             if (filter.getTechnicianId() != null) {
                predicates.add(cb.equal(root.get("requestingTechnician").get("userAccountId"), filter.getTechnicianId()));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return dispatchMaterialOrderRepository.findAll(spec, pageable).map(this::convertToSummaryDto);
    }

    @Override
    @Transactional(readOnly = true)
    public MaterialOrderDetailDto getMaterialOrderDetailById(UUID materialOrderId) {
        return dispatchMaterialOrderRepository.findById(materialOrderId)
                .filter(order -> order.getIsDeleted() == 0)
                .map(this::convertToDetailDto)
                .orElseThrow(() -> new ResourceNotFoundException("找不到領料單，ID: " + materialOrderId));
    }
    
    private MaterialOrderSummaryDto convertToSummaryDto(DispatchMaterialOrder entity) {
        return MaterialOrderSummaryDto.builder()
                .materialOrderId(entity.getMaterialOrderId())
                .materialOrderNumber(entity.getMaterialOrderNumber())
                .createTime(entity.getCreateTime())
                .warehouseName(entity.getTargetWarehouse() != null ? entity.getTargetWarehouse().getWarehouseName() : "N/A")
                .statusCode(entity.getMaterialOrderStatusCode())
                .statusDescription(MaterialOrderStatusEnum.fromCode(entity.getMaterialOrderStatusCode()).getDescription())
                .build();
    }

    private MaterialOrderDetailDto convertToDetailDto(DispatchMaterialOrder entity) {
        // --- N+1 Optimization ---
        List<UUID> repairItemIds = entity.getItems().stream()
                                         .map(DispatchMaterialOrderItem::getDispatchRepairItemId)
                                         .filter(Objects::nonNull) // Correct way to filter nulls
                                         .collect(Collectors.toList());
                                         
        Map<UUID, DispatchRepair> repairMap = repairItemIds.isEmpty() ? Collections.emptyMap() :
            dispatchRepairItemRepository.findAllById(repairItemIds).stream()
                .collect(Collectors.toMap(DispatchRepairItem::getDispatchRepairItemId, DispatchRepairItem::getDispatchRepair));
        // --- End Optimization ---

        return MaterialOrderDetailDto.builder()
            .materialOrderId(entity.getMaterialOrderId())
            .materialOrderNumber(entity.getMaterialOrderNumber())
            .requestingTechnicianName(entity.getRequestingTechnician() != null ? entity.getRequestingTechnician().getUserName() : "N/A")
            .targetWarehouseName(entity.getTargetWarehouse() != null ? entity.getTargetWarehouse().getWarehouseName() : "N/A")
            .materialOrderStatusCode(entity.getMaterialOrderStatusCode())
            .materialOrderStatusDescription(MaterialOrderStatusEnum.fromCode(entity.getMaterialOrderStatusCode()).getDescription())
            .pickerName(entity.getPicker() != null ? entity.getPicker().getUserName() : null)
            .pickedTime(entity.getPickedTime())
            .collectedTime(entity.getCollectedTime())
            .remarks(entity.getRemarks())
            .createTime(entity.getCreateTime())
            .items(entity.getItems().stream().map(item -> convertItemToDto(item, repairMap)).collect(Collectors.toList()))
            .build();
    }

    private MaterialOrderItemDto convertItemToDto(DispatchMaterialOrderItem item, Map<UUID, DispatchRepair> repairMap) {
        String dispatchRepairNumber = Optional.ofNullable(repairMap.get(item.getDispatchRepairItemId()))
                                              .map(DispatchRepair::getDispatchRepairNumber)
                                              .orElse(null);

        return MaterialOrderItemDto.builder()
            .materialOrderItemId(item.getMaterialOrderItemId())
            .dispatchRepairItemId(item.getDispatchRepairItemId())
            .dispatchRepairNumber(dispatchRepairNumber)
            .productBarcode(item.getProductBarcode())
            .productName(item.getProductName())
            .requestedQuantity(item.getRequestedQuantity())
            .pickedQuantity(item.getPickedQuantity())
            .collectedQuantity(item.getCollectedQuantity())
            .build();
    }

    @Override
    @Transactional
    public void completeMaterialCollection(UUID materialOrderId, MaterialOrderStatusEnum status) {
        DispatchMaterialOrder order = dispatchMaterialOrderRepository.findById(materialOrderId)
                .orElseThrow(() -> new ResourceNotFoundException("領料單不存在，ID: " + materialOrderId));
        
        if (!order.getMaterialOrderStatusCode().equals(MaterialOrderStatusEnum.PICKING_COMPLETED.getCode())) {
            throw new InvalidOperationException("只有狀態為「已揀料」的領料單才能完成領料。");
        }

        order.setMaterialOrderStatusCode(status.getCode());
        for (DispatchMaterialOrderItem item : order.getItems()) {
            item.setCollectedQuantity(item.getPickedQuantity());
        }
        dispatchMaterialOrderRepository.save(order);

        // Check related dispatch repair order
        UUID dispatchRepairItemId = order.getItems().get(0).getDispatchRepairItemId();
        if (dispatchRepairItemId != null) {
            DispatchRepair dispatchRepair = dispatchRepairItemRepository.findByDispatchRepairItemId(dispatchRepairItemId).getDispatchRepair();
            UUID dispatchRepairId = dispatchRepair.getDispatchRepairId();
            List<DispatchRepairItem> repairItems = dispatchRepairItemRepository.findAllByDispatchRepair_DispatchRepairId(dispatchRepairId);

            boolean allCollected = true;
            if (repairItems !=null && repairItems.size()>0) {
                //從派工項目檢查該領料項目是否已經全部領料
                for (DispatchRepairItem repairItem : repairItems) {
                    Integer collectQuantity = dispatchMaterialOrderItemRepository.sumCollectedByDispatchRepairItemId(repairItem.getDispatchRepairItemId());
                    if (repairItem.getQuantity().compareTo(collectQuantity) != 0) {
                        allCollected = false;
                    }
                    if (!allCollected) break;
                }
            }
            log.info("completeMaterialCollection dispatchRepairId: {},  allCollected: {}", dispatchRepairId, allCollected);

            if (allCollected) {
                if (MaterialOrderStatusEnum.COLLECTED==status) {
                    dispatchRepair.setStatusCode(DispatchStatusEnum.DEPARTED.getCode());
                } else if (MaterialOrderStatusEnum.REFUND_COLLECTED==status) {
                    dispatchRepair.setStatusCode(DispatchStatusEnum.REFUND_ACCOUNT_CONFIRM.getCode());
                }
                dispatchRepairRepository.save(dispatchRepair);

                DispatchTechRecord record = new DispatchTechRecord();
                record.setDispatchRepairId(dispatchRepairId);
                record.setStatusCode(DispatchStatusEnum.DEPARTED.getCode());
                record.setTechnicianId(dispatchRepair.getAssignedTechnicianId());
                record.setRecordType(RecordTypeEnum.REMARKS.getCode());
                record.setRecord1("已完成領料");
                dispatchTechRecordRepository.save(record);
            }
        }
    }
} 