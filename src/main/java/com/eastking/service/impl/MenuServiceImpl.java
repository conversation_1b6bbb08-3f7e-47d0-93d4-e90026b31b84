package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.enums.FunctionTypeEnum;
import com.eastking.enums.PermissionStatusEnum;
import com.eastking.enums.MenuVisibilityEnum;
import com.eastking.model.dto.response.UserMenuDto;
import com.eastking.model.entity.Role;
import com.eastking.model.entity.RoleFunctionPermission;
import com.eastking.model.entity.SystemFunction;
import com.eastking.model.entity.UserAccount;
import com.eastking.model.entity.UserRoleMap;
import com.eastking.repository.RoleFunctionPermissionRepository;
import com.eastking.repository.SystemFunctionRepository;
import com.eastking.repository.UserRoleMapRepository;
import com.eastking.security.UserDetailsImpl;
import com.eastking.service.MenuService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MenuServiceImpl implements MenuService {

    private final UserRoleMapRepository userRoleMapRepository;
    private final RoleFunctionPermissionRepository roleFunctionPermissionRepository;
    private final SystemFunctionRepository systemFunctionRepository;

    @Override
    @Transactional(readOnly = true)
    public List<UserMenuDto> getUserSidebarMenu() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || !(authentication.getPrincipal() instanceof UserDetailsImpl)) {
            return Collections.emptyList();
        }
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        UUID userAccountId = userDetails.getId();

        // 1. Get user's roles
        List<UserRoleMap> userRoleMaps = userRoleMapRepository.findByUserAccountUserAccountIdAndIsDeleted(
                userAccountId, DeleteStatusEnum.NOT_DELETED.getCode());
        List<Role> userRoles = userRoleMaps.stream().map(UserRoleMap::getRole).collect(Collectors.toList());
        if (userRoles.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. Get all function permissions for these roles where canRead is true
        List<RoleFunctionPermission> permissions = roleFunctionPermissionRepository.findByRoleInAndIsDeleted(
                userRoles, DeleteStatusEnum.NOT_DELETED.getCode());

        Set<UUID> readableFunctionIds = permissions.stream()
                .filter(p -> p.getCanRead().equals(PermissionStatusEnum.HAS_PERMISSION.getCode()))
                .map(p -> p.getSystemFunction().getSystemFunctionId())
                .collect(Collectors.toSet());

        if (readableFunctionIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. Fetch all SystemFunction entities that are readable and not deleted
        List<SystemFunction> allAllowedFunctions = systemFunctionRepository.findAllById(readableFunctionIds).stream()
                .filter(sf -> sf.getIsDeleted().equals(DeleteStatusEnum.NOT_DELETED.getCode()))
                .filter(sf -> sf.getIsShow().equals(MenuVisibilityEnum.SHOW.getCode()))
                .collect(Collectors.toList());
        
        // Ensure parent groups are included if any child is readable, even if the group itself has no direct read permission record
        // or if its read permission wasn't explicitly set but children are accessible.
        Set<SystemFunction> finalFunctionsToDisplay = new HashSet<>(allAllowedFunctions);
        allAllowedFunctions.forEach(sf -> {
            if (sf.getParentFunctionCode() != null) {
                systemFunctionRepository.findByFunctionCodeAndIsDeleted(sf.getParentFunctionCode(), DeleteStatusEnum.NOT_DELETED.getCode())
                    .ifPresent(finalFunctionsToDisplay::add);
            }
        });

        // 4. Build the hierarchical menu
        List<SystemFunction> functionsToBuild = new ArrayList<>(finalFunctionsToDisplay);
        functionsToBuild.sort(Comparator.comparing(SystemFunction::getSequenceOrder, Comparator.nullsLast(Comparator.naturalOrder()))
                                       .thenComparing(SystemFunction::getFunctionName, Comparator.nullsLast(Comparator.naturalOrder())));

        Map<String, UserMenuDto> menuMap = new LinkedHashMap<>(); // Preserve insertion order for root items
        functionsToBuild.forEach(sf -> {
            UserMenuDto dto = convertToUserMenuDto(sf);
            menuMap.put(sf.getFunctionCode(), dto);
        });

        List<UserMenuDto> rootMenus = new ArrayList<>();
        menuMap.forEach((code, dto) -> {
            SystemFunction sf = functionsToBuild.stream().filter(f -> f.getFunctionCode().equals(code)).findFirst().orElse(null);
            if (sf != null && sf.getParentFunctionCode() != null && menuMap.containsKey(sf.getParentFunctionCode())) {
                UserMenuDto parentDto = menuMap.get(sf.getParentFunctionCode());
                if (parentDto.getChildren() == null) {
                    parentDto.setChildren(new ArrayList<>());
                }
                parentDto.getChildren().add(dto);
            } else {
                rootMenus.add(dto);
            }
        });
        
        // Sort children within each DTO
        rootMenus.forEach(this::sortMenuChildrenRecursive);

        return rootMenus;
    }

    private UserMenuDto convertToUserMenuDto(SystemFunction sf) {
        return UserMenuDto.builder()
                .code(sf.getFunctionCode())
                .name(sf.getFunctionName())
                .type(sf.getFunctionType().equals(FunctionTypeEnum.GROUP.getCode()) ? "GROUP" : "OPERATION")
                .path(sf.getFrontendPath())
                .icon(sf.getIconClass())
                .sequenceOrder(sf.getSequenceOrder())
                .children(new ArrayList<>()) // Initialize for potential children
                .build();
    }
    
    private void sortMenuChildrenRecursive(UserMenuDto menuDto) {
        if (menuDto.getChildren() != null && !menuDto.getChildren().isEmpty()) {
            menuDto.getChildren().sort(Comparator.comparing(UserMenuDto::getSequenceOrder, Comparator.nullsLast(Comparator.naturalOrder()))
                                               .thenComparing(UserMenuDto::getName, Comparator.nullsLast(Comparator.naturalOrder())));
            menuDto.getChildren().forEach(this::sortMenuChildrenRecursive);
        }
    }
} 