package com.eastking.service.impl;

import com.eastking.model.dto.AuditLogDto;
import com.eastking.model.dto.AuditLogQueryDto;
import com.eastking.model.entity.AuditLog;
import com.eastking.model.entity.Dept;
import com.eastking.model.entity.UserAccount; // For potential future use if needed
import com.eastking.repository.AuditLogRepository;
import com.eastking.repository.UserAccountRepository; // For fetching user details if needed by ID
import com.eastking.service.AuditLogService;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import com.eastking.util.RequestUtil;
import com.eastking.util.SecurityUtil;
import com.eastking.security.UserDetailsImpl;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;


import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.util.CollectionUtils;
import jakarta.persistence.criteria.Predicate;


import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Map; // Added for Map.of

@Service
public class AuditLogServiceImpl implements AuditLogService {

    private final AuditLogRepository auditLogRepository;
    private final UserAccountRepository userAccountRepository; // To fetch user display name if only ID is available
    private final ObjectMapper objectMapper;


    public AuditLogServiceImpl(AuditLogRepository auditLogRepository, 
                               UserAccountRepository userAccountRepository) {
        this.auditLogRepository = auditLogRepository;
        this.userAccountRepository = userAccountRepository;
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule()); // For OffsetDateTime serialization in detailsJson
    }

    private AuditLogDto convertToDto(AuditLog entity) {
        AuditLogDto dto = new AuditLogDto();
        BeanUtils.copyProperties(entity, dto);
        // Ensure enums are correctly represented if needed, though actionType is already Enum in DTO
        return dto;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW) // Ensure audit log is saved even if main transaction rolls back
    public void logAction(UUID userAccountId, String employeeId, String userName, String userDepartmentName,
                          AuditActionTypeEnum actionType, AuditDataTypeEnum dataType,
                          String entityIdStr, String entityDescription, String detailsJson,
                          String clientIpAddress, String traceId) {
        try {
            AuditLog auditLog = new AuditLog();
            auditLog.setUserAccountId(userAccountId);
            auditLog.setEmployeeId(employeeId);
            auditLog.setUserName(userName);
            auditLog.setUserDepartmentName(userDepartmentName); // Can be enhanced
            auditLog.setOperationTime(OffsetDateTime.now());
            auditLog.setActionType(actionType);
            auditLog.setDataType(dataType != null ? dataType.getCode() : AuditDataTypeEnum.UNKNOWN.getCode());
            auditLog.setEntityIdStr(entityIdStr);
            auditLog.setEntityDescription(entityDescription);
            
            if (detailsJson != null && detailsJson.length() > 4000) { // Example length limit for DB
                 auditLog.setDetailsJson(detailsJson.substring(0, 3997) + "...");
            } else {
                auditLog.setDetailsJson(detailsJson);
            }

            auditLog.setClientIpAddress(clientIpAddress);
            auditLog.setTraceId(traceId);

            auditLogRepository.save(auditLog);
        } catch (Exception e) {
            // Log error during audit logging, but don't let it break the main operation
            System.err.println("Error saving audit log: " + e.getMessage());
            // Consider using a dedicated logger: logger.error("Error saving audit log", e);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logAction(AuditActionTypeEnum actionType, AuditDataTypeEnum dataType, String entityIdStr, String entityDescription, String detailsJson) {
        Optional<UserDetailsImpl> currentUserOpt = SecurityUtil.getCurrentUserDetails();
        UUID currentUserId = currentUserOpt.map(UserDetailsImpl::getId).orElse(null);
        String currentEmployeeId = currentUserOpt.map(UserDetailsImpl::getUsername).orElse("SYSTEM");
        String currentUserName = currentUserOpt.map(UserDetailsImpl::getUserDisplayName).orElse("System Process");
        
        // --- NEW LOGIC to get department name ---
        String currentDeptName = currentUserOpt
            .flatMap(ud -> userAccountRepository.findById(ud.getId())) // Fetch the full UserAccount entity
            .map(UserAccount::getDept) // Get the associated Dept entity
            .map(Dept::getDeptDesc) // Get the department description
            .orElse("未分配"); // Fallback value

        String clientIp = RequestUtil.getClientIpAddress();
        String traceId = RequestUtil.getTraceId();

        String safeDetailsJson = detailsJson;
        try {
            if (detailsJson != null && detailsJson.length() > 4000) { // Prevent too large JSON
                safeDetailsJson = objectMapper.writeValueAsString(Map.of("truncated", true, "original_length", detailsJson.length()));
            }
        } catch (JsonProcessingException e) {
            safeDetailsJson = "{\"error_serializing_details\": \"" + e.getMessage() + "\"}";
        }


        logAction(currentUserId, currentEmployeeId, currentUserName, currentDeptName,
                  actionType, dataType, entityIdStr, entityDescription, safeDetailsJson, clientIp, traceId);
    }


    @Override
    @Transactional(readOnly = true)
    public Page<AuditLogDto> getAuditLogs(AuditLogQueryDto queryDto, Pageable pageable) {
        Specification<AuditLog> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (queryDto.getStartTime() != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("operationTime"), queryDto.getStartTime()));
            }
            if (queryDto.getEndTime() != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("operationTime"), queryDto.getEndTime()));
            }
            if (!CollectionUtils.isEmpty(queryDto.getDataTypes())) {
                predicates.add(root.get("dataType").in(queryDto.getDataTypes()));
            }
            if (StringUtils.hasText(queryDto.getDepartmentName())) {
                predicates.add(cb.like(cb.lower(root.get("userDepartmentName")), "%" + queryDto.getDepartmentName().toLowerCase() + "%"));
            }
            if (StringUtils.hasText(queryDto.getEmployeeId())) {
                predicates.add(cb.equal(root.get("employeeId"), queryDto.getEmployeeId()));
            }
            if (StringUtils.hasText(queryDto.getUserName())) {
                predicates.add(cb.like(cb.lower(root.get("userName")), "%" + queryDto.getUserName().toLowerCase() + "%"));
            }
            if (StringUtils.hasText(queryDto.getActionType())) {
                try {
                    AuditActionTypeEnum actionEnum = AuditActionTypeEnum.fromCode(queryDto.getActionType());
                     if (actionEnum != AuditActionTypeEnum.OTHER || "OTHER".equalsIgnoreCase(queryDto.getActionType())) { // Allow querying "OTHER"
                        predicates.add(cb.equal(root.get("actionType"), actionEnum));
                    }
                } catch (IllegalArgumentException e) {
                    // Ignore if action type is invalid
                }
            }

            if (StringUtils.hasText(queryDto.getSearchText())) {
                String likePattern = "%" + queryDto.getSearchText().toLowerCase() + "%";
                predicates.add(cb.or(
                    cb.like(cb.lower(root.get("userName")), likePattern),
                    cb.like(cb.lower(root.get("employeeId")), likePattern),
                    cb.like(cb.lower(root.get("entityIdStr")), likePattern),
                    cb.like(cb.lower(root.get("entityDescription")), likePattern),
                    cb.like(cb.lower(root.get("dataType")), likePattern) 
                ));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return auditLogRepository.findAll(spec, pageable).map(this::convertToDto);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<String> getDistinctDataTypes() {
        // This could be slow on large tables. Consider a dedicated table or caching if performance is an issue.
        // For now, a simple distinct query on the audit log table itself.
        // This requires a custom query in AuditLogRepository or using EntityManager.
        // Placeholder:
        return auditLogRepository.findAll().stream()
                                 .map(AuditLog::getDataType)
                                 .filter(StringUtils::hasText)
                                 .distinct()
                                 .sorted()
                                 .collect(Collectors.toList());
        // A better way would be:
        // return auditLogRepository.findDistinctDataTypes(); // (Requires custom query in repository)
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getDistinctUserDepartmentNames() {
        // Similar to getDistinctDataTypes, placeholder for now.
        return auditLogRepository.findAll().stream()
                                 .map(AuditLog::getUserDepartmentName)
                                 .filter(StringUtils::hasText)
                                 .distinct()
                                 .sorted()
                                 .collect(Collectors.toList());
        // A better way would be:
        // return auditLogRepository.findDistinctUserDepartmentNames(); // (Requires custom query in repository)
    }
} 