package com.eastking.service.impl;

import com.eastking.model.dto.TechnicianAreaConfigDto;
import com.eastking.model.entity.TechnicianAreaConfig;
import com.eastking.repository.TechnicianAreaConfigRepository;
import com.eastking.service.TechnicianAreaConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TechnicianAreaConfigServiceImpl implements TechnicianAreaConfigService {

    private final TechnicianAreaConfigRepository repository;

    @Override
    @Transactional(readOnly = true)
    public List<TechnicianAreaConfigDto> findByMonth(String month) {
        return repository.findByConfigMonth(month).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void saveConfig(String month, List<TechnicianAreaConfigDto> configs) {
        // First, delete all existing configurations for the given month.
        repository.deleteByConfigMonth(month);

        // Then, save the new configurations.
        List<TechnicianAreaConfig> entities = configs.stream()
                .map(dto -> convertToEntity(month, dto))
                .collect(Collectors.toList());
        
        repository.saveAll(entities);
    }

    private TechnicianAreaConfigDto convertToDto(TechnicianAreaConfig entity) {
        return TechnicianAreaConfigDto.builder()
                .technicianId(entity.getTechnicianId())
                .technicianName(entity.getTechnicianName())
                .serviceAreas(entity.getServiceAreas())
                .build();
    }

    private TechnicianAreaConfig convertToEntity(String month, TechnicianAreaConfigDto dto) {
        TechnicianAreaConfig entity = new TechnicianAreaConfig();
        entity.setConfigMonth(month);
        entity.setTechnicianId(dto.getTechnicianId());
        entity.setTechnicianName(dto.getTechnicianName());
        entity.setServiceAreas(dto.getServiceAreas());
        return entity;
    }
} 