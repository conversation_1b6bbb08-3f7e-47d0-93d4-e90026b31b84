package com.eastking.service.impl;

import com.eastking.model.entity.DispatchRepair;
import com.eastking.model.entity.DispatchRepairHistory;
import com.eastking.model.entity.UserAccount;
import com.eastking.repository.DispatchRepairHistoryRepository;
import com.eastking.service.DispatchRepairLogService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;

@Service
@RequiredArgsConstructor
public class DispatchRepairLogServiceImpl implements DispatchRepairLogService {

    private static final Logger logger = LoggerFactory.getLogger(DispatchRepairLogServiceImpl.class);

    private final DispatchRepairHistoryRepository historyRepository;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public void logChange(DispatchRepair dispatchRepair, Short previousStatus, Short newStatus, UserAccount changeByUser, String changeReason, Object requestPayload) {
        try {
            DispatchRepairHistory history = new DispatchRepairHistory();
            history.setDispatchRepairId(dispatchRepair.getDispatchRepairId());
            history.setDispatchRepairNumber(dispatchRepair.getDispatchRepairNumber());
            history.setPreviousStatusCode(previousStatus);
            history.setNewStatusCode(newStatus);
            history.setChangeByUserId(changeByUser.getUserAccountId());
            history.setChangeTime(OffsetDateTime.now());
            history.setTechnicianId(dispatchRepair.getAssignedTechnicianId());
            history.setChangeReason(changeReason);

            if (requestPayload != null) {
                try {
                    history.setRequestPayload(objectMapper.writeValueAsString(requestPayload));
                } catch (JsonProcessingException e) {
                    logger.error("Error serializing request payload for dispatch repair history: {}", dispatchRepair.getDispatchRepairId(), e);
                    history.setRequestPayload("{\"error\":\"Failed to serialize payload\"}");
                }
            }

            historyRepository.save(history);
        } catch (Exception e) {
            logger.error("Failed to save dispatch repair history for dispatchRepairId: {}", dispatchRepair.getDispatchRepairId(), e);
            // We catch the exception to prevent the main transaction from rolling back
            // just because the history logging failed.
        }
    }
} 