package com.eastking.service.impl;

import com.eastking.model.dto.StoreInventoryDto;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.service.WarehouseStoreInventoryService;
import com.eastking.enums.DeleteStatusEnum;
import com.eastking.exception.ResourceNotFoundException;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;
import com.eastking.util.SecurityUtil;
import com.eastking.enums.InventoryTransactionTypeEnum;
import java.util.stream.Collectors;
import com.eastking.model.entity.WarehouseInventoryTransactionLog;
import com.eastking.repository.WarehouseInventoryTransactionLogRepository;
import com.eastking.security.UserDetailsImpl;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class WarehouseStoreInventoryServiceImpl implements WarehouseStoreInventoryService {
    private static final Logger logger = LoggerFactory.getLogger(WarehouseStoreInventoryServiceImpl.class);

    private final StoreInventoryRepository storeInventoryRepository;
    private final StoreRepository storeRepository;
    private final ProductSettingRepository productSettingRepository;
    private final StoreInventoryTransactionLogRepository transactionLogRepository;
    private final WarehouseRepository warehouseRepository;
    private final WarehouseInventoryRepository warehouseInventoryRepository;
    private final UserAccountRepository userAccountRepository;
    private final WarehouseInventoryTransactionLogRepository warehouseInventoryTransactionLogRepository;
    // private final AuditLogService auditLogService; // For logging stock changes

    @Override
    @Transactional(readOnly = true)
    public Page<StoreInventoryDto> getStoreInventory(UUID storeId, String keyword, Pageable pageable) {
        storeRepository.findById(storeId)
            .filter(s -> s.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("Store", "ID", storeId));

        Specification<StoreInventory> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("store").get("storeId"), storeId));
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));

            if (StringUtils.hasText(keyword)) {
                Predicate barcodeMatch = cb.like(cb.lower(root.get("productBarcode")), "%" + keyword.toLowerCase() + "%");
                Predicate nameMatch = cb.like(cb.lower(root.get("productName")), "%" + keyword.toLowerCase() + "%");
                predicates.add(cb.or(barcodeMatch, nameMatch));
            }
            // Add more filters if needed (e.g., quantity range, category from product master)
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return storeInventoryRepository.findAll(spec, pageable).map(this::convertToStoreInventoryDto);
    }

    @Override
    @Transactional(readOnly = true)
    public StoreInventoryDto getSingleStoreInventoryItem(UUID storeId, String productBarcode) {
        StoreEntity store = storeRepository.findById(storeId)
            .filter(s -> s.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("Store", "ID", storeId));
        
        StoreInventory inventory = storeInventoryRepository.findByStoreAndProductBarcodeAndIsDeleted(store, productBarcode, DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("StoreInventory", "storeId and productBarcode", storeId + ", " + productBarcode));
        return convertToStoreInventoryDto(inventory);
    }

    @Override
    @Transactional(readOnly = true)
    public List<StoreInventoryDto> getAllStoreInventory(UUID storeId) {
        storeRepository.findById(storeId)
            .filter(s -> s.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("Store", "ID", storeId));

        List<StoreInventory> inventoryList = storeInventoryRepository.findByStore_StoreIdAndIsDeleted(storeId, DeleteStatusEnum.NOT_DELETED.getCode());
        
        return inventoryList.stream()
                .map(this::convertToStoreInventoryDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void updateInventory(UUID storeId, String productBarcode, String productName, int quantityChange, String transactionType, String transactionId) {
        StoreEntity store = storeRepository.findById(storeId)
                .orElseThrow(() -> new ResourceNotFoundException("Store", "id", storeId));

        StoreInventory inventory = storeInventoryRepository.findByStoreAndProductBarcodeAndIsDeleted(store, productBarcode, DeleteStatusEnum.NOT_DELETED.getCode())
                .orElseGet(() -> {
                    StoreInventory newInventory = new StoreInventory();
                    newInventory.setStore(store);
                    newInventory.setProductBarcode(productBarcode);
                    newInventory.setProductName(productName); // Set product name for new inventory records
                    newInventory.setQuantityOnHand(0);
                    newInventory.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                    // createBy, createTime, updateBy, updateTime are handled by BaseEntity and Auditing
                    return newInventory;
                });

        int quantityBefore = inventory.getQuantityOnHand();
        inventory.setQuantityOnHand(quantityBefore + quantityChange);
        inventory.setLastStockUpdateTime(OffsetDateTime.now());
        StoreInventory savedInventory = storeInventoryRepository.save(inventory);

        // Log the transaction
        InventoryTransactionTypeEnum typeEnum = InventoryTransactionTypeEnum.valueOf(transactionType);
        UUID currentUserId = SecurityUtil.getCurrentUserId().orElse(null); 

        StoreInventoryTransactionLog logEntry = StoreInventoryTransactionLog.builder()
            .store(store)
            .productBarcode(productBarcode)
            .transactionType(typeEnum)
            .quantityChange(quantityChange)
            .quantityAfterTransaction(savedInventory.getQuantityOnHand())
            .transactionDate(OffsetDateTime.now())
            .referenceDocumentId(transactionId != null ? UUID.fromString(transactionId) : null) // Assuming transactionId is UUID of the order
            // .referenceDocumentNumber() // TODO: Get this from the order if possible or needed
            .createBy(currentUserId)
            .notes(typeEnum.getDescription() + (transactionId != null ? " - Ref ID: " + transactionId : ""))
            .build();
        transactionLogRepository.save(logEntry);
    }

    @Override
    @Transactional
    public void updateInventoryForWarehouse(UUID warehouseId, String productBarcode, String productName, int quantityChange, String transactionType, String transactionId) {
        Warehouse warehouse = warehouseRepository.findById(warehouseId)
                .orElseThrow(() -> new ResourceNotFoundException("Warehouse", "id", warehouseId));

        // For HQ/Warehouse, we might use a convention for store_id (e.g., a dummy one or null) 
        // or adapt sm_store_inventory to directly support warehouse_id, or have a separate hq_inventory table.
        // Current sm_store_inventory links to sm_store. 
        // For this example, we will log it against the warehouse in the transaction log,
        // but sm_store_inventory itself is more complex to adapt directly without schema change for warehouse-only stock.
        // A common approach is a dedicated "HQ Store" entity or handling HQ stock in a separate system/table.
        // Given the current structure, we'll focus on the LOGGING aspect primarily for warehouse.
        // Actual quantityOnHand for a pure warehouse (not a store) isn't directly managed by sm_store_inventory in its current form.
        // This implies HQ stock might be tracked differently or this method mainly logs movements for HQ.
        // Let's assume for logging, `quantityAfterTransaction` for HQ might be a conceptual value or fetched from another source if available.

        logger.info("Logging HQ/Warehouse inventory change: WarehouseID={}, ProductBarcode={}, Change={}, TxType={}, TxID={}", 
            warehouseId, productBarcode, quantityChange, transactionType, transactionId);

        InventoryTransactionTypeEnum typeEnum = InventoryTransactionTypeEnum.valueOf(transactionType);
        UUID currentUserId = SecurityUtil.getCurrentUserId().orElse(null); 
        
        // For HQ, quantity_after_transaction might be tricky if not directly tracked in sm_store_inventory
        // We'll put a placeholder or assume it can be calculated/retrieved if an HQ inventory system exists.
        // For now, let's put 0 as a placeholder for quantityAfterTransaction for HQ if not otherwise determined.
        // In a real scenario, this would need to integrate with how HQ stock is actually managed.
        int conceptualHqStockAfter = 0; // Placeholder - needs real logic if HQ stock is tracked this way

        StoreInventoryTransactionLog logEntry = StoreInventoryTransactionLog.builder()
            .warehouse(warehouse) // Link to warehouse instead of store
            .productBarcode(productBarcode)
            .transactionType(typeEnum)
            .quantityChange(quantityChange)
            .quantityAfterTransaction(conceptualHqStockAfter) // Placeholder - HQ stock might be managed differently
            .transactionDate(OffsetDateTime.now())
            .referenceDocumentId(transactionId != null ? UUID.fromString(transactionId) : null)
            .createBy(currentUserId)
            .notes("HQ " + typeEnum.getDescription() + (transactionId != null ? " - Ref ID: " + transactionId : ""))
            .build();
        transactionLogRepository.save(logEntry);
    }

    @Override
    public Integer getStockQuantity(UUID warehouseId, String productBarcode) {
        // This logic now correctly uses storeId as a proxy for warehouseId for store-based inventory.
        // For technician stock, warehouseId is the technician's ID, which should be linked to a store.
        Warehouse warehouse = warehouseRepository.findByWarehouseId(warehouseId);
        return warehouseInventoryRepository.findByWarehouseAndProductBarcode(warehouse, productBarcode)
                .map(WarehouseInventory::getQuantityOnHand)
                .orElse(0);
    }
    
    //技師車存倉的 warehouse_id 就是技師自己的 user_account_id，不用特別做關聯
    @Override
    public List<StoreInventoryDto> searchTechnicianStock(UUID technicianId, String keyword) {
        // As per business rule, technician's user_account_id IS the warehouse_id for their van stock.
        UUID warehouseId = technicianId;

        List<WarehouseInventory> inventoryItems;

        if (StringUtils.hasText(keyword)) {
            inventoryItems = warehouseInventoryRepository.findByWarehouse_WarehouseIdAndProductNameContainingIgnoreCase(warehouseId, keyword);
        } else {
            inventoryItems = warehouseInventoryRepository.findByWarehouse_WarehouseId(warehouseId);
        }

        if (inventoryItems.isEmpty()) {
            return Collections.emptyList();
        }

        // --- N+1 Optimization: Fetch all prices in one go ---
        List<String> barcodes = inventoryItems.stream()
            .map(WarehouseInventory::getProductBarcode)
            .collect(Collectors.toList());
        
        Map<String, BigDecimal> priceMap = productSettingRepository.findByProductBarcodeInAndIsDeleted(barcodes, DeleteStatusEnum.NOT_DELETED.getCode())
            .stream()
            .collect(Collectors.toMap(ProductSetting::getProductBarcode, ps -> ps.getSalePrice() != null ? ps.getSalePrice() : BigDecimal.ZERO));

        // --- End Optimization ---

        return inventoryItems.stream()
                .map(entity -> {
                    StoreInventoryDto dto = convertToWarehouseInventoryDto(entity);
                    // Manually set the sale price from the map
                    dto.setSalePrice(priceMap.getOrDefault(entity.getProductBarcode(), BigDecimal.ZERO));
                    return dto;
                })
                .collect(Collectors.toList());
    }

    //技師車存倉的 warehouse_id 就是技師自己的 user_account_id，不用特別做關聯
    @Override
    public void updateTechnicianInventory(UUID technicianId, String productBarcode, String productName, int quantityChange, InventoryTransactionTypeEnum transactionType, String referenceNumber) {
        UUID warehouseId = technicianId; // Per business rule

        Warehouse warehouse = warehouseRepository.findById(warehouseId)
            .orElseThrow(() -> new ResourceNotFoundException("找不到技師對應的虛擬倉庫，ID: " + warehouseId));
            
        WarehouseInventory inventory = warehouseInventoryRepository.findByWarehouse_WarehouseIdAndProductBarcode(warehouse.getWarehouseId(), productBarcode)
                .orElseGet(() -> {
                    WarehouseInventory newInventory = new WarehouseInventory();
                    newInventory.setWarehouse(warehouse);
                    newInventory.setProductBarcode(productBarcode);
                    newInventory.setProductName(productName);
                    newInventory.setQuantityOnHand(0);
                    return newInventory;
                });

        int oldQuantity = inventory.getQuantityOnHand();
        int newQuantity = oldQuantity + quantityChange;
        inventory.setQuantityOnHand(newQuantity);
        warehouseInventoryRepository.save(inventory);

        // Log the transaction
        WarehouseInventoryTransactionLog log = new WarehouseInventoryTransactionLog();
        log.setWarehouseInventory(inventory);
        log.setTransactionType(transactionType.getCode());
        log.setQuantityChange(quantityChange);
        log.setOldQuantity(oldQuantity);
        log.setNewQuantity(newQuantity);
        log.setReferenceNumber(referenceNumber);
        log.setCreatedBy(SecurityUtil.getCurrentUserDetails().map(UserDetailsImpl::getId).orElse(null));
        warehouseInventoryTransactionLogRepository.save(log);
    }

    private StoreInventoryDto convertToStoreInventoryDto(StoreInventory entity) {
        StoreInventoryDto dto = new StoreInventoryDto();
        BeanUtils.copyProperties(entity, dto);
        if (entity.getStore() != null) {
            dto.setStoreId(entity.getStore().getStoreId());
            dto.setStoreName(entity.getStore().getStoreName());
        }
        return dto;
    }

    private StoreInventoryDto convertToWarehouseInventoryDto(WarehouseInventory entity) {
        StoreInventoryDto dto = new StoreInventoryDto();
        BeanUtils.copyProperties(entity, dto);
        if (entity.getWarehouse() != null) {
            dto.setStoreId(entity.getWarehouseInventoryId());
            dto.setStoreName(entity.getWarehouse().getWarehouseName());
        }
        return dto;
    }
} 