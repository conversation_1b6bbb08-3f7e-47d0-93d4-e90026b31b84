package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.exception.DataConflictException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.request.RolePermissionDataRequest;
import com.eastking.model.dto.response.RoleDetailResponse;
import com.eastking.model.dto.response.RoleSummaryResponse;
import com.eastking.model.dto.response.SystemFunctionResponseDto;
import com.eastking.model.entity.Role;
import com.eastking.model.entity.RoleFunctionPermission;
import com.eastking.model.entity.SystemFunction;
import com.eastking.model.entity.UserAccount;
import com.eastking.repository.RoleFunctionPermissionRepository;
import com.eastking.repository.RoleRepository;
import com.eastking.repository.SystemFunctionRepository;
import com.eastking.repository.UserAccountRepository;
import com.eastking.service.RolePermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class RolePermissionServiceImpl implements RolePermissionService {

    private final RoleRepository roleRepository;
    private final SystemFunctionRepository systemFunctionRepository;
    private final RoleFunctionPermissionRepository roleFunctionPermissionRepository;
    private final UserAccountRepository userAccountRepository;

    @Override
    @Transactional
    public RoleDetailResponse createRoleWithPermissions(RolePermissionDataRequest request) {
        roleRepository.findByRoleCodeAndIsDeleted(request.getRoleCode(), DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(r -> { throw new DataConflictException("角色代碼已存在: " + request.getRoleCode()); });

        Role newRole = new Role();
        newRole.setRoleCode(request.getRoleCode());
        newRole.setRoleName(request.getRoleName());
        newRole.setRoleDescription(request.getRoleDescription());
        newRole.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        Role savedRole = roleRepository.save(newRole);
        updatePermissionsForRole(savedRole, request);
        return getRoleWithPermissionsById(savedRole.getRoleId());
    }

    @Override
    @Transactional
    public RoleDetailResponse updateRoleWithPermissions(UUID roleId, RolePermissionDataRequest request) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role", "ID", roleId));
        
        if (!role.getRoleCode().equals(request.getRoleCode())) {
            roleRepository.findByRoleCodeAndIsDeleted(request.getRoleCode(), DeleteStatusEnum.NOT_DELETED.getCode())
                .filter(r -> !r.getRoleId().equals(roleId))
                .ifPresent(r -> { throw new DataConflictException("角色代碼已存在: " + request.getRoleCode()); });
        }
        log.debug("roleId/roleName: {}/{}", roleId, request.getRoleName());
        
        role.setRoleCode(request.getRoleCode());
        role.setRoleName(request.getRoleName());
        role.setRoleDescription(request.getRoleDescription());
        roleRepository.save(role);
        updatePermissionsForRole(role, request);
        return getRoleWithPermissionsById(roleId);
    }

    private void updatePermissionsForRole(Role role, RolePermissionDataRequest request) {
        // 1. 獲取現有的權限並放入 Map
        Map<UUID, RoleFunctionPermission> existingPermissions = roleFunctionPermissionRepository.findByRole(role)
                .stream()
                .collect(Collectors.toMap(p -> p.getSystemFunction().getSystemFunctionId(), p -> p));

        if (request.getFunctionPermissions() != null) {
            for (RolePermissionDataRequest.FunctionPermissionDto permDto : request.getFunctionPermissions()) {
                RoleFunctionPermission permission = existingPermissions.get(permDto.getSystemFunctionId());

                if (permission != null) {
                    // 2. 如果存在，則更新
                    permission.setCanCreate(permDto.getCanCreate());
                    permission.setCanRead(permDto.getCanRead());
                    permission.setCanUpdate(permDto.getCanUpdate());
                    permission.setCanDelete(permDto.getCanDelete());
                    permission.setCanApprove(permDto.getCanApprove());
                    permission.setCanChangePrice(permDto.getCanChangePrice());
                    permission.setCanPrint(permDto.getCanPrint());
                    permission.setCanStoreApprove(permDto.getCanStoreApprove());
                    permission.setCanDispatchApprove(permDto.getCanDispatchApprove());
                    roleFunctionPermissionRepository.save(permission);
                    // 從 Map 中移除，剩下的就是需要刪除的
                    existingPermissions.remove(permDto.getSystemFunctionId());
                } else {
                    // 3. 如果不存在，則新增
                    systemFunctionRepository.findById(permDto.getSystemFunctionId()).ifPresent(function -> {
                        RoleFunctionPermission newPerm = new RoleFunctionPermission();
                        newPerm.setRole(role);
                        newPerm.setSystemFunction(function);
                        newPerm.setCanCreate(permDto.getCanCreate());
                        newPerm.setCanRead(permDto.getCanRead());
                        newPerm.setCanUpdate(permDto.getCanUpdate());
                        newPerm.setCanDelete(permDto.getCanDelete());
                        newPerm.setCanApprove(permDto.getCanApprove());
                        newPerm.setCanChangePrice(permDto.getCanChangePrice());
                        newPerm.setCanPrint(permDto.getCanPrint());
                        newPerm.setCanStoreApprove(permDto.getCanStoreApprove());
                        newPerm.setCanDispatchApprove(permDto.getCanDispatchApprove());
                        roleFunctionPermissionRepository.save(newPerm);
                    });
                }
            }
        }
        
        // 4. 刪除 Map 中剩餘的（即前端未傳入的）權限
        if (!existingPermissions.isEmpty()) {
            roleFunctionPermissionRepository.deleteAll(existingPermissions.values());
        }
        
        log.debug("Role permissions updated for role: {}", role.getRoleName());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<SystemFunctionResponseDto> getAllSystemFunctionsForPermissionSetting() {
        List<SystemFunction> rootFunctions = systemFunctionRepository.findByParentFunctionCodeIsNullOrderBySequenceOrderAsc();
        return rootFunctions.stream()
                .map(this::convertSystemFunctionToDto)
                .collect(Collectors.toList());
    }

    private SystemFunctionResponseDto convertSystemFunctionToDto(SystemFunction function) {
        List<String> availablePermissions = new ArrayList<>();
        if (function.getCanCreate() == 1) availablePermissions.add("CREATE");
        if (function.getCanRead() == 1) availablePermissions.add("READ");
        if (function.getCanUpdate() == 1) availablePermissions.add("UPDATE");
        if (function.getCanDelete() == 1) availablePermissions.add("DELETE");
        if (function.getCanApprove() == 1) availablePermissions.add("APPROVE");
        if (function.getCanChangePrice() == 1) availablePermissions.add("CHANGE_PRICE");
        if (function.getCanPrint() == 1) availablePermissions.add("PRINT");
        if (function.getCanStoreApprove() == 1) availablePermissions.add("STORE_APPROVE");
        if (function.getCanDispatchApprove() == 1) availablePermissions.add("DISPATCH_APPROVE");

        List<SystemFunctionResponseDto> childrenDto = (function.getChildren() != null && !function.getChildren().isEmpty()) ?
                function.getChildren().stream()
                    .sorted(Comparator.comparing(SystemFunction::getSequenceOrder, Comparator.nullsLast(Integer::compareTo)))
                    .map(this::convertSystemFunctionToDto)
                    .collect(Collectors.toList()) :
                new ArrayList<>();

        return SystemFunctionResponseDto.builder()
                .id(function.getSystemFunctionId())
                .code(function.getFunctionCode())
                .name(function.getFunctionName())
                .parentCode(function.getParentFunctionCode())
                .functionType(function.getFunctionType())
                .isShow(function.getIsShow())
                .availablePermissions(availablePermissions)
                .children(childrenDto)
                .build();
    }

    @Override
    @Transactional
    public void deleteRole(UUID roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role", "ID", roleId));
        role.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        roleRepository.save(role);
    }
    
    @Override
    @Transactional(readOnly = true)
    public RoleDetailResponse getRoleWithPermissionsById(UUID roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role", "ID", roleId));

        List<RoleFunctionPermission> permissions = roleFunctionPermissionRepository.findByRole(role);
        
        List<RolePermissionDataRequest.FunctionPermissionDto> functionPerms = permissions.stream()
            .map(p -> {
                RolePermissionDataRequest.FunctionPermissionDto dto = new RolePermissionDataRequest.FunctionPermissionDto();
                dto.setSystemFunctionId(p.getSystemFunction().getSystemFunctionId());
                dto.setCanCreate(p.getCanCreate());
                dto.setCanRead(p.getCanRead());
                dto.setCanUpdate(p.getCanUpdate());
                dto.setCanDelete(p.getCanDelete());
                dto.setCanApprove(p.getCanApprove());
                dto.setCanChangePrice(p.getCanChangePrice());
                dto.setCanPrint(p.getCanPrint());
                dto.setCanStoreApprove(p.getCanStoreApprove());
                dto.setCanDispatchApprove(p.getCanDispatchApprove());
                return dto;
            }).collect(Collectors.toList());

        RoleDetailResponse responseDto = new RoleDetailResponse();
        responseDto.setRoleId(role.getRoleId());
        responseDto.setRoleCode(role.getRoleCode());
        responseDto.setRoleName(role.getRoleName());
        responseDto.setRoleDescription(role.getRoleDescription());
        responseDto.setFunctionPermissions(functionPerms);
        
        return responseDto;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RoleSummaryResponse> getAllRolesSummary(Pageable pageable) {
        Page<Role> rolePage = roleRepository.findByIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode(), pageable);
        return rolePage.map(this::mapRoleToSummaryResponse);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<RoleSummaryResponse> getAllRolesAsList() {
        return roleRepository.findByIsDeletedOrderByRoleName(DeleteStatusEnum.NOT_DELETED.getCode())
            .stream()
            .map(this::mapRoleToSummaryResponse)
            .collect(Collectors.toList());
    }

    private RoleSummaryResponse mapRoleToSummaryResponse(Role role) {
        String updatedByName = role.getUpdateBy() != null ? 
            userAccountRepository.findById(role.getUpdateBy()).map(UserAccount::getUserName).orElse(null) : 
            null;
        
        return RoleSummaryResponse.builder()
                .roleId(role.getRoleId())
                .roleCode(role.getRoleCode())
                .roleName(role.getRoleName())
                .roleDescription(role.getRoleDescription())
                .updateByName(updatedByName)
                .updateTime(role.getUpdateTime())
                .build();
    }
} 