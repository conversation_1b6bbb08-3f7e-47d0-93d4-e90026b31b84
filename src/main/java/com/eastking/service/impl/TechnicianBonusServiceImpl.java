package com.eastking.service.impl;

// import com.eastking.enums.ActivationStatusEnum; // Keep commented if issue persists, use literals
import com.eastking.enums.DeleteStatusEnum;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import com.eastking.exception.DataConflictException;
import com.eastking.exception.InvalidInputException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.BonusItemDto;
import com.eastking.model.dto.TechnicianBonusSettingDto;
import com.eastking.model.dto.TechnicianBonusSetupDto;
import com.eastking.model.dto.TechnicianRoleConfigDto;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.service.TechnicianBonusService;
import com.eastking.service.AuditLogService;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.Map;

@Service
public class TechnicianBonusServiceImpl implements TechnicianBonusService {

    private static final short STATUS_ACTIVE = 1;
    private static final short STATUS_INACTIVE = 0;

    private final TechnicianRoleConfigRepository technicianRoleConfigRepository;
    private final RoleRepository roleRepository;
    private final BonusItemRepository bonusItemRepository;
    private final TechnicianBonusSettingRepository technicianBonusSettingRepository;
    private final AuditLogService auditLogService;
    private final ObjectMapper objectMapper;

    public TechnicianBonusServiceImpl(
            TechnicianRoleConfigRepository technicianRoleConfigRepository,
            RoleRepository roleRepository,
            BonusItemRepository bonusItemRepository,
            TechnicianBonusSettingRepository technicianBonusSettingRepository,
            @Lazy AuditLogService auditLogService,
            ObjectMapper objectMapper) {
        this.technicianRoleConfigRepository = technicianRoleConfigRepository;
        this.roleRepository = roleRepository;
        this.bonusItemRepository = bonusItemRepository;
        this.technicianBonusSettingRepository = technicianBonusSettingRepository;
        this.auditLogService = auditLogService;
        this.objectMapper = objectMapper;
    }

    private String toJson(Object obj) {
        try {
            if (obj == null) return null;
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            System.err.println("Error serializing object to JSON: " + e.getMessage());
            return "{\"error\":\"Error serializing details\"}";
        }
    }

    // === Technician Role Configuration ===

    private TechnicianRoleConfigDto convertRoleConfigToDto(TechnicianRoleConfig entity) {
        TechnicianRoleConfigDto dto = new TechnicianRoleConfigDto();
        dto.setTechnicianRoleConfigId(entity.getTechnicianRoleConfigId());
        dto.setIsActive(STATUS_ACTIVE == entity.getIsActive());
        if (entity.getRole() != null) {
            dto.setRoleId(entity.getRole().getRoleId());
            dto.setRoleCode(entity.getRole().getRoleCode());
            dto.setRoleName(entity.getRole().getRoleName());
        }
        return dto;
    }
    
    @Override
    @Transactional
    public TechnicianRoleConfigDto addTechnicianRole(UUID roleId) {
        Role role = roleRepository.findById(roleId)
            .orElseThrow(() -> new ResourceNotFoundException("Role not found with ID: " + roleId));

        Optional<TechnicianRoleConfig> existingConfigOpt = technicianRoleConfigRepository.findByRoleAndIsDeleted(role, DeleteStatusEnum.NOT_DELETED.getCode());
        TechnicianRoleConfig savedConfig;
        AuditActionTypeEnum actionType = AuditActionTypeEnum.CREATE;
        String logMessagePrefix = "Added technician role config: ";

        if (existingConfigOpt.isPresent()) {
            TechnicianRoleConfig existingConfig = existingConfigOpt.get();
            if (STATUS_ACTIVE == existingConfig.getIsActive()) {
                 throw new DataConflictException("Role '" + role.getRoleName() + "' is already configured as an active technician role.");
            }
            existingConfig.setIsActive(STATUS_ACTIVE);
            existingConfig.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode()); 
            savedConfig = technicianRoleConfigRepository.save(existingConfig);
            actionType = AuditActionTypeEnum.UPDATE; 
            logMessagePrefix = "Reactivated technician role config: ";
        } else {
            TechnicianRoleConfig newConfig = new TechnicianRoleConfig();
            newConfig.setRole(role);
            newConfig.setIsActive(STATUS_ACTIVE);
            newConfig.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
            savedConfig = technicianRoleConfigRepository.save(newConfig);
        }
        
        auditLogService.logAction(actionType, AuditDataTypeEnum.TECHNICIAN_BONUS, savedConfig.getTechnicianRoleConfigId().toString(), 
                                logMessagePrefix + role.getRoleName(), toJson(convertRoleConfigToDto(savedConfig)));
        return convertRoleConfigToDto(savedConfig);
    }

    @Override
    @Transactional
    public void removeTechnicianRole(UUID technicianRoleConfigId) {
        TechnicianRoleConfig config = technicianRoleConfigRepository.findById(technicianRoleConfigId)
            .orElseThrow(() -> new ResourceNotFoundException("Technician Role Config not found with ID: " + technicianRoleConfigId));
        
        if (DeleteStatusEnum.DELETED.getCode().equals(config.getIsDeleted())) {
             throw new ResourceNotFoundException("Technician Role Config already deleted with ID: " + technicianRoleConfigId);
        }
        String roleNameForLog = config.getRole() != null ? config.getRole().getRoleName() : "N/A";
        TechnicianRoleConfigDto dtoForLog = convertRoleConfigToDto(config);

        config.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        config.setIsActive(STATUS_INACTIVE); 
        technicianRoleConfigRepository.save(config);
        auditLogService.logAction(AuditActionTypeEnum.DELETE, AuditDataTypeEnum.TECHNICIAN_BONUS, technicianRoleConfigId.toString(), 
                                "Removed technician role config: " + roleNameForLog, toJson(Map.of("removedConfig", dtoForLog)));
    }

    @Override
    @Transactional(readOnly = true)
    public List<TechnicianRoleConfigDto> getActiveTechnicianRoles() {
        return technicianRoleConfigRepository.findByIsActiveAndIsDeleted(
                STATUS_ACTIVE,
                DeleteStatusEnum.NOT_DELETED.getCode())
            .stream()
            .map(this::convertRoleConfigToDto)
            .collect(Collectors.toList());
    }

    // === Bonus Items & Settings ===

    private BonusItemDto convertBonusItemToDto(BonusItem entity, List<TechnicianBonusSetting> settings) {
        BonusItemDto dto = new BonusItemDto();
        BeanUtils.copyProperties(entity, dto, "bonusSettings");
        dto.setIsActive(STATUS_ACTIVE == entity.getIsActive());
        if (settings != null) {
            dto.setBonusSettings(settings.stream().map(this::convertBonusSettingToDto).collect(Collectors.toList()));
        } else {
             dto.setBonusSettings(new ArrayList<>());
        }
        return dto;
    }
    
    private TechnicianBonusSettingDto convertBonusSettingToDto(TechnicianBonusSetting entity) {
        TechnicianBonusSettingDto dto = new TechnicianBonusSettingDto();
        BeanUtils.copyProperties(entity, dto);
        if (entity.getRole() != null) {
            dto.setRoleId(entity.getRole().getRoleId());
            dto.setRoleName(entity.getRole().getRoleName());
        }
         if (entity.getBonusItem() != null) { 
            dto.setBonusItemId(entity.getBonusItem().getBonusItemId());
        }
        return dto;
    }


    @Override
    @Transactional
    public BonusItemDto saveBonusItem(BonusItemDto bonusItemDto) {
        BonusItem bonusItem;
        AuditActionTypeEnum actionType = AuditActionTypeEnum.CREATE;
        Map<String, Object> logDetails = new java.util.HashMap<>();

        if (bonusItemDto.getBonusItemId() != null) { // Update
            actionType = AuditActionTypeEnum.UPDATE;
            bonusItem = bonusItemRepository.findById(bonusItemDto.getBonusItemId())
                .orElseThrow(() -> new ResourceNotFoundException("BonusItem not found with ID: " + bonusItemDto.getBonusItemId()));
            logDetails.put("before", convertBonusItemToDto(bonusItem, technicianBonusSettingRepository.findByBonusItemAndIsDeleted(bonusItem, DeleteStatusEnum.NOT_DELETED.getCode())));
            
            bonusItemRepository.findByItemNameAndIsDeleted(bonusItemDto.getItemName(), DeleteStatusEnum.NOT_DELETED.getCode())
                .filter(existing -> !existing.getBonusItemId().equals(bonusItem.getBonusItemId()))
                .ifPresent(existing -> {
                    throw new DataConflictException("Bonus item name '" + bonusItemDto.getItemName() + "' already exists.");
                });
        } else { // Create
            bonusItemRepository.findByItemNameAndIsDeleted(bonusItemDto.getItemName(), DeleteStatusEnum.NOT_DELETED.getCode())
                .ifPresent(existing -> {
                    throw new DataConflictException("Bonus item name '" + bonusItemDto.getItemName() + "' already exists.");
                });
            bonusItem = new BonusItem();
            bonusItem.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        }

        bonusItem.setItemName(bonusItemDto.getItemName());
        bonusItem.setItemDescription(bonusItemDto.getItemDescription());
        bonusItem.setIsActive(bonusItemDto.getIsActive() ? STATUS_ACTIVE : STATUS_INACTIVE);
        
        BonusItem savedItem = bonusItemRepository.save(bonusItem);
        
        if (!CollectionUtils.isEmpty(bonusItemDto.getBonusSettings())) {
            updateBonusSettingsForItem(savedItem, bonusItemDto.getBonusSettings());
        }
        
        List<TechnicianBonusSetting> currentSettings = technicianBonusSettingRepository.findByBonusItemAndIsDeleted(savedItem, DeleteStatusEnum.NOT_DELETED.getCode());
        BonusItemDto resultDto = convertBonusItemToDto(savedItem, currentSettings);
        logDetails.put(actionType == AuditActionTypeEnum.CREATE ? "created" : "after", resultDto);

        auditLogService.logAction(actionType, AuditDataTypeEnum.TECHNICIAN_BONUS, savedItem.getBonusItemId().toString(), 
                                "Bonus Item: " + savedItem.getItemName(), toJson(logDetails));
        return resultDto;
    }
    
    private void updateBonusSettingsForItem(BonusItem item, List<TechnicianBonusSettingDto> settingDtos) {
        List<TechnicianBonusSetting> existingSettings = technicianBonusSettingRepository.findByBonusItemAndIsDeleted(item, DeleteStatusEnum.NOT_DELETED.getCode());
        Map<UUID, TechnicianBonusSetting> existingSettingsMap = existingSettings.stream()
            .collect(Collectors.toMap(s -> s.getRole().getRoleId(), s -> s));

        List<TechnicianRoleConfigDto> activeTechRoles = getActiveTechnicianRoles();
        List<UUID> activeTechRoleIds = activeTechRoles.stream().map(TechnicianRoleConfigDto::getRoleId).collect(Collectors.toList());

        for (TechnicianBonusSettingDto settingDto : settingDtos) {
            if (!activeTechRoleIds.contains(settingDto.getRoleId())) {
                System.err.println("Skipping bonus setting for role ID " + settingDto.getRoleId() + " as it is not an active technician role for item: " + item.getItemName());
                continue; 
            }

            Role role = roleRepository.findById(settingDto.getRoleId())
                .orElseThrow(() -> new ResourceNotFoundException("Role not found with ID: " + settingDto.getRoleId()));

            TechnicianBonusSetting setting = existingSettingsMap.getOrDefault(settingDto.getRoleId(), new TechnicianBonusSetting());
            setting.setBonusItem(item);
            setting.setRole(role);
            setting.setBonusAmount(settingDto.getBonusAmount() != null ? settingDto.getBonusAmount() : BigDecimal.ZERO);
            setting.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode()); 
            technicianBonusSettingRepository.save(setting);
            existingSettingsMap.remove(settingDto.getRoleId()); 
        }
        
        for (TechnicianBonusSetting settingToDelete : existingSettingsMap.values()) {
            settingToDelete.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
            technicianBonusSettingRepository.save(settingToDelete);
        }
    }

    @Override
    @Transactional
    public void deleteBonusItem(UUID bonusItemId) {
        BonusItem bonusItem = bonusItemRepository.findById(bonusItemId)
            .orElseThrow(() -> new ResourceNotFoundException("BonusItem not found with ID: " + bonusItemId));

        if (DeleteStatusEnum.DELETED.getCode().equals(bonusItem.getIsDeleted())) {
             throw new ResourceNotFoundException("BonusItem already deleted with ID: " + bonusItemId);
        }
        String itemNameForLog = bonusItem.getItemName();
        BonusItemDto dtoForLog = convertBonusItemToDto(bonusItem, technicianBonusSettingRepository.findByBonusItemAndIsDeleted(bonusItem, DeleteStatusEnum.NOT_DELETED.getCode()));

        bonusItem.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        bonusItemRepository.save(bonusItem);

        List<TechnicianBonusSetting> settings = technicianBonusSettingRepository.findByBonusItemAndIsDeleted(bonusItem, DeleteStatusEnum.NOT_DELETED.getCode());
        for (TechnicianBonusSetting setting : settings) {
            setting.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
            technicianBonusSettingRepository.save(setting);
        }
        auditLogService.logAction(AuditActionTypeEnum.DELETE, AuditDataTypeEnum.TECHNICIAN_BONUS, bonusItemId.toString(), 
                                "Bonus Item: " + itemNameForLog, toJson(Map.of("deletedItem", dtoForLog)));
    }

    @Override
    @Transactional(readOnly = true)
    public BonusItemDto getBonusItemById(UUID bonusItemId) {
        BonusItem bonusItem = bonusItemRepository.findById(bonusItemId)
            .filter(item -> DeleteStatusEnum.NOT_DELETED.getCode().equals(item.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("BonusItem not found with ID: " + bonusItemId));
        List<TechnicianBonusSetting> settings = technicianBonusSettingRepository.findByBonusItemAndIsDeleted(bonusItem, DeleteStatusEnum.NOT_DELETED.getCode());
        return convertBonusItemToDto(bonusItem, settings);
    }

    @Override
    @Transactional(readOnly = true)
    public List<BonusItemDto> getAllBonusItemsWithSettings() {
        List<BonusItem> items = bonusItemRepository.findByIsActiveAndIsDeleted(
            STATUS_ACTIVE, 
            DeleteStatusEnum.NOT_DELETED.getCode()
        );
        
        List<TechnicianRoleConfigDto> activeTechRoles = getActiveTechnicianRoles();

        return items.stream().map(item -> {
            List<TechnicianBonusSetting> settings = technicianBonusSettingRepository.findByBonusItemAndIsDeleted(item, DeleteStatusEnum.NOT_DELETED.getCode());
            
            List<TechnicianBonusSettingDto> enrichedSettings = new ArrayList<>();
            Map<UUID, TechnicianBonusSettingDto> existingRoleSettingsMap = settings.stream()
                .map(this::convertBonusSettingToDto)
                .collect(Collectors.toMap(TechnicianBonusSettingDto::getRoleId, dto -> dto));

            for (TechnicianRoleConfigDto techRole : activeTechRoles) {
                TechnicianBonusSettingDto settingDto = existingRoleSettingsMap.get(techRole.getRoleId());
                if (settingDto == null) { 
                    settingDto = new TechnicianBonusSettingDto();
                    settingDto.setBonusItemId(item.getBonusItemId());
                    settingDto.setRoleId(techRole.getRoleId());
                    settingDto.setRoleName(techRole.getRoleName());
                    settingDto.setBonusAmount(BigDecimal.ZERO); 
                }
                enrichedSettings.add(settingDto);
            }
            BonusItemDto itemDto = new BonusItemDto();
            BeanUtils.copyProperties(item, itemDto, "bonusSettings");
            itemDto.setIsActive(STATUS_ACTIVE == item.getIsActive());
            itemDto.setBonusSettings(enrichedSettings);
            return itemDto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void saveTechnicianBonusSetup(TechnicianBonusSetupDto setupDto) {
        if (setupDto == null || CollectionUtils.isEmpty(setupDto.getBonusItems())) {
            return;
        }
        auditLogService.logAction(AuditActionTypeEnum.CONFIG_CHANGE, AuditDataTypeEnum.TECHNICIAN_BONUS, "ALL_SETUP", 
                                "Batch Technician Bonus Setup Update", toJson(setupDto));

        for (BonusItemDto itemDto : setupDto.getBonusItems()) {
            saveBonusItem(itemDto); 
        }
    }
} 