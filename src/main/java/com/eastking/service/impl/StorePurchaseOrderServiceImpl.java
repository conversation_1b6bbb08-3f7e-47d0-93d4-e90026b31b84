package com.eastking.service.impl;

import com.eastking.model.dto.StorePurchaseOrderDto;
import com.eastking.model.dto.StorePurchaseOrderItemDto;
import com.eastking.model.dto.request.PurchaseOrderDiscrepancyRequest;
import com.eastking.model.entity.*;
import com.eastking.repository.*;
import com.eastking.service.StorePurchaseOrderService;
import com.eastking.enums.StorePurchaseOrderStatusEnum;
import com.eastking.enums.DiscrepancyStatusEnum;
import com.eastking.enums.DeleteStatusEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.exception.InvalidOperationException; // To be created
import com.eastking.util.SecurityUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Join;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StorePurchaseOrderServiceImpl implements StorePurchaseOrderService {
    private static final Logger logger = LoggerFactory.getLogger(StorePurchaseOrderServiceImpl.class);

    private final StorePurchaseOrderRepository storePurchaseOrderRepository;
    private final StorePurchaseOrderItemRepository storePurchaseOrderItemRepository;
    private final StorePurchaseOrderDiscrepancyRepository discrepancyRepository;
    private final StoreRepository storeRepository; 
    private final UserAccountRepository userAccountRepository; 
    private final ProductSettingRepository productSettingRepository; // To get product names if not on item
    private final StoreInventoryRepository storeInventoryRepository;
    private final ObjectMapper objectMapper; // For logging or detail generation if needed

    @Override
    @Transactional(readOnly = true)
    public Page<StorePurchaseOrderDto> searchStorePurchaseOrders(
        UUID storeId, 
        OffsetDateTime shipmentDateFrom, 
        OffsetDateTime shipmentDateTo, 
        Short status, 
        String keyword, 
        String companyContext,
        Pageable pageable) {
            
        Specification<StorePurchaseOrder> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));

            if (storeId != null) {
                predicates.add(cb.equal(root.get("store").get("storeId"), storeId));
            }
            if (StringUtils.hasText(companyContext)) {
                Join<StorePurchaseOrder, StoreEntity> storeJoin = root.join("store");
                if ("EASTKING".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(storeJoin.get("erpCompanyDivision"), ErpCompanyDivisionEnum.EASTKING.getCode()));
                } else if ("QUEYOU".equalsIgnoreCase(companyContext)) {
                    predicates.add(cb.equal(storeJoin.get("erpCompanyDivision"), ErpCompanyDivisionEnum.QUEYOU.getCode()));
                }
            }

            if (shipmentDateFrom != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("shipmentTime"), shipmentDateFrom));
            }
            if (shipmentDateTo != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("shipmentTime"), shipmentDateTo));
            }
            if (status != null) {
                predicates.add(cb.equal(root.get("purchaseOrderStatus"), status));
            }
            if (StringUtils.hasText(keyword)) {
                predicates.add(cb.like(cb.lower(root.get("purchaseOrderNumber")), "%" + keyword.toLowerCase() + "%"));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        Page<StorePurchaseOrder> page = storePurchaseOrderRepository.findAll(spec, pageable);
        return page.map(this::convertToDto); 
    }

    @Override
    @Transactional(readOnly = true)
    public StorePurchaseOrderDto getStorePurchaseOrderById(UUID id) {
        StorePurchaseOrder order = storePurchaseOrderRepository.findById(id)
            .filter(o -> o.getIsDeleted() == DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseThrow(() -> new ResourceNotFoundException("StorePurchaseOrder", "ID", id));
        return convertToDtoWithItems(order);
    }

    @Override
    @Transactional
    public StorePurchaseOrderDto reportDiscrepancy(UUID orderId, PurchaseOrderDiscrepancyRequest discrepancyRequest) {
        StorePurchaseOrder order = storePurchaseOrderRepository.findById(orderId)
            .orElseThrow(() -> new ResourceNotFoundException("StorePurchaseOrder", "ID", orderId));

        if (order.getPurchaseOrderStatus() == StorePurchaseOrderStatusEnum.COMPLETED.getCode()) {
            throw new InvalidOperationException("已完成的進貨單無法回報異常。");
        }

        UUID currentUserId = SecurityUtil.getCurrentUserId().orElseThrow(() -> new InvalidOperationException("無法取得當前使用者資訊"));

        boolean hasActualDiscrepancies = false;
        for (PurchaseOrderDiscrepancyRequest.DiscrepancyItemDto itemDto : discrepancyRequest.getItems()) {
            StorePurchaseOrderItem orderItem = storePurchaseOrderItemRepository.findById(itemDto.getStorePurchaseOrderItemId())
                .orElseThrow(() -> new ResourceNotFoundException("StorePurchaseOrderItem", "ID", itemDto.getStorePurchaseOrderItemId()));
            
            if (!orderItem.getStorePurchaseOrder().getStorePurchaseOrderId().equals(orderId)){
                throw new InvalidOperationException("品項 " + orderItem.getProductBarcode() + " 不屬於此進貨單。");
            }

            orderItem.setReceivedQuantity(itemDto.getActualQuantity());
            storePurchaseOrderItemRepository.save(orderItem);

            if (!itemDto.getActualQuantity().equals(orderItem.getOrderedQuantity())) {
                hasActualDiscrepancies = true;
                StorePurchaseOrderDiscrepancy discrepancy = new StorePurchaseOrderDiscrepancy();
                discrepancy.setStorePurchaseOrderItem(orderItem);
                discrepancy.setReportedAt(OffsetDateTime.now());
                discrepancy.setReportedByUser(userAccountRepository.findById(currentUserId).orElse(null));
                discrepancy.setExpectedQuantity(orderItem.getOrderedQuantity());
                discrepancy.setActualQuantity(itemDto.getActualQuantity());
                discrepancy.setReason(itemDto.getReason());
                discrepancy.setDiscrepancyStatus(DiscrepancyStatusEnum.PENDING_RESOLUTION.getCode());
                discrepancyRepository.save(discrepancy);
            }
        }

        if (hasActualDiscrepancies) {
            order.setPurchaseOrderStatus(StorePurchaseOrderStatusEnum.DISCREPANCY.getCode());
        } else {
            // If all items match ordered quantity after reporting, perhaps move to PENDING_CONFIRMATION or COMPLETED?
            // For now, if discrepancies were reported but all corrected to match, it implies no discrepancy.
            // However, the action implies a discrepancy check was performed.
            // If current status was already PENDING_CONFIRMATION, it stays there if no *new* discrepancies.
            // If current status was SHIPPING or PARTIALLY_RECEIVED, it should become PENDING_CONFIRMATION.
             if(order.getPurchaseOrderStatus() != StorePurchaseOrderStatusEnum.PENDING_CONFIRMATION.getCode()) {
                 order.setPurchaseOrderStatus(StorePurchaseOrderStatusEnum.PENDING_CONFIRMATION.getCode());
             }
        }
        StorePurchaseOrder updatedOrder = storePurchaseOrderRepository.save(order);
        return convertToDtoWithItems(updatedOrder);
    }

    @Override
    @Transactional
    public StorePurchaseOrderDto confirmReceipt(UUID orderId) {
        StorePurchaseOrder order = storePurchaseOrderRepository.findById(orderId)
            .orElseThrow(() -> new ResourceNotFoundException("StorePurchaseOrder", "ID", orderId));

        if (order.getPurchaseOrderStatus() == StorePurchaseOrderStatusEnum.COMPLETED.getCode()) {
            throw new InvalidOperationException("此進貨單已完成，無法重複確認。");
        }
        // Logic: Ensure all items have received_quantity set (e.g. after discrepancy check or if all items perfect)
        // Then update inventory, then set order to COMPLETED.
        
        List<StorePurchaseOrderItem> items = storePurchaseOrderItemRepository.findByStorePurchaseOrder_StorePurchaseOrderIdAndIsDeleted(orderId, DeleteStatusEnum.NOT_DELETED.getCode());
        for (StorePurchaseOrderItem item : items) {
            if (item.getReceivedQuantity() == null) {
                throw new InvalidOperationException("尚有商品未確認收到數量 (條碼: " + item.getProductBarcode() + ")，請先處理進貨數量異常或確認所有品項。");
            }
            // Update store inventory
            updateStoreInventory(order.getStore(), item.getProductBarcode(), item.getProductName(), item.getReceivedQuantity());
        }

        order.setPurchaseOrderStatus(StorePurchaseOrderStatusEnum.COMPLETED.getCode());
        order.setConfirmedAt(OffsetDateTime.now());
        order.setConfirmedByUser(SecurityUtil.getCurrentUserId().flatMap(userAccountRepository::findById).orElse(null));
        StorePurchaseOrder updatedOrder = storePurchaseOrderRepository.save(order);
        return convertToDtoWithItems(updatedOrder);
    }

    private void updateStoreInventory(StoreEntity store, String productBarcode, String productName, int quantityChange) {
        if (quantityChange == 0) return;

        StoreInventory inventory = storeInventoryRepository.findByStoreAndProductBarcodeAndIsDeleted(store, productBarcode, DeleteStatusEnum.NOT_DELETED.getCode())
            .orElseGet(() -> {
                StoreInventory newInventory = new StoreInventory();
                newInventory.setStore(store);
                newInventory.setProductBarcode(productBarcode);
                newInventory.setProductName(productName);
                newInventory.setQuantityOnHand(0);
                newInventory.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
                return newInventory;
            });

        int quantityBefore = inventory.getQuantityOnHand();
        inventory.setQuantityOnHand(quantityBefore + quantityChange);
        inventory.setLastStockUpdateTime(OffsetDateTime.now());
        storeInventoryRepository.save(inventory);
        logger.info("Inventory updated for store: {}, product: {}, new quantity: {}", store.getStoreId(), productBarcode, inventory.getQuantityOnHand());
    }

    private StorePurchaseOrderDto convertToDto(StorePurchaseOrder entity) {
        StorePurchaseOrderDto dto = new StorePurchaseOrderDto();
        BeanUtils.copyProperties(entity, dto, "items");
        if (entity.getStore() != null) {
            dto.setStoreId(entity.getStore().getStoreId());
            dto.setStoreName(entity.getStore().getStoreName());
        }
        if (entity.getConfirmedByUser() != null) {
            dto.setConfirmedByUserName(entity.getConfirmedByUser().getUserName());
        }
         StorePurchaseOrderStatusEnum statusEnum = StorePurchaseOrderStatusEnum.fromCode(entity.getPurchaseOrderStatus());
        if (statusEnum != null) {
            dto.setStatusDescription(statusEnum.getDescription());
        }
        return dto;
    }

    private StorePurchaseOrderDto convertToDtoWithItems(StorePurchaseOrder entity) {
        StorePurchaseOrderDto dto = convertToDto(entity);
        List<StorePurchaseOrderItem> items = storePurchaseOrderItemRepository.findByStorePurchaseOrder_StorePurchaseOrderIdAndIsDeleted(entity.getStorePurchaseOrderId(), DeleteStatusEnum.NOT_DELETED.getCode());
        dto.setItems(items.stream().map(this::convertItemToDto).collect(Collectors.toList()));
        return dto;
    }

    private StorePurchaseOrderItemDto convertItemToDto(StorePurchaseOrderItem entity) {
        StorePurchaseOrderItemDto dto = new StorePurchaseOrderItemDto();
        BeanUtils.copyProperties(entity, dto);
         if (entity.getProductBarcode() != null && !StringUtils.hasText(entity.getProductName())) {
            productSettingRepository.findByProductBarcodeAndIsDeleted(entity.getProductBarcode(), DeleteStatusEnum.NOT_DELETED.getCode())
                .ifPresent(ps -> dto.setProductName(ps.getProductName()));
        }
        return dto;
    }
} 