package com.eastking.service.impl;

import com.eastking.enums.ProductMenuTypeEnum;
import com.eastking.model.dto.ProductMenuCategoryDto;
import com.eastking.model.dto.ProductMenuItemDto;
import com.eastking.model.dto.ProductMenuNodeDto;
import com.eastking.model.entity.ProductMenuCategory;
import com.eastking.model.entity.ProductMenuItem;
import com.eastking.repository.ProductMenuCategoryRepository;
import com.eastking.repository.ProductMenuItemRepository;
import com.eastking.service.ProductMenuService;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.exception.InvalidDataException;
// Assuming an external product service might be used in the future to fetch product details by barcode
// import com.eastking.service.ExternalProductService; 
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ProductMenuServiceImpl implements ProductMenuService {

    private static final Logger logger = LoggerFactory.getLogger(ProductMenuServiceImpl.class);
    private static final Short NOT_DELETED = 0;

    private final ProductMenuCategoryRepository categoryRepository;
    private final ProductMenuItemRepository itemRepository;
    // private final ExternalProductService externalProductService; // For fetching product names

    @Override
    @Transactional
    public ProductMenuCategoryDto createCategory(ProductMenuCategoryDto categoryDto) {
        logger.info("Creating product menu category: {}", categoryDto.getCategoryName());
        validateCategoryNameUniqueness(categoryDto.getCategoryName(), categoryDto.getParentCategoryId(), categoryDto.getMenuType(), null);

        ProductMenuCategory entity = new ProductMenuCategory();
        mapDtoToCategoryEntity(categoryDto, entity);
        
        if (entity.getSortOrder() == null) {
            if (entity.getParentCategory() != null) {
                entity.setSortOrder(categoryRepository.findMaxSortOrderUnderParent(entity.getMenuType(), entity.getParentCategory().getProductMenuCategoryId()) + 1);
            } else {
                entity.setSortOrder(categoryRepository.findMaxSortOrderAtTopLevel(entity.getMenuType()) + 1);
            }
        }

        ProductMenuCategory savedEntity = categoryRepository.save(entity);
        return convertCategoryEntityToDto(savedEntity);
    }

    @Override
    @Transactional
    public ProductMenuCategoryDto updateCategory(UUID categoryId, ProductMenuCategoryDto categoryDto) {
        logger.info("Updating product menu category with ID: {}", categoryId);
        ProductMenuCategory existingEntity = categoryRepository.findById(categoryId)
            .orElseThrow(() -> new ResourceNotFoundException("ProductMenuCategory not found with id: " + categoryId));

        if (!existingEntity.getCategoryName().equals(categoryDto.getCategoryName()) || 
            (existingEntity.getParentCategory() != null && !existingEntity.getParentCategory().getProductMenuCategoryId().equals(categoryDto.getParentCategoryId())) ||
            (existingEntity.getParentCategory() == null && categoryDto.getParentCategoryId() != null) ||
            !existingEntity.getMenuType().equals(categoryDto.getMenuType())) {
            validateCategoryNameUniqueness(categoryDto.getCategoryName(), categoryDto.getParentCategoryId(), categoryDto.getMenuType(), categoryId);
        }

        mapDtoToCategoryEntity(categoryDto, existingEntity);
        // Sort order might be adjusted by a separate call to updateMenuOrder if drag-drop is main mechanism

        ProductMenuCategory updatedEntity = categoryRepository.save(existingEntity);
        return convertCategoryEntityToDto(updatedEntity);
    }

    @Override
    @Transactional
    public void deleteCategory(UUID categoryId) {
        logger.info("Deleting product menu category with ID: {}", categoryId);
        ProductMenuCategory category = categoryRepository.findById(categoryId)
            .orElseThrow(() -> new ResourceNotFoundException("ProductMenuCategory not found with id: " + categoryId));

        // Recursively delete child categories and their items
        deleteCategoryRecursive(category);
    }
    
    private void deleteCategoryRecursive(ProductMenuCategory category) {
        // Soft delete items in this category
        List<ProductMenuItem> items = itemRepository.findByProductMenuCategory_ProductMenuCategoryIdAndIsDeletedOrderBySortOrderAsc(category.getProductMenuCategoryId(), NOT_DELETED);
        if (!CollectionUtils.isEmpty(items)) {
            items.forEach(item -> item.setIsDeleted((short) 1));
            itemRepository.saveAll(items);
        }

        // Recursively delete child categories
        List<ProductMenuCategory> children = categoryRepository.findByMenuTypeAndParentCategory_ProductMenuCategoryIdAndIsDeletedOrderBySortOrderAsc(category.getMenuType(), category.getProductMenuCategoryId(), NOT_DELETED);
        for (ProductMenuCategory child : children) {
            deleteCategoryRecursive(child);
        }

        // Soft delete the category itself
        category.setIsDeleted((short) 1);
        categoryRepository.save(category);
        logger.debug("Soft deleted category: {}", category.getCategoryName());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProductMenuNodeDto> getMenuTree(ProductMenuTypeEnum menuType) {
        List<ProductMenuCategory> rootCategories = categoryRepository.findByMenuTypeAndParentCategoryIsNullAndIsDeletedOrderBySortOrderAsc(menuType, NOT_DELETED);
        return rootCategories.stream()
            .map(this::convertCategoryToNodeDtoRecursive)
            .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ProductMenuCategoryDto> getCategoriesByParent(ProductMenuTypeEnum menuType, UUID parentCategoryId) {
        List<ProductMenuCategory> categories;
        if (parentCategoryId == null) {
            categories = categoryRepository.findByMenuTypeAndParentCategoryIsNullAndIsDeletedOrderBySortOrderAsc(menuType, NOT_DELETED);
        } else {
            categories = categoryRepository.findByMenuTypeAndParentCategory_ProductMenuCategoryIdAndIsDeletedOrderBySortOrderAsc(menuType, parentCategoryId, NOT_DELETED);
        }
        return categories.stream().map(this::convertCategoryEntityToDto).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public ProductMenuItemDto addItemToCategory(UUID categoryId, ProductMenuItemDto itemDto) {
        logger.info("Adding item {} to category ID: {}", itemDto.getProductBarcode(), categoryId);
        ProductMenuCategory category = categoryRepository.findById(categoryId)
            .orElseThrow(() -> new ResourceNotFoundException("ProductMenuCategory not found with id: " + categoryId));

        if (itemRepository.existsByProductMenuCategory_ProductMenuCategoryIdAndProductBarcodeAndIsDeleted(categoryId, itemDto.getProductBarcode(), NOT_DELETED)) {
            throw new InvalidDataException("Product barcode \'" + itemDto.getProductBarcode() + "\' already exists in this category.");
        }

        ProductMenuItem itemEntity = new ProductMenuItem();
        itemEntity.setProductMenuCategory(category);
        itemEntity.setProductBarcode(itemDto.getProductBarcode());
        // In a real scenario, productName might be fetched from an external product service/DB based on barcode
        itemEntity.setProductName(itemDto.getProductName() != null ? itemDto.getProductName() : "Product name for " + itemDto.getProductBarcode()); 
        
        if (itemDto.getSortOrder() == null) {
            itemEntity.setSortOrder(itemRepository.findMaxSortOrderInCategory(categoryId) + 1);
        }

        ProductMenuItem savedItem = itemRepository.save(itemEntity);
        return convertItemEntityToDto(savedItem);
    }

    @Override
    @Transactional
    public void removeItemFromCategory(UUID itemId) {
        logger.info("Removing menu item with ID: {}", itemId);
        ProductMenuItem item = itemRepository.findById(itemId)
            .orElseThrow(() -> new ResourceNotFoundException("ProductMenuItem not found with id: " + itemId));
        item.setIsDeleted((short)1); // Soft delete
        itemRepository.save(item);
    }

    @Override
    @Transactional
    public void updateMenuOrder(ProductMenuTypeEnum menuType, List<ProductMenuNodeDto> orderedNodesFromClient) {
        logger.info("Updating menu order for type: {}. Received {} nodes.", menuType, orderedNodesFromClient.size());

        java.util.Map<String, UUID> clientTempIdToRealDbIdMap = new java.util.HashMap<>();
        java.util.Set<UUID> processedCategoryIdsFromPayload = new java.util.HashSet<>();
        java.util.Set<UUID> processedItemIdsFromPayload = new java.util.HashSet<>();

        // First pass: Create or Update categories from the payload
        // This pass ensures all categories (new or existing) are processed and new ones get DB IDs.
        for (ProductMenuNodeDto nodeDto : orderedNodesFromClient) {
            if (!"CATEGORY".equals(nodeDto.getType())) {
                continue;
            }

            ProductMenuCategory category;
            if (nodeDto.getId() == null) { // New category
                category = new ProductMenuCategory();
                category.setMenuType(menuType);
                // UUID will be generated on save
            } else { // Existing category
                category = categoryRepository.findById(nodeDto.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Processing order: ProductMenuCategory not found with id: " + nodeDto.getId()));
            }

            category.setCategoryName(nodeDto.getName());
            category.setSortOrder(nodeDto.getSortOrder());
            // Parent will be set in the next pass. Set to null for now if new, or keep existing if updating.
            if (nodeDto.getId() == null) {
                category.setParentCategory(null); 
            }
            
            ProductMenuCategory savedCategory = categoryRepository.save(category);
            processedCategoryIdsFromPayload.add(savedCategory.getProductMenuCategoryId());

            if (nodeDto.getClientTempId() != null && nodeDto.getId() == null) {
                clientTempIdToRealDbIdMap.put(nodeDto.getClientTempId(), savedCategory.getProductMenuCategoryId());
                logger.debug("Mapped clientTempId {} to new DB ID {} for category {}", nodeDto.getClientTempId(), savedCategory.getProductMenuCategoryId(), savedCategory.getCategoryName());
            }
        }

        // Second pass: Set parents for categories and create/update items, linking them to parents.
        for (ProductMenuNodeDto nodeDto : orderedNodesFromClient) {
            if ("CATEGORY".equals(nodeDto.getType())) {
                // Re-fetch the category to ensure it's a managed entity from this session
                UUID currentCategoryId = nodeDto.getId() != null ? nodeDto.getId() : clientTempIdToRealDbIdMap.get(nodeDto.getClientTempId());
                if (currentCategoryId == null) {
                    logger.error("Cannot find category in DB or map for DTO name: {}, clientTempId: {}", nodeDto.getName(), nodeDto.getClientTempId());
                    throw new InvalidDataException("Cannot find category that should have been processed in first pass: " + nodeDto.getName());
                }
                ProductMenuCategory category = categoryRepository.findById(currentCategoryId)
                     .orElseThrow(() -> new ResourceNotFoundException("Category disappeared after first pass: " + currentCategoryId));

                if (nodeDto.getParentCategoryId() != null) {
                    UUID parentDbId = clientTempIdToRealDbIdMap.get(nodeDto.getParentCategoryId().toString()); // Check map if parentCatId is a String (temp ID)
                    if (parentDbId == null) { // Not in map, or parentCatId was already a UUID
                        try {
                            parentDbId = UUID.fromString(nodeDto.getParentCategoryId().toString()); //Handles if parentId is already a UUID string
                        } catch (IllegalArgumentException e) {
                            logger.error("Invalid UUID format for parentCategoryId: {} for category {}", nodeDto.getParentCategoryId(), nodeDto.getName());
                            throw new InvalidDataException("Invalid Parent Category ID format for category " + nodeDto.getName());
                        }
                    }
                    
                    final UUID finalParentDbIdForCategory = parentDbId; // Effectively final for lambda
                    ProductMenuCategory parentCategory = categoryRepository.findById(finalParentDbIdForCategory)
                        .orElseThrow(() -> new ResourceNotFoundException("Parent category (for category " + nodeDto.getName() + ") not found with DB ID: " + finalParentDbIdForCategory + " (original client parent ID: "+nodeDto.getParentCategoryId()+")"));
                    category.setParentCategory(parentCategory);
                } else {
                    category.setParentCategory(null); // Root category
                }
                categoryRepository.save(category); // Save changes to parentage

            } else if ("ITEM".equals(nodeDto.getType())) {
                ProductMenuItem menuItem;
                if (nodeDto.getId() == null) { // New item
                    menuItem = new ProductMenuItem();
                    // productMenuItemId will be generated on save
                } else { // Existing item
                    menuItem = itemRepository.findById(nodeDto.getId())
                        .orElseThrow(() -> new ResourceNotFoundException("Processing order: ProductMenuItem not found with id: " + nodeDto.getId()));
                }

                menuItem.setProductName(nodeDto.getName());
                menuItem.setProductBarcode(nodeDto.getProductBarcode());
                menuItem.setSortOrder(nodeDto.getSortOrder());

                if (nodeDto.getParentCategoryId() == null) {
                    throw new InvalidDataException("ProductMenuItem " + nodeDto.getName() + " must have a parent category.");
                }

                UUID parentDbId = clientTempIdToRealDbIdMap.get(nodeDto.getParentCategoryId().toString());
                if (parentDbId == null) { 
                    try {
                        parentDbId = UUID.fromString(nodeDto.getParentCategoryId().toString());
                    } catch (IllegalArgumentException e) {
                        logger.error("Invalid UUID format for parentCategoryId: {} for item {}", nodeDto.getParentCategoryId(), nodeDto.getName());
                        throw new InvalidDataException("Invalid Parent Category ID format for item " + nodeDto.getName());
                    }
                }

                final UUID finalParentDbIdForItem = parentDbId; // Effectively final for lambda
                ProductMenuCategory parentCategory = categoryRepository.findById(finalParentDbIdForItem)
                    .orElseThrow(() -> new ResourceNotFoundException("Parent category (for item " + nodeDto.getName() + ") not found with DB ID: " + finalParentDbIdForItem + " (original client parent ID: "+nodeDto.getParentCategoryId()+")"));
                menuItem.setProductMenuCategory(parentCategory);
                
                ProductMenuItem savedItem = itemRepository.save(menuItem);
                processedItemIdsFromPayload.add(savedItem.getProductMenuItemId());

                if (nodeDto.getClientTempId() != null && nodeDto.getId() == null) {
                     // Although items don't act as parents, mapping their temp IDs might be useful for debugging or future features
                    clientTempIdToRealDbIdMap.put(nodeDto.getClientTempId(), savedItem.getProductMenuItemId());
                    logger.debug("Mapped clientTempId {} to new DB ID {} for item {}", nodeDto.getClientTempId(), savedItem.getProductMenuItemId(), savedItem.getProductName());
                }
            }
        }

        // Final step: Soft delete entities for this menuType that were not in the payload
        List<ProductMenuCategory> allCategoriesInDbForMenuType = categoryRepository.findByMenuTypeAndIsDeleted(menuType, NOT_DELETED);
        for (ProductMenuCategory dbCategory : allCategoriesInDbForMenuType) {
            if (!processedCategoryIdsFromPayload.contains(dbCategory.getProductMenuCategoryId())) {
                logger.info("Soft deleting category not in provided order: ID={}, Name={}", dbCategory.getProductMenuCategoryId(), dbCategory.getCategoryName());
                deleteCategoryRecursive(dbCategory); // Uses your existing recursive soft delete
            }
        }

        List<ProductMenuItem> allItemsInDbForMenuType = itemRepository.findByProductMenuCategory_MenuTypeAndIsDeleted(menuType, NOT_DELETED);
        for (ProductMenuItem dbItem : allItemsInDbForMenuType) {
            if (!processedItemIdsFromPayload.contains(dbItem.getProductMenuItemId())) {
                logger.info("Soft deleting item not in provided order: ID={}, Name={}", dbItem.getProductMenuItemId(), dbItem.getProductName());
                dbItem.setIsDeleted((short)1);
                itemRepository.save(dbItem);
            }
        }
        logger.info("Successfully updated menu order for type: {}", menuType);
    }
    
    // private void updateNestedOrder(List<ProductMenuNodeDto> children, UUID parentCategoryId, ProductMenuTypeEnum menuType) {
    // This method is likely no longer needed if updateMenuOrder processes the flat list comprehensively.
    // If it was used elsewhere, it needs to be reviewed.
    // }

    // Helper methods for DTO/Entity conversion and validation

    private void validateCategoryNameUniqueness(String categoryName, UUID parentId, ProductMenuTypeEnum menuType, UUID currentCategoryId) {
        boolean exists;
        if (parentId == null) {
            exists = categoryRepository.existsByCategoryNameAndParentCategoryIsNullAndMenuTypeAndIsDeleted(categoryName, menuType, NOT_DELETED);
        } else {
            exists = categoryRepository.existsByCategoryNameAndParentCategory_ProductMenuCategoryIdAndMenuTypeAndIsDeleted(categoryName, parentId, menuType, NOT_DELETED);
        }
        if (exists) {
            // If updating, check if the conflict is with itself
            Optional<ProductMenuCategory> conflictingCategory = parentId == null ? 
                categoryRepository.findByMenuTypeAndParentCategoryIsNullAndIsDeletedOrderBySortOrderAsc(menuType, NOT_DELETED).stream().filter(c -> c.getCategoryName().equals(categoryName)).findFirst() :
                categoryRepository.findByMenuTypeAndParentCategory_ProductMenuCategoryIdAndIsDeletedOrderBySortOrderAsc(menuType, parentId, NOT_DELETED).stream().filter(c -> c.getCategoryName().equals(categoryName)).findFirst();
            
            if (conflictingCategory.isPresent() && (currentCategoryId == null || !conflictingCategory.get().getProductMenuCategoryId().equals(currentCategoryId))) {
                 throw new InvalidDataException("Category name \'" + categoryName + "\' already exists under the same parent and menu type.");
            }
        }
    }

    private void mapDtoToCategoryEntity(ProductMenuCategoryDto dto, ProductMenuCategory entity) {
        entity.setCategoryName(dto.getCategoryName());
        entity.setMenuType(dto.getMenuType());
        if (dto.getParentCategoryId() != null) {
            ProductMenuCategory parent = categoryRepository.findById(dto.getParentCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException("Parent category not found with id: " + dto.getParentCategoryId()));
            entity.setParentCategory(parent);
        } else {
            entity.setParentCategory(null);
        }
        entity.setSortOrder(dto.getSortOrder()); // Can be null, will be auto-assigned if so during create
    }

    private ProductMenuCategoryDto convertCategoryEntityToDto(ProductMenuCategory entity) {
        ProductMenuCategoryDto dto = new ProductMenuCategoryDto();
        BeanUtils.copyProperties(entity, dto, "childCategories", "menuItems"); // Exclude collections for basic DTO
        if (entity.getParentCategory() != null) {
            dto.setParentCategoryId(entity.getParentCategory().getProductMenuCategoryId());
            dto.setParentCategoryName(entity.getParentCategory().getCategoryName());
        }
        // For a full DTO with children, those would be populated recursively or as needed.
        return dto;
    }

    private ProductMenuItemDto convertItemEntityToDto(ProductMenuItem entity) {
        ProductMenuItemDto dto = new ProductMenuItemDto();
        BeanUtils.copyProperties(entity, dto);
        // productName is already on the entity, potentially fetched from external source or set during creation
        return dto;
    }
    
    private ProductMenuNodeDto convertCategoryToNodeDtoRecursive(ProductMenuCategory category) {
        ProductMenuNodeDto node = new ProductMenuNodeDto(
            category.getProductMenuCategoryId(),
            category.getCategoryName(),
            category.getSortOrder(),
            category.getMenuType(),
            category.getParentCategory() != null ? category.getParentCategory().getProductMenuCategoryId() : null
        );
        node.setType("CATEGORY");

        List<ProductMenuNodeDto> childrenNodes = new ArrayList<>();
        // Add child categories
        List<ProductMenuCategory> childCategories = categoryRepository.findByMenuTypeAndParentCategory_ProductMenuCategoryIdAndIsDeletedOrderBySortOrderAsc(
            category.getMenuType(), category.getProductMenuCategoryId(), NOT_DELETED);
        if (!CollectionUtils.isEmpty(childCategories)) {
            childrenNodes.addAll(childCategories.stream().map(this::convertCategoryToNodeDtoRecursive).collect(Collectors.toList()));
        }
        // Add items in this category
        List<ProductMenuItem> items = itemRepository.findByProductMenuCategory_ProductMenuCategoryIdAndIsDeletedOrderBySortOrderAsc(
            category.getProductMenuCategoryId(), NOT_DELETED);
        if (!CollectionUtils.isEmpty(items)) {
            childrenNodes.addAll(items.stream().map(item -> {
                ProductMenuNodeDto itemNode = new ProductMenuNodeDto(
                    item.getProductMenuItemId(), 
                    item.getProductName(), // Or fetch from external service based on item.getProductBarcode()
                    item.getProductBarcode(), 
                    item.getSortOrder(), 
                    category.getProductMenuCategoryId()
                );
                itemNode.setType("ITEM");
                return itemNode;
            }).collect(Collectors.toList()));
        }
        
        // Sort combined children by their sortOrder
        childrenNodes.sort(Comparator.comparing(ProductMenuNodeDto::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));
        node.setChildren(childrenNodes);
        return node;
    }
} 