package com.eastking.service.impl;

import com.eastking.model.dto.CustomerDto;
import com.eastking.model.entity.Customer;
import com.eastking.model.entity.CustomerSegment;
import com.eastking.model.entity.MemberLevelEntity;
import com.eastking.model.entity.UserAccount;
import com.eastking.model.dto.CustomerDeviceDto;
import com.eastking.model.entity.CustomerDevice;
import com.eastking.model.entity.ProductSetting;
import com.eastking.repository.CustomerRepository;
import com.eastking.repository.CustomerSegmentRepository;
import com.eastking.repository.MemberLevelRepository;
import com.eastking.repository.UserAccountRepository;
import com.eastking.repository.CustomerDeviceRepository;
import com.eastking.repository.ProductSettingRepository;
import com.eastking.service.CustomerService;
import com.eastking.util.SecurityUtil;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.exception.DataConflictException;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Set;
import com.eastking.model.dto.CustomerSegmentDto;

@Service
@RequiredArgsConstructor
@Transactional
public class CustomerServiceImpl implements CustomerService {

    private final CustomerRepository customerRepository;
    private final MemberLevelRepository memberLevelRepository;
    private final CustomerSegmentRepository customerSegmentRepository;
    private final UserAccountRepository userAccountRepository; // For createBy/updateBy names
    private final CustomerDeviceRepository customerDeviceRepository;
    private final ProductSettingRepository productSettingRepository;

    @Override
    public CustomerDto createCustomer(CustomerDto customerDto) {
        // 電話號碼唯一性檢查已被移除
        
        // 如果前端未提供公司別，則從當前登入使用者獲取
        if (customerDto.getCompanyDivisionCode() == null) {
            SecurityUtil.getCurrentUserCompanyDivisionCode().ifPresent(customerDto::setCompanyDivisionCode);
        }

        // 如果前端未提供會員等級，則設定預設為 "一般會員"
        if (customerDto.getMemberLevelId() == null) {
            memberLevelRepository.findByLevelNameAndIsDeleted("一般會員", (short)0)
                .ifPresent(defaultLevel -> customerDto.setMemberLevelId(defaultLevel.getMemberLevelId()));
        }

        Customer customer = convertToEntity(customerDto);
        customer.setIsDeleted((short) 0);
        
        Customer savedCustomer = customerRepository.save(customer);
        return convertToDto(savedCustomer);
    }

    @Override
    public CustomerDto updateCustomer(UUID customerId, CustomerDto customerDto) {
        Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new ResourceNotFoundException("找不到客戶，ID: " + customerId));

        // 電話號碼唯一性檢查已被移除

        // --- 欄位更新邏輯 ---
        customer.setCustomerName(customerDto.getCustomerName());
        customer.setPhoneNumber(customerDto.getPhoneNumber());
        customer.setEmail(customerDto.getEmail());
        customer.setGenderCode(customerDto.getGenderCode());
        customer.setBirthDate(customerDto.getBirthDate());
        customer.setMaritalStatusCode(customerDto.getMaritalStatusCode());
        customer.setHomePhone(customerDto.getHomePhone());
        
        // 職業與偏好
        customer.setOccupationCode(customerDto.getOccupationCode());
        customer.setJobTitle(customerDto.getJobTitle());
        customer.setInterests(customerDto.getInterests());
        customer.setAcceptsMarketingInfo(customerDto.getAcceptsMarketingInfo());
        customer.setRemarks(customerDto.getRemarks());

        // 結構化地址
        customer.setAddressCityName(customerDto.getAddressCityName());
        customer.setAddressDistrictName(customerDto.getAddressDistrictName());
        customer.setAddressStreetName(customerDto.getAddressStreetName());
        customer.setAddressLane(customerDto.getAddressLane());
        customer.setAddressAlley(customerDto.getAddressAlley());
        customer.setAddressNumber(customerDto.getAddressNumber());
        customer.setAddressFloor(customerDto.getAddressFloor());
        customer.setAddressUnit(customerDto.getAddressUnit());
        customer.setFullAddress(customerDto.getFullAddress());
        
        // 更新客群管道關聯
        customer.getSegments().clear(); // 先清除舊的關聯
        if (customerDto.getSegments() != null && !customerDto.getSegments().isEmpty()) {
            Set<CustomerSegment> newSegments = customerDto.getSegments().stream()
                .map(segmentDto -> customerSegmentRepository.findById(segmentDto.getSegmentId()))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toSet());
            customer.setSegments(newSegments);
        }

        // 關聯欄位 (只有在提供新值時才更新)
        if (customerDto.getMemberLevelId() != null) {
            memberLevelRepository.findById(customerDto.getMemberLevelId())
                .ifPresent(customer::setMemberLevel);
        }
        
        if (customerDto.getCompanyDivisionCode() != null) {
            customer.setCompanyDivisionCode(customerDto.getCompanyDivisionCode());
        }

        Customer updatedCustomer = customerRepository.save(customer);
        return convertToDto(updatedCustomer);
    }

    @Override
    @Transactional(readOnly = true)
    public CustomerDto getCustomerById(UUID customerId) {
        Customer customer = customerRepository.findById(customerId)
            .filter(c -> c.getIsDeleted() == 0)
            .orElseThrow(() -> new ResourceNotFoundException("找不到客戶，ID: " + customerId));
        return convertToDto(customer);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CustomerDto> searchCustomers(String keyword, String phoneNumber, Short companyDivisionCode, Pageable pageable) {
        Specification<Customer> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));

            if (companyDivisionCode != null) {
                predicates.add(cb.equal(root.get("companyDivisionCode"), companyDivisionCode));
            }

            if (StringUtils.hasText(phoneNumber)) {
                 predicates.add(cb.like(cb.lower(root.get("phoneNumber")), "%" + phoneNumber.toLowerCase() + "%"));
            }
            
            if (StringUtils.hasText(keyword)) {
                Predicate namePredicate = cb.like(cb.lower(root.get("customerName")), "%" + keyword.toLowerCase() + "%");
                // If keyword is not a phone number, also search by phone number using keyword
                if (!StringUtils.hasText(phoneNumber)) { 
                    Predicate phonePredicateFromKeyword = cb.like(cb.lower(root.get("phoneNumber")), "%" + keyword.toLowerCase() + "%");
                     predicates.add(cb.or(namePredicate, phonePredicateFromKeyword));
                } else {
                    predicates.add(namePredicate);
                }
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return customerRepository.findAll(spec, pageable).map(this::convertToDto);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CustomerDto> findByPhoneNumber(String phoneNumber, Short companyDivisionCode) {
        Specification<Customer> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));
            
            if (StringUtils.hasText(phoneNumber)) {
                predicates.add(cb.like(root.get("phoneNumber"), "%" + phoneNumber + "%"));
            }

            if (companyDivisionCode != null) {
                predicates.add(cb.equal(root.get("companyDivisionCode"), companyDivisionCode));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };

        return customerRepository.findAll(spec).stream()
                                 .map(this::convertToDto)
                                 .collect(Collectors.toList());
    }

    @Override
    public void deleteCustomer(UUID customerId) {
        Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new ResourceNotFoundException("找不到客戶，ID: " + customerId));
        customer.setIsDeleted((short) 1);
        // Set updateBy via AuditingEntityListener
        customerRepository.save(customer);
    }

    @Override
    @Transactional
    public CustomerDeviceDto addDeviceToCustomer(UUID customerId, CustomerDeviceDto deviceDto) {
        Customer customer = customerRepository.findById(customerId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到客戶，ID: " + customerId));
        
        ProductSetting productSetting = productSettingRepository.findById(deviceDto.getProductSettingId())
                .orElseThrow(() -> new ResourceNotFoundException("找不到對應的產品設定，ID: " + deviceDto.getProductSettingId()));

        CustomerDevice device = new CustomerDevice();
        device.setCustomer(customer);
        device.setProductSetting(productSetting);
        device.setDeviceSerialNumber(deviceDto.getDeviceSerialNumber());
        device.setInstallationAddress(deviceDto.getInstallationAddress());
        device.setWarrantyDate(deviceDto.getWarrantyDate());
        device.setUserNameRemark(deviceDto.getUserNameRemark());
        device.setIsDeleted((short) 0);

        CustomerDevice savedDevice = customerDeviceRepository.save(device);
        return convertToDeviceDto(savedDevice);
    }

    private Customer convertToEntity(CustomerDto dto) {
        Customer entity = new Customer();
        BeanUtils.copyProperties(dto, entity, "createTime", "updateTime", "createByEmployeeId", "createByName", "updateByEmployeeId", "updateByName", "memberLevelName", "companyDivisionDescription", "customerSegmentName", "isDeleted", "segments");

        if (dto.getMemberLevelId() != null) {
            memberLevelRepository.findById(dto.getMemberLevelId()).ifPresent(entity::setMemberLevel);
        }
        
        // 處理客群管道
        if (dto.getSegments() != null && !dto.getSegments().isEmpty()) {
            Set<CustomerSegment> segments = dto.getSegments().stream()
                .map(segmentDto -> customerSegmentRepository.findById(segmentDto.getSegmentId()))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toSet());
            entity.setSegments(segments);
        }

        // isDeleted is handled separately or by default in BaseEntity
        return entity;
    }

    private CustomerDto convertToDto(Customer entity) {
        if (entity == null) return null;
        CustomerDto dto = new CustomerDto();
        BeanUtils.copyProperties(entity, dto, "segments", "devices", "customerSegment"); // Exclude collections and single segment from deep copy

        // Manually map fields that need special handling or are in related entities
        if (entity.getMemberLevel() != null) {
            dto.setMemberLevelId(entity.getMemberLevel().getMemberLevelId());
            dto.setMemberLevelName(entity.getMemberLevel().getLevelName());
        }
        if (entity.getCustomerSegment() != null) {
            dto.setCustomerSegmentId(entity.getCustomerSegment().getSegmentId());
            dto.setCustomerSegmentName(entity.getCustomerSegment().getSegmentName());
        }
        
        // 手動、明確地對映結構化地址欄位，確保資料完整性
        dto.setAddressCityName(entity.getAddressCityName());
        dto.setAddressDistrictName(entity.getAddressDistrictName());
        dto.setAddressStreetName(entity.getAddressStreetName());
        dto.setAddressLane(entity.getAddressLane());
        dto.setAddressAlley(entity.getAddressAlley());
        dto.setAddressNumber(entity.getAddressNumber());
        dto.setAddressFloor(entity.getAddressFloor());
        dto.setAddressUnit(entity.getAddressUnit());
        dto.setFullAddress(entity.getFullAddress());

        if (entity.getSegments() != null && !entity.getSegments().isEmpty()) {
            Set<CustomerSegmentDto> segmentDtos = entity.getSegments().stream()
                .map(segment -> {
                    CustomerSegmentDto segmentDto = new CustomerSegmentDto();
                    segmentDto.setSegmentId(segment.getSegmentId());
                    segmentDto.setSegmentName(segment.getSegmentName());
                    segmentDto.setIsActive(segment.getIsActive());
                    segmentDto.setSequenceOrder(segment.getSequenceOrder());
                    return segmentDto;
                })
                .collect(Collectors.toSet());
            dto.setSegments(segmentDtos);
        }
        
        // Explicitly map the new genderCode field
        dto.setGenderCode(entity.getGenderCode());

        if (entity.getCreateBy() != null) {
            userAccountRepository.findById(entity.getCreateBy()).ifPresent(user -> {
                dto.setCreateByEmployeeId(user.getEmployeeId());
                dto.setCreateByName(user.getUserName());
            });
        }
        if (entity.getUpdateBy() != null) {
            userAccountRepository.findById(entity.getUpdateBy()).ifPresent(user -> {
                dto.setUpdateByEmployeeId(user.getEmployeeId());
                dto.setUpdateByName(user.getUserName());
            });
        }
        dto.setIsDeleted(entity.getIsDeleted() == 1);
        return dto;
    }

    private CustomerDeviceDto convertToDeviceDto(CustomerDevice device) {
        if (device == null) return null;

        return CustomerDeviceDto.builder()
                .customerDeviceId(device.getCustomerOwnedDeviceId())
                .deviceSerialNumber(device.getDeviceSerialNumber())
            .productName(device.getProductSetting() != null ? device.getProductSetting().getProductName() : null)
            .installationAddress(device.getInstallationAddress())
            .warrantyDate(device.getWarrantyDate())
            .lastRepairDate(device.getLastRepairDate())
            .userNameRemark(device.getUserNameRemark())
            .build();
    }
} 