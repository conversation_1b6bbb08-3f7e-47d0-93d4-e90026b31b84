package com.eastking.service.impl;

import com.eastking.enums.ActivationStatusEnum;
import com.eastking.enums.DeleteStatusEnum;
import com.eastking.exception.DataConflictException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.CustomerSegmentDto;
import com.eastking.model.entity.CustomerSegment;
import com.eastking.model.entity.UserAccount; // For audit name population
import com.eastking.repository.CustomerSegmentRepository;
import com.eastking.repository.UserAccountRepository; // For audit name population
import com.eastking.service.CustomerSegmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CustomerSegmentServiceImpl implements CustomerSegmentService {

    private final CustomerSegmentRepository segmentRepository;
    private final UserAccountRepository userAccountRepository; // For createdBy/updatedByName

    @Override
    @Transactional
    public CustomerSegmentDto createSegment(CustomerSegmentDto dto) {
        segmentRepository.findBySegmentNameAndIsDeleted(dto.getSegmentName(), DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(existing -> {
                throw new DataConflictException("客群管道名稱 '" + dto.getSegmentName() + "' 已存在。");
            });
        CustomerSegment entity = new CustomerSegment();
        entity.setSegmentName(dto.getSegmentName());
        entity.setIsActive(ActivationStatusEnum.YES.getCode()); // New segments are active by default
        // Set sequence order to be last among active segments
        List<CustomerSegment> activeSegments = segmentRepository.findByIsActiveAndIsDeletedOrderBySequenceOrderAsc(ActivationStatusEnum.YES.getCode(), DeleteStatusEnum.NOT_DELETED.getCode());
        entity.setSequenceOrder(activeSegments.isEmpty() ? 10 : (activeSegments.get(activeSegments.size() - 1).getSequenceOrder() / 10 + 1) * 10);
        entity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        CustomerSegment saved = segmentRepository.save(entity);
        return convertToDto(saved);
    }

    @Override
    @Transactional
    public CustomerSegmentDto updateSegment(UUID segmentId, CustomerSegmentDto dto) {
        CustomerSegment entity = segmentRepository.findById(segmentId)
            .orElseThrow(() -> new ResourceNotFoundException("客群管道", "ID", segmentId));
        
        if (!entity.getSegmentName().equals(dto.getSegmentName())) {
            segmentRepository.findBySegmentNameAndIsDeleted(dto.getSegmentName(), DeleteStatusEnum.NOT_DELETED.getCode())
                .filter(existing -> !existing.getSegmentId().equals(segmentId))
                .ifPresent(existing -> {
                    throw new DataConflictException("客群管道名稱 '" + dto.getSegmentName() + "' 已存在。");
                });
        }
        entity.setSegmentName(dto.getSegmentName());
        // is_active and sequence_order are handled by separate methods
        CustomerSegment updated = segmentRepository.save(entity);
        return convertToDto(updated);
    }

    @Override
    @Transactional
    public void deleteSegment(UUID segmentId) {
        CustomerSegment entity = segmentRepository.findById(segmentId)
            .orElseThrow(() -> new ResourceNotFoundException("客群管道", "ID", segmentId));
        // TODO: Add check if segment is in use before deletion
        entity.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        segmentRepository.save(entity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CustomerSegmentDto> getActiveSegments() {
        return segmentRepository.findByIsActiveAndIsDeletedOrderBySequenceOrderAsc(ActivationStatusEnum.YES.getCode(), DeleteStatusEnum.NOT_DELETED.getCode())
            .stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<CustomerSegmentDto> getInactiveSegments() {
        return segmentRepository.findByIsActiveAndIsDeletedOrderBySequenceOrderAsc(ActivationStatusEnum.NO.getCode(), DeleteStatusEnum.NOT_DELETED.getCode())
            .stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public CustomerSegmentDto setSegmentStatus(UUID segmentId, boolean isActive) {
        CustomerSegment entity = segmentRepository.findById(segmentId)
            .orElseThrow(() -> new ResourceNotFoundException("客群管道", "ID", segmentId));
        entity.setIsActive(isActive ? ActivationStatusEnum.YES.getCode() : ActivationStatusEnum.NO.getCode());
        if (isActive) { // Moving to active, set sequence order to last
            List<CustomerSegment> activeSegments = segmentRepository.findByIsActiveAndIsDeletedOrderBySequenceOrderAsc(ActivationStatusEnum.YES.getCode(), DeleteStatusEnum.NOT_DELETED.getCode());
            entity.setSequenceOrder(activeSegments.isEmpty() ? 10 : (activeSegments.get(activeSegments.size() - 1).getSequenceOrder() / 10 + 1) * 10);
        } else {
            entity.setSequenceOrder(0); // Reset order if disabled
        }
        CustomerSegment updated = segmentRepository.save(entity);
        return convertToDto(updated);
    }

    @Override
    @Transactional
    public void updateActiveSegmentsOrder(List<CustomerSegmentDto> orderedActiveSegments) {
        if (orderedActiveSegments == null) return;
        for (int i = 0; i < orderedActiveSegments.size(); i++) {
            CustomerSegmentDto dto = orderedActiveSegments.get(i);
            if (dto.getSegmentId() == null) continue;
            final int sequence = (i + 1) * 10;
            segmentRepository.findById(dto.getSegmentId()).ifPresent(segment -> {
                segment.setSequenceOrder(sequence);
                segmentRepository.save(segment);
            });
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public CustomerSegmentDto getSegmentById(UUID segmentId) {
        return segmentRepository.findById(segmentId)
            .filter(s -> DeleteStatusEnum.NOT_DELETED.getCode().equals(s.getIsDeleted()))
            .map(this::convertToDto)
            .orElseThrow(() -> new ResourceNotFoundException("CustomerSegment", "ID", segmentId));
    }

    private CustomerSegmentDto convertToDto(CustomerSegment entity) {
        CustomerSegmentDto dto = new CustomerSegmentDto();
        BeanUtils.copyProperties(entity, dto);
        dto.setIsActive(entity.getIsActive());

        if (entity.getCreateBy() != null) {
            userAccountRepository.findById(entity.getCreateBy()).ifPresent(u -> dto.setCreateByName(u.getUserName()));
        }
        if (entity.getUpdateBy() != null) {
            userAccountRepository.findById(entity.getUpdateBy()).ifPresent(u -> dto.setUpdateByName(u.getUserName()));
        }
        return dto;
    }
} 