package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import com.eastking.enums.BooleanStatusEnum;
import com.eastking.exception.DataConflictException;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.model.dto.ExternalWarehouseDto;
import com.eastking.model.dto.WarehouseDto;
import com.eastking.model.entity.RegionEntity;
import com.eastking.model.entity.Warehouse;
import com.eastking.model.entity.StoreEntity;
import com.eastking.repository.RegionRepository;
import com.eastking.repository.WarehouseRepository;
import com.eastking.repository.StoreRepository;
import com.eastking.service.AuditLogService;
import com.eastking.service.WarehouseService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Transactional
public class WarehouseServiceImpl implements WarehouseService {

    private final WarehouseRepository warehouseRepository;
    private final RegionRepository regionRepository;
    private final AuditLogService auditLogService;
    private final ObjectMapper objectMapper;
    private final StoreRepository storeRepository;

    private String toJson(Object obj) {
        try {
            if (obj == null) return null;
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            // Log this error properly in a real app
            System.err.println("Error serializing object to JSON: " + e.getMessage());
            return "{\"error\":\"Error serializing details\"}";
        }
    }

    private WarehouseDto convertToDto(Warehouse entity) {
        WarehouseDto dto = new WarehouseDto();
        BeanUtils.copyProperties(entity, dto);
        if (entity.getRegion() != null) {
            dto.setRegionId(entity.getRegion().getRegionId());
            dto.setRegionName(entity.getRegion().getRegionName());
        }
        return dto;
    }

    private Warehouse convertToEntity(WarehouseDto dto) {
        Warehouse entity = new Warehouse();
        BeanUtils.copyProperties(dto, entity, "region"); // region will be set separately
        if (dto.getRegionId() != null) {
            RegionEntity region = regionRepository.findById(dto.getRegionId())
                .orElseThrow(() -> new ResourceNotFoundException("Region not found with ID: " + dto.getRegionId()));
            entity.setRegion(region);
        }
        return entity;
    }

    @Override
    @Transactional
    public WarehouseDto createWarehouse(WarehouseDto warehouseDto) {
        RegionEntity region = regionRepository.findById(warehouseDto.getRegionId())
            .orElseThrow(() -> new ResourceNotFoundException("Region not found with ID: " + warehouseDto.getRegionId()));

        warehouseRepository.findByWarehouseCodeAndRegionAndIsDeleted(
            warehouseDto.getWarehouseCode(), region, DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(existing -> {
                throw new DataConflictException("Warehouse code '" + warehouseDto.getWarehouseCode() + "' already exists in region '" + region.getRegionName() + "'.");
            });
         warehouseRepository.findByWarehouseNameAndRegionAndIsDeleted(
            warehouseDto.getWarehouseName(), region, DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(existing -> {
                throw new DataConflictException("Warehouse name '" + warehouseDto.getWarehouseName() + "' already exists in region '" + region.getRegionName() + "'.");
            });

        Warehouse warehouse = new Warehouse();
        warehouse.setWarehouseCode(warehouseDto.getWarehouseCode());
        warehouse.setWarehouseName(warehouseDto.getWarehouseName());
        warehouse.setRegion(region);
        warehouse.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        // Auditor fields will be set by JpaAuditing

        Warehouse savedWarehouse = warehouseRepository.save(warehouse);
        auditLogService.logAction(
            AuditActionTypeEnum.CREATE, 
            AuditDataTypeEnum.WAREHOUSE, 
            savedWarehouse.getWarehouseId().toString(), 
            savedWarehouse.getWarehouseName(), 
            toJson(convertToDto(savedWarehouse))
        );
        return convertToDto(savedWarehouse);
    }

    @Override
    @Transactional
    public WarehouseDto updateWarehouse(UUID warehouseId, WarehouseDto warehouseDto) {
        Warehouse warehouse = warehouseRepository.findById(warehouseId)
            .orElseThrow(() -> new ResourceNotFoundException("Warehouse not found with ID: " + warehouseId));

        if (DeleteStatusEnum.DELETED.getCode().equals(warehouse.getIsDeleted())) {
            throw new ResourceNotFoundException("Warehouse not found with ID: " + warehouseId + " (may be deleted).");
        }
        
        WarehouseDto oldDtoForLog = convertToDto(warehouse); // Capture state before change

        RegionEntity region = regionRepository.findById(warehouseDto.getRegionId())
            .orElseThrow(() -> new ResourceNotFoundException("Region not found with ID: " + warehouseDto.getRegionId()));

        if (!warehouse.getWarehouseCode().equals(warehouseDto.getWarehouseCode()) || !warehouse.getRegion().getRegionId().equals(region.getRegionId())) {
            warehouseRepository.findByWarehouseCodeAndRegionAndIsDeleted(
                warehouseDto.getWarehouseCode(), region, DeleteStatusEnum.NOT_DELETED.getCode())
                .filter(existing -> !existing.getWarehouseId().equals(warehouseId))
                .ifPresent(existing -> {
                    throw new DataConflictException("Warehouse code '" + warehouseDto.getWarehouseCode() + "' already exists in region '" + region.getRegionName() + "'.");
                });
        }
        if (!warehouse.getWarehouseName().equals(warehouseDto.getWarehouseName()) || !warehouse.getRegion().getRegionId().equals(region.getRegionId())) {
            warehouseRepository.findByWarehouseNameAndRegionAndIsDeleted(
                warehouseDto.getWarehouseName(), region, DeleteStatusEnum.NOT_DELETED.getCode())
                .filter(existing -> !existing.getWarehouseId().equals(warehouseId))
                .ifPresent(existing -> {
                    throw new DataConflictException("Warehouse name '" + warehouseDto.getWarehouseName() + "' already exists in region '" + region.getRegionName() + "'.");
                });
        }

        warehouse.setWarehouseCode(warehouseDto.getWarehouseCode());
        warehouse.setWarehouseName(warehouseDto.getWarehouseName());
        warehouse.setRegion(region);
        // Auditor fields will be updated by JpaAuditing

        Warehouse updatedWarehouse = warehouseRepository.save(warehouse);
        WarehouseDto newDtoForLog = convertToDto(updatedWarehouse);
        auditLogService.logAction(
            AuditActionTypeEnum.UPDATE, 
            AuditDataTypeEnum.WAREHOUSE, 
            updatedWarehouse.getWarehouseId().toString(), 
            updatedWarehouse.getWarehouseName(), 
            toJson(Map.of("before", oldDtoForLog, "after", newDtoForLog))
        );
        return newDtoForLog;
    }

    @Override
    @Transactional
    public void deleteWarehouse(UUID warehouseId) {
        Warehouse warehouse = warehouseRepository.findById(warehouseId)
            .orElseThrow(() -> new ResourceNotFoundException("Warehouse not found with ID: " + warehouseId));
        
        if (DeleteStatusEnum.DELETED.getCode().equals(warehouse.getIsDeleted())) {
             throw new ResourceNotFoundException("Warehouse already deleted with ID: " + warehouseId);
        }
        String warehouseNameForLog = warehouse.getWarehouseName();
        WarehouseDto dtoForLog = convertToDto(warehouse);

        warehouse.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        warehouseRepository.save(warehouse);
        auditLogService.logAction(
            AuditActionTypeEnum.DELETE, 
            AuditDataTypeEnum.WAREHOUSE, 
            warehouseId.toString(), 
            warehouseNameForLog, 
            toJson(Map.of("deletedWarehouse", dtoForLog))
        );
    }

    @Override
    @Transactional(readOnly = true)
    public WarehouseDto getWarehouseById(UUID warehouseId) {
        return warehouseRepository.findById(warehouseId)
            .filter(w -> DeleteStatusEnum.NOT_DELETED.getCode().equals(w.getIsDeleted()))
            .map(this::convertToDto)
            .orElseThrow(() -> new ResourceNotFoundException("Warehouse not found with ID: " + warehouseId));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WarehouseDto> getAllWarehouses(Pageable pageable, UUID regionId, String searchText) {
        Specification<Warehouse> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));

            if (regionId != null) {
                predicates.add(cb.equal(root.get("region").get("regionId"), regionId));
            }
            if (StringUtils.hasText(searchText)) {
                Predicate codeMatch = cb.like(cb.lower(root.get("warehouseCode")), "%" + searchText.toLowerCase() + "%");
                Predicate nameMatch = cb.like(cb.lower(root.get("warehouseName")), "%" + searchText.toLowerCase() + "%");
                predicates.add(cb.or(codeMatch, nameMatch));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return warehouseRepository.findAll(spec, pageable).map(this::convertToDto);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<WarehouseDto> getWarehousesByRegionId(UUID regionId) {
        RegionEntity region = regionRepository.findById(regionId)
            .orElseThrow(() -> new ResourceNotFoundException("Region not found with ID: " + regionId));
        return warehouseRepository.findByRegionAndIsDeleted(region, DeleteStatusEnum.NOT_DELETED.getCode())
            .stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ExternalWarehouseDto> getExternalWarehousesForDropdown(UUID regionId) {
        // Fetch all warehouses that are NOT assigned to the given regionId
        List<Warehouse> unassignedWarehouses = warehouseRepository.findByRegion_RegionIdNotAndIsDeleted(regionId, DeleteStatusEnum.NOT_DELETED.getCode());

        return unassignedWarehouses.stream()
            .map(warehouse -> new ExternalWarehouseDto(warehouse.getWarehouseCode(), warehouse.getWarehouseName()))
            .collect(Collectors.toList());
    }

    @Override
    public List<WarehouseDto> findWarehousesByCriteria(UUID regionId, Boolean isMain, Short companyDivisionCode) {
        Specification<Warehouse> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("isDeleted"), (short) 0));
            if (regionId != null) {
                predicates.add(cb.equal(root.get("region").get("regionId"), regionId));
            }
            if (isMain != null) {
                predicates.add(cb.equal(root.get("isMain"), isMain ? (short)1 : (short)0));
            }
            if (companyDivisionCode != null) {
                predicates.add(cb.equal(root.get("erpCompanyDivision"), companyDivisionCode));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
        return warehouseRepository.findAll(spec).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<WarehouseDto> getWarehousesByStoreId(UUID storeId, Short companyDivisionCode) {
        StoreEntity store = storeRepository.findById(storeId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到門市，ID: " + storeId));
        if (store.getRegion() == null) {
            return Collections.emptyList();
        }
        return findWarehousesByCriteria(store.getRegion().getRegionId(), null, companyDivisionCode);
    }
} 