package com.eastking.service.impl;

import com.eastking.model.dto.CityDto;
import com.eastking.model.dto.DistrictDto;
import com.eastking.model.dto.StreetDto;
import com.eastking.model.entity.City;
import com.eastking.model.entity.District;
import com.eastking.repository.CityRepository;
import com.eastking.repository.DistrictRepository;
import com.eastking.service.AddressService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AddressServiceImpl implements AddressService {

    private final CityRepository cityRepository;
    private final DistrictRepository districtRepository;
    private final ObjectMapper objectMapper;
    private final RestTemplate restTemplate = new RestTemplate();
    private static final Logger logger = LoggerFactory.getLogger(AddressServiceImpl.class);

    @Override
    public List<CityDto> getAllCities() {
        return cityRepository.findByIsDeletedOrderBySequenceOrderAsc((short) 0).stream()
                .map(city -> new CityDto(city.getCityId(), city.getCityName(), city.getCityCode(), null))
                .collect(Collectors.toList());
    }

    @Override
    public List<DistrictDto> getDistrictsByCityId(UUID cityId) {
        return districtRepository.findByCity_CityIdAndIsDeletedOrderBySequenceOrderAsc(cityId, (short) 0).stream()
                .map(district -> new DistrictDto(district.getDistrictId(), district.getDistrictName(), district.getDistrictCode(), district.getCity().getCityId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<StreetDto> getStreetsFromExternalApi(String cityName, String districtName) {
        final String externalApiUrl = "https://www.post.gov.tw/post/internet/Postal/streetNameData_zip6.jsp";

        URI uri = UriComponentsBuilder.fromHttpUrl(externalApiUrl)
                .queryParam("city", cityName)
                .queryParam("cityarea", districtName)
                .queryParam("rand", System.currentTimeMillis())
                .build()
                .encode(StandardCharsets.UTF_8)
                .toUri();
        
        try {
            String responseBody = restTemplate.getForObject(uri, String.class);

            if (responseBody == null || responseBody.trim().isEmpty() || responseBody.trim().equals("[]")) {
                return Collections.emptyList();
            }

            TypeReference<List<Map<String, String>>> typeRef = new TypeReference<>() {};
            List<Map<String, String>> streetMaps = objectMapper.readValue(responseBody, typeRef);

            return streetMaps.stream()
                    .map(map -> map.get("street_name"))
                    .filter(name -> name != null && !name.trim().isEmpty())
                    .distinct()
                    .sorted()
                    .map(StreetDto::new)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("Error fetching or parsing streets from external API for city '{}' and district '{}'. Error: {}", cityName, districtName, e.getMessage());
            return Collections.emptyList();
        }
    }

    @Override
    public List<CityDto> getCitiesWithDistricts() {
        List<City> cities = cityRepository.findAllByOrderBySequenceOrderAsc();
        List<District> allDistricts = districtRepository.findAll();

        Map<UUID, List<DistrictDto>> districtsByCityId = allDistricts.stream()
            .map(district -> new DistrictDto(district.getDistrictId(), district.getDistrictName(), district.getDistrictCode(), district.getCity().getCityId()))
            .collect(Collectors.groupingBy(DistrictDto::getCityId));

        return cities.stream()
                .map(city -> {
                    CityDto cityDto = new CityDto(city.getCityId(), city.getCityName(), city.getCityCode(), null);
                    cityDto.setDistricts(districtsByCityId.getOrDefault(city.getCityId(), Collections.emptyList()));
                    return cityDto;
                })
                .collect(Collectors.toList());
    }
} 