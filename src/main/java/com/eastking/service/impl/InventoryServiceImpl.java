package com.eastking.service.impl;

import com.eastking.enums.ReservationStatusEnum;
import com.eastking.model.entity.InventoryReservation;
import com.eastking.model.entity.Order;
import com.eastking.model.entity.OrderItem;
import com.eastking.model.entity.Warehouse;
import com.eastking.repository.InventoryReservationRepository;
import com.eastking.repository.WarehouseInventoryRepository;
import com.eastking.service.InventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.UUID;
import java.util.Optional;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class InventoryServiceImpl implements InventoryService {

    //這裡主要是處理倉庫的預扣庫存檢查

    private final WarehouseInventoryRepository warehouseInventoryRepository;
    private final InventoryReservationRepository inventoryReservationRepository;

    @Override
    @Transactional
    public boolean checkAndReserveStock(Order order) {
        for (OrderItem item : order.getItems()) {
            // 只處理需要出貨且有指定倉庫的品項
            if (!StringUtils.hasText(item.getWarehouseCode())) {
                continue;
            }

            UUID warehouseId = UUID.fromString(item.getWarehouseCode());
            String productBarcode = item.getProductBarcode();
            int requiredQuantity = item.getQuantity();

            // 1. 查詢實體庫存
            int physicalStock = warehouseInventoryRepository
                .findByWarehouseWarehouseIdAndProductBarcode(warehouseId, productBarcode)
                .map(inv -> inv.getQuantityOnHand())
                .orElse(0);

            // 2. 查詢已預留庫存
            Integer reservedStockNullable = inventoryReservationRepository
                .sumActiveReservationsForProductInWarehouse(warehouseId, productBarcode, ReservationStatusEnum.ACTIVE.getCode());
            int reservedStock = Optional.ofNullable(reservedStockNullable).orElse(0);
            
            // 3. 計算可銷售庫存
            int availableStock = physicalStock - reservedStock;
            log.info("庫存檢查 - 商品: {}, 倉庫ID: {}. 實體庫存: {}, 已預留: {}, 可銷售: {}, 需求: {}",
                    productBarcode, warehouseId, physicalStock, reservedStock, availableStock, requiredQuantity);

            // 4. 判斷庫存是否足夠
            if (availableStock < requiredQuantity) {
                log.warn("庫存不足 - 商品: {}, 倉庫ID: {}. 需求量 {} 超過可銷售庫存 {}",
                        productBarcode, warehouseId, requiredQuantity, availableStock);
                return false; // 任何一項不足，則預留失敗
            }
        }
        
        // 如果所有品項庫存都足夠，則創建預留記錄
        for (OrderItem item : order.getItems()) {
            if (!StringUtils.hasText(item.getWarehouseCode())) {
                continue;
            }
            
            InventoryReservation reservation = new InventoryReservation();
            reservation.setOrder(order);
            reservation.setOrderItem(item);
            
            Warehouse warehouseRef = new Warehouse();
            warehouseRef.setWarehouseId(UUID.fromString(item.getWarehouseCode()));
            reservation.setWarehouse(warehouseRef);
            
            reservation.setProductBarcode(item.getProductBarcode());
            reservation.setReservedQuantity(item.getQuantity());
            reservation.setReservationStatusCode(ReservationStatusEnum.ACTIVE.getCode());
            
            inventoryReservationRepository.save(reservation);
            log.info("庫存預留成功 - 訂單: {}, 商品: {}, 數量: {}", order.getOrderNumber(), item.getProductBarcode(), item.getQuantity());
        }

        return true;
    }

    @Override
    @Transactional
    public void cancelReservation(Order order) {
        log.info("正在取消訂單 {} 的庫存預留...", order.getOrderNumber());
        List<InventoryReservation> reservations = inventoryReservationRepository
            .findByOrder_OrderIdAndReservationStatusCode(order.getOrderId(), ReservationStatusEnum.ACTIVE.getCode());

        for (InventoryReservation reservation : reservations) {
            reservation.setReservationStatusCode(ReservationStatusEnum.CANCELLED.getCode());
        }
        inventoryReservationRepository.saveAll(reservations);
        log.info("已成功取消訂單 {} 的 {} 筆庫存預留。", order.getOrderNumber(), reservations.size());
    }

    @Override
    public void consumeReservation(Order order) {
        // TODO: 訂單完成出貨時的邏輯
        // 1. 找到此訂單的所有 ACTIVE 預留
        // 2. 將預留狀態更新為 CONSUMED
        // 3. 根據預留記錄，在 sm_store_inventory_transaction_log 中創建一筆正式的庫存扣減記錄 (SALE or DISPATCH)
        // 4. 更新 sm_warehouse_inventory 中的實體庫存 (quantity_on_hand)
        log.warn("consumeReservation 方法尚未完全實作。訂單: {}", order.getOrderNumber());
    }
} 