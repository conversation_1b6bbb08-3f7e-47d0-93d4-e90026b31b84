package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.model.dto.RegionDto;
import com.eastking.model.entity.RegionEntity;
import com.eastking.repository.RegionRepository;
import com.eastking.repository.StoreRepository;
import com.eastking.service.RegionService;
import com.eastking.exception.ResourceNotFoundException;
import com.eastking.exception.DataConflictException;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.persistence.criteria.Predicate;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class RegionServiceImpl implements RegionService {

    private final RegionRepository regionRepository;
    private final StoreRepository storeRepository;

    public RegionServiceImpl(RegionRepository regionRepository, StoreRepository storeRepository) {
        this.regionRepository = regionRepository;
        this.storeRepository = storeRepository;
    }

    @Override
    @Transactional
    public RegionDto createRegion(RegionDto regionDto) {
        regionRepository.findByRegionNameAndIsDeleted(regionDto.getRegionName(), DeleteStatusEnum.NOT_DELETED.getCode())
            .ifPresent(e -> {
                throw new DataConflictException("地區名稱已存在: " + regionDto.getRegionName());
            });
        RegionEntity regionEntity = new RegionEntity();
        BeanUtils.copyProperties(regionDto, regionEntity, "regionId", "createTime", "updateTime", "createBy", "updateBy", "isDeleted");
        regionEntity.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());
        // createBy/updateBy will be set by AuditingEntityListener
        RegionEntity savedEntity = regionRepository.save(regionEntity);
        return convertToDto(savedEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RegionDto> getRegionById(UUID regionId) {
        return regionRepository.findById(regionId)
            .filter(region -> !DeleteStatusEnum.DELETED.getCode().equals(region.getIsDeleted()))
            .map(this::convertToDto);
    }
    
    @Override
    @Transactional(readOnly = true)
    public RegionEntity findRegionEntityById(UUID regionId) {
        return regionRepository.findById(regionId)
            .filter(region -> !DeleteStatusEnum.DELETED.getCode().equals(region.getIsDeleted()))
            .orElse(null);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RegionDto> getRegionByName(String regionName) {
        return regionRepository.findByRegionNameAndIsDeleted(regionName, DeleteStatusEnum.NOT_DELETED.getCode())
            .map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RegionDto> getAllRegions(Pageable pageable, String regionName) {
        Specification<RegionEntity> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()));
            if (StringUtils.hasText(regionName)) {
                predicates.add(criteriaBuilder.like(root.get("regionName"), "%" + regionName + "%"));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        return regionRepository.findAll(spec, pageable).map(this::convertToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RegionDto> getAllRegionsList() {
        Specification<RegionEntity> spec = (root, query, criteriaBuilder) -> 
            criteriaBuilder.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode());
        return regionRepository.findAll(spec).stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<RegionDto> getAllActiveRegions() {
        // "Active" regions are those not marked as deleted.
        // The RegionEntity itself doesn't have a separate 'isActive' field in the DDL provided.
        return regionRepository.findByIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode())
            .stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public RegionDto updateRegion(UUID regionId, RegionDto regionDto) {
        RegionEntity regionEntity = regionRepository.findById(regionId)
            .filter(r -> !DeleteStatusEnum.DELETED.getCode().equals(r.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("找不到ID為 " + regionId + " 的地區"));

        // Check for name conflict if name is being changed
        if (!regionEntity.getRegionName().equals(regionDto.getRegionName())) {
            regionRepository.findByRegionNameAndIsDeleted(regionDto.getRegionName(), DeleteStatusEnum.NOT_DELETED.getCode())
                .ifPresent(e -> {
                    if (!e.getRegionId().equals(regionId)) {
                        throw new DataConflictException("地區名稱已存在: " + regionDto.getRegionName());
                    }
                });
        }

        BeanUtils.copyProperties(regionDto, regionEntity, "regionId", "createTime", "updateTime", "createBy", "updateBy", "isDeleted");
        // updateBy will be set by AuditingEntityListener
        RegionEntity updatedEntity = regionRepository.save(regionEntity);
        return convertToDto(updatedEntity);
    }

    @Override
    @Transactional
    public void deleteRegion(UUID regionId) {
        RegionEntity regionEntity = regionRepository.findById(regionId)
            .filter(r -> !DeleteStatusEnum.DELETED.getCode().equals(r.getIsDeleted()))
            .orElseThrow(() -> new ResourceNotFoundException("找不到ID為 " + regionId + " 的地區，或已被刪除"));
        
        long storeCount = storeRepository.countByRegion_RegionIdAndIsDeleted(regionId, DeleteStatusEnum.NOT_DELETED.getCode());
        if (storeCount > 0) {
            throw new DataConflictException("無法刪除地區，尚有 " + storeCount + " 個門市使用此地區。");
        }

        regionEntity.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
        regionRepository.save(regionEntity);
    }

    private RegionDto convertToDto(RegionEntity entity) {
        RegionDto dto = new RegionDto();
        BeanUtils.copyProperties(entity, dto);
        // For createBy/updateBy, if you need to resolve to names, do it here or in a dedicated mapper
        return dto;
    }
} 