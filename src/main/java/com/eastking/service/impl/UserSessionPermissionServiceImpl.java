package com.eastking.service.impl;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.model.entity.UserAccount;
import com.eastking.model.entity.UserSessionPermission;
import com.eastking.repository.UserSessionPermissionRepository;
import com.eastking.service.UserSessionPermissionService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class UserSessionPermissionServiceImpl implements UserSessionPermissionService {

    private static final Logger logger = LoggerFactory.getLogger(UserSessionPermissionServiceImpl.class);

    private final UserSessionPermissionRepository userSessionPermissionRepository;
    private final ObjectMapper objectMapper; // For JSON processing

    @Value("${app.security.jwt.expiration-ms}")
    private long sessionPermissionExpirationMs;

    public UserSessionPermissionServiceImpl(UserSessionPermissionRepository userSessionPermissionRepository,
                                          ObjectMapper objectMapper) {
        this.userSessionPermissionRepository = userSessionPermissionRepository;
        this.objectMapper = objectMapper;
    }

    @Override
    @Transactional
    public UserSessionPermission createOrUpdateUserSessionPermission(UserAccount userAccount, List<String> grantedFunctionCodes) {
        // Invalidate previous sessions for this user (optional, but good practice)
        // For now, we just create a new one. A more robust solution might involve limiting active sessions.
        
        UserSessionPermission sessionPermission = new UserSessionPermission();
        sessionPermission.setUserSessionPermissionId(UUID.randomUUID()); // This is the new Session UUID
        sessionPermission.setUserAccount(userAccount);
        sessionPermission.setExpiryTime(OffsetDateTime.now().plusNanos(sessionPermissionExpirationMs * 1_000_000L));
        sessionPermission.setIsDeleted(DeleteStatusEnum.NOT_DELETED.getCode());

        try {
            String permissionsJson = objectMapper.writeValueAsString(grantedFunctionCodes != null ? grantedFunctionCodes : Collections.emptyList());
            sessionPermission.setPermissionsJson(permissionsJson);
        } catch (JsonProcessingException e) {
            logger.error("Error serializing permissions to JSON for user {}: {}", userAccount.getEmployeeId(), e.getMessage());
            // Depending on policy, either throw an error or save with empty permissions
            sessionPermission.setPermissionsJson("[]"); 
        }
        return userSessionPermissionRepository.save(sessionPermission);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserSessionPermission> findValidSessionPermission(UUID sessionUuid) {
        if (sessionUuid == null) return Optional.empty();
        return userSessionPermissionRepository.findByUserSessionPermissionIdAndIsDeletedAndExpiryTimeAfter(
                sessionUuid, 
                DeleteStatusEnum.NOT_DELETED.getCode(), 
                OffsetDateTime.now()
        );
    }

    @Override
    @Transactional
    public void clearUserSessionPermissions(UserAccount userAccount) {
        // This could be a soft delete or hard delete depending on requirements.
        // backend_rules.md doesn't specify, soft delete is safer.
        List<UserSessionPermission> sessions = userSessionPermissionRepository.findAll((root, query, cb) -> 
            cb.equal(root.get("userAccount"), userAccount));
        sessions.forEach(session -> {
            session.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
            session.setExpiryTime(OffsetDateTime.now()); // Expire immediately
        });
        userSessionPermissionRepository.saveAll(sessions);
        logger.info("Cleared {} active session permissions for user: {}", sessions.size(), userAccount.getEmployeeId());
    }

    @Override
    @Transactional
    public void clearExpiredSessionPermissions() {
        List<UserSessionPermission> expiredSessions = userSessionPermissionRepository.findAll((root, query, cb) ->
            cb.and(
                cb.equal(root.get("isDeleted"), DeleteStatusEnum.NOT_DELETED.getCode()),
                cb.lessThanOrEqualTo(root.get("expiryTime"), OffsetDateTime.now())
            )
        );
        if (!expiredSessions.isEmpty()) {
            expiredSessions.forEach(session -> {
                session.setIsDeleted(DeleteStatusEnum.DELETED.getCode());
            });
            userSessionPermissionRepository.saveAll(expiredSessions);
            logger.info("Cleared {} expired session permissions.", expiredSessions.size());
        } else {
            logger.debug("No expired session permissions to clear.");
        }
    }
    
    // Helper to parse JSON, might be used in JWTAuthenticationFilter
    public List<String> parsePermissionsJson(String permissionsJson) {
        if (!StringUtils.hasText(permissionsJson)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(permissionsJson, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            logger.error("Error parsing permissions JSON: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
} 