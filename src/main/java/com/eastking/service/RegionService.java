package com.eastking.service;

import com.eastking.model.dto.RegionDto;
import com.eastking.model.entity.RegionEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 地區資料服務介面
 */
public interface RegionService {

    RegionDto createRegion(RegionDto regionDto);

    Optional<RegionDto> getRegionById(UUID regionId);

    Optional<RegionDto> getRegionByName(String regionName);

    Page<RegionDto> getAllRegions(Pageable pageable, String regionName); // Added filter by name
    
    List<RegionDto> getAllRegionsList(); // For populating dropdowns, no pagination

    RegionDto updateRegion(UUID regionId, RegionDto regionDto);

    void deleteRegion(UUID regionId);
    
    RegionEntity findRegionEntityById(UUID regionId); // Helper for other services

    List<RegionDto> getAllActiveRegions(); // New method
} 