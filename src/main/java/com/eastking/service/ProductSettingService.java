package com.eastking.service;

import com.eastking.model.dto.ProductSettingDto;
import com.eastking.model.dto.ProductSearchResultDto;
import com.eastking.model.dto.ProductSearchResultWithGiftDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface ProductSettingService {

    ProductSettingDto createProductSetting(ProductSettingDto productSettingDto);

    Optional<ProductSettingDto> getProductSettingById(UUID id, String companyContext);
    
    Optional<ProductSettingDto> getProductSettingByBarcode(String productBarcode, String companyContext);

    Page<ProductSettingDto> getAllProductSettings(Pageable pageable, String productBarcode, String productName, Boolean isActive, String companyContext);

    ProductSettingDto updateProductSetting(UUID id, ProductSettingDto productSettingDto, String companyContext);

    void deleteProductSetting(UUID id);

    List<ProductSettingDto> findByProductBarcodeIn(List<String> barcodes, String companyContext);

    // Methods from ExternalProductService (now using ProductSearchResultDto)
    List<ProductSearchResultDto> searchProductsForMenu(String keyword, String companyContext, UUID storeId);
    Page<ProductSearchResultDto> searchProductsForMenu(String keyword, Pageable pageable, String companyContext, UUID storeId);

    List<ProductSettingDto> findByKeyword(String keyword);
    
    Page<ProductSettingDto> searchDispatchProducts(String productBarcode, String productName, Boolean isActive, OffsetDateTime startDate, OffsetDateTime endDate, Pageable pageable);
    
    List<ProductSearchResultDto> searchStoreProducts(String keyword, UUID storeId, String companyContext);

    List<ProductSearchResultWithGiftDto> searchDispatchableProducts(String keyword, String companyContext, UUID storeId, UUID distributorId);

    List<ProductSearchResultDto> searchAccessories(String keyword, String companyContext, UUID storeId);

    List<ProductSearchResultDto> searchProductsByMainFlag(String keyword, boolean isMain);
} 