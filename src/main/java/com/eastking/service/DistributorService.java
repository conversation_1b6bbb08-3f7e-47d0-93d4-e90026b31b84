package com.eastking.service;

import com.eastking.model.dto.DistributorDto;
import java.util.List;
import java.util.UUID;
import java.util.Optional;

public interface DistributorService {

    /**
     * Finds a distributor by its ID.
     *
     * @param id The UUID of the distributor.
     * @return An Optional containing the DistributorDto if found, otherwise empty.
     */
    Optional<DistributorDto> getDistributorById(UUID id);

    /**
     * Finds all active (not deleted) distributors, ordered by name.
     * 
     * @return A list of DistributorDto.
     */
    List<DistributorDto> getAllActiveSelectableDistributors();

    // Methods for create/update/delete/sync can be added here if management from this app is needed in the future.
    // For now, focusing on read operations as per requirement.
} 