package com.eastking.service;

import com.eastking.model.entity.UserAccount;
import com.eastking.model.entity.UserSessionPermission;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface UserSessionPermissionService {

    /**
     * 創建或更新使用者的會話權限。
     * 登入時調用，產生新的 session UUID 並存儲權限。
     * @param userAccount 使用者帳號實體
     * @param grantedFunctionCodes 使用者被授予的功能權限代碼列表 (例如 ["F0001_READ", "F0002_EDIT"])
     * @return 新創建的 UserSessionPermission 實體，包含新的 session UUID
     */
    UserSessionPermission createOrUpdateUserSessionPermission(UserAccount userAccount, List<String> grantedFunctionCodes);

    /**
     * 根據會話 UUID 查找有效的會話權限。
     * @param sessionUuid 會話 UUID
     * @return Optional<UserSessionPermission>
     */
    Optional<UserSessionPermission> findValidSessionPermission(UUID sessionUuid);

    /**
     * 清理指定使用者帳號的所有會話權限 (例如，當使用者被禁用或刪除時)。
     * @param userAccount 使用者帳號實體
     */
    void clearUserSessionPermissions(UserAccount userAccount);

    /**
     * 清理所有已過期的會話權限 (可由排程作業調用)。
     */
    void clearExpiredSessionPermissions();
} 