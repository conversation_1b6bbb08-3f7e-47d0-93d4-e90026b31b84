package com.eastking.service;

import com.eastking.model.dto.MemberLevelDto;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 會員等級服務介面
 */
public interface MemberLevelService {

    MemberLevelDto createMemberLevel(MemberLevelDto memberLevelDto);

    Optional<MemberLevelDto> getMemberLevelById(UUID id);
    
    Optional<MemberLevelDto> getMemberLevelByName(String levelName);

    List<MemberLevelDto> getAllMemberLevels(); // For UI where all levels are typically shown/edited together
    
    // Page<MemberLevelDto> getAllMemberLevels(Pageable pageable, String levelName); // If pagination is needed later

    MemberLevelDto updateMemberLevel(UUID id, MemberLevelDto memberLevelDto);

    void deleteMemberLevel(UUID id); // Consider implications if members are assigned to this level

    List<MemberLevelDto> getAllActiveMemberLevels();
} 