package com.eastking.service;

import com.eastking.model.dto.request.AnnouncementQueryRequest;
import com.eastking.model.dto.request.AnnouncementRequest;
import com.eastking.model.dto.response.AnnouncementResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface AnnouncementService {

    AnnouncementResponse createAnnouncement(AnnouncementRequest announcementRequest);

    AnnouncementResponse updateAnnouncement(UUID announcementId, AnnouncementRequest announcementRequest);

    void deleteAnnouncement(UUID announcementId);

    AnnouncementResponse getAnnouncementById(UUID announcementId);

    Page<AnnouncementResponse> getAllAnnouncements(AnnouncementQueryRequest queryRequest, Pageable pageable);

    // For public view, e.g., in login pop-up - non-paginated, filtered for active/current
    List<AnnouncementResponse> getActivePublicAnnouncements();
} 