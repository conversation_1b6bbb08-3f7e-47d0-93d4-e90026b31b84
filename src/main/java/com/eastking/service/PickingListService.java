package com.eastking.service;

import com.eastking.enums.MaterialTypeStatusEnum;
import com.eastking.model.dto.request.CompletePickingRequestDto;
import com.eastking.model.dto.request.PickingListQueryDto;
import com.eastking.model.dto.response.PickingListDetailDto;
import com.eastking.model.dto.response.PickingListSummaryDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface PickingListService {

    Page<PickingListSummaryDto> searchPickingLists(PickingListQueryDto queryDto, MaterialTypeStatusEnum materialType, Pageable pageable);

    PickingListDetailDto getPickingListDetailById(UUID id);

    void completePicking(UUID orderId, CompletePickingRequestDto requestDto);

    void completeRefundPicking(UUID orderId, CompletePickingRequestDto requestDto);
} 