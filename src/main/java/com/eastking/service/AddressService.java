package com.eastking.service;

import com.eastking.model.dto.CityDto;
import com.eastking.model.dto.DistrictDto;
import com.eastking.model.dto.StreetDto;

import java.util.List;
import java.util.UUID;

public interface AddressService {

    List<CityDto> getAllCities();

    List<DistrictDto> getDistrictsByCityId(UUID cityId);

    List<StreetDto> getStreetsFromExternalApi(String cityName, String districtName);
    
    List<CityDto> getCitiesWithDistricts();
} 