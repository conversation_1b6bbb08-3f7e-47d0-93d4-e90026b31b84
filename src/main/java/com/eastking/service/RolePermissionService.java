package com.eastking.service;

import com.eastking.model.dto.request.RolePermissionDataRequest;
import com.eastking.model.dto.response.RoleDetailResponse;
import com.eastking.model.dto.response.RoleSummaryResponse;
import com.eastking.model.dto.response.SystemFunctionResponseDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface RolePermissionService {

    RoleDetailResponse createRoleWithPermissions(RolePermissionDataRequest request);

    RoleDetailResponse updateRoleWithPermissions(UUID roleId, RolePermissionDataRequest request);

    void deleteRole(UUID roleId); // Soft delete

    RoleDetailResponse getRoleWithPermissionsById(UUID roleId);

    Page<RoleSummaryResponse> getAllRolesSummary(Pageable pageable);

    List<RoleSummaryResponse> getAllRolesAsList(); // For dropdowns, etc.
    
    // Method to get all defined system functions for building UI
    // This might better belong in a SystemFunctionService, but placing here for now for UI needs.
    List<SystemFunctionResponseDto> getAllSystemFunctionsForPermissionSetting(); 
} 