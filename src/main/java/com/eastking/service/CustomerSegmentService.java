package com.eastking.service;

import com.eastking.model.dto.CustomerSegmentDto;
import java.util.List;
import java.util.UUID;

public interface CustomerSegmentService {
    CustomerSegmentDto createSegment(CustomerSegmentDto dto);
    CustomerSegmentDto updateSegment(UUID segmentId, CustomerSegmentDto dto); // For renaming
    void deleteSegment(UUID segmentId);
    List<CustomerSegmentDto> getActiveSegments();
    List<CustomerSegmentDto> getInactiveSegments();
    CustomerSegmentDto setSegmentStatus(UUID segmentId, boolean isActive); // Enable/Disable
    void updateActiveSegmentsOrder(List<CustomerSegmentDto> orderedActiveSegments);
    CustomerSegmentDto getSegmentById(UUID segmentId);
} 