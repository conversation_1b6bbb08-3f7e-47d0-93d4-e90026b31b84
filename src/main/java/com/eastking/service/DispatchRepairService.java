package com.eastking.service;

import com.eastking.enums.DispatchRepairTypeEnum;
import com.eastking.enums.DispatchStatusEnum;
import com.eastking.model.dto.request.DispatchRepairFilterRequest;
import com.eastking.model.dto.request.DispatchOrderFilterRequest;
import com.eastking.model.dto.request.DispatchRepairRequestDto;
import com.eastking.model.dto.request.CompleteContactRequestDto;
import com.eastking.model.dto.request.UpdateScheduleRequestDto;
import com.eastking.model.dto.request.CompleteWorkingRequestDto;
import com.eastking.model.dto.request.CompletePaymentRequestDto;
import com.eastking.model.dto.request.CompleteSignatureRequestDto;
import com.eastking.model.dto.request.SubmitHandlingRequestDto;
import com.eastking.model.dto.request.DispatchCollaboratorRequest;
import com.eastking.model.dto.response.DispatchRepairDetailDto;
import com.eastking.model.dto.response.DispatchRepairSummaryDto;
import com.eastking.model.dto.response.DispatchRepairItemSummaryDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface DispatchRepairService {

    /**
     * 根據條件搜尋派工/維修/退貨單據
     * @param filter 過濾條件
     * @param pageable 分頁資訊
     * @return 分頁後的單據摘要列表
     */
    Page<DispatchRepairSummaryDto> search(DispatchRepairFilterRequest filter, Pageable pageable);

    /**
     * 根據ID獲取單一筆單據的詳細資訊
     * @param id 單據ID
     * @return 單據詳細資訊
     */
    DispatchRepairDetailDto findById(UUID id);
    
    /**
     * 查詢待技師接單的列表. (for legacy API compatibility)
     * @return 待接單列表
     */
    List<DispatchRepairSummaryDto> getPendingDispatchOrders();

    /**
     * 技師接收多個派工單. (for legacy API compatibility)
     * @param dispatchOrderIds 派工單ID列表
     */
    void acceptDispatchOrders(List<UUID> dispatchOrderIds, UUID technicianId);

    List<DispatchRepairItemSummaryDto> getPendingDispatchItems(UUID dispatchOrderId, DispatchStatusEnum status);

    void createDispatchRepairsFromOrder(UUID orderId);
    void createDispatchRepairsFromOrder(UUID orderId, DispatchRepairTypeEnum dispatchRepairType, DispatchStatusEnum dispatchStatus);

    void completeContactStep(UUID dispatchRepairId, CompleteContactRequestDto requestDto);

    void updateSchedule(UUID dispatchRepairId, UpdateScheduleRequestDto requestDto);

    void completeMaterialCollection(UUID dispatchRepairId);

    void completeDeparture(UUID dispatchRepairId);

    void completeWorkingStep(UUID dispatchRepairId, CompleteWorkingRequestDto requestDto);

    void completePaymentStep(UUID dispatchRepairId, CompletePaymentRequestDto requestDto);

    void completeSignatureStep(UUID dispatchRepairId, CompleteSignatureRequestDto requestDto);

    void submitHandlingStep(UUID dispatchRepairId, SubmitHandlingRequestDto requestDto);

    DispatchRepairDetailDto createDispatchRepair(DispatchRepairRequestDto requestDto);
    
    Page<DispatchRepairSummaryDto> searchForTechnicianView(DispatchOrderFilterRequest filter, Pageable pageable);

    DispatchRepairDetailDto updateDispatchRepair(UUID id, DispatchRepairRequestDto requestDto);

    void deleteDispatchRepair(UUID dispatchRepairId);

    void inviteCollaborators(UUID dispatchRepairId, DispatchCollaboratorRequest request);

    void acceptCollaboration(UUID dispatchRepairId, UUID technicianId);

    void updateUrgentStatus(UUID dispatchRepairId, boolean isUrgent);

    // In the future, we will add create, update, delete methods here.
} 