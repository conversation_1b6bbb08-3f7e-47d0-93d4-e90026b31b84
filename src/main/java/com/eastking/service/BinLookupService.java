package com.eastking.service;

import com.eastking.model.dto.response.BinLookupResponseDto;
import com.eastking.model.entity.BinListData;
import com.eastking.repository.BinListDataRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class BinLookupService {

    private final BinListDataRepository binListDataRepository;

    public Optional<BinLookupResponseDto> findCardInfoByCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 8) {
            return Optional.empty();
        }
        
        String cardNumberPrefix = cardNumber.substring(0, 8);
        List<BinListData> results = binListDataRepository.findBestMatchByBinPrefix(cardNumberPrefix);

        if (results.isEmpty()) {
            return Optional.empty();
        }

        // findBestMatchByBinPrefix 已經按長度排序，第一個就是最好的匹配
        BinListData bestMatch = results.get(0);
        
        BinLookupResponseDto dto = new BinLookupResponseDto(bestMatch.getBrand(), bestMatch.getIssuer());
        return Optional.of(dto);
    }
} 