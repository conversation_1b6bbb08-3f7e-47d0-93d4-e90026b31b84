package com.eastking.service;

import com.eastking.model.dto.request.DailySettlementRequestDto;
import com.eastking.model.dto.response.StoreDailySettlementDto;

import java.time.LocalDate;
import java.util.UUID;

public interface StoreDailySettlementService {

    /**
     * 獲取指定門市和日期的日結準備資料
     * @param storeId 門市ID
     * @param date 日期
     * @return 日結準備資料 DTO
     */
    StoreDailySettlementDto getDailySettlementData(UUID storeId, LocalDate date);

    /**
     * 執行並儲存門市日結
     * @param requestDto 日結請求資料
     * @return 完成的日結資料 DTO
     */
    StoreDailySettlementDto performDailySettlement(DailySettlementRequestDto requestDto);
} 