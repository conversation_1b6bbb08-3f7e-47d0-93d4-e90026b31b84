package com.eastking.service;

import com.eastking.enums.ProductMenuTypeEnum;
import com.eastking.model.dto.ProductMenuCategoryDto;
import com.eastking.model.dto.ProductMenuItemDto;
import com.eastking.model.dto.ProductMenuNodeDto;

import java.util.List;
import java.util.UUID;

public interface ProductMenuService {

    // Category Management
    ProductMenuCategoryDto createCategory(ProductMenuCategoryDto categoryDto);
    ProductMenuCategoryDto updateCategory(UUID categoryId, ProductMenuCategoryDto categoryDto);
    void deleteCategory(UUID categoryId);
    List<ProductMenuNodeDto> getMenuTree(ProductMenuTypeEnum menuType); // Gets the full tree for a menu type
    List<ProductMenuCategoryDto> getCategoriesByParent(ProductMenuTypeEnum menuType, UUID parentCategoryId); // Get immediate children of a category or root categories if parentId is null

    // Menu Item Management (linking products to categories)
    ProductMenuItemDto addItemToCategory(UUID categoryId, ProductMenuItemDto itemDto);
    void removeItemFromCategory(UUID itemId);
    // updateItemInCategorny might involve changing sort order or the product itself (if allowed)
    // For now, re-ordering handled by a batch update method

    // Reordering
    // Expects a list of nodes (categories or items) with their new parent (if changed) and sort order
    void updateMenuOrder(ProductMenuTypeEnum menuType, List<ProductMenuNodeDto> orderedNodes);
    
    // Helper for external product data - This might belong to a more generic "ExternalProductService"
    // For now, placing a simplified version here if ProductMenuService needs to resolve product names.
    // List<ExternalProductDto> searchExternalProducts(String keyword); // Not implemented in this step
} 