package com.eastking.service;

import com.eastking.model.dto.ExternalWarehouseDto;
import com.eastking.model.dto.WarehouseDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.List;
import java.util.UUID;

public interface WarehouseService {
    WarehouseDto createWarehouse(WarehouseDto warehouseDto);
    WarehouseDto updateWarehouse(UUID warehouseId, WarehouseDto warehouseDto);
    void deleteWarehouse(UUID warehouseId); // Soft delete
    WarehouseDto getWarehouseById(UUID warehouseId);
    Page<WarehouseDto> getAllWarehouses(Pageable pageable, UUID regionId, String searchText);
    List<WarehouseDto> getWarehousesByRegionId(UUID regionId);
    List<ExternalWarehouseDto> getExternalWarehousesForDropdown(UUID regionId); // From "正航"
    List<WarehouseDto> findWarehousesByCriteria(UUID regionId, Boolean isMain, Short companyDivisionCode);
    List<WarehouseDto> getWarehousesByStoreId(UUID storeId, Short companyDivisionCode);
} 