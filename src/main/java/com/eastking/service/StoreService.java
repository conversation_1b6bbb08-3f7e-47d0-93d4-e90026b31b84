package com.eastking.service;

import com.eastking.model.dto.StoreDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 門市資料服務介面
 */
public interface StoreService {

    StoreDto createStore(StoreDto storeDto);

    Optional<StoreDto> getStoreById(UUID storeId);

    Page<StoreDto> getAllStores(Pageable pageable, String storeName, UUID regionId, Boolean isActive, String companyContext);

    StoreDto updateStore(UUID storeId, StoreDto storeDto);

    void deleteStore(UUID storeId);
    
    // Additional methods for managing staff and tax IDs might be here or handled via dedicated mapping services
    // For now, assuming create/update in StoreDto will handle these associations.

    List<StoreDto> getAllActiveSelectableStores(String companyContext);

    /**
     * 取得可選的門市列表 (可依地區過濾)
     * @param regionId 可選的地區ID，若為null則不過濾地區
     * @param companyContext 公司別上下文
     * @return 門市列表
     */
    List<StoreDto> getSelectableStoresByRegion(UUID regionId, String companyContext);
} 