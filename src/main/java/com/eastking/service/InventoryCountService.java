package com.eastking.service;

import com.eastking.model.dto.InventoryCountSheetDto;
import com.eastking.model.dto.request.InventoryCountSheetRequestDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface InventoryCountService {

    Page<InventoryCountSheetDto> searchInventoryCountSheets(
        Short companyDivisionCode,
        UUID storeId,
        String countMonth,
        Short approvalStatusCode,
        Pageable pageable
    );

    InventoryCountSheetDto getInventoryCountSheetById(UUID sheetId);

    InventoryCountSheetDto createInventoryCountSheet(InventoryCountSheetRequestDto requestDto);

    // Methods for creating and updating will be added later
} 