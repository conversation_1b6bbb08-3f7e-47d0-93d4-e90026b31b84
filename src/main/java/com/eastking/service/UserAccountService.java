package com.eastking.service;

import com.eastking.model.dto.UserAccountDto;
import com.eastking.model.dto.UserAccountSlimDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 使用者帳號服務介面
 */
public interface UserAccountService {

    UserAccountDto createUserAccount(UserAccountDto userAccountDto);

    Optional<UserAccountDto> getUserAccountById(UUID userId);

    Optional<UserAccountDto> getUserByEmployeeId(String employeeId);

    Page<UserAccountDto> getAllUserAccounts(Pageable pageable, String employeeId, String userName, Boolean isActive, Short accountType, String companyContext);
    
    List<UserAccountDto> getAllActiveUsersForSelection(); // For staff selection modals

    UserAccountDto updateUserAccount(UUID userId, UserAccountDto userAccountDto);

    UserAccountDto updateUserRoles(UUID userId, List<UUID> roleIds);

    void deleteUserAccount(UUID userId); // Soft delete

    void changePassword(UUID userId, String oldPassword, String newPassword);

    List<UserAccountDto> findActiveUsersByRoleCode(String roleCode);

    List<UserAccountDto> getSalespersons(UUID storeId);

    List<UserAccountSlimDto> getTechnicians();

    List<UserAccountSlimDto> findSelectableCollaborators(UUID dispatchRepairId);
} 