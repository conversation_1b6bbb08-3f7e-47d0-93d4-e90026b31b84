package com.eastking.service;

import com.eastking.model.dto.request.WholesaleOrderRequestDto;
import com.eastking.model.dto.response.WholesaleOrderDetailDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface WholesaleOrderService {

    WholesaleOrderDetailDto createWholesaleOrder(WholesaleOrderRequestDto requestDto);

    WholesaleOrderDetailDto getWholesaleOrderById(UUID orderId);

    Page<WholesaleOrderDetailDto> searchWholesaleOrders(
        Short companyDivisionCode,
        String orderNumber,
        UUID storeId,
        Short orderStatusCode,
        String dateFrom, // Consider converting to OffsetDateTime in service impl
        String dateTo,   // Consider converting to OffsetDateTime in service impl
        Pageable pageable
    );

    WholesaleOrderDetailDto updateWholesaleOrder(UUID orderId, WholesaleOrderRequestDto requestDto);

    WholesaleOrderDetailDto cancelWholesaleOrder(UUID orderId);

    String getPrintableOrderData(UUID orderId); // Placeholder for print data retrieval

    // TODO: Add methods for other wholesale order actions based on workflow (e.g., approve, reject, completeShipment)
} 