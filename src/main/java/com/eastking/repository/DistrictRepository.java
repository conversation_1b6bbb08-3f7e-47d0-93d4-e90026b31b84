package com.eastking.repository;

import com.eastking.model.entity.District;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface DistrictRepository extends JpaRepository<District, UUID> {

    /**
     * 根據縣市ID查找所有未被刪除的鄉鎮市區，並按排序欄位排序
     * @param cityId 縣市ID
     * @param isDeleted 刪除標記
     * @return 鄉鎮市區列表
     */
    List<District> findByCity_CityIdAndIsDeletedOrderBySequenceOrderAsc(UUID cityId, short isDeleted);

    List<District> findByIsDeleted(short isDeleted);
} 