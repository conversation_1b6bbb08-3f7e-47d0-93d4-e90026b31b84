package com.eastking.repository;

import com.eastking.model.entity.Role;
import com.eastking.model.entity.RoleFunctionPermission;
import com.eastking.model.entity.SystemFunction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * 角色功能權限 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface RoleFunctionPermissionRepository extends JpaRepository<RoleFunctionPermission, UUID> {
    List<RoleFunctionPermission> findByRoleAndIsDeleted(Role role, Short isDeleted);
    List<RoleFunctionPermission> findByRoleInAndIsDeleted(List<Role> roles, Short isDeleted);
    List<RoleFunctionPermission> findBySystemFunctionAndIsDeleted(SystemFunction systemFunction, Short isDeleted);
    void deleteByRole(Role role);
    List<RoleFunctionPermission> findByRole(Role role);
} 