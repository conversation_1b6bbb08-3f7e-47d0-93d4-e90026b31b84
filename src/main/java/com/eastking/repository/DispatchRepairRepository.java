package com.eastking.repository;

import com.eastking.model.entity.DispatchRepair;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;
import com.eastking.model.entity.UserAccount;

@Repository
public interface DispatchRepairRepository extends JpaRepository<DispatchRepair, UUID>, JpaSpecificationExecutor<DispatchRepair> {

    List<DispatchRepair> findByStatusCodeAndIsDeleted(Short statusCode, short isDeleted);

    List<DispatchRepair> findByAssignedTechnicianIdAndStatusCodeInAndIsDeleted(UUID assignedTechnicianId, List<Short> statusCodes, short isDeleted);
} 