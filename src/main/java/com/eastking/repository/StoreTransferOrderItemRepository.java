package com.eastking.repository;

import com.eastking.model.entity.StoreTransferOrder;
import com.eastking.model.entity.StoreTransferOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface StoreTransferOrderItemRepository extends JpaRepository<StoreTransferOrderItem, UUID>, JpaSpecificationExecutor<StoreTransferOrderItem> {
    List<StoreTransferOrderItem> findByStoreTransferOrderAndIsDeleted(StoreTransferOrder storeTransferOrder, Short isDeleted);
    List<StoreTransferOrderItem> findByStoreTransferOrder_StoreTransferOrderIdAndIsDeleted(UUID storeTransferOrderId, Short isDeleted);
} 