package com.eastking.repository;

import com.eastking.model.entity.TechnicianAreaConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface TechnicianAreaConfigRepository extends JpaRepository<TechnicianAreaConfig, UUID> {
    Optional<TechnicianAreaConfig> findByTechnicianIdAndConfigMonth(UUID technicianId, String configMonth);
    List<TechnicianAreaConfig> findByConfigMonth(String configMonth);

    @Modifying
    void deleteByConfigMonth(String configMonth);
}