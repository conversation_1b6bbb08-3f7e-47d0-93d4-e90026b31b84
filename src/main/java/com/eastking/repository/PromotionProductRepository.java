package com.eastking.repository;

import com.eastking.model.entity.Promotion;
import com.eastking.model.entity.PromotionProduct;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.UUID;

@Repository
public interface PromotionProductRepository extends JpaRepository<PromotionProduct, UUID> {
    List<PromotionProduct> findByPromotionAndIsDeleted(Promotion promotion, Short isDeleted);
    void deleteByPromotion(Promotion promotion);
} 