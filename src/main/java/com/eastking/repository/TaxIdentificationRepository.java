package com.eastking.repository;

import com.eastking.model.entity.TaxIdentificationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

/**
 * 統編抬頭資料 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface TaxIdentificationRepository extends JpaRepository<TaxIdentificationEntity, UUID>, JpaSpecificationExecutor<TaxIdentificationEntity> {

    Optional<TaxIdentificationEntity> findByTaxIdNumberAndIsDeleted(String taxIdNumber, Short isDeleted);

} 