package com.eastking.repository;

import com.eastking.model.entity.ProductSetting;
import com.eastking.model.dto.response.ProductSettingSummaryDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.time.OffsetDateTime;

/**
 * 商品設定 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Repository
public interface ProductSettingRepository extends JpaRepository<ProductSetting, UUID>, JpaSpecificationExecutor<ProductSetting> {
    Optional<ProductSetting> findByProductBarcodeAndIsDeleted(String productBarcode, Short isDeleted);

    boolean existsByProductBarcodeAndIsDeleted(String productBarcode, Short isDeleted);

    List<ProductSetting> findByProductBarcodeInAndIsDispatchProductAndIsDeleted(List<String> barcodes, short isDispatchProduct, short isDeleted);

    List<ProductSetting> findByProductBarcodeInAndIsDeleted(List<String> productBarcodes, Short isDeleted);

    Optional<ProductSetting> findByProductSettingIdAndIsDeleted(UUID productSettingId, Short isDeleted);

    List<ProductSetting> findByIsDeleted(Short isDeleted);

    @Query("SELECT ps FROM ProductSetting ps WHERE ps.isDispatchProduct = :isDispatchProduct AND ps.isDeleted = 0 AND ps.productBarcode IN :barcodes")
    List<ProductSetting> findByProductBarcodeInAndIsDispatchProduct(@Param("isDispatchProduct") short isDispatchProduct, @Param("barcodes") List<String> barcodes);

    @Query("SELECT ps FROM ProductSetting ps WHERE ps.isMain = :isMain AND ps.isDeleted = 0 AND ps.productBarcode IN :barcodes")
    List<ProductSetting> findByProductBarcodeInAndIsMain(@Param("isMain") short isMain, @Param("barcodes") List<String> barcodes);

    @Query("SELECT new com.eastking.model.dto.response.ProductSettingSummaryDto(ps.productSettingId, ps.productBarcode, ps.productName, ps.salePrice, 0) " +
           "FROM ProductSetting ps WHERE ps.isDeleted = 0 AND ps.productBarcode IN :barcodes " +
           "AND (:keyword IS NULL OR LOWER(ps.productName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(ps.productBarcode) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<ProductSettingSummaryDto> findSummariesByBarcodesAndKeyword(@Param("barcodes") List<String> barcodes, @Param("keyword") String keyword);
} 