package com.eastking.repository;

import com.eastking.model.entity.SmsTemplateEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;
import java.util.List;

/**
 * 簡訊模板資料 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface SmsTemplateRepository extends JpaRepository<SmsTemplateEntity, UUID>, JpaSpecificationExecutor<SmsTemplateEntity> {

    Optional<SmsTemplateEntity> findByTemplateNameAndIsDeleted(String templateName, Short isDeleted);

    List<SmsTemplateEntity> findByTemplateTypeAndIsDeleted(String templateType, Short isDeleted);

} 