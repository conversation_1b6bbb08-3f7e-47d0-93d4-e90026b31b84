package com.eastking.repository;

import com.eastking.model.entity.DispatchMaterialOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.UUID;

@Repository
public interface DispatchMaterialOrderRepository extends JpaRepository<DispatchMaterialOrder, UUID>, JpaSpecificationExecutor<DispatchMaterialOrder> {

} 