package com.eastking.repository;

import com.eastking.model.entity.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface OrderRepository extends JpaRepository<Order, UUID>, JpaSpecificationExecutor<Order>, OrderRepositoryCustom {
    Optional<Order> findByOrderNumberAndIsDeleted(String orderNumber, Short isDeleted);
    
    List<Order> findByOrderStatusCode(Short orderStatusCode);
    
    Optional<Order> findByOrderIdAndOrderTypeCodeAndIsDeleted(UUID orderId, Short orderTypeCode, Short isDeleted);
    // Add other finders based on common query patterns, e.g., by customer, store, status, order_type
} 