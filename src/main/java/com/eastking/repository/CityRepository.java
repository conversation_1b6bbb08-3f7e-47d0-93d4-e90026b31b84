package com.eastking.repository;

import com.eastking.model.entity.City;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface CityRepository extends JpaRepository<City, UUID> {
    
    /**
     * 查找所有未被刪除的縣市，並按排序欄位排序
     * @return 縣市列表
     */
    List<City> findByIsDeletedOrderBySequenceOrderAsc(short isDeleted);

    List<City> findAllByOrderBySequenceOrderAsc();

    List<City> findByIsDeleted(short isDeleted);
} 