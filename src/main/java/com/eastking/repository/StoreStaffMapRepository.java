package com.eastking.repository;

import com.eastking.model.entity.StoreStaffMapEntity;
import com.eastking.model.entity.StoreEntity;
import com.eastking.model.entity.UserAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 門市人員對應資料 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface StoreStaffMapRepository extends JpaRepository<StoreStaffMapEntity, UUID>, JpaSpecificationExecutor<StoreStaffMapEntity> {

    List<StoreStaffMapEntity> findByStoreStoreIdAndIsDeleted(UUID storeId, Short isDeleted);

    List<StoreStaffMapEntity> findByUserAccountUserAccountIdAndIsDeleted(UUID userAccountId, Short isDeleted);

    void deleteByStoreStoreIdAndUserAccountUserAccountIdAndStaffType(UUID storeId, UUID userAccountId, Short staffType);

    Optional<StoreStaffMapEntity> findByStoreAndUserAccountAndStaffTypeAndIsDeleted(StoreEntity store, UserAccount userAccount, Short staffType, Short isDeleted);

    List<StoreStaffMapEntity> findByStoreAndStaffTypeAndIsDeleted(StoreEntity store, Short staffType, Short isDeleted);

    List<StoreStaffMapEntity> findByUserAccountUserAccountIdAndIsDeletedAndStoreIsActiveAndStoreIsDeletedOrderByStoreStoreNameAsc(
        UUID userAccountId, Short mapIsDeleted, Short storeIsActive, Short storeIsDeleted);
} 