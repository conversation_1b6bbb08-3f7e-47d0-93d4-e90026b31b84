package com.eastking.repository;

import com.eastking.model.entity.Role;
import com.eastking.model.entity.RoleFieldPermission;
import com.eastking.model.entity.SystemFunction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface RoleFieldPermissionRepository extends JpaRepository<RoleFieldPermission, UUID> {
    List<RoleFieldPermission> findByRoleAndIsDeleted(Role role, Short isDeleted);
    List<RoleFieldPermission> findByRoleInAndIsDeleted(List<Role> roles, Short isDeleted);
    Optional<RoleFieldPermission> findByRoleAndSystemFunctionAndFieldNameAndIsDeleted(
            Role role, SystemFunction systemFunction, String fieldName, Short isDeleted);
    void deleteByRole(Role role); // For when a role is deleted or its permissions are reset
} 