package com.eastking.repository;

import com.eastking.enums.ProductMenuTypeEnum;
import com.eastking.model.entity.ProductMenuCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * 商品選單分類 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Repository
public interface ProductMenuCategoryRepository extends JpaRepository<ProductMenuCategory, UUID>, JpaSpecificationExecutor<ProductMenuCategory> {

    List<ProductMenuCategory> findByMenuTypeAndParentCategoryIsNullAndIsDeletedOrderBySortOrderAsc(ProductMenuTypeEnum menuType, Short isDeleted);

    List<ProductMenuCategory> findByMenuTypeAndParentCategory_ProductMenuCategoryIdAndIsDeletedOrderBySortOrderAsc(ProductMenuTypeEnum menuType, UUID parentCategoryId, Short isDeleted);

    // For checking unique name under a parent and menu type
    boolean existsByCategoryNameAndParentCategory_ProductMenuCategoryIdAndMenuTypeAndIsDeleted(String categoryName, UUID parentCategoryId, ProductMenuTypeEnum menuType, Short isDeleted);
    boolean existsByCategoryNameAndParentCategoryIsNullAndMenuTypeAndIsDeleted(String categoryName, ProductMenuTypeEnum menuType, Short isDeleted);

    @Query("SELECT COALESCE(MAX(pmc.sortOrder), 0) FROM ProductMenuCategory pmc WHERE pmc.menuType = :menuType AND pmc.parentCategory.productMenuCategoryId = :parentCategoryId AND pmc.isDeleted = 0")
    Integer findMaxSortOrderUnderParent(ProductMenuTypeEnum menuType, UUID parentCategoryId);

    @Query("SELECT COALESCE(MAX(pmc.sortOrder), 0) FROM ProductMenuCategory pmc WHERE pmc.menuType = :menuType AND pmc.parentCategory IS NULL AND pmc.isDeleted = 0")
    Integer findMaxSortOrderAtTopLevel(ProductMenuTypeEnum menuType);

    List<ProductMenuCategory> findByMenuTypeAndIsDeleted(ProductMenuTypeEnum menuType, Short isDeleted);
} 