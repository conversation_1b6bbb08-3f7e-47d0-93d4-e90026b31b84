package com.eastking.repository;

import com.eastking.model.entity.Role;
import com.eastking.model.entity.UserAccount;
import com.eastking.model.entity.UserRoleMap;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * 使用者角色關聯 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface UserRoleMapRepository extends JpaRepository<UserRoleMap, UUID> {
    List<UserRoleMap> findByUserAccountAndIsDeleted(UserAccount userAccount, Short isDeleted);
    List<UserRoleMap> findByRoleAndIsDeleted(Role role, Short isDeleted);
    List<UserRoleMap> findByUserAccountUserAccountIdAndIsDeleted(UUID userAccountId, Short isDeleted);
    List<UserRoleMap> findDistinctByUserAccount_IsActiveAndUserAccount_IsDeletedAndRole_RoleIdIn(Short isActive, Short isDeleted, List<UUID> roleIds);
    List<UserRoleMap> findAllByRole_RoleIdInAndIsDeleted(List<UUID> roleIds, short isDeleted);
} 