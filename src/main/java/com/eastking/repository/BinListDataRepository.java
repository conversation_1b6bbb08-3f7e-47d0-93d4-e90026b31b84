package com.eastking.repository;

import com.eastking.model.entity.BinListData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface BinListDataRepository extends JpaRepository<BinListData, UUID> {

    /**
     * 根據卡號前綴查找最精確匹配的 BIN 碼資訊。
     * 例如，如果卡號是 42424242，而資料庫中有 4242 和 424242 兩筆資料，
     * 此查詢會返回 424242 那一筆。
     * @param cardNumberPrefix 卡號的前幾位 (例如前8位)
     * @return 匹配的 BIN 碼資訊列表 (通常只有一筆)
     */
    @Query("SELECT b FROM BinListData b WHERE :cardNumberPrefix LIKE CONCAT(b.bin, '%') ORDER BY LENGTH(b.bin) DESC")
    List<BinListData> findBestMatchByBinPrefix(@Param("cardNumberPrefix") String cardNumberPrefix);
} 