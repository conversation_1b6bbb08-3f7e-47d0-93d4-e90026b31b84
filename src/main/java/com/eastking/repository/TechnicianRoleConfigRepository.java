package com.eastking.repository;

import com.eastking.model.entity.Role;
import com.eastking.model.entity.TechnicianRoleConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface TechnicianRoleConfigRepository extends JpaRepository<TechnicianRoleConfig, UUID>, JpaSpecificationExecutor<TechnicianRoleConfig> {
    Optional<TechnicianRoleConfig> findByRoleAndIsDeleted(Role role, Short isDeleted);
    List<TechnicianRoleConfig> findByIsActiveAndIsDeleted(Short isActive, Short isDeleted);
    List<TechnicianRoleConfig> findAllByIsActive(Short isActive);
} 