package com.eastking.repository;

import com.eastking.model.entity.StoreDailySettlement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface StoreDailySettlementRepository extends JpaRepository<StoreDailySettlement, UUID>, JpaSpecificationExecutor<StoreDailySettlement> {

    Optional<StoreDailySettlement> findByStore_StoreIdAndSettlementDate(UUID storeId, LocalDate settlementDate);

    boolean existsByStore_StoreIdAndSettlementDate(UUID storeId, LocalDate date);

    Optional<StoreDailySettlement> findTopByStore_StoreIdAndSettlementDateBeforeOrderBySettlementDateDesc(UUID storeId, LocalDate date);
} 