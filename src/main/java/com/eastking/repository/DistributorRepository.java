package com.eastking.repository;

import com.eastking.model.entity.Distributor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface DistributorRepository extends JpaRepository<Distributor, UUID>, JpaSpecificationExecutor<Distributor> {

    Optional<Distributor> findByErpCodeAndIsDeleted(String erpCode, Short isDeleted);

    List<Distributor> findByIsDeletedOrderByDistributorNameAsc(Short isDeleted);

} 