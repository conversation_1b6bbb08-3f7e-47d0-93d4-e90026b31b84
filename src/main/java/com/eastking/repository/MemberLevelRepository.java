package com.eastking.repository;

import com.eastking.model.entity.MemberLevelEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 會員等級資料 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface MemberLevelRepository extends JpaRepository<MemberLevelEntity, UUID>, JpaSpecificationExecutor<MemberLevelEntity> {

    Optional<MemberLevelEntity> findByLevelNameAndIsDeleted(String levelName, Short isDeleted);

    List<MemberLevelEntity> findByIsDeletedOrderBySequenceOrder(short isDeleted);

} 