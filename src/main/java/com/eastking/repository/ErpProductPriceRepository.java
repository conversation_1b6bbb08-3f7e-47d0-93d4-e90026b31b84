package com.eastking.repository;

import com.eastking.model.entity.ErpProductPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.UUID;
import java.time.OffsetDateTime;

@Repository
public interface ErpProductPriceRepository extends JpaRepository<ErpProductPrice, UUID>, JpaSpecificationExecutor<ErpProductPrice> {

} 