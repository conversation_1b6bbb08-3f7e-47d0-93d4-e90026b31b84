package com.eastking.repository;

import com.eastking.model.entity.AuditLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, UUID>, JpaSpecificationExecutor<AuditLog> {
    // Basic CRUD and specification executor will cover most query needs for audit logs.
    // Specific finders can be added if complex, non-specification based queries are common.
} 