package com.eastking.repository;

import com.eastking.model.entity.StoreEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 門市資料 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface StoreRepository extends JpaRepository<StoreEntity, UUID>, JpaSpecificationExecutor<StoreEntity> {

    /**
     * 依門市名稱查詢 (未刪除的)
     * @param storeName 門市名稱
     * @param isDeleted 刪除狀態
     * @return Optional<StoreEntity>
     */
    Optional<StoreEntity> findByStoreNameAndIsDeleted(String storeName, Short isDeleted);

    /**
     * 依門市代碼查詢 (未刪除的)
     * @param storeCode 門市代碼
     * @param isDeleted 刪除狀態
     * @return Optional<StoreEntity>
     */
    Optional<StoreEntity> findByStoreCodeAndIsDeleted(String storeCode, Short isDeleted);

    /**
     * 依地區ID計算門市數量 (未刪除的)
     * @param regionId 地區ID
     * @param isDeleted 刪除狀態
     * @return long
     */
    long countByRegion_RegionIdAndIsDeleted(UUID regionId, Short isDeleted);

    /**
     * 依ID查詢門市 (未刪除的)
     * @param storeId 門市ID
     * @param isDeleted 刪除狀態
     * @return Optional<StoreEntity>
     */
    Optional<StoreEntity> findByStoreIdAndIsDeleted(UUID storeId, Short isDeleted);

    List<StoreEntity> findByIsActiveAndIsDeletedOrderByStoreNameAsc(Short isActive, Short isDeleted);
} 