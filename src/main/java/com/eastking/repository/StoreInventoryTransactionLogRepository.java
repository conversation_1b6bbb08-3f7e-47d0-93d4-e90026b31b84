package com.eastking.repository;

import com.eastking.model.entity.StoreInventoryTransactionLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface StoreInventoryTransactionLogRepository extends JpaRepository<StoreInventoryTransactionLog, UUID>, JpaSpecificationExecutor<StoreInventoryTransactionLog> {
    // Basic CRUD and specification executor will cover most query needs.
    // Specific finders can be added if complex, non-specification based queries are common.
    // For example:
    // List<StoreInventoryTransactionLog> findByStore_StoreIdAndProductBarcodeOrderByTransactionDateDesc(UUID storeId, String productBarcode);
    // List<StoreInventoryTransactionLog> findByWarehouse_WarehouseIdAndProductBarcodeOrderByTransactionDateDesc(UUID warehouseId, String productBarcode);
} 