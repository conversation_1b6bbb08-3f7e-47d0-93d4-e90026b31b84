package com.eastking.repository;

import com.eastking.model.entity.WholesaleCustomer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface WholesaleCustomerRepository extends JpaRepository<WholesaleCustomer, UUID>, JpaSpecificationExecutor<WholesaleCustomer> {

    Optional<WholesaleCustomer> findByErpCustomerCodeAndIsDeleted(String erpCustomerCode, Short isDeleted);

    List<WholesaleCustomer> findByIsDeletedOrderByCustomerNameAsc(Short isDeleted);

    List<WholesaleCustomer> findByCustomerNameContainingIgnoreCaseAndIsDeleted(String customerName, Short isDeleted);
    
    List<WholesaleCustomer> findByPhoneNumberAndIsDeleted(String phoneNumber, Short isDeleted);

    List<WholesaleCustomer> findAllByIsDeletedAndCustomerStatusCode(Short isDeleted, Short customerStatusCode);

} 