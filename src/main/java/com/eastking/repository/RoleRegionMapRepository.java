package com.eastking.repository;

import com.eastking.model.entity.Role;
import com.eastking.model.entity.RoleRegionMap;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface RoleRegionMapRepository extends JpaRepository<RoleRegionMap, UUID> {
    List<RoleRegionMap> findByRoleAndIsDeleted(Role role, Short isDeleted);
    List<RoleRegionMap> findByRoleInAndIsDeleted(List<Role> roles, Short isDeleted);
    void deleteByRole(Role role);
} 