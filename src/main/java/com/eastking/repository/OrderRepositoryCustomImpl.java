package com.eastking.repository;

import com.eastking.model.entity.Order;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.query.QueryUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.util.*;

@Repository
public class OrderRepositoryCustomImpl implements OrderRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<Order> findAllWithRefundConsideration(Specification<Order> spec, Pageable pageable) {
        // 檢查是否有 updateTime 排序
        boolean hasUpdateTimeSort = pageable.getSort().stream()
                .anyMatch(order -> "updateTime".equals(order.getProperty()));

        if (!hasUpdateTimeSort) {
            // 如果沒有 updateTime 排序，使用標準的 JPA Criteria 查詢
            return findAllStandard(spec, pageable);
        }

        // 如果有 updateTime 排序，使用 JPQL
        return findAllWithRefundUpdateTime(spec, pageable);
    }

    private Page<Order> findAllStandard(Specification<Order> spec, Pageable pageable) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Order> query = cb.createQuery(Order.class);
        Root<Order> root = query.from(Order.class);

        Predicate predicate = spec.toPredicate(root, query, cb);
        if (predicate != null) {
            query.where(predicate);
        }

        query.orderBy(QueryUtils.toOrders(pageable.getSort(), root, cb));

        TypedQuery<Order> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<Order> content = typedQuery.getResultList();

        // Count query
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Order> countRoot = countQuery.from(Order.class);
        countQuery.select(cb.count(countRoot));
        
        Predicate countPredicate = spec.toPredicate(countRoot, countQuery, cb);
        if (countPredicate != null) {
            countQuery.where(countPredicate);
        }

        Long total = entityManager.createQuery(countQuery).getSingleResult();

        return new PageImpl<>(content, pageable, total);
    }

    private Page<Order> findAllWithRefundUpdateTime(Specification<Order> spec, Pageable pageable) {
        // 取得排序方向
        Sort.Direction direction = pageable.getSort().stream()
                .filter(order -> "updateTime".equals(order.getProperty()))
                .findFirst()
                .map(Sort.Order::getDirection)
                .orElse(Sort.Direction.DESC);

        // 先使用標準查詢取得符合條件的訂單
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Order> query = cb.createQuery(Order.class);
        Root<Order> root = query.from(Order.class);
        
        Predicate predicate = spec.toPredicate(root, query, cb);
        if (predicate != null) {
            query.where(predicate);
        }

        // 不加排序，先取得所有符合條件的訂單
        TypedQuery<Order> typedQuery = entityManager.createQuery(query);
        List<Order> allOrders = typedQuery.getResultList();

        if (allOrders.isEmpty()) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }

        // 取得訂單 ID 列表
        List<UUID> orderIds = allOrders.stream()
                .map(Order::getOrderId)
                .toList();

        // 查詢每個訂單的最新更新時間（包含退貨單）
        String jpql = "SELECT o.orderId, " +
                "CASE WHEN MAX(r.updateTime) IS NOT NULL AND MAX(r.updateTime) > COALESCE(o.updateTime, o.createTime) " +
                "THEN MAX(r.updateTime) " +
                "ELSE COALESCE(o.updateTime, o.createTime) END AS latestUpdateTime " +
                "FROM Order o " +
                "LEFT JOIN OrderRefund r ON r.originalOrder = o AND r.isDeleted = 0 " +
                "WHERE o.orderId IN :orderIds " +
                "GROUP BY o.orderId, o.updateTime, o.createTime";

        Query updateTimeQuery = entityManager.createQuery(jpql);
        updateTimeQuery.setParameter("orderIds", orderIds);
        
        @SuppressWarnings("unchecked")
        List<Object[]> updateTimeResults = updateTimeQuery.getResultList();

        // 建立訂單 ID 到最新更新時間的映射
        Map<UUID, OffsetDateTime> orderUpdateTimeMap = new HashMap<>();
        for (Object[] result : updateTimeResults) {
            UUID orderId = (UUID) result[0];
            OffsetDateTime latestUpdateTime = (OffsetDateTime) result[1];
            orderUpdateTimeMap.put(orderId, latestUpdateTime);
        }

        // 根據最新更新時間排序
        allOrders.sort((o1, o2) -> {
            OffsetDateTime time1 = orderUpdateTimeMap.getOrDefault(o1.getOrderId(), o1.getCreateTime());
            OffsetDateTime time2 = orderUpdateTimeMap.getOrDefault(o2.getOrderId(), o2.getCreateTime());
            
            int result = direction == Sort.Direction.ASC ? 
                    time1.compareTo(time2) : time2.compareTo(time1);
            
            // 如果時間相同，使用訂單 ID 作為次要排序條件以確保穩定性
            if (result == 0) {
                result = o1.getOrderId().compareTo(o2.getOrderId());
            }
            
            return result;
        });

        // 進行分頁
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), allOrders.size());
        
        List<Order> pageContent = start < allOrders.size() ? 
                allOrders.subList(start, end) : Collections.emptyList();

        return new PageImpl<>(pageContent, pageable, allOrders.size());
    }
} 