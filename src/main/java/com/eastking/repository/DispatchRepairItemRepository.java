package com.eastking.repository;

import com.eastking.model.entity.DispatchRepairItem;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface DispatchRepairItemRepository extends JpaRepository<DispatchRepairItem, UUID> {
    DispatchRepairItem findByDispatchRepairItemId(UUID itemId);

    List<DispatchRepairItem> findAllByDispatchRepair_DispatchRepairId(UUID dispatchRepairId);
} 