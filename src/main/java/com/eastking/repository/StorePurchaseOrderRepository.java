package com.eastking.repository;

import com.eastking.model.entity.StorePurchaseOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface StorePurchaseOrderRepository extends JpaRepository<StorePurchaseOrder, UUID>, JpaSpecificationExecutor<StorePurchaseOrder> {
    Optional<StorePurchaseOrder> findByPurchaseOrderNumberAndIsDeleted(String purchaseOrderNumber, Short isDeleted);
} 