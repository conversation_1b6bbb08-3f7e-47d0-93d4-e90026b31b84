package com.eastking.repository;

import com.eastking.model.entity.RegionEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 地區資料 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface RegionRepository extends JpaRepository<RegionEntity, UUID>, JpaSpecificationExecutor<RegionEntity> {

    /**
     * 依地區名稱查詢 (未刪除的)
     * @param regionName 地區名稱
     * @param isDeleted 刪除狀態
     * @return Optional<RegionEntity>
     */
    Optional<RegionEntity> findByRegionNameAndIsDeleted(String regionName, Short isDeleted);

    List<RegionEntity> findByIsDeleted(Short isDeleted);
} 