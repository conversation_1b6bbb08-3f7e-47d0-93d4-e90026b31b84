package com.eastking.repository;

import com.eastking.model.entity.SalesOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.UUID;

/**
 * 銷貨單主表 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Repository
public interface SalesOrderRepository extends JpaRepository<SalesOrder, UUID>, JpaSpecificationExecutor<SalesOrder> {
    // Add custom query methods if needed
    // Example: Optional<SalesOrder> findBySalesOrderNumberAndIsDeleted(String salesOrderNumber, Short isDeleted);
} 