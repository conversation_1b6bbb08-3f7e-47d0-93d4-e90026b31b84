package com.eastking.repository;

import com.eastking.model.entity.ProductSettingDiscount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.UUID;

/**
 * 商品設定職權折扣 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Repository
public interface ProductSettingDiscountRepository extends JpaRepository<ProductSettingDiscount, UUID>, JpaSpecificationExecutor<ProductSettingDiscount> {
    List<ProductSettingDiscount> findByProductSetting_ProductSettingIdAndIsDeleted(UUID productSettingId, Short isDeleted);
    void deleteByProductSetting_ProductSettingId(UUID productSettingId);
} 