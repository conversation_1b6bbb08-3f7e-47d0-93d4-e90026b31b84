package com.eastking.repository;

import com.eastking.model.entity.ProductMenuItem;
import com.eastking.enums.ProductMenuTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * 商品選單項目 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Repository
public interface ProductMenuItemRepository extends JpaRepository<ProductMenuItem, UUID>, JpaSpecificationExecutor<ProductMenuItem> {
    List<ProductMenuItem> findByProductMenuCategory_ProductMenuCategoryIdAndIsDeletedOrderBySortOrderAsc(UUID categoryId, Short isDeleted);

    boolean existsByProductMenuCategory_ProductMenuCategoryIdAndProductBarcodeAndIsDeleted(UUID categoryId, String productBarcode, Short isDeleted);

    @Query("SELECT COALESCE(MAX(pmi.sortOrder), 0) FROM ProductMenuItem pmi WHERE pmi.productMenuCategory.productMenuCategoryId = :categoryId AND pmi.isDeleted = 0")
    Integer findMaxSortOrderInCategory(UUID categoryId);

    void deleteByProductMenuCategory_ProductMenuCategoryId(UUID categoryId);

    @Query("SELECT pmi FROM ProductMenuItem pmi JOIN pmi.productMenuCategory pmc WHERE pmc.menuType = :menuType AND pmi.isDeleted = :isDeleted")
    List<ProductMenuItem> findByProductMenuCategory_MenuTypeAndIsDeleted(ProductMenuTypeEnum menuType, Short isDeleted);

    List<ProductMenuItem> findByProductBarcodeAndIsDeleted(String productBarcode, Short isDeleted);

    List<ProductMenuItem> findByProductMenuCategory_ProductMenuCategoryIdAndIsDeleted(UUID productMenuCategoryId, Short isDeleted);

    List<ProductMenuItem> findByProductBarcodeInAndIsDeleted(List<String> productBarcodes, Short isDeleted);
} 