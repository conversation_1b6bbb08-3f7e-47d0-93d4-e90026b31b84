package com.eastking.repository;

import com.eastking.model.entity.SalesOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 銷貨單品項表 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Repository
public interface SalesOrderItemRepository extends JpaRepository<SalesOrderItem, UUID>, JpaSpecificationExecutor<SalesOrderItem> {
    // Add custom query methods if needed
    List<SalesOrderItem> findBySalesOrderSalesOrderIdAndIsDeleted(UUID salesOrderId, Short isDeleted);
    List<SalesOrderItem> findByProductBarcodeAndIsDeleted(String productBarcode, Short isDeleted);

    @Query("SELECT SUM(soi.quantitySold) FROM SalesOrderItem soi " +
           "WHERE soi.productBarcode = :productBarcode AND soi.isDeleted = 0 " +
           "AND (cast(:dateFrom as timestamp) IS NULL OR soi.salesOrder.orderDate >= :dateFrom) " +
           "AND (cast(:dateTo as timestamp) IS NULL OR soi.salesOrder.orderDate <= :dateTo)")
    Integer sumQuantitySoldByProductBarcodeAndDateRange(@Param("productBarcode") String productBarcode, 
                                                      @Param("dateFrom") OffsetDateTime dateFrom, 
                                                      @Param("dateTo") OffsetDateTime dateTo);

    @Query("SELECT soi FROM SalesOrderItem soi JOIN FETCH soi.salesOrder " +
           "WHERE soi.productBarcode = :productBarcode AND soi.isDeleted = 0 " +
           "AND (cast(:dateFrom as timestamp) IS NULL OR soi.salesOrder.orderDate >= :dateFrom) " +
           "AND (cast(:dateTo as timestamp) IS NULL OR soi.salesOrder.orderDate <= :dateTo)")
    List<SalesOrderItem> findByProductBarcodeAndDateRange(@Param("productBarcode") String productBarcode, 
                                                          @Param("dateFrom") OffsetDateTime dateFrom, 
                                                          @Param("dateTo") OffsetDateTime dateTo);
} 