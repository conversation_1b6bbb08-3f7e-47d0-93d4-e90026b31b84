package com.eastking.repository;

import com.eastking.model.entity.Promotion;
import com.eastking.model.entity.PromotionChannel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.UUID;

@Repository
public interface PromotionChannelRepository extends JpaRepository<PromotionChannel, UUID> {
    List<PromotionChannel> findByPromotionAndIsDeleted(Promotion promotion, Short isDeleted);
    void deleteByPromotion(Promotion promotion);
} 