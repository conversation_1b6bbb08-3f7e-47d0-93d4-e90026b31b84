package com.eastking.repository;

import com.eastking.model.entity.UserAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 使用者帳號 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface UserAccountRepository extends JpaRepository<UserAccount, UUID>, JpaSpecificationExecutor<UserAccount> {
    Optional<UserAccount> findByEmployeeIdAndIsDeleted(String employeeId, short isDeleted);
    Optional<UserAccount> findByUserAccountIdAndIsDeleted(UUID userAccountId, Short isDeleted);
    boolean existsByEmployeeIdAndIsDeleted(String employeeId, Short isDeleted);
    List<UserAccount> findByIsActiveAndIsDeleted(Short isActive, Short isDeleted);
} 