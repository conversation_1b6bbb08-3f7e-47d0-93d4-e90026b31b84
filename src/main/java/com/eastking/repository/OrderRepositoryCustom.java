package com.eastking.repository;

import com.eastking.model.entity.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

public interface OrderRepositoryCustom {
    /**
     * 搜尋訂單，當使用 updateTime 排序時會考慮退貨單的 updateTime
     */
    Page<Order> findAllWithRefundConsideration(Specification<Order> spec, Pageable pageable);
} 