package com.eastking.repository;

import com.eastking.model.entity.DocumentClause;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.UUID;
import java.time.OffsetDateTime;

@Repository
public interface DocumentClauseRepository extends JpaRepository<DocumentClause, UUID>, JpaSpecificationExecutor<DocumentClause> {

} 