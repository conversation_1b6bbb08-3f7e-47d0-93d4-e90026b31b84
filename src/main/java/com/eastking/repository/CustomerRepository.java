package com.eastking.repository;

import com.eastking.model.entity.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;
import java.util.List;

@Repository
public interface CustomerRepository extends JpaRepository<Customer, UUID>, JpaSpecificationExecutor<Customer> {
    Optional<Customer> findByPhoneNumberAndIsDeleted(String phoneNumber, Short isDeleted);
    List<Customer> findByPhoneNumberContainingAndIsDeleted(String phoneNumber, Short isDeleted);
    List<Customer> findByCustomerNameContainingAndIsDeleted(String customerName, Short isDeleted);
    // Add more finders as needed, e.g., findByEmailAndIsDeleted
} 