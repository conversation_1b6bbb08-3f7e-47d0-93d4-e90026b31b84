package com.eastking.repository;

import com.eastking.model.entity.InventoryReservation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.UUID;
import java.util.List;

@Repository
public interface InventoryReservationRepository extends JpaRepository<InventoryReservation, UUID> {

    /**
     * 計算指定倉庫中特定商品的有效預留總數。
     * @param warehouseId 倉庫 ID
     * @param productBarcode 商品條碼
     * @param reservationStatusCode 有效預留的狀態碼 (例如 ReservationStatusEnum.ACTIVE.getCode())
     * @return 預留的總數量，如果沒有則返回 null
     */
    @Query("SELECT SUM(ir.reservedQuantity) FROM InventoryReservation ir " +
           "WHERE ir.warehouse.warehouseId = :warehouseId " +
           "AND ir.productBarcode = :productBarcode " +
           "AND ir.reservationStatusCode = :statusCode")
    Integer sumActiveReservationsForProductInWarehouse(@Param("warehouseId") UUID warehouseId,
                                                       @Param("productBarcode") String productBarcode,
                                                       @Param("statusCode") Short reservationStatusCode);

    List<InventoryReservation> findByOrder_OrderIdAndReservationStatusCode(UUID orderId, Short statusCode);
} 