package com.eastking.repository;

import com.eastking.model.entity.ErpMahjongTableStockLog;
import com.eastking.model.dto.ErpWarehouseDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * ERP 電動麻將桌庫存同步記錄表 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Repository
public interface ErpMahjongTableStockLogRepository extends JpaRepository<ErpMahjongTableStockLog, UUID>, JpaSpecificationExecutor<ErpMahjongTableStockLog> {

    List<ErpMahjongTableStockLog> findByProductBarcodeAndErpWarehouseCodeAndIsLatestForProductWarehouseTrueOrderBySyncTimeDesc(
            String productBarcode, String erpWarehouseCode);

    List<ErpMahjongTableStockLog> findByIsLatestForProductWarehouseTrueAndProductBarcodeIn(
            List<String> productBarcodes);

    // Method to find by warehouse and latest flag, potentially for a list of products if needed.
    List<ErpMahjongTableStockLog> findByErpWarehouseCodeAndIsLatestForProductWarehouseTrueAndProductBarcodeIn(
            String erpWarehouseCode, List<String> productBarcodes);

    List<ErpMahjongTableStockLog> findByErpWarehouseCodeAndIsLatestForProductWarehouseTrue(
            String erpWarehouseCode);

    @Query("SELECT DISTINCT new com.eastking.model.dto.ErpWarehouseDto(s.erpWarehouseCode, s.erpWarehouseName) FROM ErpMahjongTableStockLog s WHERE s.erpWarehouseCode IS NOT NULL AND s.erpWarehouseName IS NOT NULL ORDER BY s.erpWarehouseName ASC")
    List<ErpWarehouseDto> findDistinctErpWarehouses();

} 