package com.eastking.repository;

import com.eastking.model.entity.GiftBundle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface GiftBundleRepository extends JpaRepository<GiftBundle, UUID>, JpaSpecificationExecutor<GiftBundle> {
    // Optional<GiftBundle> findByBundleNameAndIsDeleted(String bundleName, Short isDeleted);
    // Optional<GiftBundle> findByMainProductBarcodeAndIsDeleted(String mainProductBarcode, Short isDeleted);

    @Query("SELECT gb FROM GiftBundle gb LEFT JOIN FETCH gb.items WHERE gb.mainProductBarcode IN :barcodes AND gb.isActive = 1 AND gb.isDeleted = 0 AND gb.startTime <= :now AND gb.endTime >= :now")
    List<GiftBundle> findActiveBundlesByMainProductBarcodes(@Param("barcodes") List<String> barcodes, @Param("now") OffsetDateTime now);
} 