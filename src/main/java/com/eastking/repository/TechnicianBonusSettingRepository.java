package com.eastking.repository;

import com.eastking.model.entity.BonusItem;
import com.eastking.model.entity.Role;
import com.eastking.model.entity.TechnicianBonusSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface TechnicianBonusSettingRepository extends JpaRepository<TechnicianBonusSetting, UUID>, JpaSpecificationExecutor<TechnicianBonusSetting> {
    Optional<TechnicianBonusSetting> findByBonusItemAndRoleAndIsDeleted(BonusItem bonusItem, Role role, Short isDeleted);
    List<TechnicianBonusSetting> findByBonusItemAndIsDeleted(BonusItem bonusItem, Short isDeleted);
    List<TechnicianBonusSetting> findByRoleAndIsDeleted(Role role, Short isDeleted);
} 