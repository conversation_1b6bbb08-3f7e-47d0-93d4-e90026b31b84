package com.eastking.repository;

import com.eastking.model.entity.StoreTaxIdentificationMapEntity;
import com.eastking.model.entity.StoreEntity;
import com.eastking.model.entity.TaxIdentificationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 門市統編抬頭對應資料 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface StoreTaxIdentificationMapRepository extends JpaRepository<StoreTaxIdentificationMapEntity, UUID>, JpaSpecificationExecutor<StoreTaxIdentificationMapEntity> {

    List<StoreTaxIdentificationMapEntity> findByStoreStoreIdAndIsDeleted(UUID storeId, Short isDeleted);

    List<StoreTaxIdentificationMapEntity> findByTaxIdentificationTaxIdentificationIdAndIsDeleted(UUID taxIdentificationId, Short isDeleted);

    void deleteByStoreStoreIdAndTaxIdentificationTaxIdentificationId(UUID storeId, UUID taxIdentificationId);

    long countByTaxIdentification_TaxIdentificationIdAndIsDeleted(UUID taxIdentificationId, Short isDeleted);

    Optional<StoreTaxIdentificationMapEntity> findByStoreAndTaxIdentificationAndIsDeleted(StoreEntity store, TaxIdentificationEntity taxIdentification, Short isDeleted);

    List<StoreTaxIdentificationMapEntity> findByStoreAndIsDeleted(StoreEntity store, Short isDeleted);
} 