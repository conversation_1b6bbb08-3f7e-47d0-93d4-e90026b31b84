package com.eastking.repository;

import com.eastking.model.entity.StoreEntity;
import com.eastking.model.entity.StoreInventory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface StoreInventoryRepository extends JpaRepository<StoreInventory, UUID>, JpaSpecificationExecutor<StoreInventory> {
    Optional<StoreInventory> findByStoreAndProductBarcodeAndIsDeleted(StoreEntity store, String productBarcode, Short isDeleted);
    Optional<StoreInventory> findByStore_StoreIdAndProductBarcode(UUID storeId, String productBarcode);
    List<StoreInventory> findByStore_StoreIdAndIsDeleted(UUID storeId, Short isDeleted);
    List<StoreInventory> findByProductBarcodeAndIsDeleted(String productBarcode, Short isDeleted);
    List<StoreInventory> findByStore_StoreIdAndProductNameContainingIgnoreCase(UUID storeId, String productName);
} 