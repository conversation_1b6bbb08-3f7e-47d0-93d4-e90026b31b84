package com.eastking.repository;

import com.eastking.model.entity.UserAccount;
import com.eastking.model.entity.UserSessionPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * 使用者會話權限 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface UserSessionPermissionRepository extends JpaRepository<UserSessionPermission, UUID>, JpaSpecificationExecutor<UserSessionPermission> {
    Optional<UserSessionPermission> findByUserSessionPermissionIdAndIsDeletedAndExpiryTimeAfter(
            UUID userSessionPermissionId, Short isDeleted, OffsetDateTime currentTime);

    void deleteByUserAccount(UserAccount userAccount);
    void deleteByExpiryTimeBefore(OffsetDateTime expiryTime);
} 