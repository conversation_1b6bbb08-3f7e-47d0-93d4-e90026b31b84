package com.eastking.repository;

import com.eastking.model.entity.DispatchMaterialOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface DispatchMaterialOrderItemRepository extends JpaRepository<DispatchMaterialOrderItem, UUID> {
    
    @Query("SELECT SUM(dmoi.pickedQuantity) FROM DispatchMaterialOrderItem dmoi WHERE dmoi.dispatchRepairItemId = :dispatchRepairItemId")
    Integer sumPickedQuantityByDispatchRepairItemId(@Param("dispatchRepairItemId") UUID dispatchRepairItemId);

    @Query("SELECT SUM(dmoi.requestedQuantity) FROM DispatchMaterialOrderItem dmoi WHERE dmoi.dispatchRepairItemId = :dispatchRepairItemId")
    Integer sumRequestedQuantityByDispatchRepairItemId(@Param("dispatchRepairItemId") UUID dispatchRepairItemId);

    @Query("SELECT SUM(dmoi.collectedQuantity) FROM DispatchMaterialOrderItem dmoi WHERE dmoi.dispatchRepairItemId = :dispatchRepairItemId")
    Integer sumCollectedByDispatchRepairItemId(@Param("dispatchRepairItemId") UUID dispatchRepairItemId);

    List<DispatchMaterialOrderItem> findAllByDispatchRepairItemId(UUID dispatchRepairItemId);
    
    @Query("SELECT dmoi.dispatchRepairItemId, SUM(dmoi.requestedQuantity) " +
           "FROM DispatchMaterialOrderItem dmoi " +
           "JOIN dmoi.materialOrder dmo " +
           "WHERE dmo.requestingTechnician.userAccountId = :technicianId " +
           "AND dmo.isDeleted = 0 " +
           "GROUP BY dmoi.dispatchRepairItemId")
    List<Object[]> sumRequestedQuantityGroupByDispatchRepairItemIdForTechnician(@Param("technicianId") UUID technicianId);
} 