package com.eastking.repository;

import com.eastking.model.entity.RegionEntity;
import com.eastking.model.entity.StoreInventory;
import com.eastking.model.entity.Warehouse;
import com.eastking.model.entity.WarehouseInventory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface WarehouseRepository extends JpaRepository<Warehouse, UUID>, JpaSpecificationExecutor<Warehouse> {
    List<Warehouse> findByRegionAndIsDeleted(RegionEntity region, Short isDeleted);
    Optional<Warehouse> findByWarehouseCodeAndRegionAndIsDeleted(String warehouseCode, RegionEntity region, Short isDeleted);
    Optional<Warehouse> findByWarehouseNameAndRegionAndIsDeleted(String warehouseName, RegionEntity region, Short isDeleted);
    Optional<Warehouse> findByWarehouseCodeAndIsDeleted(String warehouseCode, Short isDeleted);
    List<Warehouse> findByRegionAndErpCompanyDivision(RegionEntity region, Short erpCompanyDivision);
    List<Warehouse> findByIsMainAndErpCompanyDivision(Short isMain, Short erpCompanyDivision);
    List<Warehouse> findByRegion_RegionIdAndIsDeleted(UUID regionId, Short isDeleted);
    List<Warehouse> findByIsMainAndErpCompanyDivision(short isMain, Short erpCompanyDivision);
    List<Warehouse> findByRegion_RegionIdNotAndIsDeleted(UUID regionId, short isDeleted);
    List<Warehouse> findAllByWarehouseCodeIn(List<String> warehouseCodes);

    Warehouse findByWarehouseId(UUID warehouseId);
}