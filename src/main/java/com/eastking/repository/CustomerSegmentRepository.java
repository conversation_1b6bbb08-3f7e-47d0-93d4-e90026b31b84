package com.eastking.repository;

import com.eastking.model.entity.CustomerSegment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface CustomerSegmentRepository extends JpaRepository<CustomerSegment, UUID>, JpaSpecificationExecutor<CustomerSegment> {
    Optional<CustomerSegment> findBySegmentNameAndIsDeleted(String segmentName, Short isDeleted);
    List<CustomerSegment> findByIsActiveAndIsDeletedOrderBySequenceOrderAsc(Short isActive, Short isDeleted);
} 