package com.eastking.repository;

import com.eastking.model.entity.Promotion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import java.util.UUID;

@Repository
public interface PromotionRepository extends JpaRepository<Promotion, UUID>, JpaSpecificationExecutor<Promotion> {
    // Custom query methods if needed, e.g.:
    // Optional<Promotion> findByPromotionNameAndIsDeleted(String name, Short isDeleted);
} 