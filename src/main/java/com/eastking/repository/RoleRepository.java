package com.eastking.repository;

import com.eastking.model.entity.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 角色 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, UUID> {
    Optional<Role> findByRoleCodeAndIsDeleted(String roleCode, Short isDeleted);
    Page<Role> findByIsDeleted(Short isDeleted, Pageable pageable);
    List<Role> findByIsDeletedOrderByRoleName(Short isDeleted);
    List<Role> findByRoleCodeContainingIgnoreCaseAndIsDeleted(String roleCode, short isDeleted);
} 