package com.eastking.repository;

import com.eastking.model.entity.StoreDailyJournal;
import com.eastking.model.entity.StoreEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface StoreDailyJournalRepository extends JpaRepository<StoreDailyJournal, UUID>, JpaSpecificationExecutor<StoreDailyJournal> {
    List<StoreDailyJournal> findByStoreAndEntryDatetimeBetweenOrderByEntryDatetimeDesc(
        StoreEntity store, OffsetDateTime startTime, OffsetDateTime endTime);
    List<StoreDailyJournal> findByStore_StoreIdAndCreateTimeBetween(UUID storeId, OffsetDateTime start, OffsetDateTime end);

    int deleteByRelatedOrder_OrderId(UUID relatedOrderId);
} 