package com.eastking.repository;

import com.eastking.model.entity.Warehouse;
import com.eastking.model.entity.WarehouseInventory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface WarehouseInventoryRepository extends JpaRepository<WarehouseInventory, UUID>, JpaSpecificationExecutor<WarehouseInventory> {
    List<WarehouseInventory> findByWarehouse_WarehouseIdInAndProductNameContainingIgnoreCase(List<UUID> warehouseIds, String productName);
    Optional<WarehouseInventory> findByWarehouseWarehouseIdAndProductBarcode(UUID warehouseId, String productBarcode);
    
    @Query("SELECT DISTINCT wi.productBarcode FROM WarehouseInventory wi " +
           "WHERE wi.warehouse.warehouseId IN :warehouseIds " +
           "AND wi.quantityOnHand > 0 " +
           "AND (LOWER(wi.productName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR LOWER(wi.productBarcode) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<String> findDistinctProductBarcodesByWarehouseIdsAndKeywordAndInStock(@Param("warehouseIds") List<UUID> warehouseIds, @Param("keyword") String keyword);

    @Query("SELECT wi.productBarcode, SUM(wi.quantityOnHand) FROM WarehouseInventory wi " +
           "WHERE wi.productBarcode IN :barcodes AND wi.warehouse.warehouseId IN :warehouseIds " +
           "GROUP BY wi.productBarcode")
    List<Object[]> findTotalStockByBarcodesAndWarehouses(@Param("barcodes") List<String> barcodes, @Param("warehouseIds") List<UUID> warehouseIds);

    Optional<WarehouseInventory> findByWarehouseAndProductBarcode(Warehouse warehouse, String productBarcode);

    List<WarehouseInventory> findByWarehouseWarehouseIdInAndIsDeleted(List<UUID> warehouseIds, Short isDeleted);

    Optional<WarehouseInventory> findByWarehouse_WarehouseIdAndProductBarcode(UUID warehouseId, String productBarcode);

    List<WarehouseInventory> findByWarehouse_WarehouseId(UUID warehouseId);

    List<WarehouseInventory> findByWarehouse_WarehouseIdAndProductNameContainingIgnoreCase(UUID warehouseId, String keyword);
} 