package com.eastking.repository;

import com.eastking.model.entity.OrderRefund;
import com.eastking.model.entity.OrderRefundItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface OrderRefundItemRepository extends JpaRepository<OrderRefundItem, UUID>, JpaSpecificationExecutor<OrderRefundItem> {
    List<OrderRefundItem> findByOrderRefundAndIsDeleted(OrderRefund orderRefund, Short isDeleted);
    List<OrderRefundItem> findByOrderRefundOrderRefundIdAndIsDeleted(UUID orderRefundId, Short isDeleted);
} 