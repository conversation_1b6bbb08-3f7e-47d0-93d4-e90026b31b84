package com.eastking.repository;

import com.eastking.model.entity.OrderRefund;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface OrderRefundRepository extends JpaRepository<OrderRefund, UUID>, JpaSpecificationExecutor<OrderRefund> {
    Optional<OrderRefund> findByRefundOrderNumberAndIsDeleted(String refundOrderNumber, Short isDeleted);
    List<OrderRefund> findByOriginalOrderOrderIdAndIsDeleted(UUID originalOrderId, Short isDeleted);
    List<OrderRefund> findAllByOriginalOrder_OrderId(UUID orderId);
    List<OrderRefund> findByOriginalOrder_OrderIdAndIsDeleted(UUID originalOrderId, Short isDeleted);

    @Query("SELECT r FROM OrderRefund r WHERE r.originalOrder.orderId IN :orderIds AND r.isDeleted = 0 " +
           "AND r.createTime = (SELECT MAX(r2.createTime) FROM OrderRefund r2 WHERE r2.originalOrder.orderId = r.originalOrder.orderId AND r2.isDeleted = 0)")
    List<OrderRefund> findLatestRefundsForOrders(@Param("orderIds") List<UUID> orderIds);
} 