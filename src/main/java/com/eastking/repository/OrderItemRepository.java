package com.eastking.repository;

import com.eastking.model.entity.Order;
import com.eastking.model.entity.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface OrderItemRepository extends JpaRepository<OrderItem, UUID>, JpaSpecificationExecutor<OrderItem> {
    List<OrderItem> findByOrderAndIsDeleted(Order order, Short isDeleted);
    List<OrderItem> findByOrderOrderIdAndIsDeleted(UUID orderId, Short isDeleted);
    // Add more finders as needed, e.g., by productBarcode
} 