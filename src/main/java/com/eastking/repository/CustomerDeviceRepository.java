package com.eastking.repository;

import com.eastking.model.entity.CustomerDevice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import java.util.UUID;
import java.util.Optional;
import org.springframework.data.jpa.repository.EntityGraph;

@Repository
public interface CustomerDeviceRepository extends JpaRepository<CustomerDevice, UUID>, JpaSpecificationExecutor<CustomerDevice> {
    @EntityGraph(attributePaths = {"customer", "productSetting"})
    Optional<CustomerDevice> findByDeviceSerialNumberAndIsDeleted(String serialNumber, short isDeleted);
} 