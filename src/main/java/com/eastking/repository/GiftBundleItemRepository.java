package com.eastking.repository;

import com.eastking.model.entity.GiftBundle;
import com.eastking.model.entity.GiftBundleItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.UUID;

@Repository
public interface GiftBundleItemRepository extends JpaRepository<GiftBundleItem, UUID> {
    List<GiftBundleItem> findByGiftBundleAndIsDeleted(GiftBundle giftBundle, Short isDeleted);
    void deleteByGiftBundle(GiftBundle giftBundle);
} 