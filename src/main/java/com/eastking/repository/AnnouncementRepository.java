package com.eastking.repository;

import com.eastking.model.entity.Announcement;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 系統公告 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Repository
public interface AnnouncementRepository extends JpaRepository<Announcement, UUID>, JpaSpecificationExecutor<Announcement> {

    // Find active and non-deleted announcements for general display
    List<Announcement> findByIsEnabledAndIsDeletedAndStartTimeBeforeAndEndTimeAfterOrderByIsImportantDescCreateTimeDesc(
            Short isEnabled, Short isDeleted, OffsetDateTime currentTimeForStart, OffsetDateTime currentTimeForEnd);

    // Example for admin view, might need pagination or more specific filters
    Page<Announcement> findByIsDeletedOrderByCreateTimeDesc(Short isDeleted, Pageable pageable);

} 