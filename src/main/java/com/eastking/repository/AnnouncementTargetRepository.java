package com.eastking.repository;

import com.eastking.model.entity.Announcement;
import com.eastking.model.entity.AnnouncementTarget;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * 系統公告對象關聯 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Repository
public interface AnnouncementTargetRepository extends JpaRepository<AnnouncementTarget, UUID> {
    List<AnnouncementTarget> findByAnnouncementAndIsDeleted(Announcement announcement, Short isDeleted);
    void deleteByAnnouncement(Announcement announcement);
} 