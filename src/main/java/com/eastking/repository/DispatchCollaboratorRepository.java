package com.eastking.repository;

import com.eastking.model.entity.DispatchCollaborator;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;
import com.eastking.model.entity.DispatchRepair;
import com.eastking.model.entity.UserAccount;
import java.util.Optional;

@Repository
public interface DispatchCollaboratorRepository extends JpaRepository<DispatchCollaborator, UUID> {
    List<DispatchCollaborator> findByDispatchRepair(DispatchRepair dispatchRepair);
    List<DispatchCollaborator> findByTechnicianAndInvitationStatus(UserAccount technician, Short invitationStatus);
    List<DispatchCollaborator> findByDispatchRepair_DispatchRepairIdIn(List<UUID> dispatchRepairIds);
    Optional<DispatchCollaborator> findByDispatchRepair_DispatchRepairIdAndTechnician_UserAccountId(UUID dispatchRepairId, UUID technicianId);
}