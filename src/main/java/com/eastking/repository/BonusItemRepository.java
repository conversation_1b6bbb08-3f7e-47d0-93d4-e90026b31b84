package com.eastking.repository;

import com.eastking.model.entity.BonusItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;
import java.util.List;

@Repository
public interface BonusItemRepository extends JpaRepository<BonusItem, UUID>, JpaSpecificationExecutor<BonusItem> {
    Optional<BonusItem> findByItemNameAndIsDeleted(String itemName, Short isDeleted);
    List<BonusItem> findByIsActiveAndIsDeleted(Short isActive, Short isDeleted);
} 