package com.eastking.repository;

import com.eastking.model.entity.StorePurchaseOrder;
import com.eastking.model.entity.StorePurchaseOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 門市進貨單品項表 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/17
 */
@Repository
public interface StorePurchaseOrderItemRepository extends JpaRepository<StorePurchaseOrderItem, UUID>, JpaSpecificationExecutor<StorePurchaseOrderItem> {
    List<StorePurchaseOrderItem> findByStorePurchaseOrderAndIsDeleted(StorePurchaseOrder storePurchaseOrder, Short isDeleted);
    List<StorePurchaseOrderItem> findByStorePurchaseOrder_StorePurchaseOrderIdAndIsDeleted(UUID storePurchaseOrderId, Short isDeleted);

    @Query("SELECT SUM(spoi.orderedQuantity) FROM StorePurchaseOrderItem spoi " +
           "WHERE spoi.productBarcode = :productBarcode AND spoi.isDeleted = 0 " +
           "AND (cast(:dateFrom as timestamp) IS NULL OR spoi.storePurchaseOrder.createTime >= :dateFrom) " +
           "AND (cast(:dateTo as timestamp) IS NULL OR spoi.storePurchaseOrder.createTime <= :dateTo)")
    Integer sumQuantityByProductBarcodeAndDateRange(@Param("productBarcode") String productBarcode, 
                                                  @Param("dateFrom") OffsetDateTime dateFrom, 
                                                  @Param("dateTo") OffsetDateTime dateTo);

    @Query("SELECT spoi FROM StorePurchaseOrderItem spoi JOIN FETCH spoi.storePurchaseOrder " +
           "WHERE spoi.productBarcode = :productBarcode AND spoi.isDeleted = 0 " +
           "AND (cast(:dateFrom as timestamp) IS NULL OR spoi.storePurchaseOrder.createTime >= :dateFrom) " +
           "AND (cast(:dateTo as timestamp) IS NULL OR spoi.storePurchaseOrder.createTime <= :dateTo)")
    List<StorePurchaseOrderItem> findByProductBarcodeAndDateRange(@Param("productBarcode") String productBarcode, 
                                                               @Param("dateFrom") OffsetDateTime dateFrom, 
                                                               @Param("dateTo") OffsetDateTime dateTo);
} 