package com.eastking.repository;

import com.eastking.model.entity.SystemFunction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 系統功能項目 Repository
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Repository
public interface SystemFunctionRepository extends JpaRepository<SystemFunction, UUID> {
    Optional<SystemFunction> findByFunctionCodeAndIsDeleted(String functionCode, Short isDeleted);
    List<SystemFunction> findByParentFunctionCodeAndIsDeletedOrderBySequenceOrder(String parentFunctionCode, Short isDeleted);
    List<SystemFunction> findByIsDeletedOrderBySequenceOrder(Short isDeleted);
    List<SystemFunction> findByParentFunctionCodeIsNullOrderBySequenceOrderAsc();
} 