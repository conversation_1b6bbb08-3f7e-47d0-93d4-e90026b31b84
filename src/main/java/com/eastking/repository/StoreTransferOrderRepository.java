package com.eastking.repository;

import com.eastking.model.entity.StoreTransferOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface StoreTransferOrderRepository extends JpaRepository<StoreTransferOrder, UUID>, JpaSpecificationExecutor<StoreTransferOrder> {
    Optional<StoreTransferOrder> findByTransferOrderNumberAndIsDeleted(String transferOrderNumber, Short isDeleted);
    // Add other finders as needed, e.g., by requesting_store_id, supplying_store_id, status
} 