package com.eastking.repository;

import com.eastking.model.entity.StorePurchaseOrderDiscrepancy;
import com.eastking.model.entity.StorePurchaseOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface StorePurchaseOrderDiscrepancyRepository extends JpaRepository<StorePurchaseOrderDiscrepancy, UUID>, JpaSpecificationExecutor<StorePurchaseOrderDiscrepancy> {
    List<StorePurchaseOrderDiscrepancy> findByStorePurchaseOrderItemAndIsDeleted(StorePurchaseOrderItem storePurchaseOrderItem, Short isDeleted);
    List<StorePurchaseOrderDiscrepancy> findByStorePurchaseOrderItem_StorePurchaseOrderItemIdAndIsDeleted(UUID storePurchaseOrderItemId, Short isDeleted);
} 