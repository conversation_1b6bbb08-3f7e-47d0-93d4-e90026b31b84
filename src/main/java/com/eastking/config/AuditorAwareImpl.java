package com.eastking.config;

import com.eastking.model.entity.UserAccount;
import com.eastking.security.UserDetailsImpl;
import org.springframework.data.domain.AuditorAware;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;
import java.util.UUID;

@Component("auditorProvider")
public class AuditorAwareImpl implements AuditorAware<UUID> {

    private static final Logger logger = LoggerFactory.getLogger(AuditorAwareImpl.class);

    @Override
    public Optional<UUID> getCurrentAuditor() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        logger.debug("Attempting to determine current auditor.");

        if (authentication == null) {
            logger.debug("No authentication object found in SecurityContext.");
            return Optional.empty();
        }

        logger.debug("Authentication object: {}, Authenticated: {}, Principal class: {}", 
            authentication.getName(), authentication.isAuthenticated(), authentication.getPrincipal().getClass().getName());
        logger.debug("Principal value: {}", authentication.getPrincipal());

        if (!authentication.isAuthenticated() || "anonymousUser".equals(authentication.getPrincipal())) {
            logger.debug("User is anonymous or not authenticated. Principal: {}", authentication.getPrincipal());
            return Optional.empty(); 
        }

        Object principal = authentication.getPrincipal();
        
        if (principal instanceof UserDetailsImpl) { 
            UUID userId = ((UserDetailsImpl) principal).getId(); 
            logger.debug("Auditor is UserDetailsImpl, ID: {}", userId);
            return Optional.ofNullable(userId);
        } else if (principal instanceof UserAccount) {
            UUID userId = ((UserAccount) principal).getUserAccountId();
            logger.debug("Auditor is UserAccount entity, ID: {}", userId);
            return Optional.ofNullable(userId);
        } else if (principal instanceof org.springframework.security.core.userdetails.User) {
            String username = ((org.springframework.security.core.userdetails.User) principal).getUsername();
            logger.warn("AuditorAwareImpl: Authentication principal is org.springframework.security.core.userdetails.User (username: {}). Attempting to parse username as UUID. This is likely not the correct user ID for auditing.", username);
            try {
                UUID userId = UUID.fromString(username);
                logger.debug("Successfully parsed username string to UUID: {}", userId);
                return Optional.of(userId);
            } catch (IllegalArgumentException e) {
                logger.warn("Could not parse username '{}' as UUID for auditing.", username);
                return Optional.empty();
            }
        } else if (principal instanceof String) {
            logger.debug("Principal is a String: {}", principal);
            try {
                UUID userId = UUID.fromString((String) principal);
                logger.debug("Parsed String principal to UUID: {}", userId);
                return Optional.of(userId);
            } catch (IllegalArgumentException e) {
                logger.warn("AuditorAwareImpl: Principal is a String but not a valid UUID: {}", principal, e);
                return Optional.empty();
            }
        }
        
        logger.warn("AuditorAwareImpl: Could not determine auditor UUID from principal of type: {}. Returning empty.", principal.getClass().getName());
        return Optional.empty();
    }
} 