package com.eastking.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.auditing.DateTimeProvider;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
// import org.springframework.security.core.Authentication; // Keep for future auditor implementation
// import org.springframework.security.core.context.SecurityContextHolder; // Keep for future auditor implementation
// import com.eastking.security.UserDetailsImpl; // Keep for future auditor implementation

import java.time.OffsetDateTime;
import java.util.Optional;
// import java.util.UUID; // UUID might not be needed directly here anymore

/**
 * JPA 審計功能配置
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Configuration
// @EnableJpaAuditing(auditorAwareRef = "auditorProvider", dateTimeProviderRef = "offsetDateTimeProvider")
// The @EnableJpaAuditing is already on EastkingApplication.java. 
// If we keep it here, ensure auditorAwareRef="auditorProvider" points to the @Component AuditorAwareImpl
// It's fine to have @EnableJpaAuditing on the main app or a specific config. Let's assume EastkingApplication's is primary.
// For now, the main issue is the conflicting bean, so this @EnableJpaAuditing could be removed if the one on EastkingApplication is active.
// However, if this class is the *intended* JPA config, then the one on EastkingApplication could be removed.
// Keeping it here for now and just removing the conflicting bean.
@EnableJpaAuditing(auditorAwareRef = "auditorProvider", dateTimeProviderRef = "offsetDateTimeProvider")
public class JpaAuditingConfiguration {

    // Removed conflicting bean definition for auditorProvider
    // The @Component("auditorProvider") on AuditorAwareImpl will be used.
    /*
    @Bean
    public AuditorAware<UUID> auditorProvider() {
        return () -> Optional.empty(); 
    }
    */

    @Bean(name = "offsetDateTimeProvider")
    public DateTimeProvider offsetDateTimeProvider() {
        return () -> Optional.of(OffsetDateTime.now());
    }
} 