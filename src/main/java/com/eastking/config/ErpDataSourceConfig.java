package com.eastking.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "erpEntityManagerFactory",
        transactionManagerRef = "erpTransactionManager",
        basePackages = {"com.eastking.erp.repository"}
)
public class ErpDataSourceConfig {

    private static final Logger logger = LoggerFactory.getLogger(ErpDataSourceConfig.class);

    @Bean(name = "erpDataSource")
    @ConfigurationProperties(prefix = "erp.datasource")
    public DataSource dataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = "erpJpaProperties")
    @ConfigurationProperties(prefix = "erp.jpa")
    public JpaProperties jpaProperties() {
        return new JpaProperties();
    }

    @Bean(name = "erpEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("erpDataSource") DataSource dataSource,
            @Qualifier("erpJpaProperties") JpaProperties jpaProperties
    ) {
        // 合併 JPA properties
        Map<String, Object> properties = new HashMap<>();

        // 從 JpaProperties 獲取所有屬性
        if (jpaProperties.getProperties() != null) {
            properties.putAll(jpaProperties.getProperties());
        }

        // 明確設定一些關鍵屬性以避免 null 問題
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.physical_naming_strategy", "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");

        logger.info("Building ERP EntityManagerFactory with properties: {}", properties);

        return builder
                .dataSource(dataSource)
                .packages("com.eastking.erp.model.entity") // Scan for ERP entities in this package
                .persistenceUnit("erp")
                .properties(properties)
                .build();
    }

    @Bean(name = "erpTransactionManager")
    public PlatformTransactionManager transactionManager(
            @Qualifier("erpEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory
    ) {
        return new JpaTransactionManager(entityManagerFactory.getObject());
    }
}