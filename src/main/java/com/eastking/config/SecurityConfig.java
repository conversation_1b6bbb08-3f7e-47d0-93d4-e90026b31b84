package com.eastking.config;

import com.eastking.security.JwtAuthenticationFilter;
import com.eastking.security.RestAuthenticationEntryPointImpl;
import com.eastking.security.CustomAccessDeniedHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.http.HttpMethod;

import java.util.Arrays;
import java.util.List;

/**
 * Spring Security 配置
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true) // Enable method-level security like @PreAuthorize
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final RestAuthenticationEntryPointImpl unauthorizedHandler;
    private final CustomAccessDeniedHandler accessDeniedHandler;

    @Autowired
    public SecurityConfig(JwtAuthenticationFilter jwtAuthenticationFilter, 
                          RestAuthenticationEntryPointImpl unauthorizedHandler,
                          CustomAccessDeniedHandler accessDeniedHandler) {
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
        this.unauthorizedHandler = unauthorizedHandler;
        this.accessDeniedHandler = accessDeniedHandler;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(unauthorizedHandler)
                .accessDeniedHandler(accessDeniedHandler)
            )
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                .requestMatchers(
                    "/**",  // Permit root for forwarding to login.html
                    "/css/**", "/js/**", "/img/**", "/favicon.ico"
                ).permitAll()
                .requestMatchers("/api/v1/auth/login").permitAll() // Public API
                .requestMatchers("/api/v1/announcements/public").permitAll() // Public API
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-resources/**").permitAll() // Swagger
                .requestMatchers("/eastking-app/swagger-ui/**", "/eastking-app/v3/api-docs/**", "/eastking-app/swagger-resources/**").permitAll()// Swagger with context path

                .requestMatchers("/api/v1/**").authenticated() // All other APIs require authentication
                
                .requestMatchers(HttpMethod.POST, "/api/v1/auth/change-password").authenticated()
                .requestMatchers(
                        "/api/v1/users/**",
                        "/api/v1/roles/**",
                        "/api/v1/announcements/**",
                        "/api/v1/stores/**",
                        "/api/v1/regions/**",
                        "/api/v1/member-levels/**",
                        "/api/v1/sms-templates/**",
                        "/api/v1/product-settings/**",
                        "/api/v1/product-menu/**",
                        "/api/v1/warehouses/**",
                        "/api/v1/technician-bonus/**",
                        "/api/v1/audit-log/**",
                        "/api/v1/customer-segments/**",
                        "/api/v1/gift-bundles/**",
                        "/api/v1/promotions/**",
                        "/api/v1/inventory/**",
                        "/api/v1/dispatch/**",
                        "/api/v1/material-orders/**"
                ).authenticated()
                
                // Any other .html file NOT explicitly permitted above will be caught by anyRequest() and require auth.
                // This is generally fine. If you have other public .html files, add them to the permitAll list.
                .anyRequest().authenticated() // Default for any other request
            );

        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }

    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        // Only ignore truly static resources that don't need any security filter chain processing
        return (web) -> web.ignoring()
                .requestMatchers(
                    new AntPathRequestMatcher("/css/**"),
                    new AntPathRequestMatcher("/js/**"), 
                    new AntPathRequestMatcher("/img/**"),
                    new AntPathRequestMatcher("/favicon.ico")
                );
    }

    /**
     * CORS 配置
     * @return CorsConfigurationSource
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(List.of("http://localhost:8080", "http://localhost:9700", "http://localhost:3000"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList("Authorization", "Cache-Control", "Content-Type", "X-Requested-With"));
        configuration.setAllowCredentials(true);
        configuration.setExposedHeaders(Arrays.asList("Authorization", "X-Session-UUID", "Content-Type", "Cache-Control"));
        configuration.setMaxAge(3600L);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/api/**", configuration); 
        return source;
    }

} 