package com.eastking.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 安全相關組件配置
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Configuration
public class SecurityComponentsConfig {

    /**
     * 配置密碼編碼器，使用 BCrypt
     * @return PasswordEncoder 實例
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
} 