package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
// No BaseEntity extension if it's a pure log table without user audit fields, 
// or create a simpler BaseLogEntity if common log fields are needed.
// For now, keeping it simple.
import lombok.EqualsAndHashCode;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * ERP 電動麻將桌庫存同步記錄表實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Data
@Entity
@Table(name = "sm_erp_mahjong_table_stock_log")
public class ErpMahjongTableStockLog {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "log_id", nullable = false, updatable = false)
    private UUID logId;

    @Column(name = "product_barcode", nullable = false, length = 255)
    private String productBarcode;

    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    @Column(name = "erp_warehouse_code", nullable = false, length = 100)
    private String erpWarehouseCode;

    @Column(name = "erp_warehouse_name", length = 255)
    private String erpWarehouseName;

    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @Column(name = "sync_time", nullable = false)
    private OffsetDateTime syncTime;

    @Column(name = "erp_last_update_time")
    private OffsetDateTime erpLastUpdateTime;

    @Column(name = "is_latest_for_product_warehouse", nullable = false)
    private Boolean isLatestForProductWarehouse = true;
} 