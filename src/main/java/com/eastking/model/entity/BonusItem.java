package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

/**
 * 技師獎金項目實體
 * <p>
 * 對應資料表: sm_bonus_item
 * </p>
 * <AUTHOR> Developer
 * @date 2025/05/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_bonus_item")
public class BonusItem extends BaseEntity {

    /**
     * 獎金項目ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "bonus_item_id", nullable = false, updatable = false)
    private UUID bonusItemId;

    /**
     * 項目名稱
     */
    @Column(name = "item_name", nullable = false, length = 255)
    private String itemName;

    /**
     * 項目描述
     */
    @Column(name = "item_description", columnDefinition = "TEXT")
    private String itemDescription;

    /**
     * 此項目是否啟用，0:否, 1:是. Java ENUM: ActivationStatusEnum
     */
    @Column(name = "is_active", nullable = false)
    private Short isActive = 1; // Default to 1 (ACTIVE)
} 