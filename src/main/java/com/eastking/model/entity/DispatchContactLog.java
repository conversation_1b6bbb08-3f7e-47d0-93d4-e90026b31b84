package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = "dispatchRepair")
@ToString(callSuper = true, exclude = "dispatchRepair")
@Entity
@Table(name = "sm_dispatch_contact_log")
public class DispatchContactLog extends BaseEntity {

    @Id
    @Column(name = "contact_log_id")
    private UUID logId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dispatch_repair_id")
    private DispatchRepair dispatchRepair;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "technician_id", nullable = false)
    private UserAccount technician;

    @Column(name = "contact_time", nullable = false)
    private OffsetDateTime contactTime;

    @Column(name = "contact_result_code", nullable = false)
    private Short contactResultCode;

    @Column(name = "agreed_matters", columnDefinition = "TEXT")
    private String agreedMatters;

    @Column(name = "next_contact_time")
    private OffsetDateTime nextContactTime;

    @PrePersist
    protected void onCreate() {
        if (logId == null) {
            logId = UUID.randomUUID();
        }
        if (contactTime == null) {
            contactTime = OffsetDateTime.now();
        }
    }
} 