package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = "orderRefundItem")
@ToString(exclude = "orderRefundItem")
@Entity
@Table(name = "sm_order_refund_item_group")
public class OrderRefundItemGroup extends BaseEntity {

    @Id
    @Column(name = "order_refund_item_group_id")
    private UUID orderRefundItemGroupId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_refund_item_id", nullable = false)
    private OrderRefundItem orderRefundItem;

    @Column(name = "original_order_item_group_id", nullable = false)
    private UUID originalOrderItemGroupId;

    @Column(name = "product_barcode", nullable = false)
    private String productBarcode;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @Column(name = "unit_price", nullable = false, precision = 18, scale = 2)
    private BigDecimal unitPrice;

    @Column(name = "subtotal_refund_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal subtotalRefundAmount;

    @Column(name = "item_type_code")
    private Short itemTypeCode;

    @Column(name = "restock_warehouse_code", length = 100)
    private String restockWarehouseCode;
    
    @PrePersist
    protected void onCreate() {
        if (orderRefundItemGroupId == null) {
            orderRefundItemGroupId = UUID.randomUUID();
        }
    }
} 