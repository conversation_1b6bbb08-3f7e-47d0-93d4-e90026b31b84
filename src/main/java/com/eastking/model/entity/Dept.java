package com.eastking.model.entity;

import com.eastking.enums.ErpCompanyDivisionEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_dept")
public class Dept extends BaseEntity {

    @Id
    @Column(name = "dept_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID deptId;

    @Column(name = "dept_desc", nullable = false)
    private String deptDesc;

    @Column(name = "erp_company_division", nullable = false)
    private Short erpCompanyDivision;
} 