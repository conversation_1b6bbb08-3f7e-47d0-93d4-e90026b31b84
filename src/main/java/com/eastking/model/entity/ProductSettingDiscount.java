package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

/**
 * 商品設定職權折扣實體
 * <p>
 * 對應資料表: sm_product_setting_discount
 * </p>
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_product_setting_discount", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"product_setting_id", "role_id", "is_deleted"})
})
public class ProductSettingDiscount extends BaseEntity {

    /**
     * 職權折扣ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "product_setting_discount_id", nullable = false, updatable = false)
    private java.util.UUID productSettingDiscountId;

    /**
     * 關聯的商品設定ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_setting_id", nullable = false)
    private ProductSetting productSetting;

    /**
     * 關聯的角色ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    /**
     * 折扣金額
     */
    @Column(name = "discount_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal discountAmount;
} 