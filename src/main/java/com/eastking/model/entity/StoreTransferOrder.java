package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_store_transfer_order")
public class StoreTransferOrder extends BaseEntity {

    @Id
    @Column(name = "store_transfer_order_id")
    private UUID storeTransferOrderId;

    @Column(name = "transfer_order_number", nullable = false, length = 100, unique = true)
    private String transferOrderNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requesting_store_id", nullable = false)
    private StoreEntity requestingStore;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requesting_user_id", nullable = false)
    private UserAccount requestingUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supplying_store_id", nullable = false)
    private StoreEntity supplyingStore;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dispatching_user_id")
    private UserAccount dispatchingUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "receiving_user_id")
    private UserAccount receivingUser;

    @Column(name = "transfer_status", nullable = false)
    private Short transferStatus; // Mapped to StoreTransferOrderStatusEnum

    @Column(name = "request_date", nullable = false)
    private OffsetDateTime requestDate;

    @Column(name = "dispatch_date")
    private OffsetDateTime dispatchDate;

    @Column(name = "received_date")
    private OffsetDateTime receivedDate;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    @Column(name = "rejection_reason", columnDefinition = "TEXT")
    private String rejectionReason;

    @OneToMany(mappedBy = "storeTransferOrder", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<StoreTransferOrderItem> items;

    @PrePersist
    protected void onCreate() {
        if (storeTransferOrderId == null) {
            storeTransferOrderId = UUID.randomUUID();
        }
        if (requestDate == null) {
            requestDate = OffsetDateTime.now();
        }
        // transferStatus defaults to 0 (PENDING_APPROVAL) as per DB DDL
    }
} 