package com.eastking.model.entity;

import com.eastking.model.dto.ServiceAreaDto;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;

import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_technician_area_config")
public class TechnicianAreaConfig extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "config_id")
    private UUID configId;

    @Column(name = "config_month", nullable = false, length = 7) // YYYY-MM
    private String configMonth;

    @Column(name = "technician_id", nullable = false)
    private UUID technicianId;
    
    @Column(name = "technician_name")
    private String technicianName;

    @Type(JsonType.class)
    @Column(name = "service_areas", columnDefinition = "jsonb")
    private List<ServiceAreaDto> serviceAreas;
} 