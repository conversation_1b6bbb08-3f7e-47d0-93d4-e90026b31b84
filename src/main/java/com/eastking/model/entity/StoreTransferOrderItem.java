package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_store_transfer_order_item")
public class StoreTransferOrderItem extends BaseEntity {

    @Id
    @Column(name = "store_transfer_order_item_id")
    private UUID storeTransferOrderItemId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_transfer_order_id", nullable = false)
    private StoreTransferOrder storeTransferOrder;

    @Column(name = "product_barcode", nullable = false, length = 255)
    private String productBarcode;

    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    @Column(name = "requested_quantity", nullable = false)
    private Integer requestedQuantity;

    @Column(name = "dispatched_quantity")
    private Integer dispatchedQuantity;

    @Column(name = "received_quantity")
    private Integer receivedQuantity;

    @PrePersist
    protected void onCreate() {
        if (storeTransferOrderItemId == null) {
            storeTransferOrderItemId = UUID.randomUUID();
        }
    }
} 