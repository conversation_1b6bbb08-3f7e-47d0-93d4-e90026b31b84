package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "sm_dispatch_material_order_item")
public class DispatchMaterialOrderItem extends BaseEntity {

    @Id
    @Column(name = "material_order_item_id")
    private UUID materialOrderItemId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material_order_id", nullable = false)
    private DispatchMaterialOrder materialOrder;

    @Column(name = "dispatch_repair_item_id")
    private UUID dispatchRepairItemId;

    @Column(name = "product_barcode", nullable = false)
    private String productBarcode;

    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    @Column(name = "requested_quantity", nullable = false)
    private Integer requestedQuantity;

    /**
     * 已揀料數量
     */
    @Column(name = "picked_quantity")
    private Integer pickedQuantity;

    /**
     * 是否已揀料，0:否, 1:是
     */
    @Column(name = "is_picked", nullable = false)
    private Short isPicked = 0;

    @Column(name = "collected_quantity")
    private Integer collectedQuantity;
    
    @PrePersist
    protected void onCreate() {
        if (materialOrderItemId == null) {
            materialOrderItemId = UUID.randomUUID();
        }
    }
} 