package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.UUID;

/**
 * 門市統編抬頭對應資料實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_store_tax_identification_map", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"store_id", "tax_identification_id", "is_deleted"})
})
public class StoreTaxIdentificationMapEntity extends BaseEntity {

    /**
     * 對應ID，主鍵
     */
    @Id
    @Column(name = "store_tax_map_id")
    private UUID storeTaxMapId;

    /**
     * 門市ID (FK to sm_store)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id", nullable = false)
    private StoreEntity store;

    /**
     * 統編ID (FK to sm_tax_identification)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tax_identification_id", nullable = false)
    private TaxIdentificationEntity taxIdentification;

    @PrePersist
    protected void onCreate() {
        if (storeTaxMapId == null) {
            storeTaxMapId = UUID.randomUUID();
        }
    }
} 