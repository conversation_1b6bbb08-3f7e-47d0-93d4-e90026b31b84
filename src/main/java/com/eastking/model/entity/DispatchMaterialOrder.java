package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = {"dispatchRepair", "requestingTechnician", "picker", "targetWarehouse", "items"})
@ToString(callSuper = true)
@Entity
@Table(name = "sm_dispatch_material_order")
public class DispatchMaterialOrder extends BaseEntity {

    @Id
    @Column(name = "material_order_id")
    private UUID materialOrderId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dispatch_repair_id")
    private DispatchRepair dispatchRepair;

    @Column(name = "material_order_number", nullable = false, unique = true)
    private String materialOrderNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requesting_technician_id", nullable = false)
    private UserAccount requestingTechnician;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_warehouse_id", nullable = false)
    private Warehouse targetWarehouse;

    @Column(name = "material_order_status_code", nullable = false)
    private Short materialOrderStatusCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "picker_id")
    private UserAccount picker;

    @Column(name = "picked_time")
    private OffsetDateTime pickedTime;

    @Column(name = "collected_time")
    private OffsetDateTime collectedTime;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;

    @Column(name = "material_type")
    private Short materialType;
    
    @OneToMany(mappedBy = "materialOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<DispatchMaterialOrderItem> items;

    @PrePersist
    protected void onCreate() {
        if (materialOrderId == null) {
            materialOrderId = UUID.randomUUID();
        }
    }
} 