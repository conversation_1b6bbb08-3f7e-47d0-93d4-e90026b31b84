package com.eastking.model.entity;

import com.eastking.enums.CustomerStatusEnum;
import com.eastking.enums.TaxCategoryEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "sm_wholesale_customer")
public class WholesaleCustomer extends BaseEntity {

    @Id
    @Column(name = "wholesale_customer_id", nullable = false, updatable = false)
    private UUID wholesaleCustomerId;

    @Column(name = "erp_customer_code", length = 50, unique = true)
    private String erpCustomerCode;

    @Column(name = "customer_name", nullable = false)
    private String customerName;

    @Column(name = "contact_person", length = 100)
    private String contactPerson;

    @Column(name = "phone_number", length = 50)
    private String phoneNumber;

    @Column(name = "alternative_phone", length = 50)
    private String alternativePhone;

    @Column(name = "fax_number", length = 50)
    private String faxNumber;

    @Column(name = "email")
    private String email;

    @Column(name = "company_address", columnDefinition = "TEXT")
    private String companyAddress;

    @Column(name = "shipping_address", columnDefinition = "TEXT")
    private String shippingAddress;

    @Column(name = "tax_id_number", length = 20)
    private String taxIdNumber;

    @Column(name = "is_direct_store")
    private Boolean isDirectStore = false;

    @Column(name = "payment_terms")
    private String paymentTerms;

    @Column(name = "tax_category")
    private Short taxCategoryCode; // Mapped to TaxCategoryEnum

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sales_person_id")
    private UserAccount salesPerson; // Responsible sales person

    @Column(name = "customer_status", nullable = false)
    private Short customerStatusCode = CustomerStatusEnum.ACTIVE.getCode(); // Mapped to CustomerStatusEnum

    @Column(columnDefinition = "TEXT")
    private String remarks;

    @PrePersist
    protected void onCreate() {
        if (wholesaleCustomerId == null) {
            wholesaleCustomerId = UUID.randomUUID();
        }
    }

    // Helper methods for enums if needed (can be added to DTOs as well)
    @Transient
    public TaxCategoryEnum getTaxCategoryEnum() {
        return TaxCategoryEnum.fromCode(this.taxCategoryCode);
    }

    public void setTaxCategoryEnum(TaxCategoryEnum taxCategoryEnum) {
        this.taxCategoryCode = (taxCategoryEnum == null) ? null : taxCategoryEnum.getCode();
    }

    @Transient
    public CustomerStatusEnum getCustomerStatusEnum() {
        return CustomerStatusEnum.fromCode(this.customerStatusCode);
    }

    public void setCustomerStatusEnum(CustomerStatusEnum customerStatusEnum) {
        this.customerStatusCode = (customerStatusEnum == null) ? null : customerStatusEnum.getCode();
    }
} 