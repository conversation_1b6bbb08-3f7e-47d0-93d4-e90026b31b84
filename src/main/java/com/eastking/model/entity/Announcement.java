package com.eastking.model.entity;

import com.eastking.enums.AnnouncementCategoryEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 系統公告實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_announcement")
public class Announcement extends BaseEntity {

    @Id
    @Column(name = "announcement_id", nullable = false, updatable = false)
    @GeneratedValue(strategy = GenerationType.AUTO) // To be consistent with other entities
    private UUID announcementId;

    @Column(name = "category_code", nullable = false)
    private Short categoryCode; // Mapped to AnnouncementCategoryEnum code

    @Column(name = "title", nullable = false, length = 255)
    private String title;

    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    @Column(name = "start_time", nullable = false)
    private OffsetDateTime startTime;

    @Column(name = "end_time", nullable = false)
    private OffsetDateTime endTime;

    @Column(name = "is_important", nullable = false)
    private Short isImportant = 0; // Default to 0 (false)

    @Column(name = "is_enabled", nullable = false)
    private Short isEnabled = 1; // Default to 1 (true)

    @OneToMany(mappedBy = "announcement", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<AnnouncementTarget> targets;

    // Transient field for enum description if needed for DTOs, not persisted
    @Transient
    public String getCategoryDescription() {
        AnnouncementCategoryEnum categoryEnum = AnnouncementCategoryEnum.fromCode(this.categoryCode);
        return categoryEnum != null ? categoryEnum.getDescription() : null;
    }
} 