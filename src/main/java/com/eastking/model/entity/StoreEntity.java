package com.eastking.model.entity;

import com.eastking.enums.StoreStatusEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.UUID;

/**
 * 門市資料實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_store")
public class StoreEntity extends BaseEntity {

    /**
     * 門市ID，主鍵
     */
    @Id
    @Column(name = "store_id")
    private UUID storeId;

    /**
     * 門市名稱
     */
    @Column(name = "store_name", nullable = false, length = 100)
    private String storeName;

    /**
     * 門市代碼 (可選)
     */
    @Column(name = "store_code", length = 50)
    private String storeCode;

    /**
     * 所屬地區ID (FK to sm_region)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_id")
    private RegionEntity region;

    /**
     * 門市啟用狀態，0:停用, 1:啟用。Java ENUM: StoreStatusEnum
     */
    @Column(name = "is_active", nullable = false)
    private Short isActive; // Mapped to StoreStatusEnum in DTO/Service

    /**
     * 門市地址
     */
    @Column(name = "address", columnDefinition = "TEXT")
    private String address;

    /**
     * 門市電話
     */
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;

    /**
     * 門市歸屬ERP公司別 (1: 東方不敗, 2: 雀友). Java ENUM: ErpCompanyDivisionEnum
     */
    @Column(name = "erp_company_division", nullable = false)
    private Short erpCompanyDivision;

    // Relationships
    @OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<StoreStaffMapEntity> storeStaffMaps;

    @OneToMany(mappedBy = "store", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<StoreTaxIdentificationMapEntity> storeTaxIdentificationMaps;

    @PrePersist
    protected void onCreate() {
        if (storeId == null) {
            storeId = UUID.randomUUID();
        }
        if (isActive == null) {
            isActive = StoreStatusEnum.ENABLED.getCode(); // Default to enabled
        }
        if (erpCompanyDivision == null) { // Set default if not provided
            erpCompanyDivision = ErpCompanyDivisionEnum.EASTKING.getCode();
        }
    }

    // Getter and Setter for isActive to use Enum (optional, can be handled in DTO/Service)
    public StoreStatusEnum getIsActiveEnum() {
        return StoreStatusEnum.fromCode(this.isActive);
    }

    public void setIsActiveEnum(StoreStatusEnum statusEnum) {
        this.isActive = statusEnum.getCode();
    }
} 