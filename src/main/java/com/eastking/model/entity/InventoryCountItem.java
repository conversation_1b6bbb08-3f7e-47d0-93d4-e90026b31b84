package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = {"inventoryCountSheet"})
@ToString(callSuper = true, exclude = {"inventoryCountSheet"})
@Entity
@Table(name = "sm_inventory_count_item")
public class InventoryCountItem extends BaseEntity {

    @Id
    @Column(name = "inventory_count_item_id")
    private UUID inventoryCountItemId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inventory_count_sheet_id", nullable = false)
    private InventoryCountSheet inventoryCountSheet;

    @Column(name = "product_barcode", nullable = false, length = 255)
    private String productBarcode;

    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    @Column(name = "system_quantity")
    private Integer systemQuantity;

    @Column(name = "counted_quantity", nullable = false)
    private Integer countedQuantity;

    @Column(name = "recount_quantity")
    private Integer recountQuantity;

    @Column(name = "final_quantity")
    private Integer finalQuantity;

    @Column(name = "requires_recount", nullable = false)
    private Boolean requiresRecount = false;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;

    @PrePersist
    protected void onCreate() {
        if (inventoryCountItemId == null) {
            inventoryCountItemId = UUID.randomUUID();
        }
    }
} 