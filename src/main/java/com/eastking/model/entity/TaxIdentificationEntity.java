package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.UUID;

/**
 * 統編抬頭資料實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_tax_identification")
public class TaxIdentificationEntity extends BaseEntity {

    /**
     * 統編ID，主鍵
     */
    @Id
    @Column(name = "tax_identification_id")
    private UUID taxIdentificationId;

    /**
     * 統一編號
     */
    @Column(name = "tax_id_number", nullable = false, length = 20)
    private String taxIdNumber;

    /**
     * 公司抬頭
     */
    @Column(name = "company_name", nullable = false, length = 100)
    private String companyName;

    @PrePersist
    protected void onCreate() {
        if (taxIdentificationId == null) {
            taxIdentificationId = UUID.randomUUID();
        }
    }
} 