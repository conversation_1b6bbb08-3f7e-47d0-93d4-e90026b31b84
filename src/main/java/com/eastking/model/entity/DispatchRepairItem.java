package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "sm_dispatch_repair_item")
public class DispatchRepairItem extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "dispatch_repair_item_id")
    @Comment("主鍵ID")
    private UUID dispatchRepairItemId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dispatch_repair_id", nullable = false)
    @Comment("所屬派工維修單ID (FK sm_dispatch_repair)")
    private DispatchRepair dispatchRepair;

    @Column(name = "order_item_id")
    private UUID orderItemId;

    @Column(name = "order_item_group_id")
    private UUID orderItemGroupId;
    
    @Column(name = "device_serial_number", length = 100)
    private String deviceSerialNumber;

    @Column(name = "product_model", length = 100)
    private String productModel;

    @Column(name = "warranty_expiry_date")
    private LocalDate warrantyExpiryDate;

    @Column(name = "warranty_status_code")
    private Short warrantyStatusCode;

    @Column(name = "issue_description", columnDefinition = "TEXT")
    private String issueDescription;

    @Column(name = "product_barcode", nullable = false, length = 255)
    @Comment("商品國際條碼")
    private String productBarcode;

    @Column(name = "product_name", columnDefinition = "TEXT")
    @Comment("商品名稱 (快照)")
    private String productName;

    //注意：這裡是對應到 warehouse_id
    @Column(name = "warehouse_code", length = 100)
    private String warehouseCode;

    @Column(name = "item_type_code", nullable = false)
    @Comment("品項類型. Java ENUM: DispatchRepairItemTypeEnum")
    private Short itemTypeCode;

    @Column(name = "is_addon", nullable = false)
    @Comment("是否為加購品. 0:否, 1:是. Java ENUM: BooleanStatusEnum (描述: 是否狀態)")
    private Short isAddon = 0;

    @Column(name = "quantity", nullable = false)
    @Comment("數量")
    private Integer quantity;

    @Column(name = "unit_price", precision = 18, scale = 2)
    @Comment("單價")
    private BigDecimal unitPrice;

    @Column(name = "subtotal_amount", precision = 18, scale = 2)
    @Comment("小計")
    private BigDecimal subtotalAmount;
} 