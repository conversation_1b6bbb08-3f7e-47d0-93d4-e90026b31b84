package com.eastking.model.entity;

import com.eastking.enums.ItemConditionEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = {"orderRefund", "originalOrderItem"})
@ToString(callSuper = true, exclude = {"orderRefund", "originalOrderItem"})
@Entity
@Table(name = "sm_order_refund_item")
public class OrderRefundItem extends BaseEntity {

    @Id
    @Column(name = "order_refund_item_id")
    private UUID orderRefundItemId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_refund_id", nullable = false)
    private OrderRefund orderRefund;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "original_order_item_id", nullable = false)
    private OrderItem originalOrderItem;

    @OneToMany(mappedBy = "orderRefundItem", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<OrderRefundItemGroup> itemGroups = new ArrayList<>();

    @Column(name = "product_barcode", nullable = false, length = 255)
    private String productBarcode;

    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    @Column(name = "item_type_code", nullable = false)
    private Short itemTypeCode; // Mapped to OrderItemTypeEnum

    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @Column(name = "unit_price", nullable = false, precision = 18, scale = 2)
    private BigDecimal unitPrice; // Price at the time of original sale for this item

    @Column(name = "refund_amount_per_item", nullable = false, precision = 18, scale = 2)
    private BigDecimal refundAmountPerItem; // Actual amount refunded per item for this line

    @Column(name = "subtotal_refund_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal subtotalRefundAmount; // quantity * refundAmountPerItem

    @Column(name = "item_condition_code")
    private Short itemConditionCode; // Mapped to ItemConditionEnum

    @Column(name = "restock_warehouse_code", length = 100)
    private String restockWarehouseCode;

    @Column(name = "item_notes", columnDefinition = "TEXT")
    private String itemNotes;

    @PrePersist
    protected void onCreate() {
        if (orderRefundItemId == null) {
            orderRefundItemId = UUID.randomUUID();
        }
    }

    @Transient
    public ItemConditionEnum getItemConditionEnum() {
        return ItemConditionEnum.fromCode(this.itemConditionCode);
    }
} 