package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import java.time.OffsetDateTime;
import java.util.UUID;

@Entity
@Table(name = "sm_city")
@Data
public class City {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "city_id", updatable = false, nullable = false)
    private UUID cityId;

    @Column(name = "city_code", nullable = false)
    private String cityCode;

    @Column(name = "city_name", nullable = false)
    private String cityName;

    @Column(name = "sequence_order")
    private Integer sequenceOrder;
    
    @Column(name = "create_time", nullable = false, updatable = false)
    private OffsetDateTime createTime;

    @Column(name = "update_time", nullable = false)
    private OffsetDateTime updateTime;

    @Column(name = "is_deleted", nullable = false)
    private Short isDeleted;
    
    @PrePersist
    protected void onCreate() {
        createTime = OffsetDateTime.now();
        updateTime = OffsetDateTime.now();
        if (isDeleted == null) {
            isDeleted = 0;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = OffsetDateTime.now();
    }
} 