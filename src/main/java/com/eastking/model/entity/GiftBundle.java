package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_gift_bundle")
public class GiftBundle extends BaseEntity {

    @Id
    @Column(name = "bundle_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID bundleId;

    @Column(name = "bundle_name", nullable = false)
    private String bundleName;

    @Column(name = "main_product_barcode", nullable = false)
    private String mainProductBarcode;

    @Column(name = "main_product_name", columnDefinition = "TEXT")
    private String mainProductName;

    @Column(name = "start_time", nullable = false)
    private OffsetDateTime startTime;

    @Column(name = "end_time", nullable = false)
    private OffsetDateTime endTime;

    @Column(name = "is_active", nullable = false)
    private Short isActive = 1; // Default to active

    @OneToMany(mappedBy = "giftBundle", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<GiftBundleItem> items;
} 