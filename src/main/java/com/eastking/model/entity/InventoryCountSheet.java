package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = {"store", "countedByUser", "recountByUser", "approvedByUser", "originalSheet", "items"})
@ToString(callSuper = true, exclude = {"items"})
@Entity
@Table(name = "sm_inventory_count_sheet")
public class InventoryCountSheet extends BaseEntity {

    @Id
    @Column(name = "inventory_count_sheet_id")
    private UUID inventoryCountSheetId;

    @Column(name = "sheet_number", nullable = false, unique = true, length = 100)
    private String sheetNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id", nullable = false)
    private StoreEntity store;

    @Column(name = "company_division_code", nullable = false)
    private Short companyDivisionCode;

    @Column(name = "count_date", nullable = false)
    private OffsetDateTime countDate;

    @Column(name = "count_month", nullable = false, length = 7)
    private String countMonth;

    @Column(name = "sheet_status_code", nullable = false)
    private Short sheetStatusCode;

    @Column(name = "approval_status_code", nullable = false)
    private Short approvalStatusCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "original_sheet_id")
    private InventoryCountSheet originalSheet;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "counted_by_user_id", nullable = false)
    private UserAccount countedByUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recount_by_user_id")
    private UserAccount recountByUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by_user_id")
    private UserAccount approvedByUser;

    @Column(name = "approval_time")
    private OffsetDateTime approvalTime;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;

    @OneToMany(mappedBy = "inventoryCountSheet", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<InventoryCountItem> items;

    @PrePersist
    protected void onCreate() {
        if (inventoryCountSheetId == null) {
            inventoryCountSheetId = UUID.randomUUID();
        }
    }
} 