package com.eastking.model.entity;

import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.enums.OrderOperatorTypeEnum;
import com.eastking.enums.OrderStatusEnum;
import com.eastking.enums.OrderTypeEnum;
import com.eastking.enums.PaymentStatusEnum;
import com.eastking.enums.InvoiceTypeEnum;
import com.eastking.enums.TaxTypeEnum;
import com.eastking.enums.DispatchStatusEnum;
import com.eastking.enums.ShipmentMethodEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = {"items", "store", "userAccount", "member", "technician", "sourceOrder", "wholesaleCustomer", "distributor", "promotion"})
@ToString(callSuper = true, exclude = {"items", "wholesaleCustomer", "distributor", "promotion"})
@Entity
@Table(name = "sm_order")
public class Order extends BaseEntity {

    @Id
    @Column(name = "order_id")
    private UUID orderId;

    @Column(name = "order_number", nullable = false, length = 100, unique = true)
    private String orderNumber;

    @Column(name = "company_division_code", nullable = false)
    private Short companyDivisionCode;

    @Column(name = "order_type_code", nullable = false)
    private Short orderTypeCode;

    @Column(name = "operator_type_code")
    private Short operatorTypeCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id")
    private StoreEntity store;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_account_id", nullable = false)
    private UserAccount userAccount; // 開單人員

    @Column(name = "customer_name", length = 100)
    private String customerName; // For non-member retail or as a denormalized field

    @Column(name = "customer_phone", length = 50)
    private String customerPhone; // For non-member retail or as a denormalized field

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "member_id")
    private Customer member; // For retail/dispatch orders linked to a regular Customer/Member

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "wholesale_customer_id")
    private WholesaleCustomer wholesaleCustomer; // For wholesale orders linked to a WholesaleCustomer

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "distributor_id")
    private Distributor distributor; // For orders created by a Distributor

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "promotion_id")
    private Promotion promotion;

    @Column(name = "order_date", nullable = false)
    private OffsetDateTime orderDate;

    @Column(name = "order_status_code", nullable = false)
    private Short orderStatusCode;

    @Column(name = "products_total_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal productsTotalAmount;

    @Column(name = "discount_amount", precision = 18, scale = 2)
    private BigDecimal discountAmount;

    @Column(name = "net_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal netAmount;

    @Column(name = "tax_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal taxAmount;

    @Column(name = "grand_total_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal grandTotalAmount;

    @Column(name = "paid_amount", precision = 18, scale = 2)
    private BigDecimal paidAmount;

    @Column(name = "payment_status_code")
    private Short paymentStatusCode;

    @Column(name = "invoice_type_code")
    private Short invoiceTypeCode;

    @Column(name = "tax_type")
    private Short taxType;

    @Column(name = "invoice_number", length = 50)
    private String invoiceNumber;

    @Column(name = "tax_id_number", length = 20)
    private String taxIdNumber;

    @Column(name = "invoice_company_title", length = 100)
    private String invoiceCompanyTitle;

    @Column(name = "invoice_date")
    private LocalDate invoiceDate;

    @Column(name = "invoice_amount", precision = 18, scale = 2)
    private BigDecimal invoiceAmount;

    @Column(name = "invoice_address", columnDefinition = "TEXT")
    private String invoiceAddress;

    @Column(name = "is_invoice_sent")
    private Boolean isInvoiceSent;

    @Column(name = "store_invoice_amount", precision = 18, scale = 2)
    private BigDecimal storeInvoiceAmount;

    @Column(name = "store_invoice_company_title", length = 100)
    private String storeInvoiceCompanyTitle;

    @Column(name = "store_tax_id_number", length = 20)
    private String storeTaxIdNumber;

    // Dispatch specific fields (for dispatch orders)
    @Column(name = "contact_name", length = 100)
    private String contactName;

    @Column(name = "contact_phone", length = 50)
    private String contactPhone;

    @Column(name = "installation_address", columnDefinition = "TEXT")
    private String installationAddress;

    @Column(name = "installation_address_city")
    private String installationAddressCity;
    @Column(name = "installation_address_district")
    private String installationAddressDistrict;
    @Column(name = "installation_address_street")
    private String installationAddressStreet;
    @Column(name = "installation_address_lane")
    private String installationAddressLane;
    @Column(name = "installation_address_alley")
    private String installationAddressAlley;
    @Column(name = "installation_address_number")
    private String installationAddressNumber;
    @Column(name = "installation_address_floor")
    private String installationAddressFloor;
    @Column(name = "installation_address_unit")
    private String installationAddressUnit;

    @Column(name = "installation_date")
    private LocalDate installationDate;

    @Column(name = "installation_time_slot", length = 50)
    private String installationTimeSlot;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "technician_id")
    private UserAccount technician;

    @Column(name = "actual_completion_date")
    private OffsetDateTime actualCompletionDate;
    
    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;

    // Wholesale specific logistics fields (added based on 907_alter_order_tables_for_wholesale.sql)
    @Column(name = "shipment_method_code")
    private Short shipmentMethodCode; // Maps to ShipmentMethodEnum

    @Column(name = "logistics_provider_name", length = 100)
    private String logisticsProviderName;

    @Column(name = "logistics_shipment_count")
    private Integer logisticsShipmentCount;

    @Column(name = "truck_license_plate", length = 50)
    private String truckLicensePlate;

    @Column(name = "logistics_tracking_number", length = 100)
    private String logisticsTrackingNumber;
    // End of wholesale specific fields

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "source_order_id")
    private Order sourceOrder; // For exchanges, points to the original order

    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<OrderItem> items;

    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<OrderPayment> payments;

    @OneToMany(mappedBy = "order", fetch = FetchType.LAZY)
    private List<DispatchRepair> dispatchRepairs;

    @PrePersist
    protected void onCreate() {
        if (orderId == null) {
            orderId = UUID.randomUUID();
        }
        if (orderDate == null) {
            orderDate = OffsetDateTime.now();
        }
        if (orderStatusCode == null) {
            orderStatusCode = OrderStatusEnum.DRAFT.getCode();
        }
        if (paymentStatusCode == null) {
            paymentStatusCode = PaymentStatusEnum.UNPAID.getCode();
        }
        if (isInvoiceSent == null) {
            isInvoiceSent = false;
        }
    }

    // Helper methods for enums if needed for DTO conversion or business logic
    @Transient
    public ErpCompanyDivisionEnum getCompanyDivisionEnum() { return ErpCompanyDivisionEnum.fromCode(this.companyDivisionCode); }
    @Transient
    public OrderTypeEnum getOrderTypeEnum() {
        return OrderTypeEnum.fromCode(this.orderTypeCode);
    }
    @Transient
    public OrderOperatorTypeEnum getOperatorTypeEnum() { return OrderOperatorTypeEnum.fromCode(this.operatorTypeCode); }
    @Transient
    public OrderStatusEnum getOrderStatusEnum() { return OrderStatusEnum.fromCode(this.orderStatusCode); }
    @Transient
    public PaymentStatusEnum getPaymentStatusEnum() { return PaymentStatusEnum.fromCode(this.paymentStatusCode); }
    @Transient
    public InvoiceTypeEnum getInvoiceTypeEnum() { return InvoiceTypeEnum.fromCode(this.invoiceTypeCode); }
    @Transient
    public TaxTypeEnum getTaxTypeEnum() { return TaxTypeEnum.fromCode(this.taxType); }
    @Transient
    public ShipmentMethodEnum getShipmentMethodEnum() { return ShipmentMethodEnum.fromCode(this.shipmentMethodCode); }
} 