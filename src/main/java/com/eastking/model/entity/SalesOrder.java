package com.eastking.model.entity;

import com.eastking.enums.SalesOrderTypeEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
// import com.eastking.enums.SalesOrderStatusEnum; // For business logic

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 銷貨單主表實體
 * (目前是門市調撥將物品轉出回總倉時會使用到的物件)
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_sales_order")
public class SalesOrder extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "sales_order_id", nullable = false, updatable = false)
    private UUID salesOrderId;

    @Column(name = "sales_order_number", nullable = false, length = 100, unique = true)
    private String salesOrderNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id", nullable = false)
    private StoreEntity store;

    // @Column(name = "customer_id") // Assuming no direct Customer entity yet
    // private UUID customerId; 

    @Column(name = "order_date", nullable = false)
    private OffsetDateTime orderDate;

    @Column(name = "sales_order_status", nullable = false)
    private Short salesOrderStatus;

    @Column(name = "total_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal totalAmount;

    @Column(name = "tax_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal taxAmount;

    @Column(name = "grand_total_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal grandTotalAmount;

    @Column(name = "payment_method", length = 50)
    private String paymentMethod;

    @Column(name = "order_notes", columnDefinition = "TEXT")
    private String orderNotes;

    @Column(name = "dispatch_type", length = 50)
    private String dispatchType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "technician_id")
    private UserAccount technician;

    @Column(name = "sales_order_type", nullable = false)
    private Short salesOrderType = SalesOrderTypeEnum.REGULAR_SALE.getCode(); // Default to REGULAR_SALE

    @Column(name = "related_transfer_order_id")
    private UUID relatedTransferOrderId;

    @OneToMany(mappedBy = "salesOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<SalesOrderItem> items;
} 