package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_promotion_product")
public class PromotionProduct extends BaseEntity {

    @Id
    @Column(name = "promo_product_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID promoProductId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "promotion_id", nullable = false)
    private Promotion promotion;

    @Column(name = "product_barcode", nullable = false)
    private String productBarcode;

    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    @Column(name = "promo_price", nullable = false, precision = 12, scale = 2)
    private BigDecimal promoPrice;
} 