package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品選單項目實體
 * <p>
 * 對應資料表: sm_product_menu_item
 * </p>
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_product_menu_item", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"product_menu_category_id", "product_barcode", "is_deleted"})
})
public class ProductMenuItem extends BaseEntity {

    /**
     * 選單項目ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "product_menu_item_id", nullable = false, updatable = false)
    private java.util.UUID productMenuItemId;

    /**
     * 所屬選單分類ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_menu_category_id", nullable = false)
    private ProductMenuCategory productMenuCategory;

    /**
     * 商品國際條碼 (來自正航)
     */
    @Column(name = "product_barcode", nullable = false)
    private String productBarcode;

    /**
     * 商品名稱 (來自正航, 可選，備援顯示用)
     */
    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    /**
     * 顯示排序
     */
    @Column(name = "sort_order")
    private Integer sortOrder;
} 