package com.eastking.model.entity;

import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.enums.OrderActionTypeEnum;
import com.eastking.enums.OrderStatusEnum; // Assuming refund statuses use or align with OrderStatusEnum
import com.eastking.enums.ApprovalStatusEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = {"originalOrder", "requestByUser", "processedByUser", "hqApprover", "items"})
@ToString(callSuper = true, exclude = {"items"})
@Entity
@Table(name = "sm_order_refund")
public class OrderRefund extends BaseEntity {

    @Id
    @Column(name = "order_refund_id")
    private UUID orderRefundId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "original_order_id", nullable = false)
    private Order originalOrder;

    @Column(name = "refund_order_number", nullable = false, length = 100, unique = true)
    private String refundOrderNumber;

    @Column(name = "company_division_code", nullable = false)
    private Short companyDivisionCode; // Mapped to ErpCompanyDivisionEnum

    @Column(name = "refund_type_code", nullable = false)
    private Short refundTypeCode; // Mapped to OrderActionTypeEnum (CANCEL or RETURN)

    @Column(name = "refund_status_code", nullable = false)
    private Short refundStatusCode; // Mapped to OrderStatusEnum (e.g., -40, -70)

    @Column(name = "request_date", nullable = false)
    private OffsetDateTime requestDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "request_by_user_id", nullable = false)
    private UserAccount requestByUser;

    @Column(name = "reason", columnDefinition = "TEXT")
    private String reason;

    @Column(name = "expected_refund_amount", precision = 18, scale = 2)
    private BigDecimal expectedRefundAmount;

    @Column(name = "actual_refund_amount", precision = 18, scale = 2)
    private BigDecimal actualRefundAmount;

    @Column(name = "refund_tax_amount", precision = 18, scale = 2)
    private BigDecimal refundTaxAmount;

    @Column(name = "actual_refund_date")
    private OffsetDateTime actualRefundDate;

    @Column(name = "payment_adjustment_notes", columnDefinition = "TEXT")
    private String paymentAdjustmentNotes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "processed_by_user_id")
    private UserAccount processedByUser;

    @Column(name = "processed_time")
    private OffsetDateTime processedTime;
    
    @Column(name = "hq_approval_status_code")
    private Short hqApprovalStatusCode; // Mapped to ApprovalStatusEnum

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "hq_approver_id")
    private UserAccount hqApprover;

    @Column(name = "hq_approval_time")
    private OffsetDateTime hqApprovalTime;

    @Column(name = "hq_rejection_reason", columnDefinition = "TEXT")
    private String hqRejectionReason;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks; // General remarks for the refund/cancellation record

    @Column(name = "paid_amount", precision = 18, scale = 2)
    private BigDecimal paidAmount; // For recording the amount that was refunded

    @Column(name = "new_order_id")
    private UUID newOrderId; // For partial returns, link to the new order created for remaining items

    @OneToMany(mappedBy = "orderRefund", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<OrderRefundItem> items;

    @PrePersist
    protected void onCreate() {
        if (orderRefundId == null) {
            orderRefundId = UUID.randomUUID();
        }
        if (requestDate == null) {
            requestDate = OffsetDateTime.now();
        }
    }
    
    // Helper methods for enums
    @Transient
    public ErpCompanyDivisionEnum getCompanyDivisionEnum() { return ErpCompanyDivisionEnum.fromCode(this.companyDivisionCode); }
    @Transient
    public OrderActionTypeEnum getRefundTypeEnum() { return OrderActionTypeEnum.fromCode(this.refundTypeCode); }
    @Transient
    public OrderStatusEnum getRefundStatusEnum() { return OrderStatusEnum.fromCode(this.refundStatusCode); }
    @Transient
    public ApprovalStatusEnum getHqApprovalStatusEnum() { return ApprovalStatusEnum.fromCode(this.hqApprovalStatusCode); }
} 