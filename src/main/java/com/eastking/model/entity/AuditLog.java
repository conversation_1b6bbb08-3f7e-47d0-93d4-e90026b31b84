package com.eastking.model.entity;

import com.eastking.enums.AuditActionTypeEnum;
// import com.eastking.enums.AuditDataTypeEnum; // DataType is stored as String in DB

import jakarta.persistence.*;
import lombok.Data;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 操作歷程記錄實體
 * <p>
 * 對應資料表: sm_audit_log
 * </p>
 * <AUTHOR> Developer
 * @date 2025/05/17
 */
@Data
@Entity
@Table(name = "sm_audit_log")
public class AuditLog {

    /**
     * 記錄ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "audit_log_id", nullable = false, updatable = false)
    private UUID auditLogId;

    /**
     * 操作者帳號ID
     */
    @Column(name = "user_account_id") // Not using @ManyToOne to keep audit log decoupled and simple
    private UUID userAccountId;

    /**
     * 操作者員工編號
     */
    @Column(name = "employee_id", length = 50)
    private String employeeId;

    /**
     * 操作者姓名
     */
    @Column(name = "user_name", length = 100)
    private String userName;

    /**
     * 操作者所屬單位/部門名稱
     */
    @Column(name = "user_department_name", length = 100)
    private String userDepartmentName;

    /**
     * 操作時間
     */
    @Column(name = "operation_time", nullable = false)
    private OffsetDateTime operationTime;

    /**
     * 資料種類 (例如: UserAccount, ProductSetting)
     */
    @Column(name = "data_type", length = 100)
    private String dataType; // Storing as String, can be mapped from AuditDataTypeEnum.getCode()

    /**
     * 被操作實體的ID或編號
     */
    @Column(name = "entity_id_str", length = 255)
    private String entityIdStr;

    /**
     * 被操作實體的描述性名稱
     */
    @Column(name = "entity_description", columnDefinition = "TEXT")
    private String entityDescription;

    /**
     * 操作行為 (例如: CREATE, UPDATE, DELETE). Java ENUM: AuditActionTypeEnum
     */
    @Enumerated(EnumType.STRING) // Store the ENUM string representation for readability
    @Column(name = "action_type", nullable = false, length = 50)
    private AuditActionTypeEnum actionType;

    /**
     * 操作詳情 (例如: 修改前後的資料，JSON格式)
     */
    @Column(name = "details_json", columnDefinition = "TEXT")
    private String detailsJson;

    /**
     * 客戶端IP地址
     */
    @Column(name = "client_ip_address", length = 50)
    private String clientIpAddress;
    
    /**
     * (可選) 分散式追蹤ID
     */
    @Column(name = "trace_id", length = 100)
    private String traceId;

    @PrePersist
    protected void onCreate() {
        if (operationTime == null) {
            operationTime = OffsetDateTime.now();
        }
    }
} 