package com.eastking.model.entity;

import com.eastking.enums.AnnouncementTargetTypeEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * 系統公告對象關聯實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_announcement_target")
public class AnnouncementTarget extends BaseEntity {

    @Id
    @Column(name = "announcement_target_id", nullable = false, updatable = false)
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID announcementTargetId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "announcement_id", nullable = false)
    private Announcement announcement;

    @Column(name = "target_type", nullable = false)
    private Short targetType; // Mapped to AnnouncementTargetTypeEnum code

    @Column(name = "target_identifier", nullable = false, length = 255)
    private String targetIdentifier; // Department ID or Employee ID from external system

    // Transient field for enum description if needed for DTOs, not persisted
    @Transient
    public String getTargetTypeDescription() {
        AnnouncementTargetTypeEnum typeEnum = AnnouncementTargetTypeEnum.fromCode(this.targetType);
        return typeEnum != null ? typeEnum.getDescription() : null;
    }
} 