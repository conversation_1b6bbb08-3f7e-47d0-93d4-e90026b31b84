package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

/**
 * 技師角色設定實體
 * <p>
 * 對應資料表: sm_technician_role_config
 * </p>
 * <AUTHOR> Developer
 * @date 2025/05/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_technician_role_config")
public class TechnicianRoleConfig extends BaseEntity {

    /**
     * 設定ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "technician_role_config_id", nullable = false, updatable = false)
    private UUID technicianRoleConfigId;

    /**
     * 系統角色ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    /**
     * 此角色是否啟用於獎金設定，0:否, 1:是. Java ENUM: ActivationStatusEnum
     */
    @Column(name = "is_active", nullable = false)
    private Short isActive = 1; // Default to 1 (ACTIVE)
} 