package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "sm_document_clause")
public class DocumentClause extends BaseEntity {

    @Id
    @Column(name = "document_clause_id")
    private UUID documentClauseId;

    @Column(name = "company_division_code", nullable = false)
    private Short companyDivisionCode;

    @Column(name = "clause_title", nullable = false)
    private String clauseTitle;

    @Column(name = "clause_content", nullable = false, columnDefinition = "TEXT")
    private String clauseContent;

    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    @Column(name = "start_time")
    private OffsetDateTime startTime;

    @Column(name = "end_time")
    private OffsetDateTime endTime;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "sequence_order")
    private Integer sequenceOrder = 0;

    @PrePersist
    protected void onCreate() {
        if (documentClauseId == null) {
            documentClauseId = UUID.randomUUID();
        }
    }
} 