package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

/**
 * 倉庫實體
 * <p>
 * 對應資料表: sm_warehouse
 * </p>
 * <AUTHOR> Developer
 * @date 2025/05/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_warehouse")
public class Warehouse extends BaseEntity {

    /**
     * 倉庫ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "warehouse_id", nullable = false, updatable = false)
    private UUID warehouseId;

    /**
     * 倉庫代碼 (來自正航或其他外部系統)
     */
    @Column(name = "warehouse_code", nullable = false, length = 100)
    private String warehouseCode;

    /**
     * 倉庫名稱
     */
    @Column(name = "warehouse_name", nullable = false, length = 255)
    private String warehouseName;

    /**
     * 所屬區域ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_id", nullable = false)
    private RegionEntity region;

    @Column(name = "is_main", nullable = false)
    private Short isMain;

    @Column(name = "erp_company_division", nullable = false)
    private Short erpCompanyDivision;
} 