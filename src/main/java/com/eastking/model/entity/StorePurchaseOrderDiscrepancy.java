package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_store_purchase_order_discrepancy")
public class StorePurchaseOrderDiscrepancy extends BaseEntity {

    @Id
    @Column(name = "discrepancy_id")
    private UUID discrepancyId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_purchase_order_item_id", nullable = false)
    private StorePurchaseOrderItem storePurchaseOrderItem;

    @Column(name = "reported_at", nullable = false)
    private OffsetDateTime reportedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reported_by_user_id")
    private UserAccount reportedByUser;

    @Column(name = "expected_quantity", nullable = false)
    private Integer expectedQuantity;

    @Column(name = "actual_quantity", nullable = false)
    private Integer actualQuantity;

    @Column(name = "reason", columnDefinition = "TEXT")
    private String reason;

    @Column(name = "discrepancy_status", nullable = false)
    private Short discrepancyStatus; // Mapped to DiscrepancyStatusEnum

    @Column(name = "resolution_notes", columnDefinition = "TEXT")
    private String resolutionNotes;

    @Column(name = "resolved_at")
    private OffsetDateTime resolvedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "resolved_by_user_id")
    private UserAccount resolvedByUser;

    @PrePersist
    protected void onCreate() {
        if (discrepancyId == null) {
            discrepancyId = UUID.randomUUID();
        }
        if (reportedAt == null) {
            reportedAt = OffsetDateTime.now();
        }
        // discrepancyStatus defaults to 0 (PENDING_RESOLUTION) as per DB DDL
    }
} 