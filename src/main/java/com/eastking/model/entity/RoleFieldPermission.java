package com.eastking.model.entity;

import com.eastking.enums.FieldPermissionLevelEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * 角色欄位權限實體(暫時沒用到)
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_role_field_permission", uniqueConstraints = {
    // Unique constraint defined in SQL DDL as partial index for active records
    // @UniqueConstraint(columnNames = {"role_id", "system_function_id", "field_name", "is_deleted"}) 
    // For JPA, if you want to enforce uniqueness at application level for active records, 
    // it's more complex and might require custom validation or relying on DB constraint.
})
public class RoleFieldPermission extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "role_field_permission_id", nullable = false, updatable = false)
    private UUID roleFieldPermissionId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "system_function_id", nullable = false)
    private SystemFunction systemFunction;

    @Column(name = "field_name", nullable = false, length = 100)
    private String fieldName;

    @Column(name = "permission_level", nullable = false)
    private Short permissionLevel; // Mapped to FieldPermissionLevelEnum code

    @Transient
    public String getPermissionLevelDescription() {
        FieldPermissionLevelEnum levelEnum = FieldPermissionLevelEnum.fromCode(this.permissionLevel);
        return levelEnum != null ? levelEnum.getDescription() : null;
    }
} 