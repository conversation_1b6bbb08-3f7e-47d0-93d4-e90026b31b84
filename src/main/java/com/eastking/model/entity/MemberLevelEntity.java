package com.eastking.model.entity;

import com.eastking.enums.ActivationStatusEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * 會員等級資料實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_member_level")
public class MemberLevelEntity extends BaseEntity {

    /**
     * 會員等級ID，主鍵
     */
    @Id
    @Column(name = "member_level_id")
    private UUID memberLevelId;

    /**
     * 等級名稱
     */
    @Column(name = "level_name", nullable = false, length = 50)
    private String levelName;

    /**
     * 等級暱稱
     */
    @Column(name = "level_nickname", length = 50)
    private String levelNickname;

    /**
     * 排序順序
     */
    @Column(name = "sequence_order")
    private Integer sequenceOrder;

    // --- 維持條件 (Maintenance Conditions) ---
    /**
     * 維持條件-年
     */
    @Column(name = "maintenance_period_years")
    private Integer maintenancePeriodYears;

    /**
     * 維持條件-消費金額(實際金額)
     */
    @Column(name = "maintenance_amount", precision = 18, scale = 2)
    private BigDecimal maintenanceAmount;

    // --- 折扣券發放 (Discount Coupon Issuance) ---
    /**
     * 折扣券發放啟用狀態 (0:否, 1:是)
     */
    @Column(name = "discount_coupon_enabled", nullable = false)
    private Short discountCouponEnabled; // Mapped to ActivationStatusEnum

    /**
     * 折扣券-消費金額門檻（元）
     */
    @Column(name = "discount_coupon_threshold_amount", precision = 18, scale = 2)
    private BigDecimal discountCouponThresholdAmount;

    /**
     * 折扣券-折扣金額（元）
     */
    @Column(name = "discount_coupon_discount_amount", precision = 18, scale = 2)
    private BigDecimal discountCouponDiscountAmount;

    /**
     * 折扣券-使用期限（天）
     */
    @Column(name = "discount_coupon_validity_days")
    private Integer discountCouponValidityDays;

    // --- 生日禮 (Birthday Gift) ---
    /**
     * 生日禮啟用狀態 (0:否, 1:是)
     */
    @Column(name = "birthday_gift_enabled", nullable = false)
    private Short birthdayGiftEnabled; // Mapped to ActivationStatusEnum

    /**
     * 生日禮商品ID (未來可能關聯商品表)
     */
    @Column(name = "birthday_gift_item_id")
    private UUID birthdayGiftItemId;

    /**
     * 生日禮商品描述
     */
    @Column(name = "birthday_gift_item_description", length = 255)
    private String birthdayGiftItemDescription;

    // --- 額外福利 (Additional Perks/Benefits section from Figma) ---
    /**
     * 額外福利啟用狀態 (0:否, 1:是)
     */
    @Column(name = "additional_perk_enabled", nullable = false)
    private Short additionalPerkEnabled; // Mapped to ActivationStatusEnum

    /**
     * 福利-福利名稱
     */
    @Column(name = "additional_perk_name", length = 100)
    private String additionalPerkName;

    /**
     * 福利-消費金額門檻（元）
     */
    @Column(name = "additional_perk_threshold_amount", precision = 18, scale = 2)
    private BigDecimal additionalPerkThresholdAmount;

    /**
     * 福利-折扣金額（元）
     */
    @Column(name = "additional_perk_discount_amount", precision = 18, scale = 2)
    private BigDecimal additionalPerkDiscountAmount;

    @PrePersist
    protected void onCreate() {
        if (memberLevelId == null) {
            memberLevelId = UUID.randomUUID();
        }
        if (discountCouponEnabled == null) {
            discountCouponEnabled = ActivationStatusEnum.NO.getCode();
        }
        if (birthdayGiftEnabled == null) {
            birthdayGiftEnabled = ActivationStatusEnum.NO.getCode();
        }
        if (additionalPerkEnabled == null) {
            additionalPerkEnabled = ActivationStatusEnum.NO.getCode();
        }
    }

    // Enum helper methods
    public ActivationStatusEnum getDiscountCouponEnabledEnum() { return ActivationStatusEnum.fromCode(this.discountCouponEnabled); }
    public void setDiscountCouponEnabledEnum(ActivationStatusEnum status) { this.discountCouponEnabled = status.getCode(); }

    public ActivationStatusEnum getBirthdayGiftEnabledEnum() { return ActivationStatusEnum.fromCode(this.birthdayGiftEnabled); }
    public void setBirthdayGiftEnabledEnum(ActivationStatusEnum status) { this.birthdayGiftEnabled = status.getCode(); }

    public ActivationStatusEnum getAdditionalPerkEnabledEnum() { return ActivationStatusEnum.fromCode(this.additionalPerkEnabled); }
    public void setAdditionalPerkEnabledEnum(ActivationStatusEnum status) { this.additionalPerkEnabled = status.getCode(); }
} 