package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.UUID;

/**
 * 經銷商資料實體
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_distributor", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"erp_code", "is_deleted"}) // Matches DDL for active records
})
public class Distributor extends BaseEntity {

    @Id
    @Column(name = "distributor_id")
    private UUID distributorId;

    @Column(name = "distributor_name", nullable = false)
    private String distributorName;

    @Column(name = "erp_code", length = 100) // Ensure length matches DDL
    private String erpCode;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;

    @PrePersist
    protected void onCreate() {
        if (distributorId == null) {
            distributorId = UUID.randomUUID();
        }
        // isDeleted is handled by BaseEntity or should be set explicitly if not using BaseEntity default
    }
} 