package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.util.UUID;

@Data
@Entity
@Table(name = "sm_bin_list_data")
public class BinListData {

    @Id
    @GeneratedValue(generator = "UUID")
    @Column(name = "bin_list_data_id")
    private UUID binListDataId;

    @Column(name = "bin", nullable = false)
    private String bin;

    @Column(name = "brand")
    private String brand;

    @Column(name = "card_type")
    private String cardType;

    @Column(name = "category")
    private String category;

    @Column(name = "issuer")
    private String issuer;

    @Column(name = "issuer_phone")
    private String issuerPhone;

    @Column(name = "issuer_url")
    private String issuerUrl;

    @Column(name = "iso_code2")
    private String isoCode2;

    @Column(name = "iso_code3")
    private String isoCode3;

    @Column(name = "country_name")
    private String countryName;
} 