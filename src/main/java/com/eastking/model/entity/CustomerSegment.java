package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_customer_segment")
public class CustomerSegment extends BaseEntity {

    @Id
    @Column(name = "segment_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID segmentId;

    @Column(name = "segment_name", nullable = false)
    private String segmentName;

    @Column(name = "is_active", nullable = false)
    private Short isActive = 1; // Default to active

    @Column(name = "sequence_order")
    private Integer sequenceOrder = 0;
} 