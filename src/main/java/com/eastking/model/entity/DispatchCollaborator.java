package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "sm_dispatch_collaborator")
public class DispatchCollaborator extends BaseEntity {

    @Id
    @Column(name = "dispatch_collaborator_id")
    private UUID collaboratorId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dispatch_repair_id")
    private DispatchRepair dispatchRepair;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "technician_id", nullable = false)
    private UserAccount technician;

    @Column(name = "invitation_status", nullable = false)
    private Short invitationStatus;
    
    @PrePersist
    protected void onCreate() {
        if (collaboratorId == null) {
            collaboratorId = UUID.randomUUID();
        }
    }
} 