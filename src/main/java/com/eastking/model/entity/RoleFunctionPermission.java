package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

/**
 * 角色功能權限實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_role_function_permission", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"role_id", "system_function_id"})
})
public class RoleFunctionPermission extends BaseEntity {

    /**
     * 角色功能權限ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "role_function_permission_id", nullable = false, updatable = false)
    private UUID roleFunctionPermissionId;

    /**
     * 角色ID (FK)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    /**
     * 系統功能ID (FK)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "system_function_id", nullable = false)
    private SystemFunction systemFunction;

    /**
     * 新增權限，0:無, 1:有。Java ENUM: PermissionStatusEnum
     */
    @Column(name = "can_create", nullable = false)
    private Short canCreate = 0;

    /**
     * 讀取/查看權限，0:無, 1:有。Java ENUM: PermissionStatusEnum
     */
    @Column(name = "can_read", nullable = false)
    private Short canRead = 0;

    /**
     * 更新/編輯權限，0:無, 1:有。Java ENUM: PermissionStatusEnum
     */
    @Column(name = "can_update", nullable = false)
    private Short canUpdate = 0;

    /**
     * 刪除權限，0:無, 1:有。Java ENUM: PermissionStatusEnum
     */
    @Column(name = "can_delete", nullable = false)
    private Short canDelete = 0;

    /**
     * 是否可以審核 (0:否, 1:是). Java ENUM: PermissionStatusEnum
     */
    @Column(name = "can_approve", nullable = false)
    private Short canApprove = 0;

    /**
     * 是否可以改價格 (0:否, 1:是). Java ENUM: PermissionStatusEnum
     */
    @Column(name = "can_change_price", nullable = false)
    private Short canChangePrice = 0;

    /**
     * 是否可以列印 (0:否, 1:是). Java ENUM: PermissionStatusEnum
     */
    @Column(name = "can_print", nullable = false)
    private Short canPrint = 0;

    @Column(name = "can_store_approve", nullable = false)
    private Short canStoreApprove;
    
    @Column(name = "can_dispatch_approve", nullable = false)
    private Short canDispatchApprove;

    @Column(name = "create_by")
    private UUID createBy;
} 