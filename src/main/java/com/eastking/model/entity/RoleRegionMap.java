package com.eastking.model.entity;

import com.eastking.enums.RegionEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * 角色區域權限關聯實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_role_region_map", uniqueConstraints = {
    // Unique constraint defined in SQL DDL as partial index for active records
})
public class RoleRegionMap extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "role_region_map_id", nullable = false, updatable = false)
    private UUID roleRegionMapId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    @Column(name = "region_code", nullable = false, length = 10)
    private String regionCode; // Mapped to RegionEnum code

    @Transient
    public String getRegionDescription() {
        RegionEnum regionEnum = RegionEnum.fromCode(this.regionCode);
        return regionEnum != null ? regionEnum.getDescription() : null;
    }
} 