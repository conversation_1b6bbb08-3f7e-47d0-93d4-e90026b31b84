package com.eastking.model.entity;

import com.eastking.enums.ProductMenuTypeEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * 商品選單分類實體
 * <p>
 * 對應資料表: sm_product_menu_category
 * </p>
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Data
@EqualsAndHashCode(callSuper = true, exclude = {"parentCategory", "childCategories", "menuItems"}) // Exclude to prevent recursion in toString/hashCode/equals
@Entity
@Table(name = "sm_product_menu_category", uniqueConstraints = {
    // Unique constraint for category name should be per parent (nullable), menu type, and is_deleted status
    // Database handles NULLs in unique constraints differently; application logic might be needed for top-level uniqueness if parent_category_id is NULL.
    // For now, relying on DB behavior for parent_category_id in unique key.
    @UniqueConstraint(columnNames = {"parent_category_id", "category_name", "menu_type", "is_deleted"})
})
public class ProductMenuCategory extends BaseEntity {

    /**
     * 選單分類ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "product_menu_category_id", nullable = false, updatable = false)
    private java.util.UUID productMenuCategoryId;

    /**
     * 分類名稱
     */
    @Column(name = "category_name", nullable = false)
    private String categoryName;

    /**
     * 上層分類ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_category_id")
    private ProductMenuCategory parentCategory;

    /**
     * 選單類型 (例如 DISPATCH, STORE)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "menu_type", nullable = false, length = 50)
    private ProductMenuTypeEnum menuType;

    /**
     * 顯示排序
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    // Self-referencing one-to-many for child categories
    @OneToMany(mappedBy = "parentCategory", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("sortOrder ASC")
    private List<ProductMenuCategory> childCategories;

    // One-to-many relationship with ProductMenuItem
    @OneToMany(mappedBy = "productMenuCategory", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("sortOrder ASC")
    private List<ProductMenuItem> menuItems;

    @Override
    public String toString() {
        return "ProductMenuCategory{" +
               "productMenuCategoryId=" + productMenuCategoryId +
               ", categoryName='" + categoryName + '\'' +
               ", menuType=" + menuType +
               ", sortOrder=" + sortOrder +
               ", parentCategoryId=" + (parentCategory != null ? parentCategory.getProductMenuCategoryId() : null) +
               ", isDeleted=" + getIsDeleted() +
               '}';
    }
} 