package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_customer_device")
public class CustomerDevice extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "customer_owned_device_id", updatable = false, nullable = false)
    private UUID customerOwnedDeviceId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private Customer customer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_setting_id", nullable = false)
    private ProductSetting productSetting;

    @Column(name = "device_serial_number", nullable = false, unique = true)
    private String deviceSerialNumber;

    @Column(name = "installation_address", columnDefinition = "TEXT")
    private String installationAddress;

    @Column(name = "warranty_date")
    private LocalDate warrantyDate;

    @Column(name = "last_repair_date")
    private LocalDate lastRepairDate;

    @Column(name = "user_name_remark")
    private String userNameRemark;
} 