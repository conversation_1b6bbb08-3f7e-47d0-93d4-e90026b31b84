package com.eastking.model.entity;

import com.eastking.enums.SaleUnitEnum;
import com.eastking.enums.CurrencyCodeEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.enums.ActivationStatusEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 商品設定實體
 * <p>
 * 對應資料表: sm_product_setting
 * </p>
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Data
@EqualsAndHashCode(callSuper = true, exclude = {"discounts"})
@ToString(callSuper = true, exclude = {"discounts"})
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "sm_product_setting", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"product_barcode", "is_deleted"})
})
public class ProductSetting extends BaseEntity {

    /**
     * 商品設定ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "product_setting_id", nullable = false, updatable = false)
    private UUID productSettingId;

    /**
     * 商品國際條碼 (來自正航)
     */
    @Column(name = "product_barcode", nullable = false, length = 100)
    private String productBarcode;

    /**
     * 商品名稱 (來自正航, 可選，備援顯示用)
     */
    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    /**
     * 產品簡稱
     */
    @Column(name = "product_short_name", length = 255)
    private String productShortName;

    /**
     * 銷售單位 (例如: 組, 個, 台)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "sale_unit", length = 20)
    private SaleUnitEnum saleUnit;

    /**
     * 幣值代碼 (例如: TWD, USD)
     * <p>
     * 資料庫預設值: 'TWD'
     * </p>
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "currency", length = 3)
    private CurrencyCodeEnum currency;
    
    /**
     * 保固月數 (例如 12, 24, 36 個月)
     */
    @Column(name = "warranty_months")
    private Integer warrantyMonths;

    /**
     * 是否為派工商品 (0:否, 1:是)
     */
    @Column(name = "is_dispatch_product", nullable = false)
    private Short isDispatchProduct;

    /**
     * 是否啟用 (0:否, 1:是)
     */
    @Column(name = "is_active", nullable = false)
    private Short isActive;

    /**
     * 東方不敗 - 表定價格
     */
    @Column(name = "list_price", precision = 18, scale = 2)
    private BigDecimal listPrice;

    /**
     * 東方不敗 - 銷售價格
     */
    @Column(name = "sale_price", precision = 18, scale = 2)
    private BigDecimal salePrice; //初始化時會等於 listPrice

    /**
     * 東方不敗 - 成本價格
     */
    @Column(name = "cost_price", precision = 18, scale = 2)
    private BigDecimal costPrice; //初始化時會等於 listPrice

    /**
     * 東方不敗 - 有效銷售起日
     */
    @Column(name = "sale_effective_start_date")
    private LocalDate saleEffectiveStartDate;

    /**
     * 東方不敗 - 有效銷售終日
     */
    @Column(name = "sale_effective_end_date")
    private LocalDate saleEffectiveEndDate;

    // --- ERP Fields (Part A) ---
    /**
     * ERP公司別 (0: 東方不敗, 1: 雀友, 9: 全部). Java ENUM: ErpCompanyDivisionEnum (描述: ERP公司別)
     */
    @Column(name = "erp_company_division")
    private Short erpCompanyDivision;

    /**
     * ERP商品編號
     */
    @Column(name = "erp_product_code", length = 100)
    private String erpProductCode;

    /**
     * ERP商品類別
     */
    @Column(name = "erp_product_category", length = 100)
    private String erpProductCategory;

    /**
     * ERP東方不敗價格
     */
    @Column(name = "erp_eastking_price", precision = 18, scale = 2)
    private BigDecimal erpEastkingPrice;

    /**
     * ERP雀友價格
     */
    @Column(name = "erp_queyou_price", precision = 18, scale = 2)
    private BigDecimal erpQueyouPrice;
    
    // --- QueYou Specific Price Fields (Part B) ---
    /**
     * 雀友 - 表定價格
     */
    @Column(name = "queyou_list_price", precision = 18, scale = 2)
    private BigDecimal queyouListPrice;

    /**
     * 雀友 - 銷售價格
     */
    @Column(name = "queyou_sale_price", precision = 18, scale = 2)
    private BigDecimal queyouSalePrice;

    /**
     * 雀友 - 成本價格
     */
    @Column(name = "queyou_cost_price", precision = 18, scale = 2)
    private BigDecimal queyouCostPrice;

    /**
     * 雀友 - 有效銷售起日
     */
    @Column(name = "queyou_sale_effective_start_date")
    private OffsetDateTime queyouSaleEffectiveStartDate;

    /**
     * 雀友 - 有效銷售終日
     */
    @Column(name = "queyou_sale_effective_end_date")
    private OffsetDateTime queyouSaleEffectiveEndDate;

    // One-to-many relationship with ProductSettingDiscount
    @OneToMany(mappedBy = "productSetting", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<ProductSettingDiscount> discounts;

    @Column(name = "is_main", nullable = false)
    private Short isMain;

    @PrePersist
    protected void onCreate() {
        if (isDispatchProduct == null) {
            isDispatchProduct = ActivationStatusEnum.NO.getCode();
        }
        if (isActive == null) {
            isActive = ActivationStatusEnum.YES.getCode();
        }
    }

    @Transient
    public ErpCompanyDivisionEnum getErpCompanyDivisionEnum() {
        return ErpCompanyDivisionEnum.fromCode(this.erpCompanyDivision);
    }
    
    @Transient
    public BigDecimal determinePriceForCompany(Short companyCode) {
        ErpCompanyDivisionEnum company = ErpCompanyDivisionEnum.fromCode(companyCode);
        if (company == null) {
            return this.listPrice; // Default or throw error
        }
        switch (company) {
            case EASTKING:
                return this.listPrice; // Assuming listPrice is for EastKing
            case QUEYOU:
                return this.queyouListPrice;
            case ALL:
                 // If product is ALL_COMPANY, and order is for a specific company,
                 // still return that company's specific price if available, or a primary default.
                 // This logic might need refinement based on business rules for ALL_COMPANY products.
                if (ErpCompanyDivisionEnum.EASTKING.getCode().equals(companyCode)) {
                    return this.listPrice;
                } else if (ErpCompanyDivisionEnum.QUEYOU.getCode().equals(companyCode)) {
                    return this.queyouListPrice;
                } else {
                    return this.listPrice; // Default if ALL_COMPANY product ordered under context ALL_COMPANY (should not happen)
                }
            default:
                return this.listPrice; // Fallback or throw error
        }
    }
} 