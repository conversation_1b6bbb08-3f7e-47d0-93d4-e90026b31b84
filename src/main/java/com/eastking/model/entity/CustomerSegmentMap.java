package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.io.Serializable;
import java.util.UUID;

@Data
@Entity
@Table(name = "sm_customer_segment_map")
@IdClass(CustomerSegmentMap.CustomerSegmentMapId.class)
public class CustomerSegmentMap {

    @Id
    @Column(name = "customer_id")
    private UUID customerId;

    @Id
    @Column(name = "segment_id")
    private UUID segmentId;

    // Inner class for composite key
    public static class CustomerSegmentMapId implements Serializable {
        private UUID customerId;
        private UUID segmentId;
        // equals and hashCode methods are required
    }
} 