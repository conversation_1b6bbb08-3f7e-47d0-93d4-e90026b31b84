package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;
import java.util.HashSet;
import java.util.Set;

/**
 * 系統功能項目實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@EqualsAndHashCode(callSuper = true, exclude = {"parentFunction", "children"})
@Entity
@Table(name = "sm_system_function")
public class SystemFunction extends BaseEntity {

    /**
     * 系統功能ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "system_function_id", nullable = false, updatable = false)
    private UUID systemFunctionId;

    /**
     * 功能代碼 (六位)
     */
    @Column(name = "function_code", nullable = false, unique = true, length = 6)
    private String functionCode;

    /**
     * 功能名稱
     */
    @Column(name = "function_name", nullable = false, length = 100)
    private String functionName;

    /**
     * 上層功能代碼 (用於層級結構)
     */
    @Column(name = "parent_function_code", length = 6)
    private String parentFunctionCode;

    /**
     * 功能描述
     */
    @Column(name = "function_description", columnDefinition = "TEXT")
    private String functionDescription;

    /**
     * 功能類型，例如 0:選單群組, 1:操作功能。Java ENUM: FunctionTypeEnum (描述: 功能類型)
     */
    @Column(name = "function_type", nullable = false)
    private Short functionType = 1;

    /**
     * 排序順序
     */
    @Column(name = "sequence_order")
    private Integer sequenceOrder = 0;

    /**
     * 前端路由路徑 (例如: user_management.html)
     */
    @Column(name = "frontend_path", length = 255)
    private String frontendPath;

    /**
     * 選單圖示CSS類別 (例如: bi bi-gear)
     */
    @Column(name = "icon_class", length = 100)
    private String iconClass;

    /**
     * 是否顯示於選單，0:不顯示, 1:顯示。Java ENUM: MenuVisibilityEnum
     */
    @Column(name = "is_show", nullable = false)
    private Short isShow = 1;

    // --- 權限可配置性開關 ---
    @Column(name = "can_create", nullable = false)
    private Short canCreate;
    @Column(name = "can_read", nullable = false)
    private Short canRead;
    @Column(name = "can_update", nullable = false)
    private Short canUpdate;
    @Column(name = "can_delete", nullable = false)
    private Short canDelete;
    @Column(name = "can_approve", nullable = false)
    private Short canApprove;
    @Column(name = "can_change_price", nullable = false)
    private Short canChangePrice;
    @Column(name = "can_print", nullable = false)
    private Short canPrint;
    @Column(name = "can_store_approve", nullable = false)
    private Short canStoreApprove;
    @Column(name = "can_dispatch_approve", nullable = false)
    private Short canDispatchApprove;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_function_code", referencedColumnName = "function_code", insertable = false, updatable = false)
    private SystemFunction parentFunction;

    @OneToMany(mappedBy = "parentFunction")
    @OrderBy("sequenceOrder ASC")
    private Set<SystemFunction> children = new HashSet<>();
} 