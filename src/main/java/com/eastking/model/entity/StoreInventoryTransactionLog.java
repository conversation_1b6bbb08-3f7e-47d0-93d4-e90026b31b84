package com.eastking.model.entity;

import com.eastking.enums.InventoryTransactionTypeEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "sm_store_inventory_transaction_log")
public class StoreInventoryTransactionLog {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "inventory_transaction_log_id", nullable = false, updatable = false)
    private UUID inventoryTransactionLogId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id") // Nullable for HQ/Central Warehouse transactions
    private StoreEntity store;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "warehouse_id") // Used for HQ/Central Warehouse
    private Warehouse warehouse;

    @Column(name = "product_barcode", nullable = false, length = 255)
    private String productBarcode;

    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false, length = 50)
    private InventoryTransactionTypeEnum transactionType;

    @Column(name = "quantity_change", nullable = false)
    private Integer quantityChange;

    @Column(name = "quantity_after_transaction", nullable = false)
    private Integer quantityAfterTransaction;

    @Column(name = "transaction_date", nullable = false)
    private OffsetDateTime transactionDate;

    @Column(name = "reference_document_type", length = 50)
    private String referenceDocumentType;

    @Column(name = "reference_document_id")
    private UUID referenceDocumentId;

    @Column(name = "reference_document_number", length = 100)
    private String referenceDocumentNumber;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    @Column(name = "create_by")
    private UUID createBy;

    @Column(name = "create_time", nullable = false, updatable = false)
    private OffsetDateTime createTime;

    @PrePersist
    protected void onCreate() {
        if (createTime == null) {
            createTime = OffsetDateTime.now();
        }
        if (transactionDate == null) {
            transactionDate = OffsetDateTime.now();
        }
    }
} 