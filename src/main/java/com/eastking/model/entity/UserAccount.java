package com.eastking.model.entity;

import com.eastking.enums.UserAccountTypeEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;
import java.util.List;

/**
 * 使用者帳號實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_user_account")
public class UserAccount extends BaseEntity {

    /**
     * 使用者帳號ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "user_account_id", nullable = false, updatable = false)
    private UUID userAccountId;

    /**
     * 員工編號(同ERP)，用於登入
     */
    @Column(name = "employee_id", nullable = false, unique = true, length = 50)
    private String employeeId;

    /**
     * 使用者密碼 (應儲存雜湊後的密碼)
     */
    @Column(name = "user_password", nullable = false, length = 255)
    private String userPassword;

    /**
     * 使用者姓名
     */
    @Column(name = "user_name", nullable = false, length = 100)
    private String userName;

    /**
     * 帳號狀態，0:停用, 1:啟用。Java ENUM: UserAccountStatusEnum (描述: 帳號狀態)
     */
    @Column(name = "is_active", nullable = false)
    private Short isActive = 1; // Default to 1 (ACTIVE)

    /**
     * 帳號類型，0:一般員工, 1:技師, 2:主管. Java ENUM: UserAccountTypeEnum (描述: 帳號類型)
     */
    @Column(name = "account_type", nullable = false)
    // @Enumerated(EnumType.ORDINAL) // If codes match ordinal 0, 1, 2... Can also use a converter
    private Short accountType = UserAccountTypeEnum.GENERAL_STAFF.getCode(); // Default to GENERAL_STAFF

    /**
     * 使用者歸屬ERP公司別 (0: 東方不敗, 1: 雀友). Java ENUM: ErpCompanyDivisionEnum (描述: ERP公司別)
     */
    @Column(name = "erp_company_division")
    private Short erpCompanyDivision;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dept_id")
    private Dept dept;

    @OneToMany(mappedBy = "userAccount", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<UserRoleMap> userRoleMaps;
} 