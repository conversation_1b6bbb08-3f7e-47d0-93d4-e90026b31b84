package com.eastking.model.entity;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Type;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@Entity
@Table(name = "sm_dispatch_repair_history")
public class DispatchRepairHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "history_id")
    private UUID historyId;

    @Column(name = "dispatch_repair_id", nullable = false)
    private UUID dispatchRepairId;

    @Column(name = "dispatch_repair_number")
    private String dispatchRepairNumber;
    
    @Column(name = "previous_status_code")
    private Short previousStatusCode;
    
    @Column(name = "new_status_code")
    private Short newStatusCode;

    @Column(name = "change_by_user_id", nullable = false)
    private UUID changeByUserId;
    
    @Column(name = "change_time", nullable = false)
    private OffsetDateTime changeTime;

    @Column(name = "technician_id")
    private UUID technicianId;

    @Type(JsonType.class)
    @Column(name = "request_payload", columnDefinition = "jsonb")
    private String requestPayload; // Store JSON as String

    @Column(name = "change_reason", columnDefinition = "TEXT")
    private String changeReason;
} 