package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
// import lombok.experimental.SuperBuilder; // Removed due to Linter issues with BaseEntity

import java.util.UUID;

/**
 * 地區資料實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
// @SuperBuilder // Removed
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_region")
public class RegionEntity extends BaseEntity {

    /**
     * 地區ID，主鍵
     */
    @Id
    @Column(name = "region_id")
    private UUID regionId;

    /**
     * 地區名稱 (例如: 北區, 中區, 南區)
     */
    @Column(name = "region_name", nullable = false, length = 50)
    private String regionName;

    /**
     * 排序順序
     */
    @Column(name = "sequence_order")
    private Integer sequenceOrder;

    @Column(name = "erp_code", length = 100)
    private String erpCode;

    @PrePersist
    protected void onCreate() {
        if (regionId == null) {
            regionId = UUID.randomUUID();
        }
        // isDeleted is handled by BaseEntity default or by service layer logic
    }
} 