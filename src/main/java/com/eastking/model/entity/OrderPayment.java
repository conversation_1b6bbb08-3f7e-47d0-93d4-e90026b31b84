package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_order_payment")
public class OrderPayment extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "order_payment_id", updatable = false, nullable = false)
    private UUID orderPaymentId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private Order order;

    @Column(name = "payment_method_code", nullable = false)
    private String paymentMethodCode;

    @Column(nullable = false)
    private BigDecimal amount;

    @Column(name = "payment_time", nullable = false)
    private OffsetDateTime paymentTime;

    @Column(name = "card_number")
    private String cardNumber;

    @Column(name = "card_brand")
    private String cardBrand;

    @Column(name = "card_issuer")
    private String cardIssuer;

    @Column(name = "card_installments")
    private Integer cardInstallments;

    @Column(name = "bank_name")
    private String bankName;

    @Column(name = "bank_account_last_five")
    private String bankAccountLastFive;

    @Column(name = "remitter_account_last_five")
    private String remitterAccountLastFive;

    @Column(name = "collector_name")
    private String collectorName;
    
    @Column
    private String remarks;
} 