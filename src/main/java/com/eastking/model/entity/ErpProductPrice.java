package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity
@Table(name = "sm_erp_product_price")
public class ErpProductPrice extends BaseEntity {

    @Id
    @Column(name = "erp_product_price_id")
    private UUID erpProductPriceId;

    @Column(name = "product_barcode", nullable = false, length = 100)
    private String productBarcode;

    @Column(name = "price_description", nullable = false, length = 100)
    private String priceDescription;

    @Column(name = "unit_price", nullable = false, precision = 18, scale = 2)
    private BigDecimal unitPrice;

    @Column(name = "currency_code", nullable = false, length = 10)
    private String currencyCode = "NTD";

    @Column(name = "effective_from")
    private OffsetDateTime effectiveFrom;

    @Column(name = "effective_to")
    private OffsetDateTime effectiveTo;

    @Column(name = "customer_category_code", length = 50)
    private String customerCategoryCode;

    @Column(columnDefinition = "TEXT")
    private String remarks;

    @PrePersist
    protected void onCreate() {
        if (erpProductPriceId == null) {
            erpProductPriceId = UUID.randomUUID();
        }
    }
} 