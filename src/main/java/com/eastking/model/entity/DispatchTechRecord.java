package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = {"dispatchRepair"})
@Entity
@Table(name = "sm_dispatch_tech_record")
public class DispatchTechRecord extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "record_id")
    private UUID recordId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dispatch_repair_id", nullable = false, insertable = false, updatable = false)
    private DispatchRepair dispatchRepair;

    @Column(name = "dispatch_repair_id", nullable = false)
    private UUID dispatchRepairId;

    @Column(name = "dispatch_repair_item_id")
    private UUID dispatchRepairItemId;

    @Column(name = "status_code", nullable = false)
    private Short statusCode;
    
    @Column(name = "technician_id")
    private UUID technicianId;

    @Column(name = "record_type", nullable = false)
    private Short recordType;

    @Column(name = "record1", columnDefinition = "TEXT")
    private String record1;

    @Column(name = "record2", columnDefinition = "TEXT")
    private String record2;
    
    @Column(name = "record3", columnDefinition = "TEXT")
    private String record3;
    
    @Column(name = "record4", columnDefinition = "TEXT")
    private String record4;

    @Column(name = "record5", columnDefinition = "TEXT")
    private String record5;
} 