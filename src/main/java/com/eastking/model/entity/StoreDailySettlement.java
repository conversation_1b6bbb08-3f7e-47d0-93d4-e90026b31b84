package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_store_daily_settlement")
public class StoreDailySettlement extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "store_daily_settlement_id")
    private UUID storeDailySettlementId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id", nullable = false)
    private StoreEntity store;

    @Column(name = "settlement_date", nullable = false)
    private LocalDate settlementDate;

    @Column(name = "settlement_status_code", nullable = false)
    private Short settlementStatusCode;

    @Column(name = "total_sales_amount", nullable = false)
    private BigDecimal totalSalesAmount;

    @Column(name = "total_returns_amount", nullable = false)
    private BigDecimal totalReturnsAmount;

    @Column(name = "total_revenue_amount", nullable = false)
    private BigDecimal totalRevenueAmount;

    @Column(name = "total_expenditure_amount", nullable = false)
    private BigDecimal totalExpenditureAmount;

    @Column(name = "cash_income_amount", nullable = false)
    private BigDecimal cashIncomeAmount;

    @Column(name = "credit_card_income_amount", nullable = false)
    private BigDecimal creditCardIncomeAmount;

    @Column(name = "remittance_income_amount", nullable = false)
    private BigDecimal remittanceIncomeAmount;

    @Column(name = "accounts_receivable_amount", nullable = false)
    private BigDecimal accountsReceivableAmount;

    @Column(name = "receivable_repayment_amount", nullable = false)
    private BigDecimal receivableRepaymentAmount;

    @Column(name = "actual_cash_income_amount")
    private BigDecimal actualCashIncomeAmount;

    @Column(name = "cash_discrepancy_amount")
    private BigDecimal cashDiscrepancyAmount;

    @Column(name = "total_repayment_amount", nullable = false)
    private BigDecimal totalRepaymentAmount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "settlement_by")
    private UserAccount settlementBy;

    @Column(name = "remarks", length = 500)
    private String remarks;
} 