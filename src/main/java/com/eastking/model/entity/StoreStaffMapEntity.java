package com.eastking.model.entity;

import com.eastking.enums.StoreStaffTypeEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.UUID;

/**
 * 門市人員對應資料實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_store_staff_map", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"store_id", "user_account_id", "staff_type", "is_deleted"})
})
public class StoreStaffMapEntity extends BaseEntity {

    /**
     * 對應ID，主鍵
     */
    @Id
    @Column(name = "store_staff_map_id")
    private UUID storeStaffMapId;

    /**
     * 門市ID (FK to sm_store)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id", nullable = false)
    private StoreEntity store;

    /**
     * 使用者帳號ID (FK to sm_user_account)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_account_id", nullable = false)
    private UserAccount userAccount;

    /**
     * 人員類型，例如: 0:正式員工, 1:代班人員. Java ENUM: StoreStaffTypeEnum
     */
    @Column(name = "staff_type", nullable = false)
    private Short staffType; // Mapped to StoreStaffTypeEnum in DTO/Service

    @PrePersist
    protected void onCreate() {
        if (storeStaffMapId == null) {
            storeStaffMapId = UUID.randomUUID();
        }
        if (staffType == null) {
            staffType = StoreStaffTypeEnum.REGULAR_STAFF.getCode(); // Default type
        }
    }

    public StoreStaffTypeEnum getStaffTypeEnum() {
        return StoreStaffTypeEnum.fromCode(this.staffType);
    }

    public void setStaffTypeEnum(StoreStaffTypeEnum typeEnum) {
        this.staffType = typeEnum.getCode();
    }
} 