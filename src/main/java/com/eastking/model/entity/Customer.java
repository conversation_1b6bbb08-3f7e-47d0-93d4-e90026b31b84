package com.eastking.model.entity;

import com.eastking.enums.ErpCompanyDivisionEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.UUID;
import java.time.LocalDate;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sm_customer")
public class Customer extends BaseEntity {

    @Id
    @Column(name = "customer_id")
    private UUID customerId;

    @Column(name = "customer_name", nullable = false, length = 100)
    private String customerName;

    @Column(name = "phone_number", nullable = false, length = 50)
    private String phoneNumber;

    @Column(name = "email", length = 255)
    private String email;

    @Column(name = "address_region_code", length = 10)
    private String addressRegionCode;

    @Column(name = "address_postal_code", length = 10)
    private String addressPostalCode;

    @Column(name = "full_address", columnDefinition = "TEXT")
    private String fullAddress;

    @Column(name = "address_city_name")
    private String addressCityName;
    @Column(name = "address_district_name")
    private String addressDistrictName;
    @Column(name = "address_street_name")
    private String addressStreetName;
    @Column(name = "address_lane")
    private String addressLane;
    @Column(name = "address_alley")
    private String addressAlley;
    @Column(name = "address_number")
    private String addressNumber;
    @Column(name = "address_floor")
    private String addressFloor;
    @Column(name = "address_unit")
    private String addressUnit;

    @Column(name = "member_level_id")
    private UUID memberLevelId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "member_level_id", insertable=false, updatable=false)
    private MemberLevelEntity memberLevel;

    @Column(name = "company_division_code")
    private Short companyDivisionCode; // Mapped to ErpCompanyDivisionEnum

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_segment_id")
    private CustomerSegment customerSegment;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;

    @Column(name = "birth_date")
    private LocalDate birthDate;

    @Column(name = "gender_code")
    private Short genderCode;

    @Column(name = "marital_status_code")
    private Short maritalStatusCode;

    @Column(name = "home_phone")
    private String homePhone;

    @Column(name = "occupation_code")
    private String occupationCode;

    @Column(name = "job_title")
    private String jobTitle;

    @Column
    private String interests;

    @Column(name = "accepts_marketing_info")
    private Boolean acceptsMarketingInfo;

    @ManyToMany
    @JoinTable(
        name = "sm_customer_segment_map",
        joinColumns = @JoinColumn(name = "customer_id"),
        inverseJoinColumns = @JoinColumn(name = "segment_id")
    )
    private Set<CustomerSegment> segments;

    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<CustomerDevice> devices;

    @PrePersist
    protected void onCreate() {
        if (customerId == null) {
            customerId = UUID.randomUUID();
        }
    }

    // Helper for enum description if needed in DTO mapping
    @Transient
    public String getCompanyDivisionDescription() {
        ErpCompanyDivisionEnum enumVal = ErpCompanyDivisionEnum.fromCode(this.companyDivisionCode);
        return enumVal != null ? enumVal.getDescription() : null;
    }
} 