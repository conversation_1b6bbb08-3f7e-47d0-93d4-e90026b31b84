package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.util.UUID;

/**
 * 技師獎金設定實體
 * <p>
 * 對應資料表: sm_technician_bonus_setting
 * </p>
 * <AUTHOR> Developer
 * @date 2025/05/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_technician_bonus_setting")
public class TechnicianBonusSetting extends BaseEntity {

    /**
     * 獎金設定ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "technician_bonus_setting_id", nullable = false, updatable = false)
    private UUID technicianBonusSettingId;

    /**
     * 獎金項目ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bonus_item_id", nullable = false)
    private BonusItem bonusItem;

    /**
     * 技師角色ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    /**
     * 獎金金額
     */
    @Column(name = "bonus_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal bonusAmount = BigDecimal.ZERO;
} 