package com.eastking.model.entity;

import com.eastking.enums.StoreJournalTransactionTypeEnum;
import com.eastking.enums.PaymentMethodEnum;
import com.eastking.enums.StoreJournalTypeEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "sm_store_daily_journal")
public class StoreDailyJournal {

    @Id
    @Column(name = "journal_entry_id")
    private UUID journalEntryId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id", nullable = false)
    private StoreEntity store;

    @Column(name = "entry_datetime", nullable = false)
    private OffsetDateTime entryDatetime;

    @Column(name = "transaction_type_code", nullable = false)
    private Short transactionTypeCode; // Mapped to StoreJournalTransactionTypeEnum

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "related_order_id")
    private Order relatedOrder;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "related_refund_id")
    private OrderRefund relatedRefund;
    
    @Column(name = "related_document_number", length = 100)
    private String relatedDocumentNumber;

    @Column(name = "payment_method_code")
    private Short paymentMethodCode; // Mapped to PaymentMethodEnum

    @Column(name = "store_journal_type")
    private Short storeJournalType; // Mapped to StoreJournalTypeEnum

    @Column(name = "amount_in", precision = 18, scale = 2)
    private BigDecimal amountIn;

    @Column(name = "amount_out", precision = 18, scale = 2)
    private BigDecimal amountOut;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_account_id", nullable = false) // The user who performed the action leading to this journal entry
    private UserAccount userAccount;
    
    @Column(name = "create_by", updatable = false) // This entity does not extend BaseEntity, manage audit fields manually or via specific listener if needed
    private UUID createBy; 

    @Column(name = "create_time", nullable = false, updatable = false)
    private OffsetDateTime createTime;
    
    // Typically journal entries are immutable, so updateBy/updateTime might not be strictly necessary.
    // If updates are allowed (e.g. for corrections by admin), then add them.
    // For now, omitting update_by and update_time based on typical journal immutability.
    // Also omitting is_deleted as journal entries are usually permanent.
    // If soft delete is required by policy, add is_deleted and handle it.

    @PrePersist
    protected void onCreate() {
        if (journalEntryId == null) {
            journalEntryId = UUID.randomUUID();
        }
        OffsetDateTime now = OffsetDateTime.now();
        if (entryDatetime == null) {
            entryDatetime = now;
        }
        if (createTime == null) { 
            createTime = now;
        }
        // createBy should be set by the service layer based on the current authenticated user when creating the journal entry.
    }

    // Helper methods for enums
    @Transient
    public StoreJournalTransactionTypeEnum getTransactionTypeEnum() { return StoreJournalTransactionTypeEnum.fromCode(this.transactionTypeCode); }
    @Transient
    public PaymentMethodEnum getPaymentMethodEnum() { return PaymentMethodEnum.fromCode(this.paymentMethodCode); }
    @Transient
    public StoreJournalTypeEnum getStoreJournalTypeEnum() { return StoreJournalTypeEnum.fromCode(this.storeJournalType); }
} 