package com.eastking.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 基礎實體類，包含通用審計字段
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class) // For @CreatedDate, @LastModifiedDate, @CreatedBy, @LastModifiedBy
public abstract class BaseEntity {

    /**
     * 建立人員ID
     */
    @CreatedBy
    @Column(name = "create_by", updatable = false)
    private UUID createBy;

    /**
     * 建立時間
     */
    @CreatedDate
    @Column(name = "create_time", nullable = false, updatable = false)
    private OffsetDateTime createTime;

    /**
     * 更新人員ID
     */
    @LastModifiedBy
    @Column(name = "update_by")
    private UUID updateBy;

    /**
     * 更新時間
     */
    @LastModifiedDate
    @Column(name = "update_time", nullable = false)
    private OffsetDateTime updateTime;

    /**
     * 是否刪除，0:未刪除, 1:已刪除。Java ENUM: DeleteStatusEnum (描述: 刪除狀態)
     */
    @Column(name = "is_deleted", nullable = false)
    private Short isDeleted = 0; // Default to 0 (NOT_DELETED)
} 