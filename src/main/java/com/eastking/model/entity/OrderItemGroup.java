package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.Type;

import com.eastking.enums.OrderItemTypeEnum;
import java.math.BigDecimal;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true, exclude = {"orderItem"})
@ToString(callSuper = true, exclude = {"orderItem"})
@Entity
@Table(name = "sm_order_item_group")
public class OrderItemGroup extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "order_item_group_id")
    private UUID orderItemGroupId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_item_id", nullable = false)
    private OrderItem orderItem;

    @Column(name = "product_barcode", nullable = false)
    private String productBarcode;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "warehouse_id")
    private UUID warehouseId;

    @Column(name = "requires_dispatch", nullable = false)
    private Short requiresDispatch;

    @Column(name = "awaiting_materials", length = 100)
    private String awaitingMaterials;

    @Column(name = "item_type_code", nullable = false)
    private Short itemTypeCode;

    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @Column(name = "unit_price", nullable = false, precision = 18, scale = 2)
    private BigDecimal unitPrice;

    @Column(name = "list_price", nullable = false, precision = 18, scale = 2)
    private BigDecimal listPrice;

    @Column(name = "subtotal_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal subtotalAmount;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;

    /**
     * 是否待料，0:否, 1:是
     */
    @Column(name = "is_await", nullable = false)
    private Short isAwait = 0;

    @Transient
    public OrderItemTypeEnum getItemType() {
        return OrderItemTypeEnum.fromCode(this.itemTypeCode);
    }

    public void setItemType(OrderItemTypeEnum itemType) {
        this.itemTypeCode = itemType.getCode();
    }
} 