package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_warehouse_inventory")
@Getter
@Setter
public class WarehouseInventory extends BaseEntity {

    @Id
    @GeneratedValue(generator = "UUID")
    @Column(name = "warehouse_inventory_id", updatable = false, nullable = false)
    private UUID warehouseInventoryId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "warehouse_id", nullable = false)
    private Warehouse warehouse;

    @Column(name = "product_barcode", nullable = false)
    private String productBarcode;

    @Column(name = "product_name", nullable = false)
    private String productName;

    @Column(name = "quantity_on_hand", nullable = false)
    private Integer quantityOnHand = 0;

    @Column(name = "last_stock_update_time", nullable = false)
    private OffsetDateTime lastStockUpdateTime;

} 