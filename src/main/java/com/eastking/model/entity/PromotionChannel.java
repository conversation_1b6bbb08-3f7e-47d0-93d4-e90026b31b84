package com.eastking.model.entity;

import com.eastking.enums.PromotionChannelTypeEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_promotion_channel")
public class PromotionChannel extends BaseEntity {

    @Id
    @Column(name = "promo_channel_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID promoChannelId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "promotion_id", nullable = false)
    private Promotion promotion;

    @Enumerated(EnumType.STRING)
    @Column(name = "channel_type", nullable = false, length = 50)
    private PromotionChannelTypeEnum channelType;

    @Column(name = "channel_target_id")
    private String channelTargetId;
} 