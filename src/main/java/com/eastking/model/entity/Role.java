package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

/**
 * 角色實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_role")
public class Role extends BaseEntity {

    /**
     * 角色ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "role_id", nullable = false, updatable = false)
    private UUID roleId;

    /**
     * 角色代碼
     */
    @Column(name = "role_code", nullable = false, unique = true, length = 50)
    private String roleCode;

    /**
     * 角色名稱
     */
    @Column(name = "role_name", nullable = false, length = 100)
    private String roleName;

    /**
     * 角色描述
     */
    @Column(name = "role_description", columnDefinition = "TEXT")
    private String roleDescription;
} 