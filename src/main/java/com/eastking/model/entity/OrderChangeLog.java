package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sm_order_change_log")
public class OrderChangeLog extends BaseEntity {

    @Id
    @Column(name = "log_id")
    private UUID logId;

    @Column(name = "order_id", nullable = false)
    private UUID orderId;

    @Column(name = "refund_id")
    private UUID refundId;

    @Column(name = "previous_status")
    private Short previousStatus;

    @Column(name = "new_status", nullable = false)
    private Short newStatus;

    @Column(name = "changed_by_user_id", nullable = false)
    private UUID changedByUserId;

    @Column(name = "change_time", nullable = false)
    private OffsetDateTime changeTime;

    @Column(name = "change_reason")
    private String changeReason;

    @Column(name = "request_json", columnDefinition = "TEXT")
    private String requestJson;

    @PrePersist
    protected void onCreate() {
        if (logId == null) {
            logId = UUID.randomUUID();
        }
        if (changeTime == null) {
            changeTime = OffsetDateTime.now();
        }
    }
} 