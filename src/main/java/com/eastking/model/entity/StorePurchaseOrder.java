package com.eastking.model.entity;

import com.eastking.enums.PurchaseOrderOriginEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
// import com.eastking.enums.StorePurchaseOrderStatusEnum; // Will be used for business logic, not directly in entity typically

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 門市進貨單主表實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_store_purchase_order")
public class StorePurchaseOrder extends BaseEntity {

    /**
     * 進貨單ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "store_purchase_order_id", nullable = false, updatable = false)
    private UUID storePurchaseOrderId;

    /**
     * 收貨門市ID (FK sm_store)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id", nullable = false)
    private StoreEntity store;

    /**
     * 進貨單號 (應唯一)
     */
    @Column(name = "purchase_order_number", nullable = false, length = 100, unique = true)
    private String purchaseOrderNumber;

    /**
     * 來源批發訂單號 (可選)
     */
    @Column(name = "original_wholesale_order_id", length = 100)
    private String originalWholesaleOrderId;

    /**
     * 出貨時間 (從總倉或供應商)
     */
    @Column(name = "shipment_time")
    private OffsetDateTime shipmentTime;

    /**
     * 建立人員工號 (來源單據的建立者)
     */
    @Column(name = "created_by_employee_id", length = 50)
    private String createdByEmployeeId;

    /**
     * 建立人員姓名
     */
    @Column(name = "created_by_employee_name", length = 100)
    private String createdByEmployeeName;

    /**
     * 進貨單狀態: 0:配送中, 1:部分到貨, 2:已到貨(待確認), 3:数量异常, 4:已完成.
     * Java ENUM: StorePurchaseOrderStatusEnum
     */
    @Column(name = "purchase_order_status", nullable = false)
    private Short purchaseOrderStatus;

    /**
     * 訂單合計金額 (未稅)
     */
    @Column(name = "total_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal totalAmount;

    /**
     * 稅金
     */
    @Column(name = "tax_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal taxAmount;

    /**
     * 訂單總計金額 (含稅)
     */
    @Column(name = "grand_total_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal grandTotalAmount;

    /**
     * 訂單備註
     */
    @Column(name = "order_notes", columnDefinition = "TEXT")
    private String orderNotes;

    /**
     * 出貨方式 (例如: 大榮貨運)
     */
    @Column(name = "shipping_method", length = 100)
    private String shippingMethod;

    /**
     * 貨運單號
     */
    @Column(name = "tracking_number", length = 100)
    private String trackingNumber;

    /**
     * 門市確認收貨時間
     */
    @Column(name = "confirmed_at")
    private OffsetDateTime confirmedAt;

    /**
     * 門市確認收貨人員ID (FK sm_user_account)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "confirmed_by_user_id")
    private UserAccount confirmedByUser;

    @Column(name = "purchase_order_origin", nullable = false)
    private Short purchaseOrderOrigin = PurchaseOrderOriginEnum.EXTERNAL_SUPPLIER.getCode(); // Default

    @Column(name = "related_transfer_order_id")
    private UUID relatedTransferOrderId;

    @OneToMany(mappedBy = "storePurchaseOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<StorePurchaseOrderItem> items;
} 