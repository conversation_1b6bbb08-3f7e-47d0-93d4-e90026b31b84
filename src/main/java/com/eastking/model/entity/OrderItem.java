package com.eastking.model.entity;

import com.eastking.enums.OrderItemTypeEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_order_item")
public class OrderItem extends BaseEntity {

    @Id
    @Column(name = "order_item_id")
    private UUID orderItemId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private Order order;

    @Column(name = "product_barcode", nullable = false, length = 255)
    private String productBarcode;

    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @Column(name = "unit_price", nullable = false, precision = 18, scale = 2)
    private BigDecimal unitPrice;

    @Column(name = "list_price", precision = 18, scale = 2)
    private BigDecimal listPrice;

    @Column(name = "discount_rate", precision = 5, scale = 2)
    private BigDecimal discountRate;

    @Column(name = "discount_amount_per_item", precision = 18, scale = 2)
    private BigDecimal discountAmountPerItem;

    @Column(name = "final_price_per_item", nullable = false, precision = 18, scale = 2)
    private BigDecimal finalPricePerItem;

    @Column(name = "subtotal_amount", nullable = false, precision = 18, scale = 2)
    private BigDecimal subtotalAmount;

    @Column(name = "item_type_code", nullable = false)
    private Short itemTypeCode; // Mapped to OrderItemTypeEnum

    @Column(name = "requires_dispatch")
    private Short requiresDispatch = 0;

    @Column(name = "item_notes", columnDefinition = "TEXT")
    private String itemNotes;

    //注意：這裡是對應到 warehouse_id
    @Column(name = "warehouse_code", length = 100)
    private String warehouseCode;

    @Column(name = "mahjong_table_serial_number", length = 100)
    private String mahjongTableSerialNumber;

    @Column(name = "item_remark", columnDefinition = "TEXT")
    private String itemRemark;

    @Column(name = "original_gift_total", precision = 18, scale = 2)
    private BigDecimal originalGiftTotal;

    @Column(name = "exchanged_gift_total", precision = 18, scale = 2)
    private BigDecimal exchangedGiftTotal;

    @OneToMany(mappedBy = "orderItem", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private List<OrderItemGroup> itemGroups;

    @PrePersist
    protected void onCreate() {
        if (orderItemId == null) {
            orderItemId = UUID.randomUUID();
        }
        if (discountRate == null) {
            discountRate = BigDecimal.ZERO;
        }
        if (discountAmountPerItem == null) {
            discountAmountPerItem = BigDecimal.ZERO;
        }
    }

    @Transient
    public OrderItemTypeEnum getItemTypeEnum() {
        return OrderItemTypeEnum.fromCode(this.itemTypeCode);
    }
} 