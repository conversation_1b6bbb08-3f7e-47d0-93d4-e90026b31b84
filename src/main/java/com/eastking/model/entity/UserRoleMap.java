package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

/**
 * 使用者角色關聯實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_user_role_map", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"user_account_id", "role_id"})
})
public class UserRoleMap extends BaseEntity {

    /**
     * 使用者角色關聯ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "user_role_map_id", nullable = false, updatable = false)
    private UUID userRoleMapId;

    /**
     * 使用者帳號ID (FK)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_account_id", nullable = false)
    private UserAccount userAccount;

    /**
     * 角色ID (FK)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;
} 