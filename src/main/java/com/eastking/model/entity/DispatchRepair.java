package com.eastking.model.entity;

import com.eastking.enums.DispatchRepairTypeEnum;
import com.eastking.enums.RefundMethodEnum;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.Comment;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true, exclude = {"items", "techRecords", "store", "userAccount", "member", "technician", "sourceOrder", "wholesaleCustomer", "distributor"})
@ToString(callSuper = true, exclude = {"items", "techRecords", "wholesaleCustomer", "distributor"})
@Data
@Entity
@Table(name = "sm_dispatch_repair")
public class DispatchRepair extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "dispatch_repair_id")
    @Comment("派工維修單ID，主鍵")
    private UUID dispatchRepairId;

    @Column(name = "dispatch_repair_number", nullable = false, length = 100)
    @Comment("單號 (應唯一)")
    private String dispatchRepairNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id")
    private Order order;

    @Column(name = "type_code", nullable = false)
    @Comment("單據類型. 1:派工, 2:維修, 3:退貨. Java ENUM: DispatchRepairTypeEnum")
    private Short typeCode;

    @Column(name = "status_code", nullable = false)
    @Comment("狀態碼. Java ENUM: DispatchStatusEnum, RepairOrderStatusEnum, ReturnOrderStatusEnum (根據type_code決定)")
    private Short statusCode;

    @Column(name = "is_urgent", nullable = false)
    @Comment("是否為急單. 0:否, 1:是. Java ENUM: BooleanStatusEnum (描述: 是否狀態)")
    private Short isUrgent = 0;

    @Column(name = "customer_name", length = 100)
    @Comment("客戶姓名")
    private String customerName;

    @Column(name = "customer_phone", length = 50)
    @Comment("客戶電話")
    private String customerPhone;

    @Column(name = "installation_address")
    @Comment("服務地址")
    private String installationAddress;

    @Column(name = "issue_description")
    @Comment("狀況描述 (報修原因)")
    private String issueDescription;

    @Column(name = "scheduled_date")
    @Comment("預約日期")
    private LocalDate scheduledDate;

    @Column(name = "scheduled_time_slot", length = 50)
    @Comment("預約時段")
    private String scheduledTimeSlot;

    @Column(name = "assigned_technician_id")
    @Comment("主要負責技師ID")
    private UUID assignedTechnicianId;
    
    @Column(name = "remarks")
    @Comment("備註")
    private String remarks;

    // --- Merged Fields ---
    @Column(name = "customer_id")
    @Comment("客戶ID (FK sm_customer)")
    private UUID customerId;

    @Column(name = "customer_device_id")
    @Comment("關聯的客戶設備ID (FK sm_customer_device)")
    private UUID customerDeviceId;

    @Column(name = "dispatch_remarks", columnDefinition = "TEXT")
    @Comment("裝修資訊紀錄")
    private String dispatchRemarks;

    @Column(name = "handling_method")
    @Comment("處理方式")
    private String handlingMethod;

    @Column(name = "follow_up_action")
    @Comment("後續處理")
    private String followUpAction;

    @Column(name = "total_amount", precision = 18, scale = 2)
    @Comment("應收/應退總金額")
    private BigDecimal totalAmount;

    @Column(name = "other_discount", precision = 18, scale = 2)
    @Comment("其他折扣")
    private BigDecimal otherDiscount;

    @Column(name = "paid_amount", precision = 18, scale = 2)
    @Comment("實收金額")
    private BigDecimal paidAmount;

    @Column(name = "refund_method_code")
    @Comment("退款方式. Java ENUM: RefundMethodEnum")
    private Short refundMethodCode;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "dispatchRepair")
    private List<DispatchRepairItem> items;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "dispatchRepair")
    private List<DispatchTechRecord> techRecords;
} 