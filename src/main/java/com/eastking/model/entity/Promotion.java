package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_promotion")
public class Promotion extends BaseEntity {

    @Id
    @Column(name = "promotion_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID promotionId;

    @Column(name = "promotion_name", nullable = false)
    private String promotionName;

    @Column(name = "start_time", nullable = false)
    private OffsetDateTime startTime;

    @Column(name = "end_time", nullable = false)
    private OffsetDateTime endTime;

    @Column(name = "is_active", nullable = false)
    private Short isActive = 1; // Default to active

    @OneToMany(mappedBy = "promotion", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<PromotionChannel> channels;

    @OneToMany(mappedBy = "promotion", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private List<PromotionProduct> products;
} 