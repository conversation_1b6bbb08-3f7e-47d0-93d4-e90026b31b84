package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_gift_bundle_item")
public class GiftBundleItem extends BaseEntity {

    @Id
    @Column(name = "bundle_item_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID bundleItemId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bundle_id", nullable = false)
    private GiftBundle giftBundle;

    @Column(name = "gift_product_barcode", nullable = false)
    private String giftProductBarcode;

    @Column(name = "gift_product_name", columnDefinition = "TEXT")
    private String giftProductName;

    @Column(name = "quantity", nullable = false)
    private Integer quantity = 1; // Default to 1
} 