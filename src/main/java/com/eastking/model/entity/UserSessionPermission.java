package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * 使用者會話權限快取實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_user_session_permission")
public class UserSessionPermission extends BaseEntity {

    /**
     * 會話權限ID (即登入後產生的新 UUID)，主鍵
     */
    @Id
    @Column(name = "user_session_permission_id", nullable = false, updatable = false)
    private UUID userSessionPermissionId;

    /**
     * 使用者帳號ID (FK)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_account_id", nullable = false)
    private UserAccount userAccount;

    /**
     * 使用者於此會話擁有的權限列表 (JSON格式)
     */
    @Column(name = "permissions_json", nullable = false, columnDefinition = "TEXT")
    private String permissionsJson;

    /**
     * 此會話權限的過期時間
     */
    @Column(name = "expiry_time", nullable = false)
    private OffsetDateTime expiryTime;
} 