package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.UUID;

/**
 * 簡訊模板資料實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_sms_template")
public class SmsTemplateEntity extends BaseEntity {

    /**
     * 簡訊模板ID，主鍵
     */
    @Id
    @Column(name = "sms_template_id")
    private UUID smsTemplateId;

    /**
     * 模板名稱
     */
    @Column(name = "template_name", nullable = false, length = 100)
    private String templateName;

    /**
     * 模板類型 (例如: BIRTHDAY_GIFT, PROMOTION)
     */
    @Column(name = "template_type", length = 50)
    private String templateType;

    /**
     * 簡訊內容
     */
    @Column(name = "sms_content", nullable = false, columnDefinition = "TEXT")
    private String smsContent;

    @PrePersist
    protected void onCreate() {
        if (smsTemplateId == null) {
            smsTemplateId = UUID.randomUUID();
        }
    }
} 