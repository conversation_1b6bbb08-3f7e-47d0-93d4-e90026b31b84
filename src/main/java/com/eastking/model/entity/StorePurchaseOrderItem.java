package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * 門市進貨單品項表實體
 *
 * <AUTHOR> Developer
 * @date 2025/05/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sm_store_purchase_order_item")
public class StorePurchaseOrderItem extends BaseEntity {

    /**
     * 進貨單品項ID，主鍵
     */
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "store_purchase_order_item_id", nullable = false, updatable = false)
    private UUID storePurchaseOrderItemId;

    /**
     * 所屬進貨單ID (FK sm_store_purchase_order)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_purchase_order_id", nullable = false)
    private StorePurchaseOrder storePurchaseOrder;

    /**
     * 商品國際條碼
     */
    @Column(name = "product_barcode", nullable = false, length = 255)
    private String productBarcode;

    /**
     * 商品名稱 (快照)
     */
    @Column(name = "product_name", columnDefinition = "TEXT")
    private String productName;

    /**
     * 出貨倉庫代碼
     */
    @Column(name = "warehouse_code", length = 100)
    private String warehouseCode;

    /**
     * 訂購數量 (進貨單上的數量)
     */
    @Column(name = "ordered_quantity", nullable = false)
    private Integer orderedQuantity;

    /**
     * 實際收到數量 (門市點收後填寫)
     */
    @Column(name = "received_quantity")
    private Integer receivedQuantity;

    /**
     * 商品單價 (進貨時的單價)
     */
    @Column(name = "unit_price", nullable = false, precision = 18, scale = 2)
    private BigDecimal unitPrice;

    /**
     * 商品小計 (單價 * 訂購數量)
     */
    @Column(name = "item_subtotal", nullable = false, precision = 18, scale = 2)
    private BigDecimal itemSubtotal;

    /**
     * 品項備註
     */
    @Column(name = "item_notes", columnDefinition = "TEXT")
    private String itemNotes;

} 