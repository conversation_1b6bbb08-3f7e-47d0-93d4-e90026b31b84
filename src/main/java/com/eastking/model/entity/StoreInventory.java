package com.eastking.model.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@Entity
@Table(name = "sm_store_inventory")
public class StoreInventory extends BaseEntity {

    @Id
    @Column(name = "store_inventory_id")
    private UUID storeInventoryId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "store_id")
    private StoreEntity store;

    @Column(name = "product_barcode")
    private String productBarcode;
    
    @Column(name = "product_name")
    private String productName;

    @Column(name = "quantity_on_hand")
    private Integer quantityOnHand = 0;
    
    @Column(name = "last_stock_update_time")
    private OffsetDateTime lastStockUpdateTime;

    @PrePersist
    protected void onCreate() {
        if (storeInventoryId == null) {
            storeInventoryId = UUID.randomUUID();
        }
        if (lastStockUpdateTime == null) {
            lastStockUpdateTime = OffsetDateTime.now();
        }
    }
} 