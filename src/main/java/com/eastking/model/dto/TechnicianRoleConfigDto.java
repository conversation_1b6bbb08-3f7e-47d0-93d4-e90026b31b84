package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.util.UUID;

@Schema(description = "技師角色設定資料傳輸物件")
@Data
public class TechnicianRoleConfigDto {
    @Schema(description = "設定唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID technicianRoleConfigId;

    @Schema(description = "系統角色ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "角色ID不得為空")
    private UUID roleId;
    
    @Schema(description = "角色代碼", accessMode = Schema.AccessMode.READ_ONLY)
    private String roleCode;

    @Schema(description = "角色名稱", accessMode = Schema.AccessMode.READ_ONLY)
    private String roleName;

    @Schema(description = "是否啟用此角色於獎金設定", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "啟用狀態不得為空")
    private Boolean isActive;
} 