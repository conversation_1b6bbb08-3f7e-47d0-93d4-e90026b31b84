package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Schema(description = "外部倉庫資料 (用於下拉選單)")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExternalWarehouseDto {
    @Schema(description = "倉庫代碼")
    private String warehouseCode;

    @Schema(description = "倉庫名稱")
    private String warehouseName;
} 