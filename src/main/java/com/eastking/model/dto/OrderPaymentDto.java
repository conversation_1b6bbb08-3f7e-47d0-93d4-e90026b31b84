package com.eastking.model.dto;

import com.eastking.enums.PaymentMethodEnum;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class OrderPaymentDto {
    private PaymentMethodEnum paymentMethodCode;
    private BigDecimal amount;
    private String cardNumber;
    private String cardBrand;
    private String cardIssuer;
    private Integer cardInstallments;
    private String bankName;
    private String bankAccountLastFive;
    private String remitterAccountLastFive;
    private String collectorName;
    private String remarks;
} 