package com.eastking.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import java.time.OffsetDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 地區資料 DTO
 */
@Schema(description = "地區資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RegionDto {
    @Schema(description = "地區唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID regionId;

    @Schema(description = "地區名稱", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 50)
    @NotBlank(message = "地區名稱不得為空")
    @Size(max = 50, message = "地區名稱長度不得超過50個字符")
    private String regionName;

    @Schema(description = "排序順序")
    private Integer sequenceOrder;

    @Schema(description = "地區ERP代碼", maxLength = 100)
    @Size(max = 100)
    private String erpCode;

    private Short isDeleted; // For filtering if needed, usually handled by service
    private UUID createBy;
    private OffsetDateTime createTime;
    private UUID updateBy;
    private OffsetDateTime updateTime;
} 