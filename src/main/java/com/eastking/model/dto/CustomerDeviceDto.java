package com.eastking.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerDeviceDto {
    private UUID customerDeviceId;
    private UUID customerId;
    private String customerName;
    private String customerPhone;
    private UUID productSettingId;
    private String deviceSerialNumber;
    private String productName;
    private String installationAddress;
    private LocalDate warrantyDate;
    private LocalDate lastRepairDate;
    private String userNameRemark;
    private OffsetDateTime createTime;
} 