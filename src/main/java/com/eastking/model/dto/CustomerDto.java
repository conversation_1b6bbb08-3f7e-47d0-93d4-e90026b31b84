package com.eastking.model.dto;

import com.eastking.enums.ErpCompanyDivisionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "客戶/會員資料傳輸物件")
public class CustomerDto {

    @Schema(description = "客戶/會員ID (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID customerId;

    @NotBlank(message = "客戶姓名不得為空")
    @Size(max = 100, message = "客戶姓名長度不得超過100個字元")
    @Schema(description = "客戶/會員姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王大明")
    private String customerName;

    @NotBlank(message = "聯絡電話不得為空")
    @Pattern(regexp = "^\\+?[0-9\\s\\-()]{7,20}$", message = "電話號碼格式不正確")
    @Size(max = 50, message = "電話號碼長度不得超過50個字元")
    @Schema(description = "聯絡電話", requiredMode = Schema.RequiredMode.REQUIRED, example = "0912345678")
    private String phoneNumber;

    @jakarta.validation.constraints.Email(message = "Email格式不正確")
    @Size(max = 255, message = "Email長度不得超過255個字元")
    @Schema(description = "電子郵件", example = "<EMAIL>")
    private String email;

    @Schema(description = "地址區域代碼 (N:北區, C:中區, S:南區)", example = "N")
    private String addressRegionCode;

    @Size(max = 10, message = "郵遞區號長度不得超過10個字元")
    @Schema(description = "地址郵遞區號", example = "104")
    private String addressPostalCode;

    @Schema(description = "完整地址 (由後端組合或前端顯示用)", example = "台北市中山區南京東路三段100號5樓")
    private String fullAddress;

    @Schema(description = "出生年月日")
    private java.time.LocalDate birthDate;

    @Schema(description = "婚姻狀況代碼")
    private Short maritalStatusCode;

    @Schema(description = "住家電話")
    private String homePhone;

    @Schema(description = "職業類別代碼")
    private String occupationCode;

    @Schema(description = "工作性質")
    private String jobTitle;

    @Schema(description = "興趣")
    private String interests;
    
    @Schema(description = "是否願意接受優惠資訊")
    private Boolean acceptsMarketingInfo;

    // --- 新的結構化地址欄位 ---
    @Schema(description = "地址-縣市", example = "臺北市")
    private String addressCityName;
    @Schema(description = "地址-鄉鎮市區", example = "中山區")
    private String addressDistrictName;
    @Schema(description = "地址-街路", example = "南京東路三段")
    private String addressStreetName;
    @Schema(description = "地址-巷", example = "100巷")
    private String addressLane;
    @Schema(description = "地址-弄", example = "10弄")
    private String addressAlley;
    @Schema(description = "地址-號", example = "1號")
    private String addressNumber;
    @Schema(description = "地址-樓", example = "5樓")
    private String addressFloor;
    @Schema(description = "地址-之", example = "1")
    private String addressUnit;

    @Schema(description = "會員等級ID (若為會員)")
    private UUID memberLevelId;
    
    @Schema(description = "會員等級名稱 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private String memberLevelName;

    @Schema(description = "生理性別 (0:男, 1:女, 2:其他)")
    private Short genderCode;

    @Schema(description = "客戶歸屬公司別 (0:東方不敗, 1:雀友)", example = "0")
    private Short companyDivisionCode;

    @Schema(description = "客戶歸屬公司別描述 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private String companyDivisionDescription;

    @Schema(description = "客戶來源/客群管道ID")
    private UUID customerSegmentId;

    @Schema(description = "客戶來源/客群管道名稱 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private String customerSegmentName;

    @Schema(description = "備註")
    private String remarks;

    @Schema(description = "建立者員工編號 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private String createByEmployeeId;

    @Schema(description = "建立者姓名 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private String createByName;

    @Schema(description = "建立時間 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime createTime;

    @Schema(description = "最後更新者員工編號 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private String updateByEmployeeId;
    
    @Schema(description = "最後更新者姓名 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private String updateByName;

    @Schema(description = "最後更新時間 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime updateTime;

    @Schema(description = "是否已刪除 (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private Boolean isDeleted;

    @Schema(description = "客群管道列表")
    private Set<CustomerSegmentDto> segments;
} 