package com.eastking.model.dto;

import com.eastking.enums.SaleUnitEnum;
import com.eastking.enums.CurrencyCodeEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.DecimalMin;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSettingDto {
    @Schema(description = "商品設定唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID productSettingId;

    @Schema(description = "商品國際條碼 (來自正航)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品條碼不能為空")
    private String productBarcode; // Identifier from Zhenghang DB

    @Schema(description = "商品名稱 (來自正航, 可選，備援顯示用)", accessMode = Schema.AccessMode.READ_ONLY) // Usually read-only, managed by ERP sync
    private String productName; // From Zhenghang DB, for display

    @Schema(description = "產品簡稱", maxLength = 100)
    @Size(max = 100, message = "產品簡稱長度不能超過100")
    private String productShortName;

    @Schema(description = "幣值代碼")
    private CurrencyCodeEnum currency;

    @Schema(description = "東方不敗 - 表定價格")
    @DecimalMin(value = "0.00", message = "東方不敗 - 表定價格必須大於或等於0")
    private BigDecimal listPrice;

    @Schema(description = "東方不敗 - 銷售價格")
    @DecimalMin(value = "0.00", message = "東方不敗 - 銷售價格必須大於或等於0")
    private BigDecimal salePrice;

    @Schema(description = "東方不敗 - 成本價格")
    @DecimalMin(value = "0.00", message = "東方不敗 - 成本價格必須大於或等於0")
    private BigDecimal costPrice;

    @Schema(description = "銷售單位")
    private SaleUnitEnum saleUnit; // Using ENUM directly in DTO for validation and type safety

    @Schema(description = "東方不敗 - 有效銷售起日")
    private LocalDate saleEffectiveStartDate;

    @Schema(description = "東方不敗 - 有效銷售終日")
    private LocalDate saleEffectiveEndDate;

    @Schema(description = "是否為派工商品")
    @NotNull(message = "是否為派工商品不能為空")
    private Boolean isDispatchProduct = true;

    @Schema(description = "是否啟用")
    @NotNull(message = "是否啟用不能為空")
    private Boolean isActive = true;

    @Schema(description = "保固月數")
    @Min(value = 0, message = "保固月數不能為負數") 
    private Integer warrantyMonths;

    @Schema(description = "保固期間文字描述", accessMode = Schema.AccessMode.READ_ONLY)
    private String warrantyPeriodDescription;

    // --- ERP Fields (Part A) ---
    @Schema(description = "ERP公司別 代碼 (0: 東方不敗, 1: 雀友, 9: 全部)", accessMode = Schema.AccessMode.READ_ONLY)
    private Short erpCompanyDivision;

    @Schema(description = "ERP公司別 描述", accessMode = Schema.AccessMode.READ_ONLY)
    private String erpCompanyDivisionDescription;

    @Schema(description = "ERP商品編號", accessMode = Schema.AccessMode.READ_ONLY)
    private String erpProductCode;

    @Schema(description = "ERP商品類別", accessMode = Schema.AccessMode.READ_ONLY)
    private String erpProductCategory;

    @Schema(description = "ERP東方不敗價格", accessMode = Schema.AccessMode.READ_ONLY)
    private BigDecimal erpEastkingPrice;

    @Schema(description = "ERP雀友價格", accessMode = Schema.AccessMode.READ_ONLY)
    private BigDecimal erpQueyouPrice;

    // --- QueYou Specific Price Fields (Part B) ---
    @Schema(description = "雀友 - 表定價格")
    @DecimalMin(value = "0.00", message = "雀友 - 表定價格必須大於或等於0")
    private BigDecimal queyouListPrice;

    @Schema(description = "雀友 - 銷售價格")
    @DecimalMin(value = "0.00", message = "雀友 - 銷售價格必須大於或等於0")
    private BigDecimal queyouSalePrice;

    @Schema(description = "雀友 - 成本價格")
    @DecimalMin(value = "0.00", message = "雀友 - 成本價格必須大於或等於0")
    private BigDecimal queyouCostPrice;

    @Schema(description = "雀友 - 有效銷售起日")
    private OffsetDateTime queyouSaleEffectiveStartDate;

    @Schema(description = "雀友 - 有效銷售終日")
    private OffsetDateTime queyouSaleEffectiveEndDate;

    @Schema(description = "相關折扣設定")
    @Valid
    private List<ProductSettingDiscountDto> discounts;

    // Audit fields for response
    @Schema(description = "建立者ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID createBy;
    @Schema(description = "建立者名稱", accessMode = Schema.AccessMode.READ_ONLY)
    private String createByName; //populated by service
    @Schema(description = "建立時間", accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime createTime;
    @Schema(description = "更新者ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID updateBy;
    @Schema(description = "更新者名稱", accessMode = Schema.AccessMode.READ_ONLY)
    private String updateByName; //populated by service
    @Schema(description = "更新時間", accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime updateTime;

    // Helper to set erpCompanyDivisionDescription from code
    public void setErpCompanyDivisionFields(Short code) {
        this.erpCompanyDivision = code;
        if (code != null) {
            ErpCompanyDivisionEnum typeEnum = ErpCompanyDivisionEnum.fromCode(code);
            if (typeEnum != null) {
                this.erpCompanyDivisionDescription = typeEnum.getDescription();
            }
        }
    }
} 