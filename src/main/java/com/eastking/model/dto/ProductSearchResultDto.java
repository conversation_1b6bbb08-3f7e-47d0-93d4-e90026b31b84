package com.eastking.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;

/**
 * 商品搜尋結果資料DTO，用於快速顯示商品資訊
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSearchResultDto {
    private String productBarcode;
    private String productName;
    private String productCategory;
    private Boolean isDispatchProduct;
    private Boolean isMain;
    private String companyDivisionDescription;
    private BigDecimal listPrice; // 原價
    private BigDecimal salePrice; // 活動價
    private Integer stockQuantity;
    private String stockLocation;
} 