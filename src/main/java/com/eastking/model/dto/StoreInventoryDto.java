package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreInventoryDto {
    private UUID storeInventoryId;
    private UUID storeId;
    private String storeName; // For display
    private String productBarcode;
    private String productName;
    private BigDecimal salePrice;
    private Integer quantityOnHand;
    private OffsetDateTime lastStockUpdateTime;
    
    // Audit fields if needed for display
    private UUID createBy;
    private OffsetDateTime createTime;
    private UUID updateBy;
    private OffsetDateTime updateTime;
} 