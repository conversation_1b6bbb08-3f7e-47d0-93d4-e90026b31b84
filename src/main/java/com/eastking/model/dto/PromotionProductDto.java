package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.UUID;

@Schema(description = "促銷活動商品資料")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromotionProductDto {

    @Schema(description = "促銷商品關聯ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID promoProductId;

    @Schema(description = "商品國際條碼", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品條碼不得為空")
    private String productBarcode;

    @Schema(description = "商品名稱 (顯示用)", accessMode = Schema.AccessMode.READ_ONLY) // Often populated by backend based on barcode
    private String productName;
    
    @Schema(description = "原定價 (顯示用)", accessMode = Schema.AccessMode.READ_ONLY)
    private BigDecimal originalPrice; // For display, fetched from ProductSetting

    @Schema(description = "促銷價格", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "促銷價格不得為空")
    @DecimalMin(value = "0.00", message = "促銷價格必須大於或等於0")
    private BigDecimal promoPrice;
} 