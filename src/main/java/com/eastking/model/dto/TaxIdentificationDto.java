package com.eastking.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import java.time.OffsetDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 統編抬頭資料 DTO
 */
@Schema(description = "統編抬頭資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaxIdentificationDto {
    @Schema(description = "統編ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID taxIdentificationId;

    @Schema(description = "統一編號", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 20)
    @NotBlank(message = "統一編號不得為空")
    @Size(max = 20, message = "統一編號長度不得超過20個字符")
    private String taxIdNumber;

    @Schema(description = "公司抬頭", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 100)
    @NotBlank(message = "公司抬頭不得為空")
    @Size(max = 100, message = "公司抬頭長度不得超過100個字符")
    private String companyName;
    
    private Short isDeleted;
    private UUID createBy;
    private OffsetDateTime createTime;
    private UUID updateBy;
    private OffsetDateTime updateTime;
} 