package com.eastking.model.dto;

import com.eastking.enums.ProductMenuTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductMenuCategoryDto {
    private UUID productMenuCategoryId;

    @NotBlank(message = "分類名稱不能為空")
    private String categoryName;

    private UUID parentCategoryId; // Null for top-level categories
    private String parentCategoryName; // For display

    @NotNull(message = "選單類型不能為空")
    private ProductMenuTypeEnum menuType;

    private Integer sortOrder;

    // For response, to show children. Not typically for request payload when creating/updating single category.
    private List<ProductMenuCategoryDto> childCategories;
    private List<ProductMenuItemDto> menuItems;
} 