package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.OffsetDateTime;
import java.util.List;

@Schema(description = "操作歷程查詢條件 DTO")
@Data
public class AuditLogQueryDto {
    @Schema(description = "查詢起始時間 (ISO 8601 OffsetDateTime)", example = "2023-01-01T00:00:00+08:00")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime startTime;

    @Schema(description = "查詢結束時間 (ISO 8601 OffsetDateTime)", example = "2023-01-31T23:59:59+08:00")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime endTime;

    @Schema(description = "資料種類 (可多選, 使用 AuditDataTypeEnum 的 code 值)")
    private List<String> dataTypes;

    @Schema(description = "部門名稱 (模糊查詢)")
    private String departmentName;
    
    @Schema(description = "操作人員員工編號")
    private String employeeId;

    @Schema(description = "操作人員姓名 (模糊查詢)")
    private String userName;
    
    @Schema(description = "操作行為 (使用 AuditActionTypeEnum 的 code 值)")
    private String actionType;

    @Schema(description = "通用搜尋文字 (員工姓名、編號、單號、資料名稱、實體ID)")
    private String searchText;
} 