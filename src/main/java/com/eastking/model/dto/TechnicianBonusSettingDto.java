package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import lombok.Data;
import java.math.BigDecimal;
import java.util.UUID;

@Schema(description = "技師獎金設定資料傳輸物件")
@Data
public class TechnicianBonusSettingDto {
    @Schema(description = "獎金設定唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID technicianBonusSettingId;

    @Schema(description = "獎金項目ID (通常由父層 BonusItemDto 提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID bonusItemId; // Context from parent item

    @Schema(description = "技師角色ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "角色ID不得為空")
    private UUID roleId;
    
    @Schema(description = "角色名稱", accessMode = Schema.AccessMode.READ_ONLY)
    private String roleName; // For display

    @Schema(description = "獎金金額", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "獎金金額不得為空")
    @DecimalMin(value = "0.00", message = "獎金金額不能小於0")
    private BigDecimal bonusAmount;
} 