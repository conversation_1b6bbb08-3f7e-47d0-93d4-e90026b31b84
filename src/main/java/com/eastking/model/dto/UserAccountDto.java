package com.eastking.model.dto;

import com.eastking.enums.UserAccountStatusEnum;
import com.eastking.enums.UserAccountTypeEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 使用者帳號資料 DTO
 */
@Schema(description = "使用者帳號資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAccountDto {
    @Schema(description = "使用者帳號唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID userAccountId;

    @Schema(description = "員工編號 (登入帳號)", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 50)
    @NotBlank(message = "員工編號不得為空")
    @Size(max = 50, message = "員工編號長度不得超過50")
    private String employeeId;

    @Schema(description = "使用者姓名", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 100)
    @NotBlank(message = "使用者姓名不得為空")
    @Size(max = 100, message = "使用者姓名長度不得超過100")
    private String userName;

    @Schema(description = "密碼 (僅供新增/修改時傳入，回應時不包含)", minLength = 6, maxLength = 100, accessMode = Schema.AccessMode.WRITE_ONLY)
    @Size(min = 6, max = 100, message = "密碼長度必須介於6到100之間") // For create/update requests
    private String password; // Only for request, not for response

    @Schema(description = "帳號是否啟用 (true:啟用, false:停用)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "帳號狀態不得為空")
    private Boolean isActive; // true for ENABLED (1), false for DISABLED (0)

    @Schema(description = "帳號類型代碼 (e.g., 0 for GENERAL_STAFF, 1 for TECHNICIAN)", requiredMode = Schema.RequiredMode.NOT_REQUIRED) // Not required for update if not changing, but good to have for create
    private Short accountType; 

    @Schema(description = "帳號類型描述", accessMode = Schema.AccessMode.READ_ONLY)
    private String accountTypeDescription;

    @Schema(description = "指派的角色ID列表 (用於新增/更新)")
    private List<UUID> roleIds; // For assigning roles during create/update
    @Schema(description = "指派的角色名稱列表 (用於回應)", accessMode = Schema.AccessMode.READ_ONLY)
    private List<String> roleNames; // For display in response

    @Schema(description = "歸屬ERP公司別 代碼 (e.g., 0 for 東方不敗, 1 for 雀友)", requiredMode = Schema.RequiredMode.NOT_REQUIRED) // Can be required based on rules
    private Short erpCompanyDivision;

    @Schema(description = "歸屬ERP公司別 描述", accessMode = Schema.AccessMode.READ_ONLY)
    private String erpCompanyDivisionDescription;

    // Audit fields (typically in response)
    private UUID createBy;
    private String createdByName; // Optional: for display
    private OffsetDateTime createTime;
    private UUID updateBy;
    private String updatedByName; // Optional: for display
    private OffsetDateTime updateTime;
    private Short isDeleted;

    // Helper to map boolean to UserAccountStatusEnum short code for entity saving
    public Short getIsActiveCode() {
        return (this.isActive != null && this.isActive) ? UserAccountStatusEnum.ACTIVE.getCode() : UserAccountStatusEnum.INACTIVE.getCode();
    }

    // Optional: Helper to set isActive from code (if needed for entity to DTO mapping)
    public void setIsActiveFromCode(Short code) {
        this.isActive = UserAccountStatusEnum.ACTIVE.getCode().equals(code);
    }

    // Helper to set accountTypeDescription from code
    public void setAccountTypeFields(Short code) {
        this.accountType = code;
        UserAccountTypeEnum typeEnum = UserAccountTypeEnum.fromCode(code);
        if (typeEnum != null) {
            this.accountTypeDescription = typeEnum.getDescription();
        }
    }

    // Helper to set erpCompanyDivisionDescription from code
    public void setErpCompanyDivisionFields(Short code) {
        this.erpCompanyDivision = code;
        if (code != null) {
            ErpCompanyDivisionEnum typeEnum = ErpCompanyDivisionEnum.fromCode(code);
            if (typeEnum != null) {
                this.erpCompanyDivisionDescription = typeEnum.getDescription();
            }
        }
    }
} 