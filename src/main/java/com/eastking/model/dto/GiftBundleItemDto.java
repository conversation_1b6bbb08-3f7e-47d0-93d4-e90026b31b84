package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.UUID;

@Schema(description = "贈品套裝項目資料")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GiftBundleItemDto {

    @Schema(description = "套裝項目ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID bundleItemId;

    @Schema(description = "贈品商品國際條碼", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "贈品條碼不得為空")
    private String giftProductBarcode;

    @Schema(description = "贈品商品名稱 (顯示用)", accessMode = Schema.AccessMode.READ_ONLY)
    private String giftProductName; // Populated by backend

    @Schema(description = "贈品數量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "數量不得為空")
    @Min(value = 1, message = "數量必須至少為1")
    private Integer quantity = 1;
} 