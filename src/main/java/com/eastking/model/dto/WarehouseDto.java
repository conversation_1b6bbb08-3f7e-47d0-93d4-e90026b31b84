package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

@Schema(description = "倉庫資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseDto {
    @Schema(description = "倉庫唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID warehouseId;

    @Schema(description = "倉庫代碼 (來自外部系統，例如正航)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "倉庫代碼不得為空")
    private String warehouseCode;

    @Schema(description = "倉庫名稱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "倉庫名稱不得為空")
    private String warehouseName;

    @Schema(description = "所屬區域ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "區域ID不得為空")
    private UUID regionId;

    @Schema(description = "所屬區域名稱", accessMode = Schema.AccessMode.READ_ONLY)
    private String regionName; // For display

    // Audit fields (typically in response)
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID createBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime createTime;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID updateBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime updateTime;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Short isDeleted;
} 