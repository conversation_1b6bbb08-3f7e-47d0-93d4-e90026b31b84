package com.eastking.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "表尾條文資料傳輸物件")
public class DocumentClauseDto {

    @Schema(description = "表尾條文ID (回應時提供)", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID documentClauseId;

    @Schema(description = "公司別 (0:東方不敗, 1:雀友)", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Short companyDivisionCode;

    @NotBlank(message = "條文標題不得為空")
    @Size(max = 255, message = "條文標題長度不得超過255")
    @Schema(description = "條文標題", requiredMode = Schema.RequiredMode.REQUIRED, example = "標準批發條款V2")
    private String clauseTitle;

    @NotBlank(message = "條文內容不得為空")
    @Schema(description = "條文完整內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "1. 商品售出概不退換。\n2. 付款期限為30天內。")
    private String clauseContent;

    @Schema(description = "是否為預設條文", example = "true")
    private Boolean isDefault = false;

    @Schema(description = "啟用開始時間 (ISO 8601 OffsetDateTime)", example = "2024-01-01T00:00:00+08:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private OffsetDateTime startTime;

    @Schema(description = "啟用結束時間 (ISO 8601 OffsetDateTime)", example = "2025-12-31T23:59:59+08:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private OffsetDateTime endTime;

    @Schema(description = "是否啟用 (用於手動控制，若日期範圍有效則優先依日期)", example = "true")
    private Boolean isActive = true;

    @Schema(description = "顯示排序", example = "10")
    private Integer sequenceOrder = 0;

    // Audit fields (from BaseEntity, for response)
    @Schema(description = "建立時間", accessMode = Schema.AccessMode.READ_ONLY)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private OffsetDateTime createTime;

    @Schema(description = "最後更新時間", accessMode = Schema.AccessMode.READ_ONLY)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private OffsetDateTime updateTime;

    @Schema(description = "建立人員姓名", accessMode = Schema.AccessMode.READ_ONLY)
    private String createdByName;

    @Schema(description = "更新人員姓名", accessMode = Schema.AccessMode.READ_ONLY)
    private String updatedByName;

    @Schema(description = "是否已刪除 (回應時)", accessMode = Schema.AccessMode.READ_ONLY)
    private Boolean isDeleted;
} 