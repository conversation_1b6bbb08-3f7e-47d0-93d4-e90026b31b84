package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.util.List;
import java.util.UUID;

@Schema(description = "技師獎金項目資料傳輸物件")
@Data
public class BonusItemDto {
    @Schema(description = "獎金項目唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID bonusItemId;

    @Schema(description = "項目名稱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "項目名稱不得為空")
    private String itemName;

    @Schema(description = "項目描述")
    private String itemDescription;

    @Schema(description = "此項目是否啟用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "啟用狀態不得為空")
    private Boolean isActive;
    
    @Schema(description = "此項目的技師獎金設定列表 (用於顯示/更新)")
    private List<TechnicianBonusSettingDto> bonusSettings; // For GET response or batch update
} 