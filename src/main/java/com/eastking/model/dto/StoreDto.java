package com.eastking.model.dto;

import com.eastking.enums.StoreStatusEnum;
import com.eastking.enums.ErpCompanyDivisionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.UUID;
import java.time.OffsetDateTime;

/**
 * 門市資料 DTO
 */
@Schema(description = "門市資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreDto {
    @Schema(description = "門市唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID storeId;

    @Schema(description = "門市名稱", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 100)
    @NotBlank(message = "門市名稱不得為空")
    @Size(max = 100, message = "門市名稱長度不得超過100個字符")
    private String storeName;

    @Schema(description = "門市代碼 (可選)", maxLength = 50)
    @Size(max = 50, message = "門市代碼長度不得超過50個字符")
    private String storeCode;

    @Schema(description = "所屬地區ID (用於新增/更新)")
    private UUID regionId; // For request
    @Schema(description = "所屬地區名稱 (用於回應)", accessMode = Schema.AccessMode.READ_ONLY)
    private String regionName; // For response

    @Schema(description = "啟用狀態 (true:啟用, false:停用)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "啟用狀態不得為空")
    private Boolean isActive;

    @Schema(description = "門市地址", maxLength = 255)
    @Size(max = 255, message = "門市地址長度不得超過255個字符") // Assuming TEXT can hold more, but good to have a limit
    private String address;

    @Schema(description = "門市電話", maxLength = 20)
    @Size(max = 20, message = "門市電話長度不得超過20個字符")
    private String phoneNumber;
    
    @Schema(description = "歸屬ERP公司別 代碼 (1: 東方不敗, 2: 雀友)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "歸屬ERP公司別不得為空")
    private Short erpCompanyDivision;

    @Schema(description = "歸屬ERP公司別 描述", accessMode = Schema.AccessMode.READ_ONLY)
    private String erpCompanyDivisionDescription;

    // For staff and tax ID management during create/update
    @Schema(description = "指派的正式員工UserAccount ID列表 (用於新增/更新)")
    private List<UUID> staffUserAccountIds; // Regular staff
    @Schema(description = "指派的代班人員UserAccount ID列表 (用於新增/更新)")
    private List<UUID> substituteUserAccountIds;
    @Schema(description = "關聯的統編ID列表 (用於新增/更新)")
    private List<UUID> taxIdentificationIds;

    // For response, might include more detailed objects
    @Schema(description = "門市人員詳細列表 (用於回應)", accessMode = Schema.AccessMode.READ_ONLY)
    private List<UserAccountSlimDto> staffList; // Example for response
    @Schema(description = "門市統編詳細列表 (用於回應)", accessMode = Schema.AccessMode.READ_ONLY)
    private List<TaxIdentificationDto> taxIdentifications; // Example for response

    @Schema(description = "是否已刪除 (0:未刪除, 1:已刪除)", accessMode = Schema.AccessMode.READ_ONLY)
    private Short isDeleted;
    @Schema(description = "建立人員ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID createBy;
    @Schema(description = "建立人員姓名", accessMode = Schema.AccessMode.READ_ONLY)
    private String createByName; // For display
    @Schema(description = "建立時間", accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime createTime;
    @Schema(description = "最後更新人員ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID updateBy;
    @Schema(description = "最後更新人員姓名", accessMode = Schema.AccessMode.READ_ONLY)
    private String updateByName; // For display
    @Schema(description = "最後更新時間", accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime updateTime;

    // Helper to map boolean to StoreStatusEnum for consistency if needed
    public StoreStatusEnum getIsActiveEnum() {
        return (this.isActive != null && this.isActive) ? StoreStatusEnum.ENABLED : StoreStatusEnum.DISABLED;
    }

    // Helper to set erpCompanyDivisionDescription from code
    public void setErpCompanyDivisionFields(Short code) {
        this.erpCompanyDivision = code;
        if (code != null) {
            ErpCompanyDivisionEnum typeEnum = ErpCompanyDivisionEnum.fromCode(code);
            if (typeEnum != null) {
                this.erpCompanyDivisionDescription = typeEnum.getDescription();
            }
        }
    }
}

// The UserAccountSlimDto class previously here has been moved to its own file. 