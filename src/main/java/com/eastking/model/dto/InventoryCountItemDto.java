package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "盤點單品項資料傳輸物件")
public class InventoryCountItemDto {

    @Schema(description = "盤點單品項ID")
    private UUID inventoryCountItemId;

    @Schema(description = "商品國際條碼")
    private String productBarcode;

    @Schema(description = "商品名稱")
    private String productName;

    @Schema(description = "系統帳面數量")
    private Integer systemQuantity;

    @Schema(description = "初盤實盤數量")
    private Integer countedQuantity;

    @Schema(description = "複盤實盤數量")
    private Integer recountQuantity;

    @Schema(description = "最終確認數量")
    private Integer finalQuantity;

    @Schema(description = "盤點差異 (最終數量 - 系統數量)")
    private Integer variance;

    @Schema(description = "是否需要複盤")
    private Boolean requiresRecount;

    @Schema(description = "備註")
    private String remarks;
} 