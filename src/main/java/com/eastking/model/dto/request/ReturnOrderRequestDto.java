package com.eastking.model.dto.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
public class ReturnOrderRequestDto {

    @NotNull(message = "必須關聯一個設備")
    private UUID customerDeviceId;

    private String contactName;
    private String contactPhone;
    private String pickupAddress;

    @NotNull(message = "必須有報修日期")
    private LocalDate reportDate;
    
    private LocalDate scheduledDate;
    private UUID assignedTechnicianId;
    private String remarks;

    @NotEmpty(message = "必須至少有一個退機品項")
    private List<ReturnOrderItemRequestDto> items;
} 