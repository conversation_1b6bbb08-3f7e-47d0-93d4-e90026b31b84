package com.eastking.model.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
public class DispatchRepairRequestDto {
    private Short typeCode;
    private Short statusCode;
    private UUID customerId;
    private String customerName;
    private String customerPhone;
    private String installationAddress;
    private UUID customerDeviceId;
    private String handlingMethod;
    private String followUpAction;
    private LocalDate scheduledDate;
    private String scheduledTimeSlot;
    private UUID assignedTechnicianId;
    private String remarks;

    @Valid
    @NotEmpty
    private List<DispatchRepairItemRequestDto> items;
} 