package com.eastking.model.dto.request;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class RepairOrderItemRequestDto {

    @NotBlank
    private String productBarcode;

    @NotBlank
    private String productName;

    @NotNull
    private Short itemTypeCode;

    @NotNull
    @Min(1)
    private Integer quantity;

    @NotNull
    private BigDecimal unitPrice;
} 