package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.OffsetDateTime;

@Schema(description = "贈品套裝查詢請求參數")
@Data
public class GiftBundleQueryRequest {

    @Schema(description = "關鍵字 (主商品條碼或型號/名稱)")
    private String keyword;

    @Schema(description = "套裝啟用時間 (查詢範圍起點)")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime effectiveDateFrom;

    @Schema(description = "套裝啟用時間 (查詢範圍終點)")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime effectiveDateTo;

    @Schema(description = "是否啟用 (true/false)")
    private Boolean isActive;
} 