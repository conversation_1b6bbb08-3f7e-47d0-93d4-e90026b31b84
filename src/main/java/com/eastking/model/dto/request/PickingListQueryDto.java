package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
public class PickingListQueryDto {

    @Parameter(description = "倉庫ID")
    private UUID warehouseId;

    @Parameter(description = "技師ID")
    private UUID technicianId;
    
    @Parameter(description = "關鍵字 (揀料單號、申請人)")
    private String keyword;

    @Parameter(description = "領料單狀態碼")
    private Short status;

    @Parameter(description = "申請日期 (起)")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime dateFrom;

    @Parameter(description = "申請日期 (迄)")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime dateTo;
} 