package com.eastking.model.dto.request;

import com.eastking.enums.OrderTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.FutureOrPresent;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "批發商品訂單建立/更新請求物件")
public class WholesaleProductOrderRequestDto extends BaseOrderRequestDto {

    @Schema(description = "經銷商ID (與門市ID二擇一)")
    private UUID distributorId;

    @Schema(description = "裝機聯絡人姓名", example = "李大同")
    private String contactName;

    @Schema(description = "裝機聯絡人電話", example = "0911222333")
    private String contactPhone;

    @Schema(description = "裝機地址", example = "台北市信義區市府路1號")
    private String installationAddress;

    // 結構化安裝地址
    private String installationAddressCity;
    private String installationAddressDistrict;
    private String installationAddressStreet;
    private String installationAddressLane;
    private String installationAddressAlley;
    private String installationAddressNumber;
    private String installationAddressFloor;
    private String installationAddressUnit;

    @FutureOrPresent(message = "預計安裝日期必須是今天或未來日期")
    @Schema(description = "預計安裝日期", example = "2025-07-15")
    private LocalDate installationDate;

    @Size(max = 50)
    @Schema(description = "預計安裝時段 (例如: 上午, 14:00-16:00)", example = "下午")
    private String installationTimeSlot;

    @Schema(description = "指派技師ID (可選，若由系統自動指派或後續指派)")
    private UUID technicianId;

    @Schema(description = "預計完工日")
    private LocalDate expectedCompletionDate;

    private List<OrderItemGroupRequestDto> itemGroups;

    public WholesaleProductOrderRequestDto() {
        super();
        super.setOrderTypeCode(OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode());
    }

    // Ensure orderTypeCode is not directly settable from JSON if it's fixed by this DTO type
    @Schema(hidden = true)
    @Override
    public Short getOrderTypeCode() {
        return OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode();
    }

    @Override
    public void setOrderTypeCode(Short orderTypeCode) {
        // Prevent external modification if this DTO is strictly for DISPATCH_PRODUCT_ORDER
        if (orderTypeCode == null || orderTypeCode.equals(OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode())) {
            super.setOrderTypeCode(OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode());
        } else {
            // Optionally throw an exception or log a warning
            super.setOrderTypeCode(OrderTypeEnum.DISPATCH_PRODUCT_ORDER.getCode()); // Force correct type
        }
    }
} 