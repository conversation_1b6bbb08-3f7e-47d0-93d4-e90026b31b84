package com.eastking.model.dto.request;

import lombok.Data;

import java.math.BigDecimal;
import java.util.UUID;

@Data
public class DispatchRepairItemRequestDto {
    private UUID orderItemId;
    private String deviceSerialNumber;
    private String productModel;
    private Short warrantyStatusCode;
    private String issueDescription;
    private String productBarcode;
    private String productName;
    private Integer quantity;
    private BigDecimal unitPrice;
    private Short itemTypeCode;
} 