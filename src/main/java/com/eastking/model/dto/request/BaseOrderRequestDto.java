package com.eastking.model.dto.request;

import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.enums.InvoiceTypeEnum;
import com.eastking.enums.OrderTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Setter; // Added for internal setting

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "基礎訂單請求物件")
public class BaseOrderRequestDto {

    @Schema(description = "訂單ID (更新時提供)")
    private UUID orderId; // For update scenarios

    @Schema(description = "訂單狀態碼")
    private Short orderStatusCode;

    @NotNull(message = "公司別代碼不得為空")
    @Schema(description = "公司別代碼 (0:東方不敗, 1:雀友)", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Short companyDivisionCode; // ErpCompanyDivisionEnum

    @Setter // Allow subclasses or internal logic to set this
    @Schema(description = "訂單類型代碼", accessMode = Schema.AccessMode.READ_ONLY) // Typically set by specific DTO or endpoint logic
    private Short orderTypeCode; // OrderTypeEnum

    @Schema(description = "銷售/開單門市ID (與經銷商ID二擇一)")
    private UUID storeId;

    @Schema(description = "經銷商ID (與門市ID二擇一)")
    private UUID distributorId;

    @Schema(description = "套用的優惠活動ID")
    private UUID promotionId;

    // Customer/Member info - can be either existing memberId or new customer details
    @Schema(description = "會員ID (若為現有會員)")
    private UUID memberId;

    @Schema(description = "顧客姓名 (若非會員或新會員時提供)", example = "陳大文")
    @Size(max = 100)
    private String customerName; // Required if memberId is null

    @Schema(description = "顧客電話 (若非會員或新會員時提供)", example = "0912345678")
    @Size(max = 50)
    private String customerPhone; // Required if memberId is null

    @Schema(description = "訂單日期 (若不提供，則使用當前時間)")
    private OffsetDateTime orderDate;

    @NotEmpty(message = "訂單品項不得為空")
    @Valid
    @Schema(description = "訂單品項列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<OrderItemRequestDto> items;

    // Invoice Information
    @Schema(description = "發票類型代碼 (0:不開立, 2:二聯, 3:三聯)", example = "2")
    private Short invoiceTypeCode; // InvoiceTypeEnum

    @Schema(description = "稅金類型 (1:外加, 2:內含)", example = "1")
    private Short taxType;

    @Size(max = 50)
    @Schema(description = "發票號碼 (若手動開立或B2B)", example = "XX12345678")
    private String invoiceNumber;

    @Size(max = 20)
    @Schema(description = "統一編號 (三聯式發票必填)", example = "12345678")
    private String taxIdNumber;

    @Size(max = 100)
    @Schema(description = "發票抬頭 (三聯式發票必填)", example = "東方不敗股份有限公司")
    private String invoiceCompanyTitle;
    
    @Schema(description = "發票日期")
    private LocalDate invoiceDate;

    @Schema(description = "發票地址")
    private String invoiceAddress;

    @Schema(description = "開立發票金額(門市)")
    private BigDecimal storeInvoiceAmount;

    @Schema(description = "門市發票抬頭")
    private String storeInvoiceCompanyTitle;

    @Schema(description = "門市發票統編")
    private String storeTaxIdNumber;

    @Schema(description = "訂單備註 (給內部人員)")
    private String remarks;

    @Valid
    @Schema(description = "付款明細列表")
    private List<PaymentDetailDto> paymentDetails;
} 