package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "新增/更新盤點單品項請求")
public class InventoryCountItemRequestDto {

    @NotBlank(message = "商品條碼不得為空")
    @Schema(description = "商品國際條碼", required = true)
    private String productBarcode;

    @Schema(description = "商品名稱 (可選，後端會以條碼為準重新獲取)")
    private String productName;

    @NotNull(message = "實盤數量不得為空")
    @Min(value = 0, message = "實盤數量不能為負數")
    @Schema(description = "實盤數量", required = true)
    private Integer countedQuantity;
    
    @Schema(description = "備註")
    private String remarks;
} 