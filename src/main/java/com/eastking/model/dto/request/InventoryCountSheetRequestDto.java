package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "新增/更新盤點單請求")
public class InventoryCountSheetRequestDto {

    @NotNull(message = "盤點門市ID不得為空")
    @Schema(description = "盤點門市ID", required = true)
    private UUID storeId;

    @NotBlank(message = "盤點月份不得為空")
    @Schema(description = "盤點月份 (格式: YYYY-MM)", required = true)
    private String countMonth;

    @NotBlank(message = "盤點日期不得為空")
    @Schema(description = "盤點日期 (ISO 8601 格式)", required = true)
    private String countDate;

    @Schema(description = "備註")
    private String remarks;

    @NotNull(message = "是否送出審核的標記不得為空")
    @Schema(description = "是否送出審核 (true: 送出審核, false: 暫存)", required = true)
    private Boolean isSubmitted;

    @NotEmpty(message = "盤點品項不得為空")
    @Valid
    @Schema(description = "盤點品項列表", required = true)
    private List<InventoryCountItemRequestDto> items;
} 