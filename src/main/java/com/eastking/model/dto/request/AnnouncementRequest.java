package com.eastking.model.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.OffsetDateTime;
import java.util.List;

@Data
public class AnnouncementRequest {

    @NotNull(message = "公告分類不能為空")
    private Integer categoryCode;

    @NotBlank(message = "公告標題不能為空")
    @Size(max = 255, message = "公告標題長度不能超過255個字符")
    private String title;

    @NotBlank(message = "公告內容不能為空")
    private String content;

    @NotNull(message = "開始時間不能為空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime startTime;

    @NotNull(message = "結束時間不能為空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    // TODO: Add custom validation: endTime must be after startTime
    private OffsetDateTime endTime;

    @NotNull(message = "是否重要不能為空")
    private Boolean isImportant = false;

    @NotNull(message = "是否啟用不能為空")
    private Boolean isEnabled = true;

    private List<AnnouncementTargetDto> targets;

} 