package com.eastking.model.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AnnouncementTargetDto {

    @NotNull(message = "目標類型不能為空")
    private Integer targetType; // AnnouncementTargetTypeEnum code

    @NotBlank(message = "目標識別碼不能為空")
    private String targetIdentifier; // Department ID or Employee ID

    // Optional: Fields to display name if needed from frontend, not used for saving directly
    private String targetName; // For display on frontend, e.g., department name or employee name
} 