package com.eastking.model.dto.request;

import lombok.Data;

import java.util.UUID;

@Data
public class TransferDispatchRequest {
    private UUID targetTechnicianId;
    private String reason; // optional transfer reason
    /**
     * Material handling strategy when transferring:
     * - RETURN_TO_WAREHOUSE: return collected materials to the original warehouse.
     * - TRANSFER_TO_TECH: transfer collected materials to the target technician car stock.
     */
    private String materialHandling; 
}
