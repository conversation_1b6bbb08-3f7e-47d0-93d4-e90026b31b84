package com.eastking.model.dto.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.OffsetDateTime;

@Data
public class AnnouncementQueryRequest {

    private Integer categoryCode;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime announcementDateFrom; // Combined field for active period query: start_time <= date <= end_time

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime announcementDateTo; // Combined field for active period query

    private String keyword; // For title search
    private Boolean isEnabled;
    private Boolean isImportant;

    // Standard pagination fields, to be used with Pageable
    private int page = 0; // Default to page 0
    private int size = 10; // Default to 10 items per page
    private String[] sort; // e.g., sort=createTime,desc (Format: field,direction)
} 