package com.eastking.model.dto.request;

import lombok.Data;
import java.math.BigDecimal;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;

@Data
public class StoreReturnRequestDto {

    @NotNull(message = "退款金額不能為空")
    @DecimalMin(value = "0.0", inclusive = true, message = "退款金額不可為負數")
    private BigDecimal refundAmount;

    @NotBlank(message = "原因不能為空")
    private String reason;
} 