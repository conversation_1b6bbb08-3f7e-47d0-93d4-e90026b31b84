package com.eastking.model.dto.request;

import com.eastking.enums.PaymentMethodEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 訂單支付明細傳輸對象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "付款明細物件")
public class PaymentDetailDto {

    @NotNull(message = "付款方式不能為空")
    @Schema(description = "支付方式代碼", requiredMode = Schema.RequiredMode.REQUIRED)
    private PaymentMethodEnum paymentMethodCode;

    @NotNull(message = "付款金額不能為空")
    @DecimalMin(value = "0.01", message = "付款金額必須大於0")
    @Schema(description = "支付金額", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal amount;

    @Schema(description = "信用卡完整卡號")
    private String cardNumber;

    @Schema(description = "信用卡品牌")
    private String cardBrand;

    @Schema(description = "信用卡發卡行")
    private String cardIssuer;

    @NotNull(message = "信用卡分期數不能為空")
    @Schema(description = "信用卡分期數", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer cardInstallments;

    @Schema(description = "銀行名稱")
    private String bankName;

    @Schema(description = "匯款帳號後五碼")
    private String bankAccountLastFive;

    @Schema(description = "匯出帳號後五碼")
    private String remitterAccountLastFive;

    @Schema(description = "代收人員姓名")
    private String collectorName;

    @Schema(description = "備註")
    private String remarks;

    // Fields specific to certain payment methods
    @Schema(description = "信用卡交易授權碼 (選擇信用卡支付時)", example = "AUTH12345")
    private String creditCardAuthCode;

    @Schema(description = "信用卡分期期數 (選擇信用卡分期時)", example = "3")
    private Integer creditCardInstallments;

    @Schema(description = "匯款銀行名稱 (選擇匯款時)", example = "中國信託")
    private String bankTransferBankName;

    @Schema(description = "匯款帳號後五碼 (選擇匯款時)", example = "12345")
    private String bankTransferAccountLast5;
    
    @Schema(description = "禮券/抵用券號碼 (選擇禮券時)", example = "VOUCHER2025")
    private String voucherNumber;

    @Schema(description = "其他支付方式的參考資訊或交易ID", example = "TXN_LINEPAY_XYZ")
    private String transactionReference;

    @Schema(description = "付款備註", example = "客戶預付訂金")
    private String notes;
} 