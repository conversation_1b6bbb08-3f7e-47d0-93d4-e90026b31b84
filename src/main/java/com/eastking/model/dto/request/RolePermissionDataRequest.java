package com.eastking.model.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class RolePermissionDataRequest {

    @NotBlank(message = "角色代碼不能為空")
    @Size(max = 50, message = "角色代碼長度不能超過50")
    private String roleCode;

    @NotBlank(message = "角色名稱不能為空")
    @Size(max = 100, message = "角色名稱長度不能超過100")
    private String roleName;

    @Size(max = 1000, message = "角色描述長度不能超過1000")
    private String roleDescription;

    @Valid
    private List<FunctionPermissionDto> functionPermissions; // CRUD per function

    @Valid
    private List<FieldPermissionDto> fieldPermissions; // Field level per function
    
    private List<String> regionCodes; // List of RegionEnum codes (N, C, S, A)

    @Data
    public static class FunctionPermissionDto {
        @NotNull(message = "功能ID不能為空")
        private UUID systemFunctionId;
        
        private Short canCreate = 0;
        private Short canRead = 0;
        private Short canUpdate = 0;
        private Short canDelete = 0;
        private Short canApprove = 0;
        private Short canChangePrice = 0;
        private Short canPrint = 0;
        private Short canStoreApprove = 0;
        private Short canDispatchApprove = 0;
    }

    @Data
    public static class FieldPermissionDto {
        @NotBlank(message = "功能代碼不能為空")
        private String functionCode;
        @NotBlank(message = "欄位名稱不能為空")
        private String fieldName;
        @NotNull(message = "欄位權限等級不能為空")
        private Integer permissionLevelCode; // FieldPermissionLevelEnum code
    }
} 