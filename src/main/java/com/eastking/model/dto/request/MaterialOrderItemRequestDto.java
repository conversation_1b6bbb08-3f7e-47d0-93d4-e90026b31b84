package com.eastking.model.dto.request;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import java.util.UUID;

@Data
public class MaterialOrderItemRequestDto {

    private UUID dispatchRepairItemId;

    @NotBlank(message = "商品條碼不得為空")
    private String productBarcode;
    
    @Min(value = 1, message = "數量必須大於0")
    private Integer requestedQuantity;
} 