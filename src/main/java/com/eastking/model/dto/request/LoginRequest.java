package com.eastking.model.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 登入請求 DTO
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
public class LoginRequest {

    /**
     * 員工編號
     */
    @NotBlank(message = "員工編號不能為空")
    private String employeeId;

    /**
     * 密碼
     */
    @NotBlank(message = "密碼不能為空")
    private String password;

    /**
     * 登入時選擇的公司別 (e.g., "EASTKING", "QUEYOU")
     */
    @NotBlank(message = "必須選擇登入公司別")
    private String companyContext;
} 