package com.eastking.model.dto.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class MaterialOrderRequestDto {

    @NotNull(message = "必須選擇一個領料倉庫")
    private UUID targetWarehouseId;

    @NotEmpty(message = "領料品項不得為空")
    private List<MaterialOrderItemRequestDto> items;
    
    private String remarks;
} 