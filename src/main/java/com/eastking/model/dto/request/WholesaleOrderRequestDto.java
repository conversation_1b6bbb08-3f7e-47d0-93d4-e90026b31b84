package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "批發訂單請求物件")
public class WholesaleOrderRequestDto extends BaseOrderRequestDto {

    @NotNull(message = "批發客戶ID不得為空")
    @Schema(description = "批發客戶ID (收貨對象)", requiredMode = Schema.RequiredMode.REQUIRED)
    private UUID wholesaleCustomerId; // Replaces memberId from BaseOrderRequestDto for clarity

    @Schema(description = "預計出貨日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "預計出貨日期不得為空")
    private LocalDate expectedShipmentDate;

    @Schema(description = "出貨方式代碼. Java ENUM: ShipmentMethodEnum (0:技師安裝, 1:到店自取, 2:物流)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出貨方式不得為空")
    private Short shipmentMethodCode;

    @Schema(description = "物流業者名稱 (出貨方式為物流時)")
    @Size(max = 100)
    private String logisticsProviderName;

    @Schema(description = "物流件數 (出貨方式為物流或回頭車時)")
    private Integer logisticsShipmentCount;

    @Schema(description = "貨車車號 (出貨方式為回頭車或親送時)")
    @Size(max = 50)
    private String truckLicensePlate;

    @Schema(description = "物流單號 (出貨方式為物流時)")
    @Size(max = 100)
    private String logisticsTrackingNumber;

    // Override customerName and customerPhone from BaseOrderRequestDto as they are not directly used for wholesale order creation via this DTO
    // They will be derived from wholesaleCustomerId in the service layer.
    @Schema(hidden = true)
    @Override
    public String getCustomerName() {
        return super.getCustomerName(); 
    }

    @Schema(hidden = true)
    @Override
    public String getCustomerPhone() {
        return super.getCustomerPhone();
    }
    
    @Schema(hidden = true)
    @Override
    public UUID getMemberId() {
        return super.getMemberId();
    }
} 