package com.eastking.model.dto.request;

import com.eastking.model.dto.response.ExpenditureDetailDto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
public class DailySettlementRequestDto {

    @NotNull(message = "門市ID不可為空")
    private UUID storeId;

    @NotNull(message = "結算日期不可為空")
    private LocalDate settlementDate;

    private BigDecimal actualCashIncomeAmount;

    private String remarks;

    @Valid
    private List<ExpenditureDetailDto> expenditureDetails;
} 