package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.UUID;

/**
 * 訂單退貨請求傳輸對象
 */
@Data
@Schema(description = "發起訂單退貨請求物件")
public class OrderRefundRequestDto {

    /**
     * 要從中退貨的原始訂單ID
     */
    @NotNull(message = "原訂單ID不得為空")
    @Schema(description = "要退貨的原訂單ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private UUID orderId;

    /**
     * 退貨原因
     */
    @Schema(description = "退貨原因")
    private String reason;

    /**
     * 本次要退貨的品項列表
     */
    @NotEmpty(message = "退貨品項列表不得為空")
    @Valid
    @Schema(description = "要退貨的品項列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<OrderRefundItemRequestDto> itemsToReturn;
} 