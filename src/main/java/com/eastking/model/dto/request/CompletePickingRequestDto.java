package com.eastking.model.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class CompletePickingRequestDto {

    private List<PickedItemDto> items;

    @Data
    public static class PickedItemDto {
        @NotNull
        private UUID itemId;
        @NotNull
        private Integer pickedQuantity;
    }
} 