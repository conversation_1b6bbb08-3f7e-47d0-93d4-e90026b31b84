package com.eastking.model.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransferActionRequest {

    @NotEmpty
    @Valid
    private List<ItemActionDto> items;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemActionDto {
        @NotNull(message = "調撥單品項ID不得為空")
        private UUID storeTransferOrderItemId;

        @NotNull(message = "數量不得為空")
        @Min(value = 0, message = "數量不能為負數") // Allow 0 if an item was not found/not sent
        private Integer quantity;
    }
} 