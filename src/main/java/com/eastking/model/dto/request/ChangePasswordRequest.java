package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Schema(description = "變更密碼請求物件")
@Data
public class ChangePasswordRequest {

    @Schema(description = "舊密碼 (使用者自行變更時為必填，管理員重設時可選)")
    private String oldPassword;

    @Schema(description = "新密碼", requiredMode = Schema.RequiredMode.REQUIRED, minLength = 6, maxLength = 100)
    @NotBlank(message = "新密碼不得為空")
    @Size(min = 6, max = 100, message = "新密碼長度必須介於6到100之間")
    private String newPassword;
} 