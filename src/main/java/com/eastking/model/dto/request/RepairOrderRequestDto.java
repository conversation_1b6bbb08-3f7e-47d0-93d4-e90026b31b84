package com.eastking.model.dto.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
public class RepairOrderRequestDto {
    
    @NotNull
    private UUID customerDeviceId;
    
    @NotNull
    private UUID customerId;

    private String contactName;
    private String contactPhone;
    private String contactAddress;
    
    @NotNull
    private Short repairTypeCode;

    private LocalDate reportDate;
    private LocalDate repairDate;
    private UUID assignedTechnicianId;
    
    private Short warrantyStatusCode;
    private String issueDescription;
    private String handlingMethod;
    private String followUpAction;
    private BigDecimal totalAmount;
    private BigDecimal paidAmount;
    private String remarks;

    @NotEmpty
    private List<RepairOrderItemRequestDto> items;
} 