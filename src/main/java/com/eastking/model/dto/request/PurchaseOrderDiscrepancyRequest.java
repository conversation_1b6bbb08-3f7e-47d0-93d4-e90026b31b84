package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseOrderDiscrepancyRequest {

    @NotNull
    @Valid
    private List<DiscrepancyItemDto> items;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiscrepancyItemDto {
        @NotNull(message = "進貨單品項ID不得為空")
        private UUID storePurchaseOrderItemId;

        @NotNull(message = "實際收到數量不得為空")
        @Min(value = 0, message = "實際收到數量不能為負數")
        private Integer actualQuantity;

        @Schema(description = "差異原因")
        private String reason;
    }
} 