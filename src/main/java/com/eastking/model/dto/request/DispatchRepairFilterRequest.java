package com.eastking.model.dto.request;

import lombok.Data;

import java.time.LocalDate;
import java.util.UUID;

@Data
public class DispatchRepairFilterRequest {
    private Short typeCode; // 1:派工, 2:維修, 3:退貨
    private Short statusCode;
    private String number; // 通用單號查詢
    private UUID technicianId;
    private UUID customerId;
    private LocalDate startDate;
    private LocalDate endDate;
    private String sortOrder;
} 