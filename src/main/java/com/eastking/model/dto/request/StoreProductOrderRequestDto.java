package com.eastking.model.dto.request;

import com.eastking.enums.OrderTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
// import lombok.NoArgsConstructor; // Removed to avoid conflict with custom constructor
// import lombok.AllArgsConstructor; // Also ensure this is not causing issues with super or default logic

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "門市商品訂單建立/更新請求物件")
public class StoreProductOrderRequestDto extends BaseOrderRequestDto {
    // Specific fields for store product orders, if any, can be added here.
    // For now, it primarily serves to set the orderTypeCode implicitly.

    public StoreProductOrderRequestDto() {
        super();
        // Set the order type code specifically for store product orders
        super.setOrderTypeCode(OrderTypeEnum.STORE_PRODUCT_ORDER.getCode());
    }

    // Override to ensure it's not changed, and hide from <PERSON>wagger as it's fixed
    @Schema(hidden = true)
    @Override
    public Short getOrderTypeCode() {
        return OrderTypeEnum.STORE_PRODUCT_ORDER.getCode();
    }

    @Override
    public void setOrderTypeCode(Short orderTypeCode) {
        // Prevent external modification if this DTO is strictly for STORE_PRODUCT_ORDER
        // Allow setting only if it's the correct type or if it's null (which will be set by constructor or default logic)
        if (orderTypeCode == null || orderTypeCode.equals(OrderTypeEnum.STORE_PRODUCT_ORDER.getCode())) {
            super.setOrderTypeCode(OrderTypeEnum.STORE_PRODUCT_ORDER.getCode());
        } else {
            // Optionally throw an exception or log a warning if an attempt is made to set it to a different type
            // For now, just ensure it remains the store product order type by re-setting it.
            super.setOrderTypeCode(OrderTypeEnum.STORE_PRODUCT_ORDER.getCode());
        }
    }
} 