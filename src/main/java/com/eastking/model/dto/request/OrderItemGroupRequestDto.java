package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.UUID;

@Data
@Schema(description = "訂單項目組合請求物件")
public class OrderItemGroupRequestDto {
    private UUID orderItemGroupId;
    private String productBarcode;
    private String productName;
    private UUID warehouseId;
    private Short requiresDispatch;
    private String awaitingMaterials;
    private Short itemTypeCode;
    private Integer quantity;
    private BigDecimal unitPrice;
    private BigDecimal listPrice;
    private Short isAwait;
} 