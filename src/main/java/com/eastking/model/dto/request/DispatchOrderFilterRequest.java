package com.eastking.model.dto.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.UUID;

@Data
public class DispatchOrderFilterRequest {
    private Short dispatchTypeCode;
    private Short dispatchStatusCode;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate startDate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate endDate;
    private UUID technicianId;
    private String sortOrder; // e.g., "newest", "oldest"
} 