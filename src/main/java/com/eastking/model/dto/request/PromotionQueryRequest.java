package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.OffsetDateTime;

@Schema(description = "促銷活動查詢請求參數")
@Data
public class PromotionQueryRequest {

    @Schema(description = "關鍵字 (促銷活動名稱)")
    private String keyword;

    @Schema(description = "活動開始時間 (查詢範圍起點)")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime activityDateFrom;

    @Schema(description = "活動開始時間 (查詢範圍終點)")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime activityDateTo;

    @Schema(description = "是否啟用 (true/false)")
    private Boolean isActive;

    // Pagination and sorting will be handled by Pageable in controller
} 