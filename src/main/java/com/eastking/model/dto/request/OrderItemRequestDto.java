package com.eastking.model.dto.request;

import com.eastking.enums.OrderItemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.UUID;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "訂單品項請求物件")
public class OrderItemRequestDto {

    @Schema(description = "(前端用)品項臨時ID，用於關聯主商品與其贈品/加購品，或在新增多品項時識別", example = "temp_item_123")
    private String clientTempId; // For client-side reference, especially for new items and parent-child linking before saving

    @Schema(description = "(前端用)關聯的父品項臨時ID (若此為贈品/加購品)", example = "temp_item_main_abc")
    private String parentClientTempId; // For client-side reference
    
    @Schema(description = "已存在的訂單品項ID (更新時提供)")
    private UUID orderItemId; // Used when updating an existing item

    @NotBlank(message = "商品條碼不得為空")
    @Schema(description = "商品國際條碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "PROD001")
    private String productBarcode;

    @NotBlank(message = "商品名稱不得為空")
    @Schema(description = "商品名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "Sample Product")
    private String productName;

    @NotNull(message = "數量不得為空")
    @Min(value = 1, message = "數量至少為1")
    @Schema(description = "數量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer quantity;

    @Schema(description = "原始單價 (若允許修改，則由此傳入；否則後端根據商品設定帶出)", example = "29800.00")
    private BigDecimal unitPrice; // Original price per unit, can be set if price is overridable

    @Schema(description = "商品原價/表定價", example = "32000.00")
    private BigDecimal listPrice;

    @Schema(description = "品項折扣率 (例如0.1表示9折，0.05表示95折)", example = "0.10")
    @Min(value = 0, message = "折扣率不能為負")
    private BigDecimal discountRate;

    @Schema(description = "單品項固定折扣金額", example = "100.00")
    @Min(value = 0, message = "折扣金額不能為負")
    private BigDecimal discountAmountPerItem;

    // finalPricePerItem and subtotalAmount will be calculated by backend based on above fields and product settings

    @NotNull(message = "品項類型不得為空")
    @Schema(description = "品項類型 (0:主商品, 1:贈品, 2:加購品)", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Short itemTypeCode; // Maps to OrderItemTypeEnum

    @Schema(description = "店取、派工、自載展示機", example = "1")
    private Short requiresDispatch;

    @Schema(description = "品項備註", example = "客戶指定綠色")
    private String itemNotes;

    @Schema(description = "出貨倉庫代碼 (若適用，例如派工商品或特定門市自提)", example = "WH-NRT-001")
    private String warehouseCode;
    
    @Schema(description = "電動桌機身號碼 (若此品項為電動桌主商品且已分配)", example = "SN123456789")
    private String mahjongTableSerialNumber;

    @Schema(description = "分錄備註 (批發訂單項目特定備註)")
    private String itemRemark; // Added for wholesale line item remarks

    @Schema(description = "此品項下的服務項目列表 (例如安裝費、樓層費)")
    private List<OrderItemGroupRequestDto> itemGroups;

    private BigDecimal originalGiftTotal;

    private BigDecimal exchangedGiftTotal;
} 