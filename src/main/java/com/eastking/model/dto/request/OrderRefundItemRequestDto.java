package com.eastking.model.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.UUID;

/**
 * 訂單退貨品項請求傳輸對象
 */
@Data
@Schema(description = "退貨品項請求物件")
public class OrderRefundItemRequestDto {

    /**
     * 原始訂單品項的ID (sm_order_item.order_item_id)
     */
    @NotNull(message = "原訂單品項ID不得為空")
    @Schema(description = "要退貨的原訂單品項ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private UUID orderItemId;

    /**
     * 本次要退貨的數量
     */
    @NotNull(message = "退貨數量不得為空")
    @Min(value = 1, message = "退貨數量必須大於0")
    @Schema(description = "退貨數量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer quantity;
} 