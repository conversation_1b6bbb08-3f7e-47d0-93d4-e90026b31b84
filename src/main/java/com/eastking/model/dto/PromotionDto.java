package com.eastking.model.dto;

import com.eastking.enums.ActivationStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Future;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Schema(description = "促銷活動資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromotionDto {

    @Schema(description = "促銷活動ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID promotionId;

    @Schema(description = "促銷活動名稱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "促銷活動名稱不得為空")
    private String promotionName;

    @Schema(description = "活動開始時間", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活動開始時間不得為空")
    private OffsetDateTime startTime;

    @Schema(description = "活動結束時間", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "活動結束時間不得為空")
    // @Future(message = "活動結束時間必須在未來") // Can be problematic if creating past-dated historical promos
    private OffsetDateTime endTime;

    @Schema(description = "是否啟用此促銷活動")
    private Boolean isActive = true;

    @Valid
    @NotEmpty(message = "必須至少指定一個適用管道")
    @Schema(description = "適用管道列表")
    private List<PromotionChannelDto> channels;

    @Valid
    @NotEmpty(message = "必須至少指定一個促銷商品")
    @Schema(description = "促銷商品列表")
    private List<PromotionProductDto> products;

    // Audit fields
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID createBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String createByName;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime createTime;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID updateBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String updateByName;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime updateTime;
} 