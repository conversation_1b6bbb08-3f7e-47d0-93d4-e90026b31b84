package com.eastking.model.dto;

import com.eastking.enums.ProductMenuTypeEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductMenuNodeDto {
    private UUID id; // categoryId or itemId
    private String clientTempId; // To carry the client-side temporary ID if the 'id' field is null (for new items)
    private String name; // categoryName or productName
    private String type; // "CATEGORY" or "ITEM"
    private Integer sortOrder;
    private String productBarcode; // Only for ITEM type

    // For CATEGORY type
    private ProductMenuTypeEnum menuType; // Redundant if fetched under a specific menu, but good for context
    private UUID parentCategoryId;
    private List<ProductMenuNodeDto> children; // Nested children (sub-categories or items)

    // Constructor for Category
    public ProductMenuNodeDto(UUID id, String name, Integer sortOrder, ProductMenuTypeEnum menuType, UUID parentCategoryId) {
        this.id = id;
        this.name = name;
        this.type = "CATEGORY";
        this.sortOrder = sortOrder;
        this.menuType = menuType;
        this.parentCategoryId = parentCategoryId;
    }

    // Constructor for Item
    public ProductMenuNodeDto(UUID id, String name, String productBarcode, Integer sortOrder, UUID parentCategoryId) {
        this.id = id;
        this.name = name;
        this.productBarcode = productBarcode;
        this.type = "ITEM";
        this.sortOrder = sortOrder;
        this.parentCategoryId = parentCategoryId; // The category this item belongs to
    }
} 