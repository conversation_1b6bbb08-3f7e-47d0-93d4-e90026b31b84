package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.UUID;

@Schema(description = "客群管道資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSegmentDto {

    @Schema(description = "客群管道ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID segmentId;

    @Schema(description = "客群管道名稱", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255)
    @NotBlank(message = "客群管道名稱不得為空")
    @Size(max = 255, message = "客群管道名稱長度不得超過255個字符")
    private String segmentName;

    @Schema(description = "是否啟用 (true:啟用, false:禁用)")
    private Short isActive;

    @Schema(description = "排序順序")
    private Integer sequenceOrder;

    // Audit fields (read-only)
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID createBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String createByName;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime createTime;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID updateBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String updateByName;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime updateTime;
} 