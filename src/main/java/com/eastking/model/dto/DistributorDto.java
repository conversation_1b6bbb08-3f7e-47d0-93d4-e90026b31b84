package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.UUID;
import java.time.OffsetDateTime;

@Schema(description = "經銷商資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DistributorDto {

    @Schema(description = "經銷商唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID distributorId;

    @Schema(description = "經銷商名稱", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 255)
    @NotBlank(message = "經銷商名稱不得為空")
    @Size(max = 255)
    private String distributorName;

    @Schema(description = "ERP編號", maxLength = 100)
    @Size(max = 100)
    private String erpCode;

    @Schema(description = "備註")
    private String remarks;

    // Audit fields for display if needed
    @Schema(description = "建立時間", accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime createTime;

    @Schema(description = "更新時間", accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime updateTime;

    @Schema(description = "是否已刪除", accessMode = Schema.AccessMode.READ_ONLY)
    private Short isDeleted;
} 