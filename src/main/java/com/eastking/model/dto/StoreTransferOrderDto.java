package com.eastking.model.dto;

import com.eastking.enums.StoreTransferOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreTransferOrderDto {
    private UUID storeTransferOrderId;
    private String transferOrderNumber; // Read-only after creation

    @NotNull(message = "申請門市ID不得為空")
    private UUID requestingStoreId;
    private String requestingStoreName; // For display

    // requestingUserId is usually determined by the logged-in user, not directly in DTO for create
    private UUID requestingUserId; 
    private String requestingUserName; // For display

    @NotNull(message = "調撥門市ID不得為空")
    private UUID supplyingStoreId;
    private String supplyingStoreName; // For display

    private UUID dispatchingUserId;
    private String dispatchingUserName; // For display
    private UUID receivingUserId;
    private String receivingUserName; // For display

    private Short transferStatus; // Code from Enum
    private String statusDescription; // Text from Enum for display

    private OffsetDateTime requestDate;
    private OffsetDateTime dispatchDate;
    private OffsetDateTime receivedDate;
    private String notes;

    @Schema(description = "駁回原因", accessMode = Schema.AccessMode.READ_WRITE) // Can be set on reject, read on view
    private String rejectionReason; // Added

    @Valid
    @NotEmpty(message = "調撥單必須包含至少一個品項")
    private List<StoreTransferOrderItemDto> items;

    // Audit fields
    private UUID createBy;
    private OffsetDateTime createTime;
    private UUID updateBy;
    private OffsetDateTime updateTime;

    public void setTransferStatusEnum(StoreTransferOrderStatusEnum statusEnum) {
        if (statusEnum != null) {
            this.transferStatus = statusEnum.getCode();
            this.statusDescription = statusEnum.getDescription();
        }
    }
} 