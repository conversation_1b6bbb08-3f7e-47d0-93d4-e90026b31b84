package com.eastking.model.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductMenuItemDto {
    private UUID productMenuItemId; // Only for response

    @NotBlank(message = "商品條碼不能為空")
    private String productBarcode;

    private String productName; // For display, fetched based on barcode
    private Integer sortOrder;
} 