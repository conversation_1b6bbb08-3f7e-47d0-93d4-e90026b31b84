package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Schema(description = "贈品套裝資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GiftBundleDto {

    @Schema(description = "套裝ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID bundleId;

    @Schema(description = "套裝名稱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "套裝名稱不得為空")
    private String bundleName;

    @Schema(description = "主商品國際條碼", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "主商品條碼不得為空")
    private String mainProductBarcode;

    @Schema(description = "主商品名稱 (顯示用)", accessMode = Schema.AccessMode.READ_ONLY)
    private String mainProductName;

    @Schema(description = "套裝啟用時間", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "啟用時間不得為空")
    private OffsetDateTime startTime;

    @Schema(description = "套裝結束時間", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "結束時間不得為空")
    private OffsetDateTime endTime;

    @Schema(description = "是否啟用此套裝")
    private Boolean isActive = true;

    @Valid
    @NotEmpty(message = "必須至少指定一個贈品")
    @Schema(description = "贈品項目列表")
    private List<GiftBundleItemDto> items;

    // Audit fields
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID createBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String createByName;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime createTime;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID updateBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String updateByName;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime updateTime;
} 