package com.eastking.model.dto;

import com.eastking.enums.StorePurchaseOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StorePurchaseOrderDto {
    private UUID storePurchaseOrderId;
    private UUID storeId;
    private String storeName; // For display
    private String purchaseOrderNumber;
    private String originalWholesaleOrderId;
    private OffsetDateTime shipmentTime;
    private String createdByEmployeeId;
    private String createdByEmployeeName;
    private Short purchaseOrderStatus; // Code
    private String statusDescription; // Description from Enum
    private BigDecimal totalAmount;
    private BigDecimal taxAmount;
    private BigDecimal grandTotalAmount;
    private String orderNotes;
    private String shippingMethod;
    private String trackingNumber;
    private OffsetDateTime confirmedAt;
    private UUID confirmedByUserId;
    private String confirmedByUserName; // For display

    private List<StorePurchaseOrderItemDto> items;

    // Audit fields
    private UUID createBy;
    private OffsetDateTime createTime;
    private UUID updateBy;
    private OffsetDateTime updateTime;
    private Short isDeleted;

    public void setPurchaseOrderStatusEnum(StorePurchaseOrderStatusEnum statusEnum) {
        if (statusEnum != null) {
            this.purchaseOrderStatus = statusEnum.getCode();
            this.statusDescription = statusEnum.getDescription();
        }
    }
} 