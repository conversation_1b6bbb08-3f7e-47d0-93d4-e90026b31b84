package com.eastking.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * ERP 電動麻將桌庫存顯示 DTO
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErpMahjongTableStockDto {
    private String productBarcode;
    private String productName;
    private String productCategory; // From sm_product_setting, joined for display
    private String erpWarehouseCode;
    private String erpWarehouseName;
    private Integer quantity;
    private OffsetDateTime lastSyncTime; // The time this record was synced to our DB
} 