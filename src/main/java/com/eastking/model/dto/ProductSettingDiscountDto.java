package com.eastking.model.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSettingDiscountDto {
    private UUID productSettingDiscountId; // Only for response

    @NotNull(message = "角色ID不能為空")
    private UUID roleId;
    private String roleName; // For display on frontend, populated by service

    @NotNull(message = "折扣金額不能為空")
    @DecimalMin(value = "0.00", message = "折扣金額必須大於或等於0")
    private BigDecimal discountAmount;
} 