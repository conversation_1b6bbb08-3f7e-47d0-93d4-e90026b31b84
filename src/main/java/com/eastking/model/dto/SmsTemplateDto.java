package com.eastking.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.UUID;
import java.time.OffsetDateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 簡訊模板 DTO
 */
@Schema(description = "簡訊模板資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SmsTemplateDto {
    @Schema(description = "簡訊模板唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID smsTemplateId;

    @Schema(description = "模板名稱", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 100)
    @NotBlank(message = "模板名稱不得為空")
    @Size(max = 100, message = "模板名稱長度不得超過100個字符")
    private String templateName;

    @Schema(description = "模板類型 (例如: BIRTHDAY_GIFT, PROMOTION)", maxLength = 50)
    @Size(max = 50, message = "模板類型長度不得超過50個字符")
    private String templateType; // e.g., BIRTHDAY_GIFT, PROMOTION_GENERAL

    @Schema(description = "簡訊內容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "簡訊內容不得為空")
    private String smsContent;
    
    private Short isDeleted;
    private UUID createBy;
    private OffsetDateTime createTime;
    private UUID updateBy;
    private OffsetDateTime updateTime;
} 