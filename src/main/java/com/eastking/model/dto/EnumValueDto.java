package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "通用枚舉值資料傳輸物件")
@Data
@NoArgsConstructor
public class EnumValueDto {
    @Schema(description = "枚舉的代碼/值")
    private String value;

    @Schema(description = "枚舉的描述/顯示名稱")
    private String label;

    @Schema(description = "枚舉的數字ID (可選)")
    private Short code;

    public EnumValueDto(String value, String label) {
        this.value = value;
        this.label = label;
        // Code is not always needed, can be null.
    }

    public EnumValueDto(String value, String label, Object code) {
        this.value = value;
        this.label = label;
        if (code instanceof Number) {
            this.code = ((Number) code).shortValue();
        } else {
            this.code = null; // Or handle error
        }
    }
}