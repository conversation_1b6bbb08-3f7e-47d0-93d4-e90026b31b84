package com.eastking.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "盤點單資料傳輸物件")
public class InventoryCountSheetDto {

    @Schema(description = "盤點單ID")
    private UUID inventoryCountSheetId;

    @Schema(description = "盤點單號")
    private String sheetNumber;

    @Schema(description = "盤點門市ID")
    private UUID storeId;

    @Schema(description = "盤點門市名稱")
    private String storeName;

    @Schema(description = "公司別代碼")
    private Short companyDivisionCode;

    @Schema(description = "公司別描述")
    private String companyDivisionDescription;

    @Schema(description = "盤點日期")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private OffsetDateTime countDate;
    
    @Schema(description = "盤點月份 (格式: YYYY-MM)")
    private String countMonth;

    @Schema(description = "盤點單狀態代碼")
    private Short sheetStatusCode;

    @Schema(description = "盤點單狀態描述")
    private String sheetStatusDescription;

    @Schema(description = "審核狀態代碼")
    private Short approvalStatusCode;

    @Schema(description = "審核狀態描述")
    private String approvalStatusDescription;

    @Schema(description = "原盤點單ID")
    private UUID originalSheetId;
    
    @Schema(description = "原盤點單號")
    private String originalSheetNumber;

    @Schema(description = "初盤人員姓名")
    private String countedByUserName;

    @Schema(description = "複盤人員姓名")
    private String recountByUserName;

    @Schema(description = "審核人員姓名")
    private String approvedByUserName;
    
    @Schema(description = "審核時間")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private OffsetDateTime approvalTime;

    @Schema(description = "備註")
    private String remarks;

    @Schema(description = "建立時間")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private OffsetDateTime createTime;

    @Schema(description = "最後更新時間")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private OffsetDateTime updateTime;
    
    @Schema(description = "盤點品項")
    private List<InventoryCountItemDto> items;
} 