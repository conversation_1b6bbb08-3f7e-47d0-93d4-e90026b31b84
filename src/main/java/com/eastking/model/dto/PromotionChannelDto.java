package com.eastking.model.dto;

import com.eastking.enums.PromotionChannelTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Schema(description = "促銷活動適用管道資料")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromotionChannelDto {

    @Schema(description = "管道關聯ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID promoChannelId;

    @Schema(description = "管道類型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "管道類型不得為空")
    private PromotionChannelTypeEnum channelType;

    @Schema(description = "特定目標ID (例如門市ID)，如果管道類型需要")
    private String channelTargetId; // e.g., store_id from external system, or a predefined constant for ONLINE
    
    @Schema(description = "特定目標名稱 (顯示用)", accessMode = Schema.AccessMode.READ_ONLY)
    private String channelTargetName; // For display, populated by service
} 