package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import java.util.List;

@Schema(description = "技師獎金整體設定資料傳輸物件 (用於批量儲存)")
@Data
public class TechnicianBonusSetupDto {

    @Schema(description = "要保存或更新的獎金項目及其獎金設定")
    @NotEmpty
    @Valid
    private List<BonusItemDto> bonusItems; // Contains items, and each item contains its list of TechnicianBonusSettingDto

    // Potentially add a list of technician role IDs to configure if that's also part of the same "save all" operation
    // private List<UUID> configuredTechnicianRoleIds; 
} 