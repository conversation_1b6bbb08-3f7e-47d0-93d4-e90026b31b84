package com.eastking.model.dto;

import com.eastking.enums.AuditActionTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.OffsetDateTime;
import java.util.UUID;

@Schema(description = "操作歷程記錄資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuditLogDto {
    @Schema(description = "記錄唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID auditLogId;

    @Schema(description = "操作者帳號ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID userAccountId;

    @Schema(description = "操作者員工編號", accessMode = Schema.AccessMode.READ_ONLY)
    private String employeeId;

    @Schema(description = "操作者姓名", accessMode = Schema.AccessMode.READ_ONLY)
    private String userName;
    
    @Schema(description = "操作者所屬單位/部門名稱", accessMode = Schema.AccessMode.READ_ONLY)
    private String userDepartmentName;

    @Schema(description = "操作時間", accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime operationTime;

    @Schema(description = "資料種類 (例如: UserAccount, ProductSetting)", accessMode = Schema.AccessMode.READ_ONLY)
    private String dataType; // Will be mapped from AuditDataTypeEnum or a string

    @Schema(description = "被操作實體的ID或編號 (例如訂單號)", accessMode = Schema.AccessMode.READ_ONLY)
    private String entityIdStr;
    
    @Schema(description = "被操作實體的描述性名稱", accessMode = Schema.AccessMode.READ_ONLY)
    private String entityDescription;

    @Schema(description = "操作行為", accessMode = Schema.AccessMode.READ_ONLY)
    private AuditActionTypeEnum actionType;

    @Schema(description = "操作詳情 (JSON格式，例如修改前後的資料)", accessMode = Schema.AccessMode.READ_ONLY)
    private String detailsJson;
    
    @Schema(description = "客戶端IP地址", accessMode = Schema.AccessMode.READ_ONLY)
    private String clientIpAddress;

    // Fields for AuditLogService.logAction(...)
    @Schema(hidden = true) // Not for API response/request body directly, but for service method
    private String traceId; 
} 