package com.eastking.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * 商品交易明細 DTO (進貨/銷貨)
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductTransactionDetailDto {
    private OffsetDateTime transactionDate;
    private String transactionType; // e.g., "進貨", "銷貨"
    private Integer purchaseQuantity;
    private Integer salesQuantity;
    private String orderNumber; // Reference to Purchase Order or Sales Order number
    private String orderUrl; // Optional: Direct link to the order detail page
} 