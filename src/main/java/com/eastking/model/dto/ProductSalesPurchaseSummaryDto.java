package com.eastking.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 商品進銷匯總 DTO
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSalesPurchaseSummaryDto {
    private String productBarcode;
    private String productName;
    private String productCategory; // As per Figma design for the list view
    private Integer totalPurchaseQuantity;
    private Integer totalSalesQuantity;
    // private BigDecimal currentStock; // Consider if this is needed here or from a separate stock query
} 