package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreTransferOrderItemDto {
    private UUID storeTransferOrderItemId; // Read-only usually, for response
    private UUID storeTransferOrderId; // Context, usually set by service

    @NotBlank(message = "商品條碼不得為空")
    private String productBarcode;
    private String productName; // For display, can be populated by service

    @NotNull(message = "申請調撥數量不得為空")
    @Min(value = 1, message = "申請數量必須大於0")
    private Integer requestedQuantity;

    @Schema(description = "實際出庫數量 (調撥方填寫)", accessMode = Schema.AccessMode.READ_ONLY) // Usually set by specific action
    private Integer dispatchedQuantity;

    @Schema(description = "實際入庫數量 (申請方填寫)", accessMode = Schema.AccessMode.READ_ONLY) // Usually set by specific action
    private Integer receivedQuantity;
} 