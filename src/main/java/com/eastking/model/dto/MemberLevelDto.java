package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.UUID;
import java.time.OffsetDateTime;

/**
 * 會員等級資料 DTO
 */
@Schema(description = "會員等級福利設定資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberLevelDto {
    @Schema(description = "會員等級唯一識別ID", accessMode = Schema.AccessMode.READ_ONLY)
    private UUID memberLevelId;

    @Schema(description = "等級名稱 (例如: 一般會員, 進階會員)", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 50)
    @NotBlank(message = "等級名稱不得為空")
    @Size(max = 50, message = "等級名稱長度不得超過50個字符")
    private String levelName;

    @Schema(description = "等級暱稱 (例如: 賭俠, 賭王)", maxLength = 50)
    @Size(max = 50, message = "等級暱稱長度不得超過50個字符")
    private String levelNickname;

    @Schema(description = "顯示排序順序")
    private Integer sequenceOrder;

    // Maintenance Conditions
    @Schema(description = "維持條件-年限 (單位:年)")
    private Integer maintenancePeriodYears;
    @Schema(description = "維持條件-累積消費金額 (單位:元)")
    private BigDecimal maintenanceAmount; // Assuming actual amount

    // Discount Coupon Issuance
    @Schema(description = "折扣券發放是否啟用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "折扣券啟用狀態不得為空")
    private Boolean discountCouponEnabled;
    @Schema(description = "折扣券-消費金額門檻 (單位:元)")
    private BigDecimal discountCouponThresholdAmount;
    @Schema(description = "折扣券-折扣金額 (單位:元)")
    private BigDecimal discountCouponDiscountAmount;
    @Schema(description = "折扣券-使用期限 (單位:天)")
    private Integer discountCouponValidityDays;

    // Birthday Gift
    @Schema(description = "生日禮是否啟用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "生日禮啟用狀態不得為空")
    private Boolean birthdayGiftEnabled;
    @Schema(description = "生日禮商品項目ID (未來可關聯商品表)")
    private UUID birthdayGiftItemId; // Could be a product/item ID from another table
    @Schema(description = "生日禮商品/內容描述", maxLength = 255)
    @Size(max = 255, message = "生日禮描述長度不得超過255個字符")
    private String birthdayGiftItemDescription;

    // Additional Perk
    @Schema(description = "額外福利是否啟用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "額外福利啟用狀態不得為空")
    private Boolean additionalPerkEnabled;
    @Schema(description = "額外福利名稱", maxLength = 100)
    @Size(max = 100, message = "福利名稱長度不得超過100個字符")
    private String additionalPerkName;
    @Schema(description = "額外福利-消費金額門檻 (單位:元)")
    private BigDecimal additionalPerkThresholdAmount;
    @Schema(description = "額外福利-折扣金額 (單位:元)")
    private BigDecimal additionalPerkDiscountAmount;

    // Audit fields (usually not part of request DTO for create/update, but can be in response)
    @Schema(description = "是否已刪除", accessMode = Schema.AccessMode.READ_ONLY)
    private Short isDeleted;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID createBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime createTime;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private UUID updateBy;
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private OffsetDateTime updateTime;
} 