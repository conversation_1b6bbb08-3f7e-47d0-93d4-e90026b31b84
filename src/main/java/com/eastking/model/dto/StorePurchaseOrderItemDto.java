package com.eastking.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StorePurchaseOrderItemDto {
    private UUID storePurchaseOrderItemId;
    private UUID storePurchaseOrderId; // For context, usually set by service

    @NotBlank(message = "商品條碼不得為空")
    private String productBarcode;
    private String productName; // Display, can be populated by service
    private String warehouseCode;

    @NotNull(message = "訂購數量不得為空")
    @Min(value = 1, message = "訂購數量必須大於0")
    private Integer orderedQuantity;

    @Min(value = 0, message = "收到數量不得為負數")
    private Integer receivedQuantity;

    @NotNull(message = "商品單價不得為空")
    @DecimalMin(value = "0.00", message = "商品單價不得為負數")
    private BigDecimal unitPrice;

    private BigDecimal itemSubtotal; // Usually calculated
    private String itemNotes;
} 