package com.eastking.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "訂單服務項目詳細資料")
public class OrderItemGroupDto {
    private UUID orderItemGroupId;
    private UUID orderItemId;
    private String productBarcode;
    private String productName;
    private UUID warehouseId;
    private String warehouseName;
    private Short requiresDispatch;
    private String awaitingMaterials;
    private Short itemTypeCode;
    private Integer quantity;
    private BigDecimal unitPrice;
    private BigDecimal listPrice;
    private BigDecimal subtotalAmount;
    private Short isAwait;
} 