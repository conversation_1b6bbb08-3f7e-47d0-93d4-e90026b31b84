package com.eastking.model.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.UUID;

@Data
public class DispatchRepairItemDto {
    private UUID id;
    private UUID orderItemId;
    private String deviceSerialNumber;
    private String productModel;
    private Short warrantyStatusCode;
    private String warrantyStatusDescription;
    private String issueDescription;
    private Boolean isMain;
    private String productBarcode;
    private String productName;
    private String warehouseCode;
    private Short itemTypeCode;
    private String itemTypeDescription;
    private Short isAddon;
    private Integer quantity;
    private BigDecimal unitPrice;
    private BigDecimal subtotalAmount;
} 