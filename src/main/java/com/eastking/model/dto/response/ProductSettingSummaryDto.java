package com.eastking.model.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSettingSummaryDto {
    private UUID productSettingId;
    private String productBarcode;
    private String productName;
    private BigDecimal salePrice;
    private Integer stockQuantity;
} 