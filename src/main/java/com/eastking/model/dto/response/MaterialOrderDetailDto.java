package com.eastking.model.dto.response;

import com.eastking.model.dto.request.MaterialOrderItemRequestDto;
import lombok.Builder;
import lombok.Data;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
public class MaterialOrderDetailDto {
    private UUID materialOrderId;
    private String materialOrderNumber;
    private String requestingTechnicianName;
    private String targetWarehouseName;
    private Short materialOrderStatusCode;
    private String materialOrderStatusDescription;
    private String pickerName;
    private OffsetDateTime pickedTime;
    private OffsetDateTime collectedTime;
    private String remarks;
    private OffsetDateTime createTime;
    private List<MaterialOrderItemDto> items;
} 