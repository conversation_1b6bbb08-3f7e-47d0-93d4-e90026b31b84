package com.eastking.model.dto.response;

import com.eastking.enums.OrderTypeEnum;
import com.eastking.enums.OrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "訂單摘要回應物件，用於列表顯示")
public class OrderSummaryDto {

    @Schema(description = "訂單ID")
    private UUID orderId;

    @Schema(description = "訂單號碼")
    private String orderNumber;

    @Schema(description = "公司別代碼 (0:東方不敗, 1:雀友)")
    private Short companyDivisionCode;
    @Schema(description = "公司別")
    private String companyDivisionDescription;

    @Schema(description = "訂單類型代碼")
    private Short orderTypeCode;
    @Schema(description = "訂單類型")
    private String orderTypeDescription;

    @Schema(description = "訂單日期")
    private OffsetDateTime orderDate;

    @Schema(description = "訂單狀態代碼")
    private Short orderStatusCode;
    @Schema(description = "訂單狀態")
    private String orderStatusDescription;

    @Schema(description = "稅金類型代碼")
    private Short taxType;
    @Schema(description = "稅金類型描述")
    private String taxTypeDescription;

    @Schema(description = "顧客姓名")
    private String customerName;

    @Schema(description = "顧客電話")
    private String customerPhone;
    
    @Schema(description = "銷售門市名稱")
    private String storeName;

    @Schema(description = "應付總金額")
    private BigDecimal grandTotalAmount;

    @Schema(description = "主要商品名稱 (簡化顯示)")
    private String mainProductName;

    @Schema(description = "最後更新時間")
    private OffsetDateTime updateTime;

    @Schema(description = "最後更新人員姓名")
    private String updatedByName;
} 