package com.eastking.model.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@Builder
public class RoleSummaryResponse {
    private UUID roleId;
    private String roleCode;
    private String roleName;
    private String roleDescription;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private OffsetDateTime updateTime; // For display in list
    private String updateByName; // For display in list
} 