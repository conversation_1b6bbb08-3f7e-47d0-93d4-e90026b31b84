package com.eastking.model.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY) // Include non-empty children lists
public class UserMenuDto {
    private String code; // function_code
    private String name; // function_name
    private String type; // Based on function_type (GROUP or OPERATION)
    private String path; // frontend_path
    private String icon; // icon_class
    private Integer sequenceOrder;
    private List<UserMenuDto> children;
} 