package com.eastking.model.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.util.UUID;

@Data
public class RepairOrderItemDto {
    private UUID repairOrderItemId;
    private String productBarcode;
    private String productName;
    private Short itemTypeCode;
    private String itemTypeDescription;
    private Integer quantity;
    private BigDecimal unitPrice;
    private BigDecimal subtotalAmount;
} 