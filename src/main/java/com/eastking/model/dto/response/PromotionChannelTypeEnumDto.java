package com.eastking.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Schema(description = "促銷管道類型資料傳輸物件")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PromotionChannelTypeEnumDto {
    @Schema(description = "管道類型代碼")
    public String code;
    @Schema(description = "管道類型描述")
    public String description;
    @Schema(description = "是否允許選擇特定目標")
    public boolean allowTargetSelection;
    @Schema(description = "特定目標輸入框提示文字")
    public String targetPlaceholder;
} 