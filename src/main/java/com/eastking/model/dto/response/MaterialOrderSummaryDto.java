package com.eastking.model.dto.response;

import lombok.Builder;
import lombok.Data;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
@Builder
public class MaterialOrderSummaryDto {
    private UUID materialOrderId;
    private String materialOrderNumber;
    private OffsetDateTime createTime;
    private String warehouseName;
    private Short statusCode;
    private String statusDescription;
} 