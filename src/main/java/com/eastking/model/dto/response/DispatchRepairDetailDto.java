package com.eastking.model.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;
import com.eastking.model.dto.response.OrderItemDto;
import com.eastking.model.dto.OrderPaymentDto;
import com.eastking.model.dto.response.DispatchCollaboratorDto;

@Data
public class DispatchRepairDetailDto {
    private UUID id;
    private String number;
    private Short typeCode;
    private String typeDescription;
    private Short statusCode;
    private String statusDescription;
    private Short isUrgent;
    private UUID customerId;
    private String customerName;
    private String customerPhone;
    private String installationAddress;
    private UUID customerDeviceId;
    private String handlingMethod;
    private String followUpAction;
    private LocalDate scheduledDate;
    private String scheduledTimeSlot;
    private UUID assignedTechnicianId;
    private String assignedTechnicianName;
    private List<DispatchCollaboratorDto> collaborators;
    private BigDecimal totalAmount;
    private BigDecimal paidAmount;
    private BigDecimal productsTotalAmount;
    private BigDecimal discountAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;
    private BigDecimal grandTotalAmount;
    private Short refundMethodCode;
    private String refundMethodDescription;
    private String remarks;
    private String originalOrderRemarks;
    private OffsetDateTime createTime;
    private OffsetDateTime closedTime;
    private List<DispatchRepairItemDto> items;
    private List<DispatchTechRecordDto> techRecords;
    private List<OrderItemDto> repairedParts;
    private List<OrderPaymentDto> paymentDetails;
    private BigDecimal technicianCollectionAmount;
    private BigDecimal originalOrderTaxAmount;
    private BigDecimal otherDiscount;
} 