package com.eastking.model.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.OffsetDateTime; // Ensuring OffsetDateTime is imported

import java.math.BigDecimal;
import java.util.UUID;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "訂單品項詳細回應物件")
public class OrderItemDto {

    @Schema(description = "訂單品項ID")
    private UUID orderItemId;

    @Schema(description = "關聯的父品項ID (若此為贈品/加購品)")
    private UUID parentOrderItemId;

    @Schema(description = "商品國際條碼")
    private String productBarcode;

    @Schema(description = "商品名稱")
    private String productName;

    @Schema(description = "數量")
    private Integer quantity;

    @Schema(description = "商品原始單價 (未稅)")
    private BigDecimal unitPrice;

    @Schema(description = "商品原價/表定價")
    private BigDecimal listPrice;

    @Schema(description = "品項折扣率 (例如0.1表示9折)")
    private BigDecimal discountRate;

    @Schema(description = "單品項固定折扣金額")
    private BigDecimal discountAmountPerItem;

    @Schema(description = "折扣後單價 (未稅)")
    private BigDecimal finalPricePerItem;

    @Schema(description = "品項小計 (未稅)")
    private BigDecimal subtotalAmount;

    @Schema(description = "品項類型代碼")
    private Short itemTypeCode;
    @Schema(description = "品項類型描述")
    private String itemTypeDescription;

    @Schema(description = "店取、派工、自載展示機")
    private Short requiresDispatch;

    @Schema(description = "品項備註")
    private String itemNotes;

    @Schema(description = "分錄備註 (批發訂單項目特定備註)")
    private String itemRemark;

    @Schema(description = "出貨倉庫代碼")
    private String warehouseCode;
    @Schema(description = "出貨倉庫名稱")
    private String warehouseName;

    @Schema(description = "電動桌機身號碼")
    private String mahjongTableSerialNumber;

    // Fields from BaseEntity for display purposes
    @Schema(description = "建立時間")
    private OffsetDateTime createTime;
    @Schema(description = "最後更新時間")
    private OffsetDateTime updateTime;
    @Schema(description = "建立人員姓名")
    private String createdByName;
    @Schema(description = "更新人員姓名")
    private String updatedByName;

    private BigDecimal originalGiftTotal;
    private BigDecimal exchangedGiftTotal;

    private String mainProductName; // To group repaired parts

    private List<OrderItemGroupDto> itemGroups;
} 