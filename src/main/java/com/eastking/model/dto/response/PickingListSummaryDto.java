package com.eastking.model.dto.response;

import lombok.Data;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
public class PickingListSummaryDto {
    private UUID id;
    private String pickingOrderNumber; // 揀料單號 (領料單號)
    private OffsetDateTime requestDate; // 申請日期
    private String requesterName; // 申請人
    private String technicianName; // 技師
    private Integer totalItemTypes; // 品項
    private Integer totalItemQuantity; // 總數量
    private String statusDescription; // 狀態
    private Short statusCode;
} 