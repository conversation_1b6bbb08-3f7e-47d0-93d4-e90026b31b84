package com.eastking.model.dto.response;

import lombok.Builder;
import lombok.Data;
import java.util.UUID;

@Data
@Builder
public class MaterialOrderItemDto {
    private UUID materialOrderItemId;
    private UUID dispatchRepairItemId;
    private String dispatchRepairNumber;
    private String productBarcode;
    private String productName;
    private Integer requestedQuantity;
    private Integer pickedQuantity;
    private Integer collectedQuantity;
} 