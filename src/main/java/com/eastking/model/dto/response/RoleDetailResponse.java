package com.eastking.model.dto.response;

import com.eastking.model.dto.request.RolePermissionDataRequest; // Re-use nested DTOs
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RoleDetailResponse {
    private UUID roleId;
    private String roleCode;
    private String roleName;
    private String roleDescription;
    private List<RolePermissionDataRequest.FunctionPermissionDto> functionPermissions;
    private List<RolePermissionDataRequest.FieldPermissionDto> fieldPermissions;
    private List<String> regionCodes; // List of RegionEnum codes
    private List<String> regionDescriptions; // Descriptions for display

    // Audit Info - can be expanded from BaseEntity if needed for display
    private UUID createBy;
    private String createByName;
    private String createTime;
    private UUID updateBy;
    private String updateByName;
    private String updateTime;
} 