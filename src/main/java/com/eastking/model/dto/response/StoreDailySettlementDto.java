package com.eastking.model.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
public class StoreDailySettlementDto {
    private UUID storeDailySettlementId;
    private UUID storeId;
    private String storeName;
    private LocalDate settlementDate;
    private Short settlementStatusCode;
    private String settlementStatus;
    private BigDecimal totalSalesAmount;
    private BigDecimal totalReturnsAmount;
    private BigDecimal totalRevenueAmount;
    private BigDecimal totalExpenditureAmount;
    private BigDecimal cashIncomeAmount;
    private BigDecimal creditCardIncomeAmount;
    private BigDecimal remittanceIncomeAmount;
    private BigDecimal accountsReceivableAmount;
    private BigDecimal receivableRepaymentAmount;
    private BigDecimal actualCashIncomeAmount;
    private BigDecimal cashDiscrepancyAmount;
    private BigDecimal totalRepaymentAmount;
    private UUID settlementById;
    private String settlementByName;
    private String remarks;
    private List<StoreJournalEntryDto> salesDetails;
    private List<StoreJournalEntryDto> returnDetails;
    private List<ExpenditureDetailDto> expenditureDetails;
} 