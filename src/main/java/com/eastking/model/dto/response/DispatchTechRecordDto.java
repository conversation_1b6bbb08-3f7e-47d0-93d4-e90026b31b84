package com.eastking.model.dto.response;

import lombok.Data;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
public class DispatchTechRecordDto {
    private UUID recordId;
    private Short statusCode;
    private String statusDescription;
    private UUID technicianId;
    private String technicianName;
    private Short recordType;
    private String recordTypeDescription;
    private String record1;
    private String record2;
    private String record3;
    private String record4;
    private String record5;
    private OffsetDateTime createTime;
} 