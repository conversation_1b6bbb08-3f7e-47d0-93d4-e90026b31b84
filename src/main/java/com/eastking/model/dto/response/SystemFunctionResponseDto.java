package com.eastking.model.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SystemFunctionResponseDto {
    private UUID id;
    private String code;
    private String name;
    private String parentCode;
    private Short functionType;
    private Short isShow;
    private List<String> availablePermissions;
    private List<SystemFunctionResponseDto> children;
} 