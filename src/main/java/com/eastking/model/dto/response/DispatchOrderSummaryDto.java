package com.eastking.model.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchOrderSummaryDto {
    private UUID dispatchOrderId;
    private String dispatchOrderNumber;
    private Short dispatchTypeCode;
    private String dispatchTypeDescription;
    private Short dispatchStatusCode;
    private String dispatchStatusDescription;
    private String customerName;
    private LocalDate scheduledDate;
    private String assignedTechnicianName;
    private Short isUrgent;
} 