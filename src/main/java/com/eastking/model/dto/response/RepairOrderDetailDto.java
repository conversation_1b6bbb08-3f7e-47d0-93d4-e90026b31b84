package com.eastking.model.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
public class RepairOrderDetailDto {
    private UUID repairOrderId;
    private String repairOrderNumber;
    private UUID customerDeviceId;
    private UUID customerId;
    private String customerName; // Inferred
    private String contactName;
    private String contactPhone;
    private String contactAddress;
    private Short repairStatusCode;
    private String repairStatusDescription; // Inferred
    private Short repairTypeCode;
    private String repairTypeDescription; // Inferred
    private LocalDate reportDate;
    private LocalDate repairDate;
    private UUID assignedTechnicianId;
    private String assignedTechnicianName; // Inferred
    private Short warrantyStatusCode;
    private String warrantyStatusDescription; // Inferred
    private String issueDescription;
    private String handlingMethod;
    private String followUpAction;
    private BigDecimal totalAmount;
    private BigDecimal paidAmount;
    private String remarks;
    private List<RepairOrderItemDto> items; // Inferred
} 