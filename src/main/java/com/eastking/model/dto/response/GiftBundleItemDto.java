package com.eastking.model.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GiftBundleItemDto {
    private UUID giftBundleItemId;
    private String giftProductBarcode;
    private String giftProductName;
    private Integer quantity;
    private BigDecimal listPrice;
    private BigDecimal salePrice;
} 