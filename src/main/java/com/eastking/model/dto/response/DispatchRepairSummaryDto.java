package com.eastking.model.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchRepairSummaryDto {

    private UUID dispatchRepairId;
    private String dispatchRepairNumber;
    private boolean isCollaboration; // 新增：是否為協同派工
    private String mainTechnicianName; // 新增：主要技師姓名，用於協同派工顯示

    // Type and Status
    private Short typeCode;
    private String typeDescription;
    private Short statusCode;
    private String statusDescription;
    private Short isUrgent;

    // Basic Info
    private String customerName;
    private LocalDate scheduledDate;
    private String assignedTechnicianName;
} 