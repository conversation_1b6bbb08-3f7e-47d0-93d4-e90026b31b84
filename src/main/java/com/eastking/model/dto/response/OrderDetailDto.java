package com.eastking.model.dto.response;

import com.eastking.model.dto.request.PaymentDetailDto;
import com.eastking.model.dto.OrderPaymentDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Schema(description = "訂單完整詳細回應物件")
public class OrderDetailDto {

    @Schema(description = "訂單ID")
    private UUID orderId;

    @Schema(description = "訂單號碼")
    private String orderNumber;

    @Schema(description = "公司別代碼")
    private Short companyDivisionCode;
    @Schema(description = "公司別描述")
    private String companyDivisionDescription;

    @Schema(description = "訂單類型代碼")
    private Short orderTypeCode;
    @Schema(description = "訂單類型描述")
    private String orderTypeDescription;

    @Schema(description = "操作者類型代碼")
    private Short operatorTypeCode;
    @Schema(description = "操作者類型描述")
    private String operatorTypeDescription;

    @Schema(description = "銷售/開單門市ID")
    private UUID storeId;
    @Schema(description = "銷售/開單門市名稱")
    private String storeName;

    @Schema(description = "開單人員ID")
    private UUID userAccountId;
    @Schema(description = "開單人員員工編號")
    private String userEmployeeId;
    @Schema(description = "開單人員姓名")
    private String userName;

    @Schema(description = "顧客姓名")
    private String customerName;
    @Schema(description = "顧客電話")
    private String customerPhone;
    @Schema(description = "會員ID")
    private UUID memberId;
    @Schema(description = "會員等級名稱")
    private String memberLevelName;

    @Schema(description = "訂單日期")
    private OffsetDateTime orderDate;
    @Schema(description = "訂單狀態代碼")
    private Short orderStatusCode;
    @Schema(description = "訂單狀態描述")
    private String orderStatusDescription;

    // 金額相關
    @Schema(description = "商品原總金額 (未稅)")
    private BigDecimal productsTotalAmount;
    @Schema(description = "折扣總金額")
    private BigDecimal discountAmount;
    @Schema(description = "折扣後金額 (未稅)")
    private BigDecimal netAmount;
    @Schema(description = "稅金")
    private BigDecimal taxAmount;
    @Schema(description = "應付總金額 (含稅)")
    private BigDecimal grandTotalAmount;
    @Schema(description = "已付金額")
    private BigDecimal paidAmount;
    @Schema(description = "付款狀態代碼")
    private Short paymentStatusCode;
    @Schema(description = "付款狀態描述")
    private String paymentStatusDescription;

    // 發票相關
    @Schema(description = "發票類型代碼")
    private Short invoiceTypeCode;
    @Schema(description = "發票類型描述")
    private String invoiceTypeDescription;

    @Schema(description = "稅金類型代碼")
    private Short taxType;
    @Schema(description = "稅金類型描述")
    private String taxTypeDescription;
    @Schema(description = "發票號碼")
    private String invoiceNumber;
    @Schema(description = "統一編號")
    private String taxIdNumber;
    @Schema(description = "發票抬頭")
    private String invoiceCompanyTitle;
    @Schema(description = "發票日期")
    private LocalDate invoiceDate;
    @Schema(description = "發票金額")
    private BigDecimal invoiceAmount;
    @Schema(description = "發票地址")
    private String invoiceAddress;
    @Schema(description = "是否已寄送發票")
    private Boolean isInvoiceSent;

    @Schema(description = "開立發票金額(門市)")
    private BigDecimal storeInvoiceAmount;

    @Schema(description = "門市發票抬頭")
    private String storeInvoiceCompanyTitle;

    @Schema(description = "門市發票統編")
    private String storeTaxIdNumber;

    // 派工相關
    @Schema(description = "裝機聯絡人")
    private String contactName;
    @Schema(description = "裝機聯絡電話")
    private String contactPhone;
    @Schema(description = "裝機地址")
    private String installationAddress;

    // 結構化安裝地址
    private String installationAddressCity;
    private String installationAddressDistrict;
    private String installationAddressStreet;
    private String installationAddressLane;
    private String installationAddressAlley;
    private String installationAddressNumber;
    private String installationAddressFloor;
    private String installationAddressUnit;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate installationDate;
    @Schema(description = "預計安裝時段")
    private String installationTimeSlot;
    @Schema(description = "派工技師ID")
    private UUID technicianId;
    @Schema(description = "派工技師姓名")
    private String technicianName;
    @Schema(description = "派工狀態代碼")
    private Short dispatchStatusCode;
    @Schema(description = "派工狀態描述")
    private String dispatchStatusDescription;
    
    @Schema(description = "套用的優惠活動ID")
    private UUID promotionId;
    @Schema(description = "套用的優惠活動名稱")
    private String promotionName;

    @Schema(description = "派工備註")
    private String dispatchNotes;
    @Schema(description = "預計完工日")
    private LocalDate expectedCompletionDate;
    @Schema(description = "實際完工時間")
    private OffsetDateTime actualCompletionDate;

    @Schema(description = "訂單整體備註")
    private String remarks;
    @Schema(description = "來源訂單ID (例如換貨時)")
    private UUID sourceOrderId;
    @Schema(description = "來源訂單號碼")
    private String sourceOrderNumber;

    @Schema(description = "訂單品項列表")
    private List<OrderItemDto> items;
    
    @Schema(description = "付款明細")
    private List<OrderPaymentDto> paymentDetails;

    @Schema(description = "建立時間")
    private OffsetDateTime createTime;
    @Schema(description = "最後更新時間")
    private OffsetDateTime updateTime;
    @Schema(description = "建立人員姓名")
    private String createdByName;
    @Schema(description = "最後更新人員姓名")
    private String updatedByName;
    @Schema(description = "是否已刪除")
    private Boolean isDeleted;

    @Schema(description = "最新的退貨單ID")
    private UUID latestRefundId;

    @Schema(description = "當前使用者是否可以審核此派工訂單")
    private boolean canApproveDispatch;
} 