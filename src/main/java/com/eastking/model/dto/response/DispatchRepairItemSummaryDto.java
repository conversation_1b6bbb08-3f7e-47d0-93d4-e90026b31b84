package com.eastking.model.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DispatchRepairItemSummaryDto {
    // From DispatchRepairItem
    private UUID dispatchRepairItemId;
    private String productBarcode;
    private String productName;
    private Integer quantity; // 派工數量

    // From DispatchRepair (parent)
    private UUID dispatchRepairId;
    private String dispatchRepairNumber; // 派工單號
    private LocalDate scheduledDate; // 派工日期

    // From ProductSetting (related to item)
    private String warehouseName; // 倉庫 (假設從商品主檔來)

    // Calculated
    private Integer pickedQuantity; // 已領數量
    private Integer alreadyRequestedQuantity; // 預領數量
} 