package com.eastking.model.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.UUID;

/**
 * 登入回應 DTO
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoginResponse {
    private String token;
    private UUID sessionUuid; // 新的 UUID，用於後續權限驗證
    private String userName;
    private List<String> roles;
    private Boolean requiresStoreSelection; // 是否需要選擇門市
    private List<StoreInfo> availableStores; // 可選擇的門市列表 (如果需要選擇)
    private StoreInfo selectedStore; // 已選擇或預設的門市資訊
    private String message; // For errors or additional info
    private List<String> functionPermissions; // Added: Granular function permissions

    // 內嵌類別用於門市資訊
    @Data
    @Builder
    public static class StoreInfo {
        private String storeId;
        private String storeName;
        private UUID regionId;
        private String regionName;
        private Short companyDivisionCode;
        private String companyDivisionName;
    }
} 