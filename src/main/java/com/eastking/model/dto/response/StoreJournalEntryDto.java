package com.eastking.model.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
public class StoreJournalEntryDto {
    private UUID storeDailyJournalId;
    private String transactionTypeName;
    private String sourceDocumentNumber;
    private String customerName;
    private String productName;
    private BigDecimal amount;
    private OffsetDateTime createTime;
} 