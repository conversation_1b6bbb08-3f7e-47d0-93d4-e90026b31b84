package com.eastking.model.dto.response;

import com.eastking.model.dto.CustomerDto; // For wholesale customer details
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@SuperBuilder // Enables builder pattern for inheritance
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "批發訂單詳細回應物件")
public class WholesaleOrderDetailDto extends OrderDetailDto { // Extends OrderDetailDto to inherit common fields

    @Schema(description = "批發客戶ID")
    private UUID wholesaleCustomerId;

    @Schema(description = "批發客戶ERP代碼")
    private String wholesaleCustomerErpCode;

    @Schema(description = "批發客戶名稱 (收貨對象)")
    private String wholesaleCustomerName;
    
    @Schema(description = "批發客戶聯絡人")
    private String wholesaleCustomerContactPerson;

    @Schema(description = "批發客戶電話")
    private String wholesaleCustomerPhone;

    @Schema(description = "批發客戶地址")
    private String wholesaleCustomerAddress;

    @Schema(description = "預計出貨日期")
    private LocalDate expectedShipmentDate;

    @Schema(description = "出貨方式代碼")
    private Short shipmentMethodCode;
    @Schema(description = "出貨方式描述")
    private String shipmentMethodDescription;

    @Schema(description = "物流業者名稱")
    private String logisticsProviderName;

    @Schema(description = "物流件數")
    private Integer logisticsShipmentCount;

    @Schema(description = "貨車車號")
    private String truckLicensePlate;

    @Schema(description = "物流單號")
    private String logisticsTrackingNumber;

    @Schema(description = "實際完成日期")
    private OffsetDateTime actualCompletionDate;
    
    @Schema(description = "訂單項目")
    private List<OrderItemDto> items;

    // Potentially hide fields from OrderDetailDto that are not relevant or differently represented for wholesale
    // For example, if memberId/memberLevelName/customerName/customerPhone from OrderDetailDto are not used
    // because wholesale customer info is now primary.

    @Schema(hidden = true)
    @Override
    public UUID getMemberId() { return super.getMemberId(); }

    @Schema(hidden = true)
    @Override
    public String getMemberLevelName() { return super.getMemberLevelName(); }

    // customerName and customerPhone from OrderDetailDto might still be used for general display if populated
    // but primary customer info for wholesale orders comes from wholesaleCustomer fields.
} 