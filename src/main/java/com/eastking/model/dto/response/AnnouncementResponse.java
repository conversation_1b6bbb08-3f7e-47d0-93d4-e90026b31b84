package com.eastking.model.dto.response;

import com.eastking.model.dto.request.AnnouncementTargetDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
public class AnnouncementResponse {

    private UUID announcementId;
    private Integer categoryCode;
    private String categoryDescription;
    private String title;
    private String content;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ssXXX")
    private OffsetDateTime startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ssXXX")
    private OffsetDateTime endTime;

    private Boolean isImportant;
    private Boolean isEnabled;
    private List<AnnouncementTargetDto> targets;

    private UUID createBy;
    private String createByName; // To be populated in service layer
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ssXXX")
    private OffsetDateTime createTime;

    private UUID updateBy;
    private String updateByName; // To be populated in service layer
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ssXXX")
    private OffsetDateTime updateTime;
} 