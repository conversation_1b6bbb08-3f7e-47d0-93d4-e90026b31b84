package com.eastking.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import java.util.List;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {

    private int code;
    private String message;
    private T data;
    private PageDetails pageDetails; // For paginated responses

    public ApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ApiResponse(int code, String message, T data, Page<?> page) {
        this.code = code;
        this.message = message;
        this.data = data; // This will contain the list of items
        if (page != null) {
            this.pageDetails = new PageDetails(
                page.getTotalElements(),
                page.getNumber(), // 0-indexed from Spring Page
                page.getSize(),
                page.getTotalPages()
            );
        }
    }

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(HttpStatus.OK.value(), "success", data);
    }
    
    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(HttpStatus.OK.value(), message, data);
    }

    public static <T> ApiResponse<PageData<T>> success(Page<T> pageData) {
        List<T> content = pageData.getContent();
        PageDetails details = new PageDetails(
            pageData.getTotalElements(), 
            pageData.getNumber(), 
            pageData.getSize(), 
            pageData.getTotalPages()
        );
        return new ApiResponse<>(HttpStatus.OK.value(), "success", new PageData<>(content, details));
    }

    public static <T> ApiResponse<T> created(T data) {
        return new ApiResponse<>(HttpStatus.CREATED.value(), "created", data);
    }

    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null);
    }

    @Data
    @NoArgsConstructor
    public static class PageData<T> {
        private List<T> list;
        private PageDetails page;

        public PageData(List<T> list, PageDetails pageDetails) {
            this.list = list;
            this.page = pageDetails;
        }

        public static <T> PageData<T> fromPage(Page<T> springPage) {
            PageDetails details = new PageDetails(
                springPage.getTotalElements(),
                springPage.getNumber(),
                springPage.getSize(),
                springPage.getTotalPages()
            );
            return new PageData<>(springPage.getContent(), details);
        }
    }

    @Data
    @NoArgsConstructor
    public static class PageDetails {
        private long total;
        private int page; // current page (0-indexed from Spring, or 1-indexed if adjusted)
        private int pageSize;
        private int totalPages;

        public PageDetails(long total, int page, int pageSize, int totalPages) {
            this.total = total;
            this.page = page; // Keep as is from Spring Page (0-indexed) or +1 for 1-indexed display
            this.pageSize = pageSize;
            this.totalPages = totalPages;
        }
    }
} 