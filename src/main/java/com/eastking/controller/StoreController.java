package com.eastking.controller;

import com.eastking.model.dto.StoreDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.StoreService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;

import java.util.List;
import java.util.UUID;

@Tag(name = "4. 門市管理 (Store Management)", description = "管理門市、地區、門市人員及統編抬頭等相關 API")
@RestController
@RequestMapping("/api/v1/stores")
@RequiredArgsConstructor
public class StoreController {

    private final StoreService storeService;

    @Operation(summary = "新增門市", description = "建立新的門市資料，包含其所屬地區、人員及統編配置。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "門市建立成功", 
            content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = ApiResponse.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "無效的請求資料")
    })
    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00040_CREATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.STORE_SETTING)
    public ResponseEntity<ApiResponse<StoreDto>> createStore(@Valid @RequestBody StoreDto storeDto) {
        StoreDto createdStore = storeService.createStore(storeDto);
        return new ResponseEntity<>(ApiResponse.created(createdStore), HttpStatus.CREATED);
    }

    @Operation(summary = "取得門市詳情", description = "依照門市ID取得特定門市的完整資訊。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功取得門市資料"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "找不到指定的門市")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00040_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<StoreDto>> getStoreById(@Parameter(description = "門市ID") @PathVariable UUID id) {
        return storeService.getStoreById(id)
            .map(dto -> ResponseEntity.ok(ApiResponse.success(dto)))
            .orElse(new ResponseEntity<>(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "Store not found"), HttpStatus.NOT_FOUND));
    }

    @Operation(summary = "查詢門市列表 (分頁)", description = "根據查詢條件取得門市列表，結果分頁。")
    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00040_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<StoreDto>>> getAllStores(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @Parameter(description = "頁碼 (0-indexed)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每頁筆數") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序欄位與方向 (eg: storeName,asc)") @RequestParam(defaultValue = "storeName,asc") String[] sort,
            @Parameter(description = "門市名稱關鍵字") @RequestParam(required = false) String storeName,
            @Parameter(description = "地區ID") @RequestParam(required = false) UUID regionId,
            @Parameter(description = "啟用狀態 (true/false)") @RequestParam(required = false) Boolean isActive) {
        Sort.Direction direction = sort.length > 1 && sort[1].equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sort[0]));
        Page<StoreDto> storesPage = storeService.getAllStores(pageable, storeName, regionId, isActive, companyContext);
        return ResponseEntity.ok(ApiResponse.success(storesPage));
    }

    @Operation(summary = "更新門市資料", description = "依照門市ID更新現有的門市資料。")
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00040_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.STORE_SETTING)
    public ResponseEntity<ApiResponse<StoreDto>> updateStore(@Parameter(description = "門市ID") @PathVariable UUID id, @Valid @RequestBody StoreDto storeDto) {
        StoreDto updatedStore = storeService.updateStore(id, storeDto);
        return ResponseEntity.ok(ApiResponse.success(updatedStore));
    }

    @Operation(summary = "刪除門市", description = "依照門市ID刪除門市 (軟刪除)。")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00040_DELETE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.STORE_SETTING)
    public ResponseEntity<ApiResponse<Void>> deleteStore(@Parameter(description = "門市ID") @PathVariable UUID id) {
        storeService.deleteStore(id);
        return ResponseEntity.ok(ApiResponse.success(null, "Store deleted successfully"));
    }

    @Operation(summary = "取得所有啟用中的門市列表 (供下拉選單使用)")
    @GetMapping("/selectable")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<StoreDto>>> getSelectableStores(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @Parameter(description = "(可選) 地區ID，用於篩選特定地區的門市") @RequestParam(required = false) UUID regionId) {
        List<StoreDto> stores;
        if (regionId != null) {
            stores = storeService.getSelectableStoresByRegion(regionId, companyContext);
        } else {
            stores = storeService.getAllActiveSelectableStores(companyContext);
        }
        return ResponseEntity.ok(ApiResponse.success(stores));
    }
} 