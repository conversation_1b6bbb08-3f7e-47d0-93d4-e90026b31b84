package com.eastking.controller;

import com.eastking.model.dto.request.DailySettlementRequestDto;
import com.eastking.model.dto.response.StoreDailySettlementDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.StoreDailySettlementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.UUID;

@Tag(name = "Store Daily Settlement", description = "門市日結管理 API")
@RestController
@RequestMapping("/api/v1/store-daily-settlements")
@RequiredArgsConstructor
public class StoreDailySettlementController {

    private final StoreDailySettlementService storeDailySettlementService;

    @GetMapping("/data-for-settlement")
    @PreAuthorize("hasAnyAuthority('F04010_READ', 'SYS_ADMIN')")
    @Operation(summary = "獲取日結所需資料", description = "根據門市ID和日期，獲取當日所有帳務資料以準備進行日結。")
    public ResponseEntity<ApiResponse<StoreDailySettlementDto>> getSettlementData(
            @Parameter(description = "門市ID", required = true) @RequestParam UUID storeId,
            @Parameter(description = "結算日期 (格式: yyyy-MM-dd)", required = true) @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        StoreDailySettlementDto data = storeDailySettlementService.getDailySettlementData(storeId, date);
        return ResponseEntity.ok(ApiResponse.success(data));
    }

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F04010_CREATE', 'F04010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "執行並儲存門市日結", description = "提交日結資料，系統將進行計算並儲存日結單。")
    public ResponseEntity<ApiResponse<StoreDailySettlementDto>> performSettlement(
            @Valid @RequestBody DailySettlementRequestDto requestDto) {
        StoreDailySettlementDto result = storeDailySettlementService.performDailySettlement(requestDto);
        return new ResponseEntity<>(ApiResponse.created(result), HttpStatus.CREATED);
    }
} 