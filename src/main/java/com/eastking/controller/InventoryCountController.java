package com.eastking.controller;

import com.eastking.model.dto.InventoryCountSheetDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.InventoryCountService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import com.eastking.model.dto.request.InventoryCountSheetRequestDto;
import jakarta.validation.Valid;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/api/v1/inventory-counts")
@RequiredArgsConstructor
@Tag(name = "40. 盤點管理 (Inventory Counting)", description = "盤點記錄相關 API")
public class InventoryCountController {

    private final InventoryCountService inventoryCountService;

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F04010_READ', 'SYS_ADMIN')") // Assuming a permission code for this feature
    @Operation(summary = "查詢盤點記錄列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<InventoryCountSheetDto>>> searchInventoryCountSheets(
            @Parameter(description = "公司別 (0:東方不敗, 1:雀友)", required = true) @RequestParam Short companyDivisionCode,
            @Parameter(description = "盤點門市ID") @RequestParam(required = false) UUID storeId,
            @Parameter(description = "盤點月份 (格式: YYYY-MM)") @RequestParam(required = false) String countMonth,
            @Parameter(description = "審核狀態碼") @RequestParam(required = false) Short approvalStatusCode,
            @PageableDefault(size = 10, sort = "countDate,desc") Pageable pageable) {
        
        Page<InventoryCountSheetDto> page = inventoryCountService.searchInventoryCountSheets(
            companyDivisionCode, storeId, countMonth, approvalStatusCode, pageable);
        
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @GetMapping("/{sheetId}")
    @PreAuthorize("hasAnyAuthority('F04010_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID獲取盤點單詳細資料")
    public ResponseEntity<ApiResponse<InventoryCountSheetDto>> getInventoryCountSheetById(@PathVariable UUID sheetId) {
        InventoryCountSheetDto dto = inventoryCountService.getInventoryCountSheetById(sheetId);
        return ResponseEntity.ok(ApiResponse.success(dto));
    }

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F04010_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增盤點單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.INVENTORY_COUNT_SHEET)
    public ResponseEntity<ApiResponse<InventoryCountSheetDto>> createInventoryCountSheet(
            @Valid @RequestBody InventoryCountSheetRequestDto requestDto) {
        InventoryCountSheetDto createdSheet = inventoryCountService.createInventoryCountSheet(requestDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(createdSheet));
    }

    // TODO: Add endpoints for Create, Update, Submit, Approve, Reject, Recount...
} 