package com.eastking.controller;

import com.eastking.model.dto.response.BinLookupResponseDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.BinLookupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Util", description = "通用工具 API")
@RestController
@RequestMapping("/api/v1/utils")
@RequiredArgsConstructor
public class BinLookupController {

    private final BinLookupService binLookupService;

    @GetMapping("/bin-lookup/{cardNumberPrefix}")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "查詢信用卡 BIN 碼資訊")
    public ResponseEntity<ApiResponse<BinLookupResponseDto>> lookupBin(@PathVariable String cardNumberPrefix) {
        return binLookupService.findCardInfoByCardNumber(cardNumberPrefix)
                .map(dto -> ResponseEntity.ok(ApiResponse.success(dto)))
                .orElse(ResponseEntity.notFound().build());
    }
} 