package com.eastking.controller;

import com.eastking.model.dto.request.LoginRequest;
import com.eastking.model.dto.response.LoginResponse;
import com.eastking.service.AuthService;
import com.eastking.service.AuditLogService;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import com.eastking.security.UserDetailsImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 認證控制器，處理登入等請求
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Tag(name = "1. 認證管理 (Authentication)", description = "使用者登入及認證相關 API")
@RestController
@RequestMapping("/api/v1/auth") // Versioning in URL as per backend_rules.md
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;
    private final AuditLogService auditLogService;

    /**
     * 處理使用者登入請求
     *
     * @param loginRequest 包含員工編號和密碼的登入請求體
     * @return ResponseEntity 包含 ApiResponse<LoginResponse>
     */
    @Operation(summary = "使用者登入", description = "透過員工編號和密碼進行登入，成功後返回 JWT Token 及使用者資訊。")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "登入成功", 
                     content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, 
                                        schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class))),
        @ApiResponse(responseCode = "400", description = "請求參數無效 (例如欄位為空)", 
                     content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, 
                                        schema = @Schema(example = "{\"code\": 400, \"message\": \"員工編號不能為空\"}"))),
        @ApiResponse(responseCode = "401", description = "認證失敗 (員工編號或密碼錯誤)", 
                     content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, 
                                        schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class)))
    })
    @PostMapping("/login")
    public ResponseEntity<com.eastking.model.vo.ApiResponse<LoginResponse>> login(@Valid @RequestBody LoginRequest loginRequest) {
        LoginResponse loginResponseData = authService.login(loginRequest);
        
        HttpHeaders headers = new HttpHeaders();
        if (loginResponseData.getToken() != null) {
            headers.add("Authorization", "Bearer " + loginResponseData.getToken());
        }
        if (loginResponseData.getSessionUuid() != null) {
            headers.add("X-Session-UUID", loginResponseData.getSessionUuid().toString());
        }

        if (loginResponseData.getToken() == null && loginResponseData.getMessage() != null) {
            // Login failed - an error message is present, but no token
            return new ResponseEntity<>(com.eastking.model.vo.ApiResponse.error(HttpStatus.UNAUTHORIZED.value(), loginResponseData.getMessage()), headers, HttpStatus.UNAUTHORIZED);
        }
        
        // Login successful
        return new ResponseEntity<>(com.eastking.model.vo.ApiResponse.success(loginResponseData), headers, HttpStatus.OK);
    }

    @PostMapping("/logout")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "使用者登出", description = "記錄使用者登出操作。客戶端應負責清除本地儲存的 JWT Token。")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "登出成功請求已記錄", 
                     content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, 
                                        schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class))),
        @ApiResponse(responseCode = "401", description = "未經授權 (例如 Token 無效或過期)")
    })
    public ResponseEntity<com.eastking.model.vo.ApiResponse<String>> logout() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String userIdentifier = "UNKNOWN_USER";
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            userIdentifier = userDetails.getUsername(); // Typically employeeId
            auditLogService.logAction(
                AuditActionTypeEnum.LOGOUT,
                AuditDataTypeEnum.AUTHENTICATION,
                userDetails.getId().toString(), // Using userAccountId as targetId
                "User Logout: " + userIdentifier,
                null
            );
        }
        SecurityContextHolder.clearContext(); // Clear context for this request thread
        return ResponseEntity.ok(com.eastking.model.vo.ApiResponse.success("Logout request processed successfully. Please clear your token."));
    }

    // TODO: Add /refresh-token endpoint if using refresh tokens
    
    /**
     * 測試端點 - 驗證 JWT token 更新機制
     * 此端點用於測試前端是否能正確接收到新的 JWT token
     */
    @Operation(summary = "JWT Token 更新測試", description = "測試端點，用於驗證 JWT token 更新機制是否正常運作。")
    @GetMapping("/test-token-refresh")
    public ResponseEntity<com.eastking.model.vo.ApiResponse<Map<String, String>>> testTokenRefresh(
            @RequestHeader(value = "Authorization", required = false) String authHeader) {
        
        Map<String, String> testData = new HashMap<>();
        testData.put("timestamp", Instant.now().toString());
        testData.put("message", "JWT token refresh test successful");
        
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            testData.put("receivedTokenSnippet", token.length() > 20 ? 
                token.substring(0, 10) + "..." + token.substring(token.length() - 10) : token);
        }
        
        return ResponseEntity.ok(com.eastking.model.vo.ApiResponse.success(testData));
    }

    @Operation(summary = "取得可操作門市列表", description = "獲取當前登入使用者有權限操作的門市列表。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功取得門市列表", 
                     content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, 
                                        schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "未經授權或登入逾時",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, 
                                       schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class)))
    })
    @GetMapping("/operable-stores")
    @PreAuthorize("isAuthenticated()") // Ensure user is authenticated
    public ResponseEntity<com.eastking.model.vo.ApiResponse<List<LoginResponse.StoreInfo>>> getOperableStores() {
        List<LoginResponse.StoreInfo> stores = authService.getOperableStores();
        return ResponseEntity.ok(com.eastking.model.vo.ApiResponse.success(stores));
    }
} 