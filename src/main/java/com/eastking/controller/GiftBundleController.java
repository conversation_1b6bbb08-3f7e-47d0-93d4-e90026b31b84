package com.eastking.controller;

import com.eastking.model.dto.GiftBundleDto;
import com.eastking.model.dto.request.GiftBundleQueryRequest;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.GiftBundleService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@Tag(name = "12. 贈品套裝管理 (Gift Bundles)", description = "管理商品贈品套裝 API")
@RestController
@RequestMapping("/api/v1/gift-bundles")
@RequiredArgsConstructor
public class GiftBundleController {

    private final GiftBundleService giftBundleService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00140_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增贈品套裝")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.GIFT_BUNDLE)
    public ResponseEntity<ApiResponse<GiftBundleDto>> createGiftBundle(@Valid @RequestBody GiftBundleDto dto) {
        GiftBundleDto created = giftBundleService.createGiftBundle(dto);
        return new ResponseEntity<>(ApiResponse.created(created), HttpStatus.CREATED);
    }

    @PutMapping("/{bundleId}")
    @PreAuthorize("hasAnyAuthority('F00140_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新贈品套裝")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.GIFT_BUNDLE)
    public ResponseEntity<ApiResponse<GiftBundleDto>> updateGiftBundle(
            @PathVariable UUID bundleId,
            @Valid @RequestBody GiftBundleDto dto) {
        GiftBundleDto updated = giftBundleService.updateGiftBundle(bundleId, dto);
        return ResponseEntity.ok(ApiResponse.success(updated));
    }

    @DeleteMapping("/{bundleId}")
    @PreAuthorize("hasAnyAuthority('F00140_DELETE', 'SYS_ADMIN')")
    @Operation(summary = "刪除贈品套裝")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.GIFT_BUNDLE)
    public ResponseEntity<ApiResponse<Void>> deleteGiftBundle(@PathVariable UUID bundleId) {
        giftBundleService.deleteGiftBundle(bundleId);
        return ResponseEntity.ok(ApiResponse.success(null, "贈品套裝已刪除"));
    }

    @GetMapping("/{bundleId}")
    @PreAuthorize("hasAnyAuthority('F00140_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID取得贈品套裝詳情")
    public ResponseEntity<ApiResponse<GiftBundleDto>> getGiftBundleById(@PathVariable UUID bundleId) {
        return ResponseEntity.ok(ApiResponse.success(giftBundleService.getGiftBundleById(bundleId)));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00140_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢贈品套裝列表 (分頁)")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<GiftBundleDto>>> getAllGiftBundles(
            GiftBundleQueryRequest queryRequest,
            @PageableDefault(sort = {"updateTime"}, direction = org.springframework.data.domain.Sort.Direction.DESC) Pageable pageable) {
        Page<GiftBundleDto> page = giftBundleService.getAllGiftBundles(queryRequest, pageable);
        return ResponseEntity.ok(ApiResponse.success(page)); // Uses the ApiResponse.PageData wrapper for Page
    }
} 