package com.eastking.controller;

import com.eastking.model.dto.RegionDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.RegionService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/regions")
public class RegionController {

    private final RegionService regionService;

    public RegionController(RegionService regionService) {
        this.regionService = regionService;
    }

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00150_CREATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.WAREHOUSE)
    public ResponseEntity<ApiResponse<RegionDto>> createRegion(@Valid @RequestBody RegionDto regionDto) {
        RegionDto createdRegion = regionService.createRegion(regionDto);
        return new ResponseEntity<>(ApiResponse.success(createdRegion), HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00150_READ', 'SYS_ADMIN') or isAuthenticated()")
    public ResponseEntity<ApiResponse<RegionDto>> getRegionById(@PathVariable UUID id) {
        return regionService.getRegionById(id)
            .map(dto -> ResponseEntity.ok(ApiResponse.success(dto)))
            .orElse(new ResponseEntity<>(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "Region not found"), HttpStatus.NOT_FOUND));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00150_READ', 'SYS_ADMIN') or isAuthenticated()")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<RegionDto>>> getAllRegions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "sequenceOrder,asc") String[] sort,
            @RequestParam(required = false) String regionName) {
        // sort[0] = field, sort[1] = direction (asc/desc)
        Sort.Direction direction = sort.length > 1 && sort[1].equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sort[0]));
        Page<RegionDto> regionsPage = regionService.getAllRegions(pageable, regionName);
        return ResponseEntity.ok(ApiResponse.success(regionsPage));
    }
    
    @GetMapping("/list")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<RegionDto>>> getAllRegionsList() {
        List<RegionDto> regions = regionService.getAllRegionsList();
        return ResponseEntity.ok(ApiResponse.success(regions));
    }

    @GetMapping("/active")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<RegionDto>>> getAllActiveRegions() {
        List<RegionDto> activeRegions = regionService.getAllActiveRegions();
        return ResponseEntity.ok(ApiResponse.success(activeRegions));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00150_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.WAREHOUSE)
    public ResponseEntity<ApiResponse<RegionDto>> updateRegion(@PathVariable UUID id, @Valid @RequestBody RegionDto regionDto) {
        RegionDto updatedRegion = regionService.updateRegion(id, regionDto);
        return ResponseEntity.ok(ApiResponse.success(updatedRegion));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00150_DELETE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.WAREHOUSE)
    public ResponseEntity<ApiResponse<Void>> deleteRegion(@PathVariable UUID id) {
        regionService.deleteRegion(id);
        return ResponseEntity.ok(ApiResponse.success(null, "Region deleted successfully"));
    }
} 