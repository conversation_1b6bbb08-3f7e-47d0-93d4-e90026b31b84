package com.eastking.controller;

import com.eastking.model.dto.DocumentClauseDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.DocumentClauseService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/document-clauses")
@RequiredArgsConstructor
@Tag(name = "33. 表尾條文管理 (Document Clauses)", description = "管理表尾條文 API")
public class DocumentClauseController {

    private final DocumentClauseService documentClauseService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00330_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增表尾條文")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.DOCUMENT_CLAUSE)
    public ResponseEntity<ApiResponse<DocumentClauseDto>> createDocumentClause(@Valid @RequestBody DocumentClauseDto dto) {
        DocumentClauseDto createdClause = documentClauseService.createDocumentClause(dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(createdClause));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00330_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID獲取表尾條文")
    public ResponseEntity<ApiResponse<DocumentClauseDto>> getDocumentClauseById(@PathVariable UUID id) {
        DocumentClauseDto dto = documentClauseService.getDocumentClauseById(id);
        return ResponseEntity.ok(ApiResponse.success(dto));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00330_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢表尾條文列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<DocumentClauseDto>>> searchDocumentClauses(
            @Parameter(description = "公司別 (0:東方不敗, 1:雀友)", required = true) @RequestParam Short companyDivisionCode,
            @Parameter(description = "關鍵字 (標題或內容)") @RequestParam(required = false) String keyword,
            @Parameter(description = "是否啟用") @RequestParam(required = false) Boolean isActive,
            @Parameter(description = "是否預設") @RequestParam(required = false) Boolean isDefault,
            @Parameter(description = "日期類型 (effectiveDate, createDate)") @RequestParam(required = false) String dateType,
            @Parameter(description = "日期起 (yyyy-MM-dd)") @RequestParam(required = false) String dateFrom,
            @Parameter(description = "日期迄 (yyyy-MM-dd)") @RequestParam(required = false) String dateTo,
            @PageableDefault(size = 10, sort = "sequenceOrder,asc") Pageable pageable) {
        Page<DocumentClauseDto> page = documentClauseService.searchDocumentClauses(companyDivisionCode, keyword, isActive, isDefault, dateType, dateFrom, dateTo, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @GetMapping("/active-defaults")
    @PreAuthorize("isAuthenticated()") // Typically needed by various modules when creating documents
    @Operation(summary = "獲取當前啟用的預設表尾條文")
    public ResponseEntity<ApiResponse<List<DocumentClauseDto>>> getActiveDefaultClauses(
        @Parameter(description = "公司別 (0:東方不敗, 1:雀友)", required = true) @RequestParam Short companyDivisionCode) {
        List<DocumentClauseDto> clauses = documentClauseService.getActiveDefaultClauses(companyDivisionCode);
        return ResponseEntity.ok(ApiResponse.success(clauses));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00330_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新表尾條文")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DOCUMENT_CLAUSE)
    public ResponseEntity<ApiResponse<DocumentClauseDto>> updateDocumentClause(
            @PathVariable UUID id, 
            @Valid @RequestBody DocumentClauseDto dto) {
        DocumentClauseDto updatedClause = documentClauseService.updateDocumentClause(id, dto);
        return ResponseEntity.ok(ApiResponse.success(updatedClause));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00330_DELETE', 'SYS_ADMIN')")
    @Operation(summary = "刪除表尾條文 (軟刪除)")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.DOCUMENT_CLAUSE)
    public ResponseEntity<ApiResponse<Void>> deleteDocumentClause(@PathVariable UUID id) {
        documentClauseService.deleteDocumentClause(id);
        return ResponseEntity.ok(ApiResponse.success(null, "表尾條文已刪除"));
    }
} 