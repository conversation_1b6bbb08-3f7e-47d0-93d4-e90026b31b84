package com.eastking.controller;

import com.eastking.model.dto.request.RolePermissionDataRequest;
import com.eastking.model.dto.response.RoleDetailResponse;
import com.eastking.model.dto.response.RoleSummaryResponse;
import com.eastking.model.dto.response.SystemFunctionResponseDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.RolePermissionService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Tag(name = "3. 角色權限管理 (Role & Permissions)", description = "管理角色及其相關的功能、欄位、區域權限 API")
@RestController
@RequestMapping("/api/v1/role-permissions")
@RequiredArgsConstructor
public class RolePermissionController {

    private final RolePermissionService rolePermissionService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00020_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增角色及權限", description = "建立新角色並設定其相關權限。")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.ROLE)
    public ResponseEntity<ApiResponse<RoleDetailResponse>> createRoleWithPermissions(@Valid @RequestBody RolePermissionDataRequest request) {
        RoleDetailResponse responseData = rolePermissionService.createRoleWithPermissions(request);
        return new ResponseEntity<>(ApiResponse.created(responseData), HttpStatus.CREATED);
    }

    @PutMapping("/{roleId}")
    @PreAuthorize("hasAnyAuthority('F00020_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新角色及權限", description = "依照角色ID更新現有的角色及其相關權限。")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ROLE)
    public ResponseEntity<ApiResponse<RoleDetailResponse>> updateRoleWithPermissions(
            @Parameter(description = "角色ID") @PathVariable UUID roleId,
            @Valid @RequestBody RolePermissionDataRequest request) {
        RoleDetailResponse responseData = rolePermissionService.updateRoleWithPermissions(roleId, request);
        return ResponseEntity.ok(ApiResponse.success(responseData));
    }

    @DeleteMapping("/{roleId}")
    @PreAuthorize("hasAnyAuthority('F00020_DELETE', 'SYS_ADMIN')")
    @Operation(summary = "刪除角色", description = "依照角色ID刪除角色 (軟刪除)。")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.ROLE)
    public ResponseEntity<ApiResponse<Void>> deleteRole(@Parameter(description = "角色ID") @PathVariable UUID roleId) {
        rolePermissionService.deleteRole(roleId);
        return ResponseEntity.ok(ApiResponse.success(null, "Role deleted successfully"));
    }

    @GetMapping("/{roleId}")
    @PreAuthorize("hasAnyAuthority('F00020_READ', 'SYS_ADMIN')")
    @Operation(summary = "取得單一角色權限詳情", description = "依照角色ID取得特定角色的完整權限設定資訊。")
    public ResponseEntity<ApiResponse<RoleDetailResponse>> getRoleWithPermissionsById(@Parameter(description = "角色ID") @PathVariable UUID roleId) {
        RoleDetailResponse responseData = rolePermissionService.getRoleWithPermissionsById(roleId);
        return ResponseEntity.ok(ApiResponse.success(responseData));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00020_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢角色列表 (分頁)", description = "取得角色簡要資訊列表，結果分頁。")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<RoleSummaryResponse>>> getAllRolesSummary(
            @PageableDefault(sort = "roleName") Pageable pageable) {
        Page<RoleSummaryResponse> responsePage = rolePermissionService.getAllRolesSummary(pageable);
        return ResponseEntity.ok(ApiResponse.success(responsePage));
    }

    @GetMapping("/list")
    @PreAuthorize("hasAnyAuthority('F00020_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢所有角色列表 (無分頁)", description = "取得所有未刪除角色的簡要列表，通常用於下拉選單。")
    public ResponseEntity<ApiResponse<List<RoleSummaryResponse>>> getAllRolesAsList() {
        List<RoleSummaryResponse> responseList = rolePermissionService.getAllRolesAsList();
        return ResponseEntity.ok(ApiResponse.success(responseList));
    }
    
    @GetMapping("/system-functions")
    @PreAuthorize("hasAnyAuthority('F00020_READ', 'SYS_ADMIN')")
    @Operation(summary = "取得所有系統功能 (樹狀結構)", description = "取得所有定義的系統功能項目，以樹狀結構呈現，用於權限設定畫面。")
    public ResponseEntity<ApiResponse<List<SystemFunctionResponseDto>>> getAllSystemFunctionsForPermissionSetting() {
        List<SystemFunctionResponseDto> responseList = rolePermissionService.getAllSystemFunctionsForPermissionSetting();
        return ResponseEntity.ok(ApiResponse.success(responseList));
    }
} 