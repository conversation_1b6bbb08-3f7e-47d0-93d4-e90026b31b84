package com.eastking.controller;

import com.eastking.model.dto.CustomerDeviceDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.DeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/devices")
@RequiredArgsConstructor
@Tag(name = "Device Management", description = "設備管理 API")
public class DeviceController {

    private final DeviceService deviceService;

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F06020_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢設備列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<CustomerDeviceDto>>> searchDevices(
            @Parameter(description = "關鍵字 (機身號、產品名稱、客戶姓名)") @RequestParam(required = false) String keyword,
            @PageableDefault(size = 15, sort = "createTime,desc") Pageable pageable) {
        
        Page<CustomerDeviceDto> devicesPage = deviceService.searchDevices(keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(devicesPage));
    }

    @GetMapping("/by-serial/{serialNumber}")
    @PreAuthorize("hasAnyAuthority('F06020_READ', 'SYS_ADMIN')")
    @Operation(summary = "依機身序號查詢設備")
    public ResponseEntity<ApiResponse<CustomerDeviceDto>> getDeviceBySerialNumber(@PathVariable String serialNumber) {
        return deviceService.findBySerialNumber(serialNumber)
                .map(device -> ApiResponse.success(device))
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "找不到此機身序號對應的設備")));
    }
} 