package com.eastking.controller;

import com.eastking.model.dto.MemberLevelDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.MemberLevelService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;
import java.util.UUID;

@Tag(name = "6. 會員福利設定 (Member Benefit Management)", description = "管理會員等級、福利及簡訊模板等相關 API")
@RestController
@RequestMapping("/api/v1/member-levels")
@RequiredArgsConstructor
public class MemberLevelController {

    private final MemberLevelService memberLevelService;

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00050_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<List<MemberLevelDto>>> getAllMemberLevels() {
        List<MemberLevelDto> levels = memberLevelService.getAllMemberLevels();
        return ResponseEntity.ok(ApiResponse.success(levels));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00050_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<MemberLevelDto>> getMemberLevelById(@PathVariable UUID id) {
        return memberLevelService.getMemberLevelById(id)
            .map(dto -> ResponseEntity.ok(ApiResponse.success(dto)))
            .orElse(new ResponseEntity<>(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "MemberLevel not found"), HttpStatus.NOT_FOUND));
    }

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00050_CREATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.MEMBER_LEVEL)
    public ResponseEntity<ApiResponse<MemberLevelDto>> createMemberLevel(@Valid @RequestBody MemberLevelDto dto) {
        MemberLevelDto createdLevel = memberLevelService.createMemberLevel(dto);
        return new ResponseEntity<>(ApiResponse.created(createdLevel), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00050_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.MEMBER_LEVEL)
    public ResponseEntity<ApiResponse<MemberLevelDto>> updateMemberLevel(@PathVariable UUID id, @Valid @RequestBody MemberLevelDto dto) {
        MemberLevelDto updatedLevel = memberLevelService.updateMemberLevel(id, dto);
        return ResponseEntity.ok(ApiResponse.success(updatedLevel));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00050_DELETE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.MEMBER_LEVEL)
    public ResponseEntity<ApiResponse<Void>> deleteMemberLevel(@PathVariable UUID id) {
        memberLevelService.deleteMemberLevel(id);
        return ResponseEntity.ok(ApiResponse.success(null, "MemberLevel deleted successfully"));
    }

    @Operation(summary = "取得所有可選的會員等級列表 (無分頁)")
    @GetMapping("/selectable")
    @PreAuthorize("isAuthenticated()") // Allow any authenticated user to see the levels for forms
    public ResponseEntity<ApiResponse<List<MemberLevelDto>>> getSelectableMemberLevels() {
        List<MemberLevelDto> levels = memberLevelService.getAllActiveMemberLevels();
        return ResponseEntity.ok(ApiResponse.success(levels));
    }
} 