package com.eastking.controller;

import com.eastking.model.dto.request.AnnouncementQueryRequest;
import com.eastking.model.dto.request.AnnouncementRequest;
import com.eastking.model.dto.response.AnnouncementResponse;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.AnnouncementService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Tag(name = "2. 系統公告管理 (System Announcements)", description = "管理系統公告相關 API")
@RestController
@RequestMapping("/api/v1/announcements")
@RequiredArgsConstructor
public class AnnouncementController {

    private final AnnouncementService announcementService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00030_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增系統公告", description = "建立新的系統公告。")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.ANNOUNCEMENT)
    public ResponseEntity<ApiResponse<AnnouncementResponse>> createAnnouncement(@Valid @RequestBody AnnouncementRequest request) {
        AnnouncementResponse responseData = announcementService.createAnnouncement(request);
        return new ResponseEntity<>(ApiResponse.created(responseData), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00030_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新系統公告", description = "依照公告ID更新現有的系統公告。")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ANNOUNCEMENT)
    public ResponseEntity<ApiResponse<AnnouncementResponse>> updateAnnouncement(
            @Parameter(description = "公告ID") @PathVariable UUID id,
            @Valid @RequestBody AnnouncementRequest request) {
        AnnouncementResponse responseData = announcementService.updateAnnouncement(id, request);
        return ResponseEntity.ok(ApiResponse.success(responseData));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00030_DELETE', 'SYS_ADMIN')")
    @Operation(summary = "刪除系統公告", description = "依照公告ID刪除系統公告 (軟刪除)。")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.ANNOUNCEMENT)
    public ResponseEntity<ApiResponse<Void>> deleteAnnouncement(@Parameter(description = "公告ID") @PathVariable UUID id) {
        announcementService.deleteAnnouncement(id);
        return ResponseEntity.ok(ApiResponse.success(null, "Announcement deleted successfully"));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00030_READ', 'SYS_ADMIN')")
    @Operation(summary = "取得單筆系統公告", description = "依照公告ID取得特定系統公告的詳細資訊。")
    public ResponseEntity<ApiResponse<AnnouncementResponse>> getAnnouncementById(@Parameter(description = "公告ID") @PathVariable UUID id) {
        // Assuming service throws ResourceNotFoundException if not found, which will be handled by a global exception handler
        // Otherwise, service should return Optional<AnnouncementResponse>
        AnnouncementResponse responseData = announcementService.getAnnouncementById(id);
        return ResponseEntity.ok(ApiResponse.success(responseData));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00030_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢系統公告列表 (分頁)", description = "根據查詢條件取得系統公告列表，結果分頁。")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<AnnouncementResponse>>> getAllAnnouncements(
            @Parameter(description = "查詢條件") AnnouncementQueryRequest queryRequest, 
            @PageableDefault(sort = "createTime", direction = Sort.Direction.DESC) Pageable pageable) {
        Page<AnnouncementResponse> responsePage = announcementService.getAllAnnouncements(queryRequest, pageable);
        return ResponseEntity.ok(ApiResponse.success(responsePage));
    }

    @GetMapping("/public")
    @Operation(summary = "取得公開顯示的系統公告", description = "取得目前有效且啟用的公開公告列表 (通常用於登入後彈窗)。")
    public ResponseEntity<ApiResponse<List<AnnouncementResponse>>> getActivePublicAnnouncements() {
        List<AnnouncementResponse> responses = announcementService.getActivePublicAnnouncements();
        return ResponseEntity.ok(ApiResponse.success(responses));
    }
} 