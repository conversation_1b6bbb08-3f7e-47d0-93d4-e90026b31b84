package com.eastking.controller;

import com.eastking.enums.MaterialOrderStatusEnum;
import com.eastking.enums.MaterialTypeStatusEnum;
import com.eastking.model.dto.request.MaterialOrderRequestDto;
import com.eastking.model.dto.request.MaterialOrderFilterRequest;
import com.eastking.model.dto.response.MaterialOrderSummaryDto;
import com.eastking.model.entity.DispatchMaterialOrder;
import com.eastking.model.dto.response.MaterialOrderDetailDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.MaterialOrderService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/api/v1/material-orders")
@RequiredArgsConstructor
@Tag(name = "Material Order Management", description = "技師領料單管理 API")
public class MaterialOrderController {

    private final MaterialOrderService materialOrderService;

    @GetMapping("/mgr")
    @PreAuthorize("hasAnyAuthority('F06040_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢領料單列表 (管理者視角)")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<MaterialOrderSummaryDto>>> getMaterialOrdersForManager(
            @Parameter(description = "篩選條件") MaterialOrderFilterRequest filter,
            @Parameter(hidden = true) @PageableDefault(size = 15, sort = "createTime,desc") Pageable pageable) {
        Page<MaterialOrderSummaryDto> orders = materialOrderService.searchForManagerView(filter, pageable);
        return ResponseEntity.ok(ApiResponse.success(ApiResponse.PageData.fromPage(orders)));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F05020_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢技師的領料單列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<MaterialOrderSummaryDto>>> getMaterialOrders(
            @Parameter(description = "篩選條件") MaterialOrderFilterRequest filter,
            @Parameter(hidden = true) @PageableDefault(size = 15, sort = "createTime,desc") Pageable pageable) {
        Page<MaterialOrderSummaryDto> orders = materialOrderService.searchMaterialOrders(filter, MaterialTypeStatusEnum.PICKING, pageable);
        log.info("orders.stream().count: {}", orders.stream().count());
        return ResponseEntity.ok(ApiResponse.success(ApiResponse.PageData.fromPage(orders)));
    }

    @GetMapping("/refund")
    @PreAuthorize("hasAnyAuthority('F05030_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢技師的退機單列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<MaterialOrderSummaryDto>>> getRefundMaterialOrders(
            @Parameter(description = "篩選條件") MaterialOrderFilterRequest filter,
            @Parameter(hidden = true) @PageableDefault(size = 15, sort = "createTime,desc") Pageable pageable) {
        Page<MaterialOrderSummaryDto> orders = materialOrderService.searchMaterialOrders(filter, MaterialTypeStatusEnum.REFUND_PICKING, pageable);
        return ResponseEntity.ok(ApiResponse.success(ApiResponse.PageData.fromPage(orders)));
    }

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F05020_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增領料單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.MATERIAL_ORDER)
    public ResponseEntity<ApiResponse<MaterialOrderDetailDto>> createMaterialOrder(
            @Parameter(description = "領料單請求內容") @Valid @RequestBody MaterialOrderRequestDto requestDto) {
        MaterialOrderDetailDto createdOrder = materialOrderService.createMaterialOrder(requestDto, MaterialTypeStatusEnum.PICKING);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(createdOrder));
    }

    @PostMapping("/refund")
    @PreAuthorize("hasAnyAuthority('F05030_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增退料單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.MATERIAL_ORDER)
    public ResponseEntity<ApiResponse<MaterialOrderDetailDto>> createRefundMaterialOrder(
            @Parameter(description = "退料單請求內容") @Valid @RequestBody MaterialOrderRequestDto requestDto) {
        MaterialOrderDetailDto createdOrder = materialOrderService.createMaterialOrder(requestDto, MaterialTypeStatusEnum.REFUND_PICKING);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(createdOrder));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F05020_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢領料單詳情")
    public ResponseEntity<ApiResponse<MaterialOrderDetailDto>> getMaterialOrderDetail(
            @Parameter(description = "領料單ID") @PathVariable UUID id) {
        MaterialOrderDetailDto order = materialOrderService.getMaterialOrderDetailById(id);
        return ResponseEntity.ok(ApiResponse.success(order));
    }

    @PostMapping("/{id}/complete-collection")
    @PreAuthorize("hasAnyAuthority('F05020_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成領料")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.MATERIAL_ORDER)
    public ResponseEntity<ApiResponse<Void>> completeMaterialCollection(
            @Parameter(description = "領料單ID") @PathVariable UUID id) {
        materialOrderService.completeMaterialCollection(id, MaterialOrderStatusEnum.COLLECTED);
        return ResponseEntity.ok(ApiResponse.success(null, "領料已完成"));
    }

    @PostMapping("/{id}/refund-complete-collection")
    @PreAuthorize("hasAnyAuthority('F05030_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成退料")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.MATERIAL_ORDER)
    public ResponseEntity<ApiResponse<Void>> refundCompleteMaterialCollection(
            @Parameter(description = "領料單ID") @PathVariable UUID id) {
        materialOrderService.completeMaterialCollection(id, MaterialOrderStatusEnum.REFUND_COLLECTED);
        return ResponseEntity.ok(ApiResponse.success(null, "退料已完成"));
    }
} 