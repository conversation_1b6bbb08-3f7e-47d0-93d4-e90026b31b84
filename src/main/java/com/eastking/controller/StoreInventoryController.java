package com.eastking.controller;

import com.eastking.model.dto.StoreInventoryDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.WarehouseStoreInventoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;
import java.util.List;

@Tag(name = "22. 庫存管理 - 門市商品庫存 (Store Product Inventory)", description = "門市商品庫存查詢 API")
@RestController
@RequestMapping("/api/v1/store-inventory")
@RequiredArgsConstructor
public class StoreInventoryController {

    private final WarehouseStoreInventoryService warehouseStoreInventoryService;

    @Operation(summary = "查詢門市商品庫存列表 (分頁)")
    @GetMapping
    @PreAuthorize("hasAnyAuthority('F01022_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<StoreInventoryDto>>> getStoreInventory(
            @Parameter(description = "門市ID", required = true) @RequestParam UUID storeId,
            @Parameter(description = "關鍵字 (商品條碼/名稱)") @RequestParam(required = false) String keyword,
            @PageableDefault(sort = "productName", direction = Sort.Direction.ASC) Pageable pageable) {
        Page<StoreInventoryDto> page = warehouseStoreInventoryService.getStoreInventory(storeId, keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    // Potentially, an endpoint to get a single inventory item (though list view might be primary use)
    @Operation(summary = "依門市ID和商品條碼取得單一商品庫存")
    @GetMapping("/item")
    @PreAuthorize("hasAnyAuthority('F01022_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<StoreInventoryDto>> getSingleStoreInventoryItem(
            @Parameter(description = "門市ID", required = true) @RequestParam UUID storeId,
            @Parameter(description = "商品國際條碼", required = true) @RequestParam String productBarcode) {
        StoreInventoryDto dto = warehouseStoreInventoryService.getSingleStoreInventoryItem(storeId, productBarcode);
        return ResponseEntity.ok(ApiResponse.success(dto));
    }

    @Operation(summary = "依門市ID取得所有商品庫存 (用於盤點)")
    @GetMapping("/{storeId}/all-products")
    @PreAuthorize("hasAnyAuthority('F04010_CREATE', 'SYS_ADMIN')") // Use Inventory Count create permission
    public ResponseEntity<ApiResponse<List<StoreInventoryDto>>> getAllStoreInventoryForCount(
            @Parameter(description = "門市ID", required = true) @PathVariable UUID storeId) {
        List<StoreInventoryDto> list = warehouseStoreInventoryService.getAllStoreInventory(storeId);
        return ResponseEntity.ok(ApiResponse.success(list));
    }
} 