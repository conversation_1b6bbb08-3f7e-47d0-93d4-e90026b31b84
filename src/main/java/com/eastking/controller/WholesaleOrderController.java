package com.eastking.controller;

import com.eastking.model.dto.request.WholesaleOrderRequestDto;
import com.eastking.model.dto.response.WholesaleOrderDetailDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.WholesaleOrderService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;
// Placeholder for DTOs that will be created later
// import com.eastking.model.dto.request.WholesaleOrderSearchRequestDto;

@RestController
@RequestMapping("/api/v1/wholesale-orders")
@RequiredArgsConstructor
@Tag(name = "31. 批發訂單管理 (Wholesale Orders)", description = "批發訂單相關 API")
public class WholesaleOrderController {

    private final WholesaleOrderService wholesaleOrderService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F03091_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增批發訂單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.WHOLESALE_ORDER)
    public ResponseEntity<ApiResponse<WholesaleOrderDetailDto>> createWholesaleOrder(@Valid @RequestBody WholesaleOrderRequestDto requestDto) {
        WholesaleOrderDetailDto createdOrder = wholesaleOrderService.createWholesaleOrder(requestDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(createdOrder));
    }

    @GetMapping("/{orderId}")
    @PreAuthorize("hasAnyAuthority('F03091_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID獲取批發訂單詳情")
    public ResponseEntity<ApiResponse<WholesaleOrderDetailDto>> getWholesaleOrderById(@PathVariable UUID orderId) {
        WholesaleOrderDetailDto orderDetailDto = wholesaleOrderService.getWholesaleOrderById(orderId);
        return ResponseEntity.ok(ApiResponse.success(orderDetailDto));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F03091_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢批發訂單列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<WholesaleOrderDetailDto>>> searchWholesaleOrders(
            @Parameter(description = "公司別 (0:東方不敗, 1:雀友)", required = true) @RequestParam Short companyDivisionCode,
            @Parameter(description = "訂單號碼") @RequestParam(required = false) String orderNumber,
            @Parameter(description = "開單門市ID") @RequestParam(required = false) UUID storeId,
            @Parameter(description = "狀態碼") @RequestParam(required = false) Short orderStatusCode,
            @Parameter(description = "建單日期起") @RequestParam(required = false) String dateFrom, // Consider OffsetDateTime and @DateTimeFormat
            @Parameter(description = "建單日期迄") @RequestParam(required = false) String dateTo,
            @PageableDefault(size = 10, sort = "createTime,desc") Pageable pageable) {
        
        // This currently passes individual params to service. Could use a DTO.
        Page<WholesaleOrderDetailDto> ordersPage = wholesaleOrderService.searchWholesaleOrders(companyDivisionCode, orderNumber, storeId, orderStatusCode, dateFrom, dateTo, pageable);
        return ResponseEntity.ok(ApiResponse.success(ordersPage));
    }

    @PutMapping("/{orderId}")
    @PreAuthorize("hasAnyAuthority('F03091_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新批發訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.WHOLESALE_ORDER)
    public ResponseEntity<ApiResponse<WholesaleOrderDetailDto>> updateWholesaleOrder(
            @PathVariable UUID orderId,
            @Valid @RequestBody WholesaleOrderRequestDto requestDto) {
        WholesaleOrderDetailDto updatedOrder = wholesaleOrderService.updateWholesaleOrder(orderId, requestDto);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/cancel")
    @PreAuthorize("hasAnyAuthority('F03091_UPDATE', 'SYS_ADMIN')") // Cancellation might be an update or a specific cancel permission
    @Operation(summary = "取消批發訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.WHOLESALE_ORDER)
    public ResponseEntity<ApiResponse<WholesaleOrderDetailDto>> cancelWholesaleOrder(@PathVariable UUID orderId) {
        WholesaleOrderDetailDto updatedOrder = wholesaleOrderService.cancelWholesaleOrder(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/print")
    @PreAuthorize("hasAnyAuthority('F03091_PRINT', 'SYS_ADMIN')") 
    @Operation(summary = "列印批發訂單相關單據")
    public ResponseEntity<ApiResponse<String>> printWholesaleOrder(@PathVariable UUID orderId) {
        // TODO: Implement actual print logic (e.g., generate PDF, return file stream or link)
        // For now, this is a placeholder.
        String printResult = wholesaleOrderService.getPrintableOrderData(orderId);
        return ResponseEntity.ok(ApiResponse.success(printResult, "單據準備完成，請進行列印。"));
    }
} 