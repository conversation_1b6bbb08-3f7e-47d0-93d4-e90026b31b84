package com.eastking.controller;

import com.eastking.model.dto.ProductSearchResultDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.ProductSettingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "9. 商品搜尋 (Product Search)", description = "商品資訊搜尋 API (原外部商品查詢)")
@RestController
@RequestMapping("/api/v1/external-products")
@RequiredArgsConstructor
@PreAuthorize("isAuthenticated()")
public class ExternalProductController {

    private static final Logger logger = LoggerFactory.getLogger(ExternalProductController.class);
    private final ProductSettingService productSettingService;

    @Operation(summary = "依關鍵字搜尋商品 (列表)", description = "根據關鍵字搜尋商品，返回列表。供商品選單設定使用。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功取得商品列表",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApiResponse.class)))
    })
    @GetMapping
    public ResponseEntity<ApiResponse<List<ProductSearchResultDto>>> searchExternalProducts(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @Parameter(description = "搜尋關鍵字 (商品條碼或名稱)", in = ParameterIn.QUERY, required = true) @RequestParam String keyword) {
        logger.info("Searching products with keyword: '{}', companyContext: '{}'", keyword, companyContext);
        List<ProductSearchResultDto> products = productSettingService.searchProductsForMenu(keyword, companyContext, null);
        return ResponseEntity.ok(ApiResponse.success(products));
    }

    @Operation(summary = "依關鍵字搜尋商品 (分頁)", description = "根據關鍵字搜尋商品，返回分頁結果。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功取得分頁商品列表",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApiResponse.class)))
    })
    @GetMapping("/paged")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<ProductSearchResultDto>>> searchExternalProductsPaged(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @Parameter(description = "搜尋關鍵字 (商品條碼或名稱)", in = ParameterIn.QUERY) @RequestParam(required = false) String keyword,
            @PageableDefault(size = 20, sort = "productName") Pageable pageable) {
        logger.info("Searching products (paged) with keyword: '{}', companyContext: '{}', pageable: {}", keyword, companyContext, pageable);
        Page<ProductSearchResultDto> productsPage = productSettingService.searchProductsForMenu(keyword, pageable, companyContext, null);
        return ResponseEntity.ok(ApiResponse.success(productsPage));
    }
} 