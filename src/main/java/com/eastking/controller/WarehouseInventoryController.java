package com.eastking.controller;

import com.eastking.model.vo.ApiResponse;
import com.eastking.service.WarehouseStoreInventoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

import com.eastking.model.dto.StoreInventoryDto;

@RestController
@RequestMapping("/api/v1/warehouse-inventory")
@RequiredArgsConstructor
@Tag(name = "Warehouse Inventory Management", description = "總倉庫存管理 API")
public class WarehouseInventoryController {

    private final WarehouseStoreInventoryService warehouseStoreInventoryService;

    @GetMapping("/stock")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "查詢指定倉庫的商品庫存數量")
    public ResponseEntity<ApiResponse<Integer>> getStockQuantity(
            @RequestParam UUID warehouseId,
            @RequestParam String productBarcode) {
        int stockQuantity = warehouseStoreInventoryService.getStockQuantity(warehouseId, productBarcode);
        return ResponseEntity.ok(ApiResponse.success(stockQuantity));
    }

    @GetMapping("/technician-stock")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "查詢特定技師的車存倉商品庫存")
    public ResponseEntity<ApiResponse<List<StoreInventoryDto>>> searchTechnicianStock(
            @RequestParam UUID technicianId,
            @RequestParam(required = false) String keyword) {
        
        List<StoreInventoryDto> products = warehouseStoreInventoryService.searchTechnicianStock(technicianId, keyword);
        return ResponseEntity.ok(ApiResponse.success(products));
    }
} 