package com.eastking.controller;

import com.eastking.model.dto.PromotionDto;
import com.eastking.model.dto.request.PromotionQueryRequest;
import com.eastking.model.dto.response.PromotionChannelTypeEnumDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.PromotionService;
import com.eastking.enums.PromotionChannelTypeEnum;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Tag(name = "11. 促銷價格管理 (Promotional Pricing)", description = "管理商品促銷活動 API")
@RestController
@RequestMapping("/api/v1/promotions")
@RequiredArgsConstructor
public class PromotionController {

    private final PromotionService promotionService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00120_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增促銷活動")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.PROMOTION)
    public ResponseEntity<ApiResponse<PromotionDto>> createPromotion(@Valid @RequestBody PromotionDto dto) {
        PromotionDto created = promotionService.createPromotion(dto);
        return new ResponseEntity<>(ApiResponse.created(created), HttpStatus.CREATED);
    }

    @PutMapping("/{promotionId}")
    @PreAuthorize("hasAnyAuthority('F00120_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新促銷活動")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.PROMOTION)
    public ResponseEntity<ApiResponse<PromotionDto>> updatePromotion(
            @PathVariable UUID promotionId,
            @Valid @RequestBody PromotionDto dto) {
        PromotionDto updated = promotionService.updatePromotion(promotionId, dto);
        return ResponseEntity.ok(ApiResponse.success(updated));
    }

    @DeleteMapping("/{promotionId}")
    @PreAuthorize("hasAnyAuthority('F00120_DELETE', 'SYS_ADMIN')")
    @Operation(summary = "刪除促銷活動")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.PROMOTION)
    public ResponseEntity<ApiResponse<Void>> deletePromotion(@PathVariable UUID promotionId) {
        promotionService.deletePromotion(promotionId);
        return ResponseEntity.ok(ApiResponse.success(null, "促銷活動已刪除"));
    }

    @GetMapping("/{promotionId}")
    @PreAuthorize("hasAnyAuthority('F00120_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID取得促銷活動詳情")
    public ResponseEntity<ApiResponse<PromotionDto>> getPromotionById(@PathVariable UUID promotionId) {
        return ResponseEntity.ok(ApiResponse.success(promotionService.getPromotionById(promotionId)));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00120_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢促銷活動列表 (分頁)")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<PromotionDto>>> getAllPromotions(
            PromotionQueryRequest queryRequest,
            @PageableDefault(sort = {"updateTime"}, direction = org.springframework.data.domain.Sort.Direction.DESC) Pageable pageable) {
        Page<PromotionDto> page = promotionService.getAllPromotions(queryRequest, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @GetMapping("/channel-types")
    @Operation(summary = "取得所有促銷管道類型")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<PromotionChannelTypeEnumDto>>> getPromotionChannelTypes() {
        List<PromotionChannelTypeEnumDto> channelTypes = Arrays.stream(PromotionChannelTypeEnum.values())
                .map(enumVal -> new PromotionChannelTypeEnumDto(
                        enumVal.getCode(), 
                        enumVal.getDescription(),
                        enumVal.isAllowTargetSelection(),
                        enumVal.getTargetPlaceholder()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(ApiResponse.success(channelTypes));
    }

    @Operation(summary = "派工商品訂單查詢可用的促銷活動列表 (供下拉選單使用)")
    @GetMapping("/selectable")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<PromotionDto>>> getSelectablePromotions(
            @Parameter(description = "門市ID (選填)") @RequestParam(required = false) UUID storeId,
            @Parameter(description = "經銷商/銷售據點ID (選填)") @RequestParam(required = false) UUID distributorId) {
        List<PromotionDto> promotions = promotionService.getSelectablePromotions(storeId, distributorId);
        return ResponseEntity.ok(ApiResponse.success(promotions));
    }

    @Operation(summary = "門市商品訂單查詢促銷活動列表")
    @GetMapping("/searchable")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<PromotionDto>>> searchPromotions(
            @Parameter(description = "適用門市ID") @RequestParam(required = false) UUID storeId) {
        List<PromotionDto> promotions = promotionService.searchAvailablePromotions(storeId);
        return ResponseEntity.ok(ApiResponse.success(promotions));
    }
} 