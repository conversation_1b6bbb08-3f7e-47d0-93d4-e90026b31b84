package com.eastking.controller;

import com.eastking.model.dto.SmsTemplateDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.SmsTemplateService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import java.util.UUID;

@Tag(name = "7. 簡訊模板管理 (SMS Template Management)", description = "管理簡訊模板 API")
@RestController
@RequestMapping("/api/v1/sms-templates")
@RequiredArgsConstructor
public class SmsTemplateController {

    private final SmsTemplateService smsTemplateService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00060_CREATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.SMS_TEMPLATE)
    public ResponseEntity<ApiResponse<SmsTemplateDto>> createSmsTemplate(@Valid @RequestBody SmsTemplateDto dto) {
        SmsTemplateDto created = smsTemplateService.createSmsTemplate(dto);
        return new ResponseEntity<>(ApiResponse.created(created), HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00060_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<SmsTemplateDto>> getSmsTemplateById(@PathVariable UUID id) {
        return smsTemplateService.getSmsTemplateById(id)
            .map(dto -> ResponseEntity.ok(ApiResponse.success(dto)))
            .orElse(new ResponseEntity<>(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "SmsTemplate not found"), HttpStatus.NOT_FOUND));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00060_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<SmsTemplateDto>>> getAllSmsTemplates(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "templateName,asc") String[] sort,
            @RequestParam(required = false) String templateType,
            @RequestParam(required = false) String keyword) {
        Sort.Direction direction = sort.length > 1 && sort[1].equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sort[0]));
        Page<SmsTemplateDto> templatesPage = smsTemplateService.getAllSmsTemplates(pageable, templateType, keyword);
        return ResponseEntity.ok(ApiResponse.success(templatesPage));
    }

    @GetMapping("/type/{templateType}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<SmsTemplateDto>>> getSmsTemplatesByType(@PathVariable String templateType) {
        List<SmsTemplateDto> templates = smsTemplateService.getSmsTemplatesByType(templateType);
        return ResponseEntity.ok(ApiResponse.success(templates));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00060_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.SMS_TEMPLATE)
    public ResponseEntity<ApiResponse<SmsTemplateDto>> updateSmsTemplate(@PathVariable UUID id, @Valid @RequestBody SmsTemplateDto dto) {
        SmsTemplateDto updated = smsTemplateService.updateSmsTemplate(id, dto);
        return ResponseEntity.ok(ApiResponse.success(updated));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00060_DELETE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.SMS_TEMPLATE)
    public ResponseEntity<ApiResponse<Void>> deleteSmsTemplate(@PathVariable UUID id) {
        smsTemplateService.deleteSmsTemplate(id);
        return ResponseEntity.ok(ApiResponse.success(null, "SmsTemplate deleted successfully"));
    }
} 