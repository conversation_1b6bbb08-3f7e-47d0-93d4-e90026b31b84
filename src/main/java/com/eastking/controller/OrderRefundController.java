package com.eastking.controller;

import com.eastking.model.dto.request.OrderRefundRequestDto;
import com.eastking.model.dto.response.OrderDetailDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.OrderService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.http.HttpStatus;
import java.util.UUID;
import com.eastking.enums.OrderActionTypeEnum;
import com.eastking.model.dto.request.StoreReturnRequestDto;
import com.eastking.model.dto.response.OrderRefundDto;

@RestController
@RequestMapping("/api/v1/order-refunds")
@RequiredArgsConstructor
@Tag(name = "Order Refund Management", description = "訂單退貨/換貨管理 API")
public class OrderRefundController {

    private final OrderService orderService;

    /*@PostMapping
    @PreAuthorize("hasAnyAuthority('F03010_UPDATE', 'SYS_ADMIN')") // Using a general update permission for now
    @Operation(summary = "發起門市商品訂單退貨申請")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER_REFUND)
    public ResponseEntity<ApiResponse<OrderDetailDto>> requestOrderReturn(@Valid @RequestBody OrderRefundRequestDto refundRequest) {
        OrderDetailDto updatedOrder = orderService.requestOrderReturn(refundRequest);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }*/

    @PostMapping("/{refundId}/approve")
    @PreAuthorize("hasAnyAuthority('F03011_DISPATCHAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "總公司核准派工商品訂單退貨")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER_REFUND)
    public ResponseEntity<ApiResponse<OrderDetailDto>> approveOrderReturn(@PathVariable UUID refundId) {
        OrderDetailDto updatedOrder = orderService.approveOrderReturn(refundId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{refundId}/reject")
    @PreAuthorize("hasAnyAuthority('F03011_DISPATCHAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "總公司駁回派工商品訂單退貨")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER_REFUND)
    public ResponseEntity<ApiResponse<OrderDetailDto>> rejectOrderReturn(@PathVariable UUID refundId, @Valid @RequestBody com.eastking.controller.OrderController.ReasonDto reasonDto) {
        OrderDetailDto updatedOrder = orderService.rejectOrderReturn(refundId, reasonDto.getReason());
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/initiate")
    @PreAuthorize("hasAnyAuthority('F03010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "發起門市商品訂單退貨或換貨流程")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.ORDER_REFUND)
    public ResponseEntity<ApiResponse<OrderRefundDto>> initiateReturnOrChange(
            @RequestParam UUID orderId,
            @RequestParam OrderActionTypeEnum actionType) {
        OrderRefundDto refundDto = orderService.initiateOrderReturnOrChange(orderId, actionType);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(refundDto));
    }

    @PostMapping("/initiateDispatch")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "發起派工商品訂單退貨或換貨流程")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.ORDER_REFUND)
    public ResponseEntity<ApiResponse<OrderRefundDto>> initiateDispatchReturnOrChange(
            @RequestParam UUID orderId,
            @RequestParam OrderActionTypeEnum actionType) {
        OrderRefundDto refundDto = orderService.initiateDispatchOrderReturnOrChange(orderId, actionType);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(refundDto));
    }

    @PostMapping("/{refundId}/complete-store-return")
    @PreAuthorize("hasAnyAuthority('F03010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成門市退貨/換貨並結案")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER_REFUND)
    public ResponseEntity<ApiResponse<Object>> completeStoreReturn(
            @PathVariable UUID refundId,
            @Valid @RequestBody StoreReturnRequestDto requestDto) {
        Object resultDto = orderService.completeStoreReturn(refundId, requestDto);
        return ResponseEntity.ok(ApiResponse.success(resultDto));
    }

    @PostMapping("/{refundId}/complete-dispatch-return")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成派工退貨/換貨並結案")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER_REFUND)
    public ResponseEntity<ApiResponse<Object>> completeDispatchReturn(
            @PathVariable UUID refundId,
            @Valid @RequestBody StoreReturnRequestDto requestDto) {
        Object resultDto = orderService.completeDispatchReturn(refundId, requestDto);
        return ResponseEntity.ok(ApiResponse.success(resultDto));
    }
} 