package com.eastking.controller;

import com.eastking.model.dto.response.UserMenuDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.MenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "00. 選單管理 (Menu Management)", description = "提供使用者選單相關 API")
@RestController
@RequestMapping("/api/v1/menu")
@RequiredArgsConstructor
public class MenuController {

    private final MenuService menuService;

    @GetMapping("/user-sidebar")
    @PreAuthorize("isAuthenticated()") // Ensures the user is logged in
    @Operation(summary = "取得登入使用者的側邊選單", description = "回傳目前登入使用者有權限操作的側邊選單項目結構。")
    public ResponseEntity<ApiResponse<List<UserMenuDto>>> getUserSidebarMenu() {
        List<UserMenuDto> menuData = menuService.getUserSidebarMenu();
        return ResponseEntity.ok(ApiResponse.success(menuData));
    }
} 