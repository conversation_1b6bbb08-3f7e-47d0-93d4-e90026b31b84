package com.eastking.controller;

import com.eastking.model.dto.response.WholesaleCustomerSelectDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.WholesaleCustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/wholesale-customers")
@RequiredArgsConstructor
@Tag(name = "Wholesale Customer Management", description = "批發客戶資料 API")
public class WholesaleCustomerController {

    private final WholesaleCustomerService wholesaleCustomerService;

    @GetMapping("/selectable")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "獲取所有啟用的批發客戶列表 (供下拉選單使用)")
    public ResponseEntity<ApiResponse<List<WholesaleCustomerSelectDto>>> getAllActiveSelectableCustomers() {
        List<WholesaleCustomerSelectDto> customers = wholesaleCustomerService.getAllActiveSelectableCustomers();
        return ResponseEntity.ok(ApiResponse.success(customers));
    }
} 