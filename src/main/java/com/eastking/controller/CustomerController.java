package com.eastking.controller;

import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.model.dto.CustomerDto;
import com.eastking.model.dto.CustomerDeviceDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.CustomerService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/customers")
@RequiredArgsConstructor
@Tag(name = "Member Management", description = "會員管理 API")
public class CustomerController {

    private final CustomerService customerService;
    private static final Logger logger = LoggerFactory.getLogger(CustomerController.class);

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F02010_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢會員列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<CustomerDto>>> searchCustomers(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @Parameter(description = "關鍵字 (姓名或電話)") @RequestParam(required = false) String keyword,
            @PageableDefault(size = 10, sort = "createTime,desc") Pageable pageable) {
        
        ErpCompanyDivisionEnum companyEnum = ErpCompanyDivisionEnum.fromNameIgnoreCase(companyContext);
        Short companyDivisionCode = (companyEnum != null) ? companyEnum.getCode() : null;
        
        Page<CustomerDto> customersPage = customerService.searchCustomers(keyword, null, companyDivisionCode, pageable);
        return ResponseEntity.ok(ApiResponse.success(customersPage));
    }

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F02010_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增會員")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.USER_ACCOUNT)
    public ResponseEntity<ApiResponse<CustomerDto>> createCustomer(@Valid @RequestBody CustomerDto customerDto) {
        CustomerDto createdCustomer = customerService.createCustomer(customerDto);
        return new ResponseEntity<>(ApiResponse.created(createdCustomer), HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F02010_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢會員詳情")
    public ResponseEntity<ApiResponse<CustomerDto>> getCustomerById(@PathVariable UUID id) {
        CustomerDto customer = customerService.getCustomerById(id);
        return ResponseEntity.ok(ApiResponse.success(customer));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F02010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新會員資料")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.USER_ACCOUNT)
    public ResponseEntity<ApiResponse<CustomerDto>> updateCustomer(@PathVariable UUID id, @Valid @RequestBody CustomerDto customerDto) {
        CustomerDto updatedCustomer = customerService.updateCustomer(id, customerDto);
        return ResponseEntity.ok(ApiResponse.success(updatedCustomer));
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F02010_DELETE', 'SYS_ADMIN')")
    @Operation(summary = "刪除會員")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.USER_ACCOUNT)
    public ResponseEntity<ApiResponse<Void>> deleteCustomer(@PathVariable UUID id) {
        customerService.deleteCustomer(id);
        return ResponseEntity.ok(ApiResponse.success(null, "會員刪除成功"));
    }

    @PostMapping("/{customerId}/devices")
    @PreAuthorize("hasAnyAuthority('F02010_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "為會員新增設備")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.CUSTOMER_DEVICE)
    public ResponseEntity<ApiResponse<CustomerDeviceDto>> addDeviceToCustomer(
            @PathVariable UUID customerId,
            @Valid @RequestBody CustomerDeviceDto deviceDto) {
        CustomerDeviceDto newDevice = customerService.addDeviceToCustomer(customerId, deviceDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(newDevice));
    }

    @GetMapping("/by-phone")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "依電話號碼精確查找客戶/會員 (主要用於新增訂單時快速帶入)")
    public ResponseEntity<ApiResponse<List<CustomerDto>>> findCustomerByPhoneNumber(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @Parameter(description = "電話號碼", required = true) @RequestParam String phoneNumber) {
        logger.info("Received findCustomerByPhoneNumber request with X-Company-Context: {}", companyContext);
        ErpCompanyDivisionEnum companyEnum = ErpCompanyDivisionEnum.fromNameIgnoreCase(companyContext);
        Short companyDivisionCode = (companyEnum != null) ? companyEnum.getCode() : null;
        logger.info("Received findCustomerByPhoneNumber request with companyDivisionCode: {}", companyDivisionCode);
        
        List<CustomerDto> customers = customerService.findByPhoneNumber(phoneNumber, companyDivisionCode);
        return ResponseEntity.ok(ApiResponse.success(customers));
    }
}