package com.eastking.controller;

import com.eastking.enums.ErpCompanyDivisionEnum;
import com.eastking.model.dto.ExternalWarehouseDto;
import com.eastking.model.dto.WarehouseDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.WarehouseService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Tag(name = "XW. 倉庫分區管理 (Warehouse Zoning Management)", description = "管理倉庫分區及倉庫列表相關 API")
@RestController
@RequestMapping("/api/v1/warehouses")
@RequiredArgsConstructor
public class WarehouseController {

    private final WarehouseService warehouseService;

    @Operation(summary = "新增倉庫到指定區域")
    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00090_CREATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.WAREHOUSE)
    public ResponseEntity<ApiResponse<WarehouseDto>> createWarehouse(@Valid @RequestBody WarehouseDto warehouseDto) {
        WarehouseDto createdWarehouse = warehouseService.createWarehouse(warehouseDto);
        return new ResponseEntity<>(ApiResponse.created(createdWarehouse), HttpStatus.CREATED);
    }

    @Operation(summary = "更新指定倉庫資訊")
    @PutMapping("/{warehouseId}")
    @PreAuthorize("hasAnyAuthority('F00090_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.WAREHOUSE)
    public ResponseEntity<ApiResponse<WarehouseDto>> updateWarehouse(
            @Parameter(description = "倉庫ID") @PathVariable UUID warehouseId,
            @Valid @RequestBody WarehouseDto warehouseDto) {
        WarehouseDto updatedWarehouse = warehouseService.updateWarehouse(warehouseId, warehouseDto);
        return ResponseEntity.ok(ApiResponse.success(updatedWarehouse));
    }

    @Operation(summary = "刪除指定倉庫 (軟刪除)")
    @DeleteMapping("/{warehouseId}")
    @PreAuthorize("hasAnyAuthority('F00090_DELETE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.WAREHOUSE)
    public ResponseEntity<ApiResponse<Void>> deleteWarehouse(
            @Parameter(description = "倉庫ID") @PathVariable UUID warehouseId) {
        warehouseService.deleteWarehouse(warehouseId);
        return ResponseEntity.ok(ApiResponse.success(null, "Warehouse deleted successfully."));
    }

    @Operation(summary = "根據ID取得倉庫資訊")
    @GetMapping("/{warehouseId}")
    @PreAuthorize("hasAnyAuthority('F00090_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<WarehouseDto>> getWarehouseById(
            @Parameter(description = "倉庫ID") @PathVariable UUID warehouseId) {
        WarehouseDto warehouseDto = warehouseService.getWarehouseById(warehouseId);
        return ResponseEntity.ok(ApiResponse.success(warehouseDto));
    }

    @Operation(summary = "分頁查詢所有倉庫 (可依區域及關鍵字篩選)")
    @GetMapping("/paged")
    @PreAuthorize("hasAnyAuthority('F00090_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<WarehouseDto>>> getAllWarehouses(
            @PageableDefault(sort = "warehouseName", direction = Sort.Direction.ASC) Pageable pageable,
            @Parameter(description = "區域ID (選填)") @RequestParam(required = false) UUID regionId,
            @Parameter(description = "搜尋文字(倉庫代碼或名稱，選填)") @RequestParam(required = false) String searchText) {
        Page<WarehouseDto> page = warehouseService.getAllWarehouses(pageable, regionId, searchText);
        return ResponseEntity.ok(ApiResponse.success(page));
    }
    
    @Operation(summary = "根據區域ID取得該區域所有倉庫列表")
    @GetMapping("/by-region/{regionId}")
    @PreAuthorize("hasAnyAuthority('F00090_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<List<WarehouseDto>>> getWarehousesByRegionId(
            @Parameter(description = "區域ID") @PathVariable UUID regionId) {
        List<WarehouseDto> warehouses = warehouseService.getWarehousesByRegionId(regionId);
        return ResponseEntity.ok(ApiResponse.success(warehouses));
    }

    @Operation(summary = "取得指定區域的外部倉庫列表 (用於新增倉庫時的下拉選單)", description = "此為模擬外部API(如正航)的端點")
    @GetMapping("/external/for-region/{regionId}")
    @PreAuthorize("hasAnyAuthority('F00090_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<List<ExternalWarehouseDto>>> getExternalWarehousesForDropdown(
             @Parameter(description = "區域ID") @PathVariable UUID regionId) {
        List<ExternalWarehouseDto> externalWarehouses = warehouseService.getExternalWarehousesForDropdown(regionId);
        return ResponseEntity.ok(ApiResponse.success(externalWarehouses));
    }

    @GetMapping
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "獲取倉庫列表", description = "可根據區域ID、是否為總倉、公司別進行篩選")
    public ResponseEntity<ApiResponse<List<WarehouseDto>>> getWarehouses(
            @RequestParam(required = false) UUID regionId,
            @RequestParam(required = false) Boolean isMain,
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext) {
        
        Short companyCode = "QUEYOU".equalsIgnoreCase(companyContext) ? 
                            ErpCompanyDivisionEnum.QUEYOU.getCode() : 
                            ErpCompanyDivisionEnum.EASTKING.getCode();
        
        List<WarehouseDto> warehouses = warehouseService.findWarehousesByCriteria(regionId, isMain, companyCode);
        return ResponseEntity.ok(ApiResponse.success(warehouses));
    }

    @GetMapping("/by-store/{storeId}")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "根據門市ID獲取其所在區域的倉庫")
    public ResponseEntity<ApiResponse<List<WarehouseDto>>> getWarehousesByStoreId(
            @Parameter(description = "門市ID") @PathVariable UUID storeId,
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext) {

        Short companyCode = "QUEYOU".equalsIgnoreCase(companyContext) ? 
                            ErpCompanyDivisionEnum.QUEYOU.getCode() : 
                            ErpCompanyDivisionEnum.EASTKING.getCode();

        List<WarehouseDto> warehouses = warehouseService.getWarehousesByStoreId(storeId, companyCode);
        return ResponseEntity.ok(ApiResponse.success(warehouses));
    }
} 