package com.eastking.controller;

import com.eastking.model.dto.request.*;
import com.eastking.model.dto.response.OrderDetailDto;
import com.eastking.model.dto.response.OrderSummaryDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.OrderService;
import com.eastking.enums.OrderTypeEnum;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data; // For @Data inner DTO
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/orders")
@RequiredArgsConstructor
@Tag(name = "Order Management", description = "訂單管理 API (包含門市商品訂單與派工商品訂單)")
public class OrderController {

    private final OrderService orderService;

    // DTO for request bodies requiring a reason string
    @Data // Lombok for getters, setters, toString, equals, hashCode
    static class ReasonDto {
        @NotBlank(message = "原因不得為空")
        @Schema(description = "操作原因", requiredMode = Schema.RequiredMode.REQUIRED)
        private String reason;
    }

    @PostMapping("/{orderId}/submit")
    @PreAuthorize("hasAnyAuthority('F03010_UPDATE', 'F03011_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "送出訂單以進行下一步流程")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> submitOrder(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.submitOrder(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/store-checkout")
    @PreAuthorize("hasAnyAuthority('F03010_UPDATE', 'SYS_ADMIN')") 
    @Operation(summary = "門市商品訂單結帳送出", description = "處理門市商品訂單的最終結帳，會檢查並扣除庫存。")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.STORE_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> storeCheckout(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.checkoutStoreOrder(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    // New endpoint for store manager approval
    @PostMapping("/{orderId}/store-approve")
    @PreAuthorize("hasAnyAuthority('F03011_STOREAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "店經理核准派工商品訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> storeApprove(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.storeApprove(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    // New endpoint for store manager to return order for correction
    @PostMapping("/{orderId}/store-return-for-correction")
    @PreAuthorize("hasAnyAuthority('F03011_STOREAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "店經理退回派工商品訂單要求補正")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> storeReturnForCorrection(@PathVariable UUID orderId, @Valid @RequestBody ReasonDto reasonDto) {
        OrderDetailDto updatedOrder = orderService.storeReturnForCorrection(orderId, reasonDto.getReason());
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/submit-hq-from-stock-full")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "已有庫存，派工商品訂單送出至總公司審核")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> submitHqFromStockFull(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.submitHqFromStockFull(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/hq-return-for-correction")
    @PreAuthorize("hasAnyAuthority('F03011_DISPATCHAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "總公司退回派工商品訂單要求補正")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> hqReturnForCorrection(@PathVariable UUID orderId, @Valid @RequestBody ReasonDto reasonDto) {
        OrderDetailDto updatedOrder = orderService.hqReturnForCorrection(orderId, reasonDto.getReason());
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/hq-reject")
    @PreAuthorize("hasAnyAuthority('F03011_DISPATCHAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "總公司駁回派工商品訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> hqReject(@PathVariable UUID orderId, @Valid @RequestBody ReasonDto reasonDto) {
        OrderDetailDto updatedOrder = orderService.hqReject(orderId, reasonDto.getReason());
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }
    
    @PostMapping("/{orderId}/initiate-cancellation")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "發起派工商品訂單取消流程")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> initiateCancellation(@PathVariable UUID orderId, @Valid @RequestBody ReasonDto reasonDto) {
        OrderDetailDto updatedOrder = orderService.initiateCancellation(orderId, reasonDto.getReason());
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/hq-approve")
    @PreAuthorize("hasAnyAuthority('F03011_DISPATCHAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "總公司審核通過派工商品訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> hqApprove(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.hqApprove(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/submit-for-hq-review")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')") // Assuming UPDATE permission is sufficient
    @Operation(summary = "派工商品訂單補正後重新送出至總公司審核")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> submitForHqReview(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.submitForHqReview(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/copy")
    @PreAuthorize("hasAnyAuthority('F03010_CREATE', 'SYS_ADMIN')") // Requires create permission
    @Operation(summary = "複製門市商品訂單至新的草稿訂單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> copyOrderToNew(@PathVariable UUID orderId) {
        OrderDetailDto newOrder = orderService.copyOrderToNew(orderId);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(newOrder));
    }

    // Endpoint to create a Store Product Order
    @PostMapping("/store")
    @PreAuthorize("hasAnyAuthority('F03010_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增門市商品訂單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.STORE_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> createStoreProductOrder(@Valid @RequestBody StoreProductOrderRequestDto requestDto) {
        // Ensure the DTO itself sets the orderTypeCode or it's passed/set before service call
        OrderDetailDto createdOrder = orderService.createOrder(requestDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(createdOrder));
    }

    // Endpoint to create a Dispatch Product Order
    @PostMapping("/dispatch")
    @PreAuthorize("hasAnyAuthority('F03011_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增派工商品訂單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.DISPATCH_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> createDispatchProductOrder(@Valid @RequestBody DispatchProductOrderRequestDto requestDto) {
        OrderDetailDto createdOrder = orderService.createOrder(requestDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(createdOrder));
    }

    @PutMapping("/dispatch/{orderId}")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新並送出派工商品訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> submitDispatchProductOrder(
            @PathVariable UUID orderId,
            @Valid @RequestBody DispatchProductOrderRequestDto requestDto) {
        OrderDetailDto updatedOrder = orderService.submitDispatchOrderFromForm(orderId, requestDto);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PutMapping("/dispatch/{orderId}/partial-update")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "部分更新派工商品訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> partialUpdateDispatchOrder(
            @PathVariable UUID orderId,
            @Valid @RequestBody DispatchProductOrderRequestDto requestDto) {
        OrderDetailDto updatedOrder = orderService.partialUpdateDispatchOrder(orderId, requestDto);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @GetMapping("/dispatch/{orderId}")
    @PreAuthorize("hasAnyAuthority('F03011_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID獲取派工商品訂單詳細資料")
    public ResponseEntity<ApiResponse<OrderDetailDto>> getDispatchOrderById(@PathVariable UUID orderId) {
        OrderDetailDto orderDetailDto = orderService.getDispatchOrderDetailById(orderId);
        return ResponseEntity.ok(ApiResponse.success(orderDetailDto));
    }

    @GetMapping("/{orderId}")
    @PreAuthorize("hasAnyAuthority('F03010_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID獲取門市商品訂單詳細資料")
    public ResponseEntity<ApiResponse<OrderDetailDto>> getOrderById(@PathVariable UUID orderId) {
        OrderDetailDto orderDetailDto = orderService.getOrderById(orderId);
        return ResponseEntity.ok(ApiResponse.success(orderDetailDto));
    }

    @GetMapping("/{orderId}/summary") // New Endpoint
    @PreAuthorize("hasAnyAuthority('F03010_READ', 'F03011_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID獲取訂單摘要資料")
    public ResponseEntity<ApiResponse<OrderSummaryDto>> getOrderSummaryById(@PathVariable UUID orderId) {
        OrderSummaryDto orderSummaryDto = orderService.getOrderSummaryById(orderId);
        return ResponseEntity.ok(ApiResponse.success(orderSummaryDto));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F03010_READ', 'F03011_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢訂單列表 (門市/派工)")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<OrderSummaryDto>>> searchOrders(
            @Parameter(description = "公司別 (0:東方不敗, 1:雀友)") @RequestParam(required = false) Short companyDivisionCode,
            @Parameter(description = "訂單類型 (1:門市, 2:派工)") @RequestParam(required = false) Short orderTypeCode,
            @Parameter(description = "訂單號碼") @RequestParam(required = false) String orderNumber,
            @Parameter(description = "客戶關鍵字 (姓名或電話)") @RequestParam(required = false) String customerKeyword,
            @Parameter(description = "訂單日期起 (ISO 8601 OffsetDateTime)") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime orderDateFrom,
            @Parameter(description = "訂單日期迄 (ISO 8601 OffsetDateTime)") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime orderDateTo,
            @Parameter(description = "訂單狀態碼列表") @RequestParam(required = false) List<Short> orderStatusCodes,
            @Parameter(description = "銷售門市ID") @RequestParam(required = false) UUID storeId,
            @PageableDefault(size = 10, sort = "updateTime,desc") Pageable pageable) {
        
        Page<OrderSummaryDto> ordersPage = orderService.searchOrders(
                companyDivisionCode, orderTypeCode, orderNumber, customerKeyword, 
                orderDateFrom, orderDateTo, orderStatusCodes, storeId, pageable);
        return ResponseEntity.ok(ApiResponse.success(ApiResponse.PageData.fromPage(ordersPage)));
    }
    
    @PutMapping("/{orderId}/correct")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "訂單內容補正")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> updateOrderForCorrection(
            @PathVariable UUID orderId,
            @Valid @RequestBody BaseOrderRequestDto orderRequestDto) { // Using BaseOrderRequestDto for now
        OrderDetailDto updatedOrder = orderService.updateOrderForCorrection(orderId, orderRequestDto);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    // --- Placeholder Endpoints for Status Transitions --- 

    @PostMapping("/{orderId}/dispatch-checkout")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')") // Reuse existing permission for now
    @Operation(summary = "派工商品訂單結帳送出", description = "處理派工商品訂單的結帳，會檢查庫存並進入總公司審核流程。")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.DISPATCH_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> processDispatchOrderCheckout(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.processDispatchOrderCheckout(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/submit-hq-approval")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')") // Reuse existing permission for now
    @Operation(summary = "已有庫存，送出至總公司審核 (狀態 37 -> 40)")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> submitForHqApproval(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.submitForHqApproval(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/complete-shipment-dispatch")
    @PreAuthorize("hasAnyAuthority('F03011_DISPATCHAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "完成出貨/派工 (狀態 -> 70)", description = "標記訂單已出貨或派工完成並結案。")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> markOrderShippedOrDispatchCompleted(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.markOrderShippedOrDispatchCompleted(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/request-cancellation")
    @PreAuthorize("hasAnyAuthority('F03010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "客戶/門市申請取消門市商品訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> requestOrderCancellation(
            @PathVariable UUID orderId,
            @Valid @RequestBody ReasonDto reasonDto) { // Updated to use ReasonDto
        OrderDetailDto updatedOrder = orderService.requestOrderCancellation(orderId, reasonDto.getReason());
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/approve-cancellation")
    @PreAuthorize("hasAnyAuthority('F03011_DISPATCHAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "總公司核准派工商品訂單取消 (狀態 -> 81)", description = "總公司核准訂單取消申請。原狀態 80 -> 81") // Updated description
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> approveOrderCancellation(@PathVariable UUID orderId) {
        // Assuming approverId is derived from current authenticated user in service layer
        OrderDetailDto updatedOrder = orderService.approveOrderCancellation(orderId, null); 
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PostMapping("/{orderId}/reject-cancellation")
    @PreAuthorize("hasAnyAuthority('F03011_DISPATCHAPPROVE', 'SYS_ADMIN')")
    @Operation(summary = "總公司駁回訂單取消 (狀態 -> 82)", description = "總公司駁回訂單取消申請。原狀態 80 -> 82") // Updated description
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> rejectOrderCancellation(
            @PathVariable UUID orderId,
            @Valid @RequestBody ReasonDto rejectionReasonDto) { // Updated to use ReasonDto
        // Assuming approverId is derived from current authenticated user in service layer
        OrderDetailDto updatedOrder = orderService.rejectOrderCancellation(orderId, null, rejectionReasonDto.getReason());
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }
    
    // TODO: Add endpoints for Return flow (requestOrderReturn, approveOrderReturn etc.)
    // TODO: Add endpoint for updateDispatchInfo

    @DeleteMapping("/{orderId}")
    @PreAuthorize("hasAnyAuthority('F03010_DELETE', 'F03011_DELETE', 'SYS_ADMIN')") // Assuming a DELETE permission
    @Operation(summary = "刪除草稿訂單")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<Void>> deleteOrder(@PathVariable UUID orderId) {
        orderService.deleteDraftOrder(orderId);
        return ResponseEntity.ok(ApiResponse.success(null, "訂單已成功刪除"));
    }

    @PatchMapping("/item-groups/{orderItemGroupId}/await-status")
    @PreAuthorize("hasAnyAuthority('F03011_UPDATE', 'SYS_ADMIN')") // Reuse an existing order update permission
    @Operation(summary = "設定派工商品訂單品項待料狀態")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.ORDER)
    public ResponseEntity<ApiResponse<Void>> setItemAwaitingMaterial(
            @PathVariable UUID orderItemGroupId,
            @RequestBody Map<String, Boolean> payload) {
        Boolean isAwaiting = payload.get("isAwaiting");
        if (isAwaiting == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(HttpStatus.BAD_REQUEST.value(), "Request body must contain 'isAwaiting' field."));
        }
        orderService.setItemAwaitingMaterial(orderItemGroupId, isAwaiting);
        return ResponseEntity.ok(ApiResponse.success(null, "待料狀態已更新"));
    }

    // Endpoint to create a Wholesale Product Order
    @PostMapping("/wholesale")
    @PreAuthorize("hasAnyAuthority('F03012_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增批發商品訂單")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.WHOLESALE_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> createWholesaleProductOrder(@Valid @RequestBody WholesaleProductOrderRequestDto requestDto) {
        OrderDetailDto createdOrder = orderService.createOrder(requestDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponse.created(createdOrder));
    }

    @PutMapping("/wholesale/{orderId}")
    @PreAuthorize("hasAnyAuthority('F03012_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新並送出批發商品訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.WHOLESALE_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> submitWholesaleProductOrder(
            @PathVariable UUID orderId,
            @Valid @RequestBody WholesaleProductOrderRequestDto requestDto) {
        OrderDetailDto updatedOrder = orderService.submitWholesaleOrderFromForm(orderId, requestDto);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @PutMapping("/wholesale/{orderId}/partial-update")
    @PreAuthorize("hasAnyAuthority('F03012_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "部分更新批發商品訂單")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.WHOLESALE_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> partialUpdateWholesaleOrder(
            @PathVariable UUID orderId,
            @Valid @RequestBody WholesaleProductOrderRequestDto requestDto) {
        OrderDetailDto updatedOrder = orderService.partialUpdateWholesaleOrder(orderId, requestDto);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }

    @GetMapping("/wholesale/{orderId}")
    @PreAuthorize("hasAnyAuthority('F03012_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID獲取批發商品訂單詳細資料")
    public ResponseEntity<ApiResponse<OrderDetailDto>> getWholesaleOrderById(@PathVariable UUID orderId) {
        OrderDetailDto orderDetailDto = orderService.getWholesaleOrderDetailById(orderId);
        return ResponseEntity.ok(ApiResponse.success(orderDetailDto));
    }

    @PostMapping("/{orderId}/wholesale-checkout")
    @PreAuthorize("hasAnyAuthority('F03012_UPDATE', 'SYS_ADMIN')") // Reuse existing permission for now
    @Operation(summary = "批發商品訂單結帳送出", description = "處理批發商品訂單的結帳，會檢查庫存並進入總公司審核流程。")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.WHOLESALE_ORDER)
    public ResponseEntity<ApiResponse<OrderDetailDto>> processWholesaleOrderCheckout(@PathVariable UUID orderId) {
        OrderDetailDto updatedOrder = orderService.processWholesaleOrderCheckout(orderId);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder));
    }



} 