package com.eastking.controller;

import com.eastking.model.dto.StoreTransferOrderDto;
import com.eastking.model.dto.request.TransferActionRequest;
import com.eastking.model.dto.request.RejectTransferOrderRequest;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.StoreTransferOrderService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.util.UUID;

@Tag(name = "21. 庫存管理 - 門市調撥單 (Store Transfer Orders)", description = "門市調撥單相關 API")
@RestController
@RequestMapping("/api/v1/store-transfer-orders")
@RequiredArgsConstructor
public class StoreTransferOrderController {

    private final StoreTransferOrderService storeTransferOrderService;

    @Operation(summary = "新增門市調撥申請")
    @PostMapping
    @PreAuthorize("hasAnyAuthority('F01021_CREATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.STORE_TRANSFER_ORDER)
    public ResponseEntity<ApiResponse<StoreTransferOrderDto>> createTransferOrder(@Valid @RequestBody StoreTransferOrderDto transferOrderDto) {
        StoreTransferOrderDto createdOrder = storeTransferOrderService.createTransferOrder(transferOrderDto);
        return new ResponseEntity<>(ApiResponse.created(createdOrder), HttpStatus.CREATED);
    }

    @Operation(summary = "查詢門市調撥單列表 (分頁)")
    @GetMapping
    @PreAuthorize("hasAnyAuthority('F01021_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<StoreTransferOrderDto>>> searchTransferOrders(
            @Parameter(description = "調撥類型 (ALL, OUTGOING, INCOMING)") @RequestParam(defaultValue = "ALL") String transferType,
            @Parameter(description = "建單日期起 (ISO 8601)") @RequestParam(required = false) OffsetDateTime dateFrom,
            @Parameter(description = "建單日期迄 (ISO 8601)") @RequestParam(required = false) OffsetDateTime dateTo,
            @Parameter(description = "申請狀態碼") @RequestParam(required = false) Short transferStatus,
            @Parameter(description = "關鍵字 (調撥單號)") @RequestParam(required = false) String keyword,
            @PageableDefault(sort = "requestDate", direction = Sort.Direction.DESC) Pageable pageable) {
        Page<StoreTransferOrderDto> page = storeTransferOrderService.searchTransferOrders(transferType, dateFrom, dateTo, transferStatus, keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @Operation(summary = "依ID取得門市調撥單詳情")
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F01021_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<StoreTransferOrderDto>> getTransferOrderById(@PathVariable UUID id) {
        StoreTransferOrderDto dto = storeTransferOrderService.getTransferOrderById(id);
        return ResponseEntity.ok(ApiResponse.success(dto));
    }

    @Operation(summary = "調撥門市確認轉出")
    @PostMapping("/{id}/dispatch")
    @PreAuthorize("hasAnyAuthority('F01021_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.STORE_TRANSFER_ORDER)
    public ResponseEntity<ApiResponse<StoreTransferOrderDto>> dispatchTransferOrder(
            @PathVariable UUID id,
            @Valid @RequestBody TransferActionRequest actionRequest) {
        StoreTransferOrderDto updatedOrder = storeTransferOrderService.dispatchOrder(id, actionRequest);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder, "調撥單已確認轉出。"));
    }

    @Operation(summary = "申請門市確認轉入")
    @PostMapping("/{id}/receive")
    @PreAuthorize("hasAnyAuthority('F01021_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.STORE_TRANSFER_ORDER)
    public ResponseEntity<ApiResponse<StoreTransferOrderDto>> receiveTransferOrder(
            @PathVariable UUID id,
            @Valid @RequestBody TransferActionRequest actionRequest) {
        StoreTransferOrderDto updatedOrder = storeTransferOrderService.receiveOrder(id, actionRequest);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder, "調撥單已確認轉入。"));
    }

    @Operation(summary = "取消調撥申請")
    @PostMapping("/{id}/cancel")
    @PreAuthorize("hasAnyAuthority('F01021_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.STORE_TRANSFER_ORDER)
    public ResponseEntity<ApiResponse<StoreTransferOrderDto>> cancelTransferOrder(@PathVariable UUID id) {
        StoreTransferOrderDto updatedOrder = storeTransferOrderService.cancelOrder(id);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder, "調撥申請已取消。"));
    }

    @Operation(summary = "核准調撥申請")
    @PostMapping("/{id}/approve")
    @PreAuthorize("hasAnyAuthority('F01021_APPROVE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.STORE_TRANSFER_ORDER)
    public ResponseEntity<ApiResponse<StoreTransferOrderDto>> approveTransferOrder(@PathVariable UUID id) {
        StoreTransferOrderDto updatedOrder = storeTransferOrderService.approveTransferOrder(id);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder, "調撥申請已核准。"));
    }

    @Operation(summary = "駁回調撥申請")
    @PostMapping("/{id}/reject")
    @PreAuthorize("hasAnyAuthority('F01021_APPROVE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.STORE_TRANSFER_ORDER)
    public ResponseEntity<ApiResponse<StoreTransferOrderDto>> rejectTransferOrder(
            @PathVariable UUID id,
            @Valid @RequestBody RejectTransferOrderRequest rejectionRequest) {
        StoreTransferOrderDto updatedOrder = storeTransferOrderService.rejectTransferOrder(id, rejectionRequest.getReason());
        return ResponseEntity.ok(ApiResponse.success(updatedOrder, "調撥申請已駁回。"));
    }
} 