package com.eastking.controller;

import com.eastking.model.dto.TechnicianAreaConfigDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.TechnicianAreaConfigService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/repairs/technician-areas")
@RequiredArgsConstructor
@Tag(name = "Technician Area Config", description = "各區維修人員配置 API")
public class TechnicianAreaConfigController {

    private final TechnicianAreaConfigService service;

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F06020_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢特定月份的維修人員配置")
    public ResponseEntity<ApiResponse<List<TechnicianAreaConfigDto>>> getConfigsByMonth(
            @RequestParam String month) {
        List<TechnicianAreaConfigDto> configs = service.findByMonth(month);
        return ResponseEntity.ok(ApiResponse.success(configs));
    }

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F06020_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "儲存特定月份的維修人員配置")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.TECHNICIAN_AREA_CONFIG)
    public ResponseEntity<ApiResponse<Void>> saveConfigsForMonth(
            @RequestParam String month,
            @Valid @RequestBody List<TechnicianAreaConfigDto> configs) {
        service.saveConfig(month, configs);
        return ResponseEntity.ok(ApiResponse.success(null, "配置儲存成功"));
    }
} 