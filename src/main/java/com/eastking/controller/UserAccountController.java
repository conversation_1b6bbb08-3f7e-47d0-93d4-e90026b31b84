package com.eastking.controller;

import com.eastking.model.dto.UserAccountDto;
import com.eastking.model.dto.UserAccountSlimDto;
import com.eastking.model.dto.request.ChangePasswordRequest;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.UserAccountService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import java.util.UUID;

@Tag(name = "5. 使用者帳號管理 (User Account Management)", description = "管理使用者帳號、角色指派及密碼等相關 API")
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
public class UserAccountController {

    private final UserAccountService userAccountService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00010_CREATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.USER_ACCOUNT)
    public ResponseEntity<ApiResponse<UserAccountDto>> createUser(@Valid @RequestBody UserAccountDto userDto) {
        UserAccountDto createdUser = userAccountService.createUserAccount(userDto);
        return new ResponseEntity<>(ApiResponse.created(createdUser), HttpStatus.CREATED);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00010_READ', 'SYS_ADMIN') or @authSecurityService.isSelf(authentication, #id)")
    public ResponseEntity<ApiResponse<UserAccountDto>> getUserById(@PathVariable UUID id) {
        return userAccountService.getUserAccountById(id)
            .map(dto -> ResponseEntity.ok(ApiResponse.success(dto)))
            .orElse(new ResponseEntity<>(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "User not found"), HttpStatus.NOT_FOUND));
    }

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00010_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<UserAccountDto>>> getAllUsers(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @PageableDefault(sort = "employeeId", direction = Sort.Direction.ASC) Pageable pageable,
            @RequestParam(required = false) String employeeId,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) Boolean isActive,
            @RequestParam(required = false) Short accountType) {
        Page<UserAccountDto> usersPage = userAccountService.getAllUserAccounts(pageable, employeeId, userName, isActive, accountType, companyContext);
        return ResponseEntity.ok(ApiResponse.success(usersPage));
    }
    
    @GetMapping("/selectable-staff")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "獲取所有可用於選擇的活動員工列表")
    public ResponseEntity<ApiResponse<List<UserAccountDto>>> getAllActiveUsersForSelection() {
        List<UserAccountDto> users = userAccountService.getAllActiveUsersForSelection();
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    @GetMapping("/by-role")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "依角色代碼獲取活動使用者列表", description = "例如，使用 roleCode='TECHNICIAN_ROLE' 來獲取所有技師")
    public ResponseEntity<ApiResponse<List<UserAccountDto>>> getUsersByRoleCode(
            @Parameter(description = "角色代碼 (例如 TECHNICIAN_ROLE)", required = true) @RequestParam String roleCode) {
        List<UserAccountDto> users = userAccountService.findActiveUsersByRoleCode(roleCode);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    @GetMapping("/salespersons")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "獲取銷售人員列表", description = "可選傳入 storeId 以篩選特定門市的銷售人員。")
    public ResponseEntity<ApiResponse<List<UserAccountDto>>> getSalespersons(
            @Parameter(description = "門市ID (選填)") @RequestParam(required = false) UUID storeId) {
        List<UserAccountDto> salespersons = userAccountService.getSalespersons(storeId);
        return ResponseEntity.ok(ApiResponse.success(salespersons));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00010_UPDATE', 'SYS_ADMIN') or @authSecurityService.isSelf(authentication, #id)")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.USER_ACCOUNT)
    public ResponseEntity<ApiResponse<UserAccountDto>> updateUser(@PathVariable UUID id, @Valid @RequestBody UserAccountDto userDto) {
        UserAccountDto updatedUser = userAccountService.updateUserAccount(id, userDto);
        return ResponseEntity.ok(ApiResponse.success(updatedUser));
    }
    
    @PutMapping("/{id}/roles")
    @PreAuthorize("hasAnyAuthority('F00010_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.USER_ACCOUNT)
    public ResponseEntity<ApiResponse<UserAccountDto>> updateUserRoles(@PathVariable UUID id, @RequestBody List<UUID> roleIds) {
        UserAccountDto updatedUser = userAccountService.updateUserRoles(id, roleIds);
        return ResponseEntity.ok(ApiResponse.success(updatedUser));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00010_DELETE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.USER_ACCOUNT)
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable UUID id) {
        userAccountService.deleteUserAccount(id);
        return ResponseEntity.ok(ApiResponse.success(null, "User deleted successfully"));
    }

    @PostMapping("/{id}/change-password")
    @PreAuthorize("@authSecurityService.isSelf(authentication, #id) or hasAnyAuthority('F00010_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.USER_ACCOUNT)
    public ResponseEntity<ApiResponse<Void>> changePassword(@PathVariable UUID id, @Valid @RequestBody ChangePasswordRequest request) {
        userAccountService.changePassword(id, request.getOldPassword(), request.getNewPassword());
        return ResponseEntity.ok(ApiResponse.success(null, "Password changed successfully"));
    }

    @GetMapping("/technicians")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "獲取所有技師列表", description = "用於派工指派等下拉選單。")
    public ResponseEntity<ApiResponse<List<UserAccountSlimDto>>> getTechnicians() {
        List<UserAccountSlimDto> technicians = userAccountService.getTechnicians();
        return ResponseEntity.ok(ApiResponse.success(technicians));
    }

    @GetMapping("/selectable-collaborators")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "獲取可邀請的協同技師列表", description = "根據派工單ID，篩選出與主要技師同區、且尚未被邀請的技師。")
    public ResponseEntity<ApiResponse<List<UserAccountSlimDto>>> getSelectableCollaborators(
            @Parameter(description = "派工單ID", required = true) @RequestParam UUID dispatchRepairId) {
        List<UserAccountSlimDto> technicians = userAccountService.findSelectableCollaborators(dispatchRepairId);
        return ResponseEntity.ok(ApiResponse.success(technicians));
    }
} 