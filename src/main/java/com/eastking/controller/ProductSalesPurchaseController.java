package com.eastking.controller;

import com.eastking.model.dto.ProductSalesPurchaseSummaryDto;
import com.eastking.model.dto.ProductTransactionDetailDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.ProductSalesPurchaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.util.UUID;

@Tag(name = "23. 庫存管理 - 商品進銷列表 (Product Sales & Purchase List)", description = "查詢商品進貨與銷售記錄 API")
@RestController
@RequestMapping("/api/v1/product-sales-purchase")
@RequiredArgsConstructor
public class ProductSalesPurchaseController {

    private final ProductSalesPurchaseService productSalesPurchaseService;

    @Operation(summary = "查詢商品進銷匯總列表 (分頁)", 
               description = "根據篩選條件查詢商品的進銷貨匯總情況。包括商品分類、日期範圍和關鍵字查詢。")
    @GetMapping("/summary")
    @PreAuthorize("hasAnyAuthority('F01010_READ', 'SYS_ADMIN')") // F0101 is '商品進銷列表'
    public ResponseEntity<ApiResponse<ApiResponse.PageData<ProductSalesPurchaseSummaryDto>>> searchProductSalesPurchaseSummary(
            @Parameter(description = "商品分類ID (UUID)") @RequestParam(required = false) UUID productCategoryId,
            @Parameter(description = "日期範圍起 (ISO 8601 DateTime)") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime dateFrom,
            @Parameter(description = "日期範圍迄 (ISO 8601 DateTime)") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime dateTo,
            @Parameter(description = "關鍵字 (商品條碼/名稱)") @RequestParam(required = false) String keyword,
            @PageableDefault(sort = "productName", direction = Sort.Direction.ASC, size = 20) Pageable pageable) {
        
        Page<ProductSalesPurchaseSummaryDto> page = productSalesPurchaseService.searchProductSalesPurchaseSummary(
                productCategoryId, dateFrom, dateTo, keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @Operation(summary = "查詢特定商品的交易明細 (分頁)", 
               description = "查詢單一商品的詳細進銷貨交易記錄 (依日期排序)。")
    @GetMapping("/details/{productBarcode}")
    @PreAuthorize("hasAnyAuthority('F01010_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<ProductTransactionDetailDto>>> getProductTransactionDetails(
            @Parameter(description = "商品國際條碼") @PathVariable String productBarcode,
            @Parameter(description = "日期範圍起 (ISO 8601 DateTime)") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime dateFrom,
            @Parameter(description = "日期範圍迄 (ISO 8601 DateTime)") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime dateTo,
            @PageableDefault(sort = "transactionDate", direction = Sort.Direction.DESC, size = 20) Pageable pageable) {

        Page<ProductTransactionDetailDto> page = productSalesPurchaseService.getProductTransactionDetails(
                productBarcode, dateFrom, dateTo, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }
} 