package com.eastking.controller;

import com.eastking.model.dto.TaxIdentificationDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.TaxIdentificationService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import java.util.UUID;

@Tag(name = "4.1 統編管理 (Tax Identification Management)", description = "管理統編抬頭相關 API (屬門市設定子功能)")
@RestController
@RequestMapping("/api/v1/tax-identifications")
@RequiredArgsConstructor
public class TaxIdentificationController {

    private final TaxIdentificationService taxIdService;

    @Operation(summary = "新增統編抬頭", description = "建立新的統編抬頭資料。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "統編建立成功"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "無效的請求資料")
    })
    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00160_CREATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.SYSTEM_CONFIG)
    public ResponseEntity<ApiResponse<TaxIdentificationDto>> createTaxId(@Valid @RequestBody TaxIdentificationDto dto) {
        TaxIdentificationDto created = taxIdService.createTaxIdentification(dto);
        return new ResponseEntity<>(ApiResponse.created(created), HttpStatus.CREATED);
    }

    @Operation(summary = "取得特定統編抬頭", description = "依照ID取得特定統編抬頭的詳細資訊。")
    @GetMapping("/{id}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<TaxIdentificationDto>> getTaxIdById(@Parameter(description = "統編ID") @PathVariable UUID id) {
        return taxIdService.getTaxIdentificationById(id)
            .map(taxDto -> ResponseEntity.ok(ApiResponse.success(taxDto)))
            .orElse(new ResponseEntity<>(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "TaxIdentification not found"), HttpStatus.NOT_FOUND));
    }

    @Operation(summary = "查詢統編抬頭列表 (分頁)", description = "根據關鍵字查詢統編抬頭列表，結果分頁。")
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<TaxIdentificationDto>>> getAllTaxIds(
            @Parameter(description = "頁碼 (0-indexed)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每頁筆數") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "排序欄位與方向 (eg: companyName,asc)") @RequestParam(defaultValue = "companyName,asc") String[] sort,
            @Parameter(description = "關鍵字 (統一編號/公司抬頭)") @RequestParam(required = false) String keyword) {
        Sort.Direction direction = sort.length > 1 && sort[1].equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sort[0]));
        Page<TaxIdentificationDto> taxIdsPage = taxIdService.getAllTaxIdentifications(pageable, keyword);
        return ResponseEntity.ok(ApiResponse.success(taxIdsPage));
    }
    
    @Operation(summary = "查詢所有統編抬頭列表 (無分頁)", description = "取得所有統編抬頭列表，用於下拉選單等。")
    @GetMapping("/list")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<List<TaxIdentificationDto>>> getAllTaxIdsList() {
        List<TaxIdentificationDto> taxIds = taxIdService.getAllTaxIdentificationsList();
        return ResponseEntity.ok(ApiResponse.success(taxIds));
    }

    @Operation(summary = "更新統編抬頭資料", description = "依照ID更新現有的統編抬頭資料。")
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00160_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.SYSTEM_CONFIG)
    public ResponseEntity<ApiResponse<TaxIdentificationDto>> updateTaxId(@Parameter(description = "統編ID") @PathVariable UUID id, @Valid @RequestBody TaxIdentificationDto dto) {
        TaxIdentificationDto updated = taxIdService.updateTaxIdentification(id, dto);
        return ResponseEntity.ok(ApiResponse.success(updated));
    }

    @Operation(summary = "刪除統編抬頭", description = "依照ID刪除統編抬頭 (軟刪除)。")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00160_DELETE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.SYSTEM_CONFIG)
    public ResponseEntity<ApiResponse<Void>> deleteTaxId(@Parameter(description = "統編ID") @PathVariable UUID id) {
        taxIdService.deleteTaxIdentification(id);
        return ResponseEntity.ok(ApiResponse.success(null, "TaxIdentification deleted successfully"));
    }
} 