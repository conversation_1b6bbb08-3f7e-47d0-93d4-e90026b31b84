package com.eastking.controller;

import com.eastking.model.dto.CustomerSegmentDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.CustomerSegmentService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Tag(name = "10. 客群管道管理 (Customer Segments)", description = "管理客群管道 API")
@RestController
@RequestMapping("/api/v1/customer-segments")
@RequiredArgsConstructor
public class CustomerSegmentController {

    private final CustomerSegmentService segmentService;

    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00130_CREATE', 'SYS_ADMIN')")
    @Operation(summary = "新增客群管道")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.CUSTOMER_SEGMENT)
    public ResponseEntity<ApiResponse<CustomerSegmentDto>> createSegment(@Valid @RequestBody CustomerSegmentDto dto) {
        CustomerSegmentDto created = segmentService.createSegment(dto);
        return new ResponseEntity<>(ApiResponse.created(created), HttpStatus.CREATED);
    }

    @PutMapping("/{segmentId}")
    @PreAuthorize("hasAnyAuthority('F00130_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新客群管道名稱")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.CUSTOMER_SEGMENT)
    public ResponseEntity<ApiResponse<CustomerSegmentDto>> updateSegmentName(
            @PathVariable UUID segmentId,
            @Valid @RequestBody CustomerSegmentDto dto) { // DTO likely only needs segmentName for this specific update
        CustomerSegmentDto updated = segmentService.updateSegment(segmentId, dto);
        return ResponseEntity.ok(ApiResponse.success(updated));
    }

    @DeleteMapping("/{segmentId}")
    @PreAuthorize("hasAnyAuthority('F00130_DELETE', 'SYS_ADMIN')")
    @Operation(summary = "刪除客群管道")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.CUSTOMER_SEGMENT)
    public ResponseEntity<ApiResponse<Void>> deleteSegment(@PathVariable UUID segmentId) {
        segmentService.deleteSegment(segmentId);
        return ResponseEntity.ok(ApiResponse.success(null, "客群管道已刪除"));
    }

    @GetMapping("/active")
    @PreAuthorize("hasAnyAuthority('F00130_READ', 'SYS_ADMIN') or isAuthenticated()")
    @Operation(summary = "取得所有啟用的客群管道")
    public ResponseEntity<ApiResponse<List<CustomerSegmentDto>>> getActiveSegments() {
        return ResponseEntity.ok(ApiResponse.success(segmentService.getActiveSegments()));
    }

    @GetMapping("/inactive")
    @PreAuthorize("hasAnyAuthority('F00130_READ', 'SYS_ADMIN')")
    @Operation(summary = "取得所有禁用的客群管道")
    public ResponseEntity<ApiResponse<List<CustomerSegmentDto>>> getInactiveSegments() {
        return ResponseEntity.ok(ApiResponse.success(segmentService.getInactiveSegments()));
    }

    @PutMapping("/{segmentId}/status")
    @PreAuthorize("hasAnyAuthority('F00130_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "啟用/禁用客群管道")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.CUSTOMER_SEGMENT)
    public ResponseEntity<ApiResponse<CustomerSegmentDto>> setSegmentStatus(
            @PathVariable UUID segmentId,
            @RequestParam boolean isActive) {
        CustomerSegmentDto updated = segmentService.setSegmentStatus(segmentId, isActive);
        return ResponseEntity.ok(ApiResponse.success(updated));
    }

    @PutMapping("/active/order")
    @PreAuthorize("hasAnyAuthority('F00130_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "更新啟用客群管道的排序")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.CUSTOMER_SEGMENT)
    public ResponseEntity<ApiResponse<Void>> updateActiveSegmentsOrder(@RequestBody List<CustomerSegmentDto> orderedSegments) {
        segmentService.updateActiveSegmentsOrder(orderedSegments);
        return ResponseEntity.ok(ApiResponse.success(null, "啟用客群管道排序已更新"));
    }
    
    @GetMapping("/{segmentId}") // Added for fetching single segment for edit
    @PreAuthorize("hasAnyAuthority('F00130_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID取得客群管道")
    public ResponseEntity<ApiResponse<CustomerSegmentDto>> getSegmentById(@PathVariable UUID segmentId) {
        return ResponseEntity.ok(ApiResponse.success(segmentService.getSegmentById(segmentId)));
    }
} 