package com.eastking.controller;

import com.eastking.enums.ProductMenuTypeEnum;
import com.eastking.model.dto.ProductMenuCategoryDto;
import com.eastking.model.dto.ProductMenuItemDto;
import com.eastking.model.dto.ProductMenuNodeDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.ProductMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Tag(name = "8. 商品選單管理 (Product Menu Management)", description = "管理商品側邊選單的分類、項目及排序 API")
@RestController
@RequestMapping("/api/v1/product-menus")
@RequiredArgsConstructor
public class ProductMenuController {

    private final ProductMenuService productMenuService;

    @Operation(summary = "取得指定選單類型的完整層級樹", description = "獲取指定選單類型 (例如 DISPATCH, STORE) 的完整分類與商品層級樹狀結構。")
    @GetMapping("/tree/{menuType}")
    @PreAuthorize("hasAnyAuthority('F00080_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<List<ProductMenuNodeDto>>> getMenuTree(
            @Parameter(description = "選單類型 (DISPATCH 或 STORE)", required = true, in = ParameterIn.PATH) 
            @PathVariable ProductMenuTypeEnum menuType) {
        List<ProductMenuNodeDto> menuTree = productMenuService.getMenuTree(menuType);
        return ResponseEntity.ok(ApiResponse.success(menuTree));
    }

    @Operation(summary = "新增商品選單分類", description = "在指定的選單類型下新增一個分類。可指定上層分類ID以建立子分類。")
    @PostMapping("/categories")
    @PreAuthorize("hasAnyAuthority('F00080_CREATE', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ProductMenuCategoryDto>> createCategory(@Valid @RequestBody ProductMenuCategoryDto categoryDto) {
        ProductMenuCategoryDto createdCategory = productMenuService.createCategory(categoryDto);
        return new ResponseEntity<>(ApiResponse.created(createdCategory), HttpStatus.CREATED);
    }

    @Operation(summary = "更新商品選單分類", description = "更新指定ID的分類資訊。")
    @PutMapping("/categories/{categoryId}")
    @PreAuthorize("hasAnyAuthority('F00080_UPDATE', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ProductMenuCategoryDto>> updateCategory(
            @PathVariable UUID categoryId, 
            @Valid @RequestBody ProductMenuCategoryDto categoryDto) {
        ProductMenuCategoryDto updatedCategory = productMenuService.updateCategory(categoryId, categoryDto);
        return ResponseEntity.ok(ApiResponse.success(updatedCategory));
    }

    @Operation(summary = "刪除商品選單分類", description = "刪除指定ID的分類及其下所有子分類和商品連結。此為邏輯刪除。")
    @DeleteMapping("/categories/{categoryId}")
    @PreAuthorize("hasAnyAuthority('F00080_DELETE', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteCategory(@PathVariable UUID categoryId) {
        productMenuService.deleteCategory(categoryId);
        return ResponseEntity.ok(ApiResponse.success(null, "Category deleted successfully."));
    }

    @Operation(summary = "新增商品至分類", description = "將一個商品加入指定的分類中。")
    @PostMapping("/categories/{categoryId}/items")
    @PreAuthorize("hasAnyAuthority('F00080_UPDATE', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ProductMenuItemDto>> addItemToCategory(
            @PathVariable UUID categoryId,
            @Valid @RequestBody ProductMenuItemDto itemDto) {
        ProductMenuItemDto createdItem = productMenuService.addItemToCategory(categoryId, itemDto);
        return new ResponseEntity<>(ApiResponse.created(createdItem), HttpStatus.CREATED);
    }

    @Operation(summary = "從分類中移除商品", description = "移除指定的商品選單項目。此為邏輯刪除。")
    @DeleteMapping("/items/{itemId}")
    @PreAuthorize("hasAnyAuthority('F00080_UPDATE', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<Void>> removeItemFromCategory(@PathVariable UUID itemId) {
        productMenuService.removeItemFromCategory(itemId);
        return ResponseEntity.ok(ApiResponse.success(null, "Menu item removed successfully."));
    }
    
    @Operation(summary = "更新選單排序", description = "更新指定選單類型下的分類與商品排序。傳入的節點列表應包含ID、類型(CATEGORY/ITEM)、新排序值，以及可能的父節點ID變更。")
    @PutMapping("/order/{menuType}")
    @PreAuthorize("hasAnyAuthority('F00080_UPDATE', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateMenuOrder(
            @Parameter(description = "選單類型 (DISPATCH 或 STORE)", required = true, in = ParameterIn.PATH) 
            @PathVariable ProductMenuTypeEnum menuType,
            @RequestBody List<ProductMenuNodeDto> orderedNodes) {
        productMenuService.updateMenuOrder(menuType, orderedNodes);
        return ResponseEntity.ok(ApiResponse.success(null, "Menu order updated successfully."));
    }
} 