package com.eastking.controller;

import com.eastking.enums.MaterialTypeStatusEnum;
import com.eastking.model.dto.request.PickingListQueryDto;
import com.eastking.model.dto.response.PickingListDetailDto;
import com.eastking.model.dto.response.PickingListSummaryDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.PickingListService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;
import java.util.UUID;

@Tag(name = "41. 倉庫管理 - 揀料單管理", description = "揀料單查詢與操作 API")
@RestController
@RequestMapping("/api/v1/picking-lists")
@RequiredArgsConstructor
public class PickingListController {

    private final PickingListService pickingListService;

    @GetMapping
    @PreAuthorize("hasAnyAuthority('F07010_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢揀料單列表 (分頁)")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<PickingListSummaryDto>>> searchPickingLists(
            @ParameterObject PickingListQueryDto queryDto,
            @PageableDefault(size = 15, sort = "createTime", direction = Sort.Direction.DESC) Pageable pageable) {
        Page<PickingListSummaryDto> page = pickingListService.searchPickingLists(queryDto, MaterialTypeStatusEnum.PICKING, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @GetMapping("/refund")
    @PreAuthorize("hasAnyAuthority('F07020_READ', 'SYS_ADMIN')")
    @Operation(summary = "查詢驗料單列表 (分頁)")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<PickingListSummaryDto>>> searchRefundPickingLists(
            @ParameterObject PickingListQueryDto queryDto,
            @PageableDefault(size = 15, sort = "createTime", direction = Sort.Direction.DESC) Pageable pageable) {
        Page<PickingListSummaryDto> page = pickingListService.searchPickingLists(queryDto, MaterialTypeStatusEnum.REFUND_PICKING, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F07010_READ', 'F07020_READ', 'SYS_ADMIN')")
    @Operation(summary = "依ID查詢揀料單及驗料單詳情")
    public ResponseEntity<ApiResponse<PickingListDetailDto>> getPickingListDetail(@PathVariable UUID id) {
        PickingListDetailDto detail = pickingListService.getPickingListDetailById(id);
        return ResponseEntity.ok(ApiResponse.success(detail));
    }

    @PostMapping("/{orderId}/complete-picking")
    @PreAuthorize("hasAnyAuthority('F07010_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成揀料")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.PICKING_LIST)
    public ResponseEntity<ApiResponse<Void>> completePicking(
            @PathVariable UUID orderId,
            @Valid @RequestBody com.eastking.model.dto.request.CompletePickingRequestDto requestDto) {
        pickingListService.completePicking(orderId, requestDto);
        return ResponseEntity.ok(ApiResponse.success(null, "揀料已完成"));
    }

    @PostMapping("/{orderId}/complete-refund-picking")
    @PreAuthorize("hasAnyAuthority('F07020_UPDATE', 'SYS_ADMIN')")
    @Operation(summary = "完成驗料")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.PICKING_LIST)
    public ResponseEntity<ApiResponse<Void>> completeRefundPicking(
            @PathVariable UUID orderId,
            @Valid @RequestBody com.eastking.model.dto.request.CompletePickingRequestDto requestDto) {
        pickingListService.completeRefundPicking(orderId, requestDto);
        return ResponseEntity.ok(ApiResponse.success(null, "驗料已完成"));
    }
} 