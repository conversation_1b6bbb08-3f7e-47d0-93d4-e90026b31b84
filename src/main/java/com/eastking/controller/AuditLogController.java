package com.eastking.controller;

import com.eastking.model.dto.AuditLogDto;
import com.eastking.model.dto.AuditLogQueryDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.AuditLogService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "XA. 操作歷程管理 (Audit Log Management)", description = "查詢系統操作歷程記錄 API")
@RestController
@RequestMapping("/api/v1/audit-logs")
@RequiredArgsConstructor
public class AuditLogController {

    private final AuditLogService auditLogService;

    @Operation(summary = "分頁查詢操作歷程記錄")
    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00110_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<AuditLogDto>>> getAuditLogs(
            @ParameterObject AuditLogQueryDto queryDto, // Use @ParameterObject for complex query DTOs with GET
            @PageableDefault(sort = "operationTime", direction = Sort.Direction.DESC) Pageable pageable) {
        Page<AuditLogDto> page = auditLogService.getAuditLogs(queryDto, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }
    
    @Operation(summary = "取得操作歷程中所有唯一的資料種類列表 (用於篩選器)")
    @GetMapping("/meta/data-types")
    @PreAuthorize("hasAnyAuthority('F00110_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<List<String>>> getDistinctDataTypes() {
        return ResponseEntity.ok(ApiResponse.success(auditLogService.getDistinctDataTypes()));
    }

    @Operation(summary = "取得操作歷程中所有唯一的操作者部門列表 (用於篩選器)")
    @GetMapping("/meta/department-names")
    @PreAuthorize("hasAnyAuthority('F00110_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<List<String>>> getDistinctUserDepartmentNames() {
        return ResponseEntity.ok(ApiResponse.success(auditLogService.getDistinctUserDepartmentNames()));
    }
} 