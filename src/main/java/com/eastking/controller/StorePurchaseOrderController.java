package com.eastking.controller;

import com.eastking.model.dto.StorePurchaseOrderDto;
import com.eastking.model.dto.request.PurchaseOrderDiscrepancyRequest;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.StorePurchaseOrderService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.util.UUID;

@Tag(name = "20. 庫存管理 - 門市進貨單 (Store Purchase Orders)", description = "門市進貨單相關 API")
@RestController
@RequestMapping("/api/v1/store-purchase-orders")
@RequiredArgsConstructor
public class StorePurchaseOrderController {

    private final StorePurchaseOrderService storePurchaseOrderService;

    @Operation(summary = "查詢門市進貨單列表 (分頁)")
    @GetMapping
    @PreAuthorize("hasAnyAuthority('F01012_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<StorePurchaseOrderDto>>> searchStorePurchaseOrders(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @Parameter(description = "收貨門市ID") @RequestParam(required = false) UUID storeId,
            @Parameter(description = "出貨日期起 (ISO 8601)") @RequestParam(required = false) OffsetDateTime shipmentDateFrom,
            @Parameter(description = "出貨日期迄 (ISO 8601)") @RequestParam(required = false) OffsetDateTime shipmentDateTo,
            @Parameter(description = "進貨單狀態碼") @RequestParam(required = false) Short purchaseOrderStatus,
            @Parameter(description = "關鍵字 (進貨單號)") @RequestParam(required = false) String keyword,
            @PageableDefault(sort = "createTime", direction = Sort.Direction.DESC) Pageable pageable) {
        Page<StorePurchaseOrderDto> page = storePurchaseOrderService.searchStorePurchaseOrders(
                storeId, shipmentDateFrom, shipmentDateTo, purchaseOrderStatus, keyword, companyContext, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @Operation(summary = "依ID取得門市進貨單詳情")
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F01012_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<StorePurchaseOrderDto>> getStorePurchaseOrderById(@PathVariable UUID id) {
        StorePurchaseOrderDto dto = storePurchaseOrderService.getStorePurchaseOrderById(id);
        return ResponseEntity.ok(ApiResponse.success(dto));
    }

    @Operation(summary = "回報進貨數量異常")
    @PostMapping("/{id}/report-discrepancy")
    @PreAuthorize("hasAnyAuthority('F01012_UPDATE', 'SYS_ADMIN')") 
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.STORE_ORDER)
    public ResponseEntity<ApiResponse<StorePurchaseOrderDto>> reportDiscrepancy(
            @PathVariable UUID id,
            @Valid @RequestBody PurchaseOrderDiscrepancyRequest discrepancyRequest) {
        StorePurchaseOrderDto updatedOrder = storePurchaseOrderService.reportDiscrepancy(id, discrepancyRequest);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder, "數量異常已成功回報。"));
    }

    @Operation(summary = "確認進貨完成")
    @PostMapping("/{id}/confirm-receipt")
    @PreAuthorize("hasAnyAuthority('F01012_UPDATE', 'SYS_ADMIN')") 
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.STORE_ORDER)
    public ResponseEntity<ApiResponse<StorePurchaseOrderDto>> confirmReceipt(@PathVariable UUID id) {
        StorePurchaseOrderDto updatedOrder = storePurchaseOrderService.confirmReceipt(id);
        return ResponseEntity.ok(ApiResponse.success(updatedOrder, "進貨已確認完成。"));
    }
} 