package com.eastking.controller;

import com.eastking.model.dto.DistributorDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.DistributorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/distributors")
@RequiredArgsConstructor
@Tag(name = "Distributor Management", description = "經銷商資料 API")
public class DistributorController {

    private final DistributorService distributorService;

    @GetMapping
    @PreAuthorize("isAuthenticated()") // Any authenticated user can view the list for selection
    @Operation(summary = "獲取所有啟用的經銷商列表")
    public ResponseEntity<ApiResponse<List<DistributorDto>>> getAllActiveDistributors() {
        List<DistributorDto> distributors = distributorService.getAllActiveSelectableDistributors();
        return ResponseEntity.ok(ApiResponse.success(distributors));
    }

    // Other endpoints (GET by ID, POST, PUT, DELETE) can be added here if full CRUD management from this app is needed.
} 