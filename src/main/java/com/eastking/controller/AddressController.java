package com.eastking.controller;

import com.eastking.model.dto.CityDto;
import com.eastking.model.dto.DistrictDto;
import com.eastking.model.dto.StreetDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.AddressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Tag(name = "00. 地址資料 (Address Data)", description = "提供台灣縣市、鄉鎮市區、街道等地址資料 API")
@RestController
@RequestMapping("/api/v1/address")
@RequiredArgsConstructor
@PreAuthorize("isAuthenticated()") // 整體授權，確保只有登入用戶可訪問
public class AddressController {

    private final AddressService addressService;

    @Operation(summary = "取得所有縣市列表")
    @GetMapping("/cities")
    public ResponseEntity<ApiResponse<List<CityDto>>> getAllCities() {
        List<CityDto> cities = addressService.getAllCities();
        return ResponseEntity.ok(ApiResponse.success(cities));
    }

    @Operation(summary = "取得所有縣市及其對應的行政區列表")
    @GetMapping("/cities-with-districts")
    public ResponseEntity<ApiResponse<List<CityDto>>> getCitiesWithDistricts() {
        return ResponseEntity.ok(ApiResponse.success(addressService.getCitiesWithDistricts()));
    }

    @Operation(summary = "依縣市ID取得鄉鎮市區列表")
    @GetMapping("/districts/{cityId}")
    public ResponseEntity<ApiResponse<List<DistrictDto>>> getDistrictsByCityId(
            @Parameter(description = "縣市的UUID") @PathVariable UUID cityId) {
        List<DistrictDto> districts = addressService.getDistrictsByCityId(cityId);
        return ResponseEntity.ok(ApiResponse.success(districts));
    }

    @Operation(summary = "依縣市和鄉鎮市區名稱取得街道列表 (代理中華郵政API)")
    @GetMapping("/streets")
    public ResponseEntity<ApiResponse<List<StreetDto>>> getStreets(
            @Parameter(description = "縣市名稱", required = true) @RequestParam String cityName,
            @Parameter(description = "鄉鎮市區名稱", required = true) @RequestParam String districtName) {
        List<StreetDto> streets = addressService.getStreetsFromExternalApi(cityName, districtName);
        return ResponseEntity.ok(ApiResponse.success(streets));
    }
} 