package com.eastking.controller;

import com.eastking.model.dto.BonusItemDto;
import com.eastking.model.dto.TechnicianBonusSetupDto;
import com.eastking.model.dto.TechnicianRoleConfigDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.TechnicianBonusService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Tag(name = "XT. 技師獎金設定 (Technician Bonus Settings)", description = "管理技師角色及各項目獎金 API")
@RestController
@RequestMapping("/api/v1/technician-bonus")
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('F00100_READ', 'SYS_ADMIN', 'F00100_UPDATE')") // Base auth for controller
public class TechnicianBonusController {

    private final TechnicianBonusService technicianBonusService;

    @Operation(summary = "新增系統角色為技師角色")
    @PostMapping("/roles/{roleId}")
    @PreAuthorize("hasAnyAuthority('F00100_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.TECHNICIAN_BONUS)
    public ResponseEntity<ApiResponse<TechnicianRoleConfigDto>> addTechnicianRole(
            @Parameter(description = "系統角色ID") @PathVariable UUID roleId) {
        TechnicianRoleConfigDto configDto = technicianBonusService.addTechnicianRole(roleId);
        return new ResponseEntity<>(ApiResponse.success(configDto), HttpStatus.CREATED);
    }

    @Operation(summary = "移除技師角色設定 (軟刪除)")
    @DeleteMapping("/roles/{technicianRoleConfigId}")
    @PreAuthorize("hasAnyAuthority('F00100_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.TECHNICIAN_BONUS)
    public ResponseEntity<ApiResponse<Void>> removeTechnicianRole(
            @Parameter(description = "技師角色設定ID") @PathVariable UUID technicianRoleConfigId) {
        technicianBonusService.removeTechnicianRole(technicianRoleConfigId);
        return ResponseEntity.ok(ApiResponse.success(null, "Technician role configuration removed."));
    }

    @Operation(summary = "取得所有啟用的技師角色設定")
    @GetMapping("/roles")
    @PreAuthorize("hasAnyAuthority('F00100_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<List<TechnicianRoleConfigDto>>> getActiveTechnicianRoles() {
        List<TechnicianRoleConfigDto> roles = technicianBonusService.getActiveTechnicianRoles();
        return ResponseEntity.ok(ApiResponse.success(roles));
    }

    @Operation(summary = "新增或更新獎金項目 (包含其獎金設定)")
    @PostMapping("/items")
    @PreAuthorize("hasAnyAuthority('F00100_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.BONUS_ITEM)
    public ResponseEntity<ApiResponse<BonusItemDto>> saveBonusItem(@Valid @RequestBody BonusItemDto bonusItemDto) {
        BonusItemDto savedItem = technicianBonusService.saveBonusItem(bonusItemDto);
        return new ResponseEntity<>(ApiResponse.success(savedItem), bonusItemDto.getBonusItemId() == null ? HttpStatus.CREATED : HttpStatus.OK);
    }
    
    @Operation(summary = "根據ID取得獎金項目及其設定")
    @GetMapping("/items/{itemId}")
    @PreAuthorize("hasAnyAuthority('F00100_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<BonusItemDto>> getBonusItemById(@PathVariable UUID itemId) {
        BonusItemDto itemDto = technicianBonusService.getBonusItemById(itemId);
        return ResponseEntity.ok(ApiResponse.success(itemDto));
    }

    @Operation(summary = "取得所有啟用的獎金項目及其設定")
    @GetMapping("/items")
    @PreAuthorize("hasAnyAuthority('F00100_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<List<BonusItemDto>>> getAllBonusItemsWithSettings() {
        List<BonusItemDto> items = technicianBonusService.getAllBonusItemsWithSettings();
        return ResponseEntity.ok(ApiResponse.success(items));
    }

    @Operation(summary = "刪除獎金項目及其所有相關設定 (軟刪除)")
    @DeleteMapping("/items/{bonusItemId}")
    @PreAuthorize("hasAnyAuthority('F00100_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.BONUS_ITEM)
    public ResponseEntity<ApiResponse<Void>> deleteBonusItem(
            @Parameter(description = "獎金項目ID") @PathVariable UUID bonusItemId) {
        technicianBonusService.deleteBonusItem(bonusItemId);
        return ResponseEntity.ok(ApiResponse.success(null, "Bonus item and its settings deleted."));
    }

    @Operation(summary = "儲存整體技師獎金設定 (批量更新多個項目及其獎金)")
    @PostMapping("/setup")
    @PreAuthorize("hasAnyAuthority('F00100_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.TECHNICIAN_BONUS)
    public ResponseEntity<ApiResponse<Void>> saveTechnicianBonusSetup(@Valid @RequestBody TechnicianBonusSetupDto setupDto) {
        technicianBonusService.saveTechnicianBonusSetup(setupDto);
        return ResponseEntity.ok(ApiResponse.success(null, "Technician bonus setup saved successfully."));
    }
} 