package com.eastking.controller;

import com.eastking.model.dto.ErpMahjongTableStockDto;
import com.eastking.model.dto.ErpWarehouseDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.ElectricMahjongTableStockService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Tag(name = "24. 庫存管理 - 電動桌庫存列表(ERP)", description = "查詢從ERP同步的電動麻將桌庫存 API")
@RestController
@RequestMapping("/api/v1/erp-mahjong-stock")
@RequiredArgsConstructor
public class ElectricMahjongTableStockController {

    private final ElectricMahjongTableStockService electricMahjongTableStockService;

    @Operation(summary = "查詢電動麻將桌ERP庫存列表 (分頁)", 
               description = "根據篩選條件查詢從ERP同步的電動麻將桌庫存數據。商品分類用於篩選屬於電動桌的商品。")
    @GetMapping
    @PreAuthorize("hasAnyAuthority('F01020_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<ErpMahjongTableStockDto>>> getErpMahjongTableStock(
            @Parameter(description = "ERP倉庫代碼") @RequestParam(required = false) String erpWarehouseCode,
            @Parameter(description = "商品分類ID (用於篩選電動桌類型商品)") @RequestParam(required = false) UUID productCategoryId, 
            @Parameter(description = "關鍵字 (商品條碼/名稱)") @RequestParam(required = false) String productKeyword,
            @PageableDefault(sort = "productName", direction = Sort.Direction.ASC) Pageable pageable) {
        
        Page<ErpMahjongTableStockDto> page = electricMahjongTableStockService.getErpMahjongTableStock(
                erpWarehouseCode, productCategoryId, productKeyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @Operation(summary = "取得所有用於篩選的ERP倉庫列表")
    @GetMapping("/erp-warehouses")
    @PreAuthorize("hasAnyAuthority('F01020_READ', 'SYS_ADMIN')") // Reuse F0102_READ or define a more general one if needed
    public ResponseEntity<ApiResponse<List<ErpWarehouseDto>>> getDistinctErpWarehousesForFilter() {
        List<ErpWarehouseDto> warehouses = electricMahjongTableStockService.getDistinctErpWarehouses();
        return ResponseEntity.ok(ApiResponse.success(warehouses));
    }
} 