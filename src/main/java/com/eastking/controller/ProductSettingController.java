package com.eastking.controller;

import com.eastking.model.dto.ProductSettingDto;
import com.eastking.model.dto.ProductSearchResultDto;
import com.eastking.model.dto.ProductSearchResultWithGiftDto;
import com.eastking.model.vo.ApiResponse;
import com.eastking.service.ProductSettingService;
import com.eastking.aop.AuditAction;
import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Tag(name = "7. 商品設定管理 (Product Setting Management)", description = "管理商品特有設定，如保固、價格、銷售效期及職權折扣等 API")
@RestController
@RequestMapping("/api/v1/product-settings")
@RequiredArgsConstructor
public class ProductSettingController {

    private final ProductSettingService productSettingService;

    @Operation(summary = "新增商品設定", description = "建立新的商品設定及其相關職權折扣。商品條碼對於未刪除的設定必須是唯一的。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "201", description = "商品設定成功建立", 
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "無效的輸入資料 (例如：欄位為空、條碼重複)",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class)))
    })
    @PostMapping
    @PreAuthorize("hasAnyAuthority('F00070_CREATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.CREATE, dataType = AuditDataTypeEnum.PRODUCT_SETTING)
    public ResponseEntity<ApiResponse<ProductSettingDto>> createProductSetting(@Valid @RequestBody ProductSettingDto productSettingDto) {
        ProductSettingDto createdSetting = productSettingService.createProductSetting(productSettingDto);
        return new ResponseEntity<>(ApiResponse.created(createdSetting), HttpStatus.CREATED);
    }

    @Operation(summary = "取得所有商品設定 (分頁)", description = "依條件查詢商品設定列表，支援分頁。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功取得商品設定列表",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class)))
    })
    @GetMapping
    @PreAuthorize("hasAnyAuthority('F00070_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<ProductSettingDto>>> getAllProductSettings(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @PageableDefault(size = 10, sort = "updateTime") Pageable pageable,
            @Parameter(description = "商品國際條碼 (模糊查詢)", in = ParameterIn.QUERY) @RequestParam(required = false) String productBarcode,
            @Parameter(description = "商品名稱 (模糊查詢)", in = ParameterIn.QUERY) @RequestParam(required = false) String productName,
            @Parameter(description = "是否啟用 (true/false)", in = ParameterIn.QUERY) @RequestParam(required = false) Boolean isActive) {
        Page<ProductSettingDto> settingsPage = productSettingService.getAllProductSettings(pageable, productBarcode, productName, isActive, companyContext);
        return ResponseEntity.ok(ApiResponse.success(settingsPage));
    }

    @Operation(summary = "依ID取得商品設定", description = "取得特定ID的商品設定詳細資訊，包含職權折扣。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功取得商品設定",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "找不到指定ID的商品設定",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class)))
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00070_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ProductSettingDto>> getProductSettingById(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @PathVariable UUID id) {
        return productSettingService.getProductSettingById(id, companyContext)
            .map(dto -> ResponseEntity.ok(ApiResponse.success(dto)))
            .orElse(new ResponseEntity<>(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "ProductSetting not found with id: " + id), HttpStatus.NOT_FOUND));
    }
    
    @Operation(summary = "依商品條碼取得商品設定", description = "取得特定商品條碼的商品設定詳細資訊，包含職權折扣。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功取得商品設定",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "找不到指定條碼的商品設定",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class)))
    })
    @GetMapping("/barcode/{barcode}")
    @PreAuthorize("hasAnyAuthority('F00070_READ', 'SYS_ADMIN')")
    public ResponseEntity<ApiResponse<ProductSettingDto>> getProductSettingByBarcode(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @PathVariable String barcode) {
        return productSettingService.getProductSettingByBarcode(barcode, companyContext)
            .map(dto -> ResponseEntity.ok(ApiResponse.success(dto)))
            .orElse(new ResponseEntity<>(ApiResponse.error(HttpStatus.NOT_FOUND.value(), "ProductSetting not found with barcode: " + barcode), HttpStatus.NOT_FOUND));
    }

    @Operation(summary = "更新商品設定", description = "更新指定ID的商品設定。商品條碼對於未刪除的設定必須是唯一的。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "商品設定成功更新",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "無效的輸入資料 (例如：條碼重複)",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "找不到指定ID的商品設定",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class)))
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00070_UPDATE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.UPDATE, dataType = AuditDataTypeEnum.PRODUCT_SETTING)
    public ResponseEntity<ApiResponse<ProductSettingDto>> updateProductSetting(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @PathVariable UUID id, 
            @Valid @RequestBody ProductSettingDto productSettingDto) {
        ProductSettingDto updatedSetting = productSettingService.updateProductSetting(id, productSettingDto, companyContext);
        return ResponseEntity.ok(ApiResponse.success(updatedSetting));
    }

    @Operation(summary = "刪除商品設定", description = "邏輯刪除指定ID的商品設定及其相關職權折扣。")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "商品設定成功刪除", 
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "找不到指定ID的商品設定",
                     content = @Content(mediaType = "application/json", schema = @Schema(implementation = com.eastking.model.vo.ApiResponse.class)))
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('F00070_DELETE', 'SYS_ADMIN')")
    @AuditAction(action = AuditActionTypeEnum.DELETE, dataType = AuditDataTypeEnum.PRODUCT_SETTING)
    public ResponseEntity<ApiResponse<Void>> deleteProductSetting(@PathVariable UUID id) {
        productSettingService.deleteProductSetting(id);
        return ResponseEntity.ok(ApiResponse.success(null, "ProductSetting deleted successfully."));
    }

    @Operation(summary = "搜尋商品供訂單選擇", description = "依關鍵字搜尋商品，可選擇性提供門市ID以查詢門市庫存。")
    @GetMapping("/search")
    @PreAuthorize("isAuthenticated()") // Allow any authenticated user to search for products
    public ResponseEntity<ApiResponse<List<com.eastking.model.dto.ProductSearchResultDto>>> searchProductsForMenu(
            @RequestParam String keyword,
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @RequestParam(required = false) UUID storeId) {
        
        List<com.eastking.model.dto.ProductSearchResultDto> results = productSettingService.searchProductsForMenu(keyword, companyContext, storeId);
        return ResponseEntity.ok(ApiResponse.success(results));
    }

    @GetMapping("/dispatch-only")
    @Operation(summary = "查詢僅派工的商品設定列表")
    public ResponseEntity<ApiResponse<ApiResponse.PageData<ProductSettingDto>>> searchDispatchProducts(
            @Parameter(description = "商品國際條碼 (模糊查詢)", in = ParameterIn.QUERY) @RequestParam(required = false) String productBarcode,
            @Parameter(description = "商品名稱 (模糊查詢)", in = ParameterIn.QUERY) @RequestParam(required = false) String productName,
            @Parameter(description = "是否啟用") @RequestParam(required = false) Boolean isActive,
            @Parameter(description = "最後編輯時間(起)", in = ParameterIn.QUERY) @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
            @Parameter(description = "最後編輯時間(迄)", in = ParameterIn.QUERY) @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
            @PageableDefault(size = 10, sort = "updateTime,desc") Pageable pageable) {
        
        Page<ProductSettingDto> page = productSettingService.searchDispatchProducts(productBarcode, productName, isActive, startDate, endDate, pageable);
        return ResponseEntity.ok(ApiResponse.success(page));
    }

    @GetMapping("/search-store-products")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "搜尋特定門市的非派工商品", description = "依關鍵字搜尋特定門市的非派工商品，並回傳庫存。")
    public ResponseEntity<ApiResponse<List<com.eastking.model.dto.ProductSearchResultDto>>> searchStoreProducts(
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @RequestParam String keyword,
            @RequestParam UUID storeId) {
        
        List<com.eastking.model.dto.ProductSearchResultDto> results = productSettingService.searchStoreProducts(keyword, storeId, companyContext);
        return ResponseEntity.ok(ApiResponse.success(results));
    }

    @GetMapping("/search-dispatch-products")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "搜尋可派工的商品", description = "依關鍵字搜尋可派工商品。可選擇性提供門市或經銷商ID來查詢特定地點的庫存。")
    public ResponseEntity<ApiResponse<List<ProductSearchResultWithGiftDto>>> searchDispatchableProducts(
            @RequestParam String keyword,
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @RequestParam(required = false) UUID storeId,
            @RequestParam(required = false) UUID distributorId) {
        
        List<ProductSearchResultWithGiftDto> results = productSettingService.searchDispatchableProducts(keyword, companyContext, storeId, distributorId);
        return ResponseEntity.ok(ApiResponse.success(results));
    }

    @GetMapping("/search-accessories")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "搜尋可作為贈品或加購品的配件", description = "依關鍵字搜尋非主商品的品項，並檢查庫存。")
    public ResponseEntity<ApiResponse<List<com.eastking.model.dto.ProductSearchResultDto>>> searchAccessories(
            @RequestParam String keyword,
            @RequestHeader(name = "X-Company-Context", required = false) String companyContext,
            @RequestParam(required = false) UUID storeId) {
        
        List<com.eastking.model.dto.ProductSearchResultDto> results = productSettingService.searchAccessories(keyword, companyContext, storeId);
        return ResponseEntity.ok(ApiResponse.success(results));
    }

    @GetMapping("/search-main-products")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "搜尋主商品 (is_main = 1)")
    public ResponseEntity<List<ProductSearchResultDto>> searchMainProducts(@RequestParam String keyword) {
        return ResponseEntity.ok(productSettingService.searchProductsByMainFlag(keyword, true));
    }
    
    @GetMapping("/search-non-main-products")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "搜尋非主商品 (is_main = 0)")
    public ResponseEntity<List<ProductSearchResultDto>> searchNonMainProducts(@RequestParam String keyword) {
        return ResponseEntity.ok(productSettingService.searchProductsByMainFlag(keyword, false));
    }
} 