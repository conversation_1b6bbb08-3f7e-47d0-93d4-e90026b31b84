package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum MaterialOrderStatusEnum implements BaseEnum {
    PENDING_PICKING(10, "待揀料"),
    PICKING_COMPLETED(20, "已揀料"),
    COLLECTED(30, "領料完成"),
    CANCELLED(40, "取消領料"),
    REFUND_PENDING_PICKING(-10, "待退料"),
    REFUND_PICKING_COMPLETED(-20, "已驗料"),
    REFUND_COLLECTED(-30, "退料完成"),
    REFUND_CANCELLED(-40, "取消退料"),;

    @JsonValue
    private final Short code;
    private final String description;

    MaterialOrderStatusEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }

    private static final Map<Short, MaterialOrderStatusEnum> map =
            Arrays.stream(values()).collect(Collectors.toMap(MaterialOrderStatusEnum::getCode, e -> e));

    public static MaterialOrderStatusEnum fromCode(Short code) {
        return map.get(code);
    }
} 