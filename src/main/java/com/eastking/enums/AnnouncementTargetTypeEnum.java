package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 公告對象類型枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Getter
@RequiredArgsConstructor
public enum AnnouncementTargetTypeEnum {
    DEPARTMENT((short)1, "部門"),
    EMPLOYEE((short)2, "員工");

    private final Short code;
    private final String description;

    public static AnnouncementTargetTypeEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        for (AnnouncementTargetTypeEnum type : AnnouncementTargetTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 