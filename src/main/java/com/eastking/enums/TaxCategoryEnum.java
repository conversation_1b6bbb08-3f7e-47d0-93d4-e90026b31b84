package com.eastking.enums;

import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 稅務類別枚舉
 */
@Getter
public enum TaxCategoryEnum implements BaseEnum<Short> {
    TAXABLE((short) 0, "應稅"),
    TAX_EXEMPT((short) 1, "免稅"),
    ZERO_RATED((short) 2, "零稅率");

    private final Short code;
    private final String description;

    TaxCategoryEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, TaxCategoryEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(TaxCategoryEnum::getCode, Function.identity()));

    public static TaxCategoryEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        TaxCategoryEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 