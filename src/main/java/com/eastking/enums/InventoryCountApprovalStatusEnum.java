package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

@Getter
@RequiredArgsConstructor
public enum InventoryCountApprovalStatusEnum {
    PENDING_SUBMISSION((short)10, "未送審"),
    PENDING_APPROVAL((short)20, "已送審"),
    APPROVED((short)30, "審核通過"),
    REJECTED((short)40, "審核未通過"),
    RECOUNT_APPROVED((short)50, "複盤通過");

    private final Short code;
    private final String description;

    public static InventoryCountApprovalStatusEnum fromCode(Short code) {
        return Arrays.stream(values())
                .filter(e -> e.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getDescriptionByCode(Short code) {
        InventoryCountApprovalStatusEnum status = fromCode(code);
        return status != null ? status.getDescription() : "未知狀態";
    }
} 