package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 欄位權限等級枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Getter
@RequiredArgsConstructor
public enum FieldPermissionLevelEnum {
    HIDDEN((short)0, "不可見"),
    READ_ONLY((short)1, "可見唯讀"),
    EDITABLE((short)2, "可編輯");

    private final Short code;
    private final String description;

    public static FieldPermissionLevelEnum fromCode(Short code) {
        if (code == null) return null;
        for (FieldPermissionLevelEnum level : FieldPermissionLevelEnum.values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return null;
    }
} 