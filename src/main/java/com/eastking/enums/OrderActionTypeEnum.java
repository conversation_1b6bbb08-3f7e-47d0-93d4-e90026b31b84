package com.eastking.enums;

import lombok.Getter;
import java.util.Arrays;

@Getter
public enum OrderActionTypeEnum implements BaseEnum {
    CANCEL((short) 1, "訂單取消"),
    RETURN((short) 2, "訂單退貨"),
    CHANGE((short) 3, "訂單換貨");

    private final Short code;
    private final String description;

    OrderActionTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    public static OrderActionTypeEnum fromCode(Short code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
} 