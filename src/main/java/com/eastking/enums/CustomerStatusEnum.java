package com.eastking.enums;

import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客戶狀態枚舉
 */
@Getter
public enum CustomerStatusEnum implements BaseEnum<Short> {
    INACTIVE((short) 0, "停用"),
    ACTIVE((short) 1, "啟用");

    private final Short code;
    private final String description;

    CustomerStatusEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, CustomerStatusEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(CustomerStatusEnum::getCode, Function.identity()));

    public static CustomerStatusEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        CustomerStatusEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 