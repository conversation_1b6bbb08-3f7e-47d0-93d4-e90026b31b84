package com.eastking.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RequireDispatchEnum implements BaseEnum {
    STORECATCH((short) 0, "店取"),
    DISPATCH((short) 1, "派工"),
    CARRYSELF((short) 2, "自載展示機");

    private final Short code;
    private final String description;

    RequireDispatchEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, RequireDispatchEnum> CODE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(RequireDispatchEnum::getCode, e -> e));

    public static RequireDispatchEnum fromCode(Short code) {
        return CODE_MAP.get(code);
    }
} 