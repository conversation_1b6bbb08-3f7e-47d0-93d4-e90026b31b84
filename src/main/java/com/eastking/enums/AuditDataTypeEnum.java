package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 操作歷程的資料類型枚舉
 */
@Getter
@RequiredArgsConstructor
public enum AuditDataTypeEnum {
    USER_ACCOUNT("UserAccount", "使用者帳號"),
    ROLE("Role", "角色權限"),
    SYSTEM_FUNCTION("SystemFunction", "系統功能"),
    PRODUCT_SETTING("ProductSetting", "商品設定"),
    CUSTOMER("Customer", "客戶會員"),
    PRODUCT_MENU("ProductMenu", "商品選單"),
    PROMOTION("Promotion", "促銷活動"),
    CUSTOMER_SEGMENT("CustomerSegment", "客群管道"),
    GIFT_BUNDLE("GiftBundle", "贈品套裝"),
    WAREHOUSE("Warehouse", "倉庫分區"),
    TECHNICIAN_BONUS("TechnicianBonus", "技師獎金"),
    BONUS_ITEM("BonusItem", "獎金項目"),
    STORE_SETTING("StoreSetting", "門市設定"),
    MEMBER_BENEFIT("MemberBenefit", "會員福利"),
    SMS_TEMPLATE("SmsTemplate", "簡訊模板"),
    ANNOUNCEMENT("Announcement", "系統公告"),
    ORDER("Order", "訂單"), // General order type
    ORDER_REFUND("OrderRefund", "訂單退貨"),
    DISPATCH_ORDER("DispatchOrder", "派工商品訂單"),
    STORE_ORDER("StoreOrder", "門市商品訂單"),
    AUTHENTICATION("Authentication", "認證作業"),
    SYSTEM_CONFIG("SystemConfig", "系統配置"),
    AUDIT_LOG("AuditLog", "操作歷程"), // Meta-entry for viewing audit logs itself
    STORE_TRANSFER_ORDER("StoreTransferOrder", "門市調撥單"),
    DISPATCH_REPAIR("DispatchRepair", "派工維修單"),
    DOCUMENT_CLAUSE("DocumentClause", "表尾條文"),
    MEMBER_LEVEL("MemberLevel", "會員等級"),
    TECHNICIAN_AREA_CONFIG("TechnicianAreaConfig", "技師區域配置"),
    WHOLESALE_ORDER("WholesaleOrder", "批發訂單"),
    INVENTORY_COUNT_SHEET("InventoryCountSheet", "盤點單"),
    CUSTOMER_DEVICE("CustomerDevice", "客戶設備"),
    MATERIAL_ORDER("MaterialOrder", "領料單"),
    PICKING_LIST("PickingList", "揀料單"),
    UNKNOWN("Unknown", "未知");

    @JsonValue
    private final String code;
    private final String description;

    @JsonCreator
    public static AuditDataTypeEnum fromCode(String code) {
        for (AuditDataTypeEnum type : AuditDataTypeEnum.values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return UNKNOWN;
    }
} 