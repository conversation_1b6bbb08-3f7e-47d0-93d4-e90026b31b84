package com.eastking.enums;

import lombok.Getter;
// import lombok.RequiredArgsConstructor; // Not strictly needed with explicit constructor

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
// @RequiredArgsConstructor // Lombok's @RequiredArgsConstructor is fine with final fields, or use explicit below
public enum OrderTypeEnum implements BaseEnum<Short> { // Implement BaseEnum<Short>
    STORE_PRODUCT_ORDER((short) 1, "門市商品訂單"),
    DISPATCH_PRODUCT_ORDER((short) 2, "派工商品訂單"),
    WHOLESALE_ORDER((short) 3, "批發商品訂單");

    private final Short code;
    private final String description;

    // Explicit constructor is also fine, or @RequiredArgsConstructor can generate this
    OrderTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, OrderTypeEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(OrderTypeEnum::getCode, Function.identity()));

    public static OrderTypeEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        OrderTypeEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 