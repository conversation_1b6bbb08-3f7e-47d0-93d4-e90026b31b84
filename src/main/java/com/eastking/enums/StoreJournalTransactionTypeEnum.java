package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum StoreJournalTransactionTypeEnum implements BaseEnum {
    SALE_RECEIPT((short) 10, "銷售"),
    REFUND_PAYOUT((short) 20, "銷退"),
    OTHER_INCOME((short) 80, "雜項收入"),
    OTHER_EXPENSE((short) 90, "雜項支出"),
    DAILY_CLOSING_CASH_DEPOSIT((short) 99, "現金繳庫"),
    CASH_SHORTAGE((short) -10, "現金短少"),
    CASH_OVERAGE((short) -20, "現金溢余");

    private final Short code;
    private final String description;

    public static StoreJournalTransactionTypeEnum fromCode(Short code) {
        if (code == null) return null;
        for (StoreJournalTransactionTypeEnum type : StoreJournalTransactionTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 