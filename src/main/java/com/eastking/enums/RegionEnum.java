package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 地區枚舉 (用於區域限制)
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Getter
@RequiredArgsConstructor
public enum RegionEnum {
    NORTH("N", "北區"),
    CENTRAL("C", "中區"),
    SOUTH("S", "南區"),
    ALL("A", "全區"); // Represents no specific regional restriction / access to all

    private final String code; // Using String for code as it's N, C, S
    private final String description;

    public static RegionEnum fromCode(String code) {
        if (code == null) return null;
        for (RegionEnum region : RegionEnum.values()) {
            if (region.getCode().equalsIgnoreCase(code)) {
                return region;
            }
        }
        return null;
    }
} 