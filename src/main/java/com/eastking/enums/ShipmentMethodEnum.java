package com.eastking.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 配送方式枚舉
 *
 * <AUTHOR> INC.
 * @date 2024-07-29
 */
@Getter
public enum ShipmentMethodEnum {

    TECHNICIAN_DELIVERY((short) 0, "技師安裝"),
    STORE_PICKUP((short) 1, "到店自取"),
    LOGISTICS((short) 2, "物流");

    private final Short code;
    private final String description;

    ShipmentMethodEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ShipmentMethodEnum fromCode(Short code) {
        return Arrays.stream(ShipmentMethodEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getDescriptionByCode(Short code) {
        ShipmentMethodEnum shipmentMethodEnum = fromCode(code);
        return shipmentMethodEnum != null ? shipmentMethodEnum.getDescription() : "";
    }
} 