package com.eastking.enums;

import lombok.Getter;
// import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
// @RequiredArgsConstructor
public enum OrderStatusEnum implements BaseEnum<Short> { // Implement BaseEnum<Short>
    DRAFT((short) 10, "草稿"),
    PENDING_STORE_APPROVAL((short) 20, "訂單審核中 (店經理)"), // 門市改價超權限等情況
    AWAITING_CORRECTION_STORE((short) 23, "訂單待補正 (店經理退回)"),
    //STORE_APPROVED((short) 26, "店經理審核通過"),

    STORE_CHECKOUT_COMPLETE((short) 30, "門市結帳"),
    //STORE_STOCK_DEDUCTED((short) 33, "門市扣庫存"),
    HQ_STOCK_INSUFFICIENT((short) 36, "庫存不足 (總倉)"),
    HQ_STOCK_FULL((short) 37, "已有庫存 (總倉)"),

    PENDING_HQ_APPROVAL((short) 40, "訂單審核中 (總公司)"),
    AWAITING_CORRECTION_HQ((short) 43, "訂單待補正 (總公司退回)"),
    HQ_REJECTED((short) 46, "訂單駁回 (總公司)"),
    HQ_APPROVED((short) 49, "總公司審核通過"),

    CANCEL_PENDING_HQ_APPROVAL((short) -40, "訂單取消審核中"),
    CANCEL_REJECTED_BY_HQ((short) -46, "訂單取消駁回"),
    //CANCEL_PART_APPROVED_BY_HQ((short) -48, "部分訂單取消成立"),
    CANCEL_APPROVED_BY_HQ((short) -49, "訂單取消成立"),
    CANCEL_STORE_REFUND((short) -50, "門市已退款"),

    DISPATCH_PENDING_PICKUP((short) 60, "派工商品派工中"),
    DISPATCH_TECHNICIAN_LOADED((short) 66, "技師裝貨完成"),

    SHIPPED_CLOSED((short) 70, "已出貨結案"),

    RETURN_PENDING_HQ_APPROVAL((short) -70, "退貨審核中"),
    RETURN_AWAITING_CORRECTION_HQ((short) -73, "退貨待補正 (總公司退回)"),
    RETURN_REJECTED_BY_HQ((short) -77, "退貨駁回 (總公司駁回)"),
    //RETURN_SUBMITTED_HQ_APPROVED((short) -80, "提交退貨 (總公司核准)"), // 總公司審核退貨通過
    RETURN_PART_APPROVED_PENDING_PICKUP((short) -82, "部分退貨成立待取貨"),
    RETURN_APPROVED_PENDING_PICKUP((short) -83, "退貨成立待取貨"),
    RETURN_PART_APPROVED_ITEM_RECEIVED((short) -85, "部分退貨成立已取貨"),
    RETURN_APPROVED_ITEM_RECEIVED((short) -86, "退貨成立已取貨/收貨"),
    STORE_REFUND_CHECKOUT_COMPLETE((short) -88, "門市已退款結帳"),
    STORE_REFUND_CHANGE((short) -89, "門市已轉換貨"),

    POSTED_TO_ERP_SALES((short) 95, "已入正航銷帳"),
    ERP_SALES_FAILED((short) 99, "入正航銷賬失敗"),
    POSTED_TO_ERP_SALES_RETURN((short) -95, "已入正航銷退"),
    ERP_SALES_RETURN_FAILED((short) -99, "入正航銷退失敗");

    private final Short code;
    private final String description;

    OrderStatusEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, OrderStatusEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(OrderStatusEnum::getCode, Function.identity()));

    public static OrderStatusEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        OrderStatusEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 