package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

@Getter
@RequiredArgsConstructor
public enum InventoryCountSheetStatusEnum {
    DRAFT_INITIAL((short)10, "初盤暫存"),
    SUBMITTED_INITIAL((short)20, "初盤送出"),
    DRAFT_RECOUNT((short)30, "複盤暫存"),
    SUBMITTED_RECOUNT((short)40, "複盤送出");

    private final Short code;
    private final String description;

    public static InventoryCountSheetStatusEnum fromCode(Short code) {
        return Arrays.stream(values())
                .filter(e -> e.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getDescriptionByCode(Short code) {
        InventoryCountSheetStatusEnum status = fromCode(code);
        return status != null ? status.getDescription() : "未知狀態";
    }
} 