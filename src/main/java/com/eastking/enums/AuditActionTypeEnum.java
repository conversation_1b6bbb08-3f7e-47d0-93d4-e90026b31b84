package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 操作歷程的行為類型枚舉
 */
@Getter
@RequiredArgsConstructor
public enum AuditActionTypeEnum {
    CREATE("CREATE", "新增"),
    UPDATE("UPDATE", "修改"),
    DELETE("DELETE", "刪除"),
    VIEW("VIEW", "查看"),
    LOGIN_SUCCESS("LOGIN_SUCCESS", "登入成功"),
    LOGIN_FAILURE("LOGIN_FAILURE", "登入失敗"),
    LOGOUT("LOGOUT", "登出"),
    SEARCH("SEARCH", "查詢"),
    DOWNLOAD("DOWNLOAD", "下載"),
    UPLOAD("UPLOAD", "上傳"),
    PERMISSION_CHANGE("PERMISSION_CHANGE", "權限變更"),
    CONFIG_CHANGE("CONFIG_CHANGE", "設定變更"),
    APPROVE("APPROVE", "核准"),
    REJECT("REJECT", "駁回"),
    OTHER("OTHER", "其他");

    private final String code;
    private final String description;

    public static AuditActionTypeEnum fromCode(String code) {
        if (code == null) return null;
        for (AuditActionTypeEnum status : AuditActionTypeEnum.values()) {
            if (status.getCode().equalsIgnoreCase(code)) {
                return status;
            }
        }
        return OTHER; // Default to OTHER if not found
    }
} 