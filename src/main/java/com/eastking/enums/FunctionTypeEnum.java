package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 功能類型枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Getter
@RequiredArgsConstructor
public enum FunctionTypeEnum {
    GROUP((short)0, "選單群組"),
    OPERATION((short)1, "操作功能");

    private final Short code;
    private final String description;

    public static FunctionTypeEnum fromCode(Short code) {
        for (FunctionTypeEnum type : FunctionTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 