package com.eastking.enums;

import lombok.Getter;
// import lombok.RequiredArgsConstructor; // Not needed if constructor is explicit

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ERP公司別 枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/20
 */
@Getter
// @RequiredArgsConstructor // Replaced by explicit constructor
public enum ErpCompanyDivisionEnum implements BaseEnum<Short> { // Changed to BaseEnum<Short>
    EASTKING((short) 0, "東方不敗"),
    QUEYOU((short) 1, "雀友"),
    ALL((short) 9, "全部公司適用");

    private final Short code;
    private final String description;

    ErpCompanyDivisionEnum(Short code, String description) { // Explicit constructor
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, ErpCompanyDivisionEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(ErpCompanyDivisionEnum::getCode, Function.identity()));

    public static ErpCompanyDivisionEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        ErpCompanyDivisionEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }

    public static ErpCompanyDivisionEnum fromNameIgnoreCase(String name) {
        if (name == null) return null;
        for (ErpCompanyDivisionEnum type : ErpCompanyDivisionEnum.values()) {
            if (type.name().equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }

    // fromDescription might still be useful internally if needed.
    public static ErpCompanyDivisionEnum fromDescription(String description) {
        if (description == null) return null;
        for (ErpCompanyDivisionEnum type : ErpCompanyDivisionEnum.values()) {
            if (type.getDescription().equalsIgnoreCase(description)) {
                return type;
            }
        }
        return null;
    }
} 