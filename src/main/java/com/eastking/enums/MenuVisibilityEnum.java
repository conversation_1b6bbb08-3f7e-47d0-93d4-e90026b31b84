package com.eastking.enums;

import lombok.Getter;
import java.util.Arrays;

/**
 * 選單可見性枚舉
 *
 * <AUTHOR> INC.
 * @date 2024-07-30
 */
@Getter
public enum MenuVisibilityEnum {

    HIDE((short) 0, "隱藏"),
    SHOW((short) 1, "顯示");

    private final Short code;
    private final String description;

    MenuVisibilityEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    public static MenuVisibilityEnum fromCode(Short code) {
        return Arrays.stream(MenuVisibilityEnum.values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
} 