package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 門市進貨單狀態枚舉
 */
@Getter
@RequiredArgsConstructor
public enum StorePurchaseOrderStatusEnum {
    SHIPPING((short)0, "配送中"),
    PARTIALLY_RECEIVED((short)20, "部分到貨"), // If a partial receipt is possible before full confirmation
    PENDING_CONFIRMATION((short)40, "已到貨(待確認)"),
    COMPLETED((short)60, "已完成"),

    DISCREPANCY((short)-40, "數量異常");

    private final Short code;
    private final String description;

    public static StorePurchaseOrderStatusEnum fromCode(Short code) {
        if (code == null) return null;
        for (StorePurchaseOrderStatusEnum status : StorePurchaseOrderStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 