package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ApprovalStatusEnum implements BaseEnum {
    PENDING_APPROVAL((short) 0, "待審核"),
    APPROVED((short) 1, "已核准"),
    REJECTED((short) 2, "已駁回"),
    NOT_REQUIRED((short) 3, "無需審核");

    private final Short code;
    private final String description;

    public static ApprovalStatusEnum fromCode(Short code) {
        if (code == null) return null;
        for (ApprovalStatusEnum status : ApprovalStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 