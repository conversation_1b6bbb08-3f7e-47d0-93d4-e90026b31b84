package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 刪除狀態枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Getter
@RequiredArgsConstructor
public enum DeleteStatusEnum {
    NOT_DELETED((short)0, "未刪除"),
    DELETED((short)1, "已刪除");

    private final Short code;
    private final String description;

    public static DeleteStatusEnum fromCode(Short code) {
        if (code == null) return null;
        for (DeleteStatusEnum status : DeleteStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 