package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 公告分類枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/16
 */
@Getter
@RequiredArgsConstructor
public enum AnnouncementCategoryEnum {
    PERSONNEL((short)1, "人事公告"),
    CURRENT_EVENTS((short)2, "時事公告"),
    COMPANY_POLICY((short)3, "公司規範"),
    COMPANY_BENEFITS((short)4, "公司福利"),
    NEW_PRODUCT((short)5, "新品上市"),
    OUT_OF_STOCK((short)6, "缺貨通知"),
    PROMOTION((short)7, "促銷通知"),
    INTERNAL((short)8, "內部公告");

    private final Short code;
    private final String description;

    public static AnnouncementCategoryEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        for (AnnouncementCategoryEnum category : AnnouncementCategoryEnum.values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }

    public static AnnouncementCategoryEnum fromDescription(String description) {
        if (description == null) {
            return null;
        }
        for (AnnouncementCategoryEnum category : AnnouncementCategoryEnum.values()) {
            if (category.getDescription().equals(description)) {
                return category;
            }
        }
        return null;
    }
} 