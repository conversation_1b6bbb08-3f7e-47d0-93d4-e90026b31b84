package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 使用者帳號狀態枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Getter
@RequiredArgsConstructor
public enum UserAccountStatusEnum {
    INACTIVE((short)0, "停用"),
    ACTIVE((short)1, "啟用");

    private final Short code;
    private final String description;

    public static UserAccountStatusEnum fromCode(Short code) {
        if (code == null) return null;
        for (UserAccountStatusEnum status : UserAccountStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null; // Or throw IllegalArgumentException
    }
} 