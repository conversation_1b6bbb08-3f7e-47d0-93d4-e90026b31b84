package com.eastking.enums;

import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum RecordTypeEnum implements BaseEnum<Short> {
    REMARKS(10, "備註"),
    DEVICE_ID(20, "機身號碼"),
    DISPATCH_REMARKS(30, "裝修訊息"),
    REPAIR_PRODUCT(40, "裝修商品"),
    IMAGE_DATA(50, "照片資訊"),
    PAYMENT(60, "付款資訊"),
    SIGNED_INFO(70, "簽收資訊"),
    PROCESS_INFO(80, "處理方式");

    private final Short code;
    private final String description;

    RecordTypeEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }

    private static final Map<Short, RecordTypeEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(RecordTypeEnum::getCode, Function.identity()));

    public static RecordTypeEnum fromCode(Short code) {
        if (code == null) return null;
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        RecordTypeEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 