package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 門市啟用狀態枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Getter
@RequiredArgsConstructor
public enum StoreStatusEnum {

    DISABLED((short)0, "停用"),
    ENABLED((short)1, "啟用");

    private final Short code;
    private final String description;

    public static StoreStatusEnum fromCode(Short code) {
        for (StoreStatusEnum status : StoreStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的門市狀態碼: " + code);
    }
} 