package com.eastking.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum InvoiceTypeEnum implements BaseEnum<Short> {
    NONE((short) 0, "不開立"),
    TWO_PART((short) 2, "二聯式發票"),
    THREE_PART((short) 3, "三聯式發票");

    private final Short code;
    private final String description;

    InvoiceTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, InvoiceTypeEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(InvoiceTypeEnum::getCode, Function.identity()));

    public static InvoiceTypeEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        InvoiceTypeEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 