package com.eastking.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum PaymentStatusEnum implements BaseEnum<Short> {
    UNPAID((short) 0, "未付款"),
    PARTIALLY_PAID((short) 1, "部分付款"),
    FULLY_PAID((short) 2, "已付清"),
    REFUND_PENDING((short) 3, "待退款"),
    REFUNDED((short) 4, "已退款");

    private final Short code;
    private final String description;

    PaymentStatusEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, PaymentStatusEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(PaymentStatusEnum::getCode, Function.identity()));

    public static PaymentStatusEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        PaymentStatusEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 