package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 門市調撥單狀態枚舉
 */
@Getter
@RequiredArgsConstructor
public enum StoreTransferOrderStatusEnum {
    PENDING_APPROVAL((short)0, "待審核"), // Or "申請中"
    APPROVED((short)20, "待出庫"), // Or "已核可"
    DISPATCHED((short)40, "已出庫"), // Or "配送中"
    RECEIVED_COMPLETE((short)60, "已入庫(完成)"),

    RECEIVED_DISCREPANCY((short)-40, "數量異常"),
    CANCELLED((short)-45, "已取消"),
    REJECTED((short)-50, "已駁回");

    private final Short code;
    private final String description;

    public static StoreTransferOrderStatusEnum fromCode(Short code) {
        if (code == null) return null;
        for (StoreTransferOrderStatusEnum status : StoreTransferOrderStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 