package com.eastking.enums;

import lombok.Getter;

/**
 * 銷售單位枚舉
 */
@Getter
public enum SaleUnitEnum {
    PIECE1("PIECE1", "個"),
    PIECE2("PIECE2", "張"),
    PIECE3("PIECE3", "片"),
    PIECE4("PIECE4", "件"),
    SET1("SET1", "組"),
    SET2("SET2", "套"),
    SET3("SET3", "副"),
    UNIT1("UNIT1", "台"),
    UNIT2("UNIT2", "臺"),
    BOX("BOX", "盒"),
    PAIR("PAIR", "雙"),
    DOZEN("DOZEN", "打");
    // Add other units as needed

    private final String code;       // For storing in DB (and API if preferred)
    private final String description; // For display in UI

    SaleUnitEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static SaleUnitEnum fromCode(String code) {
        if (code == null) return null;
        for (SaleUnitEnum unit : SaleUnitEnum.values()) {
            if (unit.getCode().equalsIgnoreCase(code)) {
                return unit;
            }
        }
        throw new IllegalArgumentException("Unknown sale unit code: " + code);
    }

    public static SaleUnitEnum fromDescription(String description) {
        if (description == null) return null;
        for (SaleUnitEnum unit : SaleUnitEnum.values()) {
            if (unit.getDescription().equals(description)) {
                return unit;
            }
        }
        throw new IllegalArgumentException("Unknown sale unit description: " + description);
    }
} 