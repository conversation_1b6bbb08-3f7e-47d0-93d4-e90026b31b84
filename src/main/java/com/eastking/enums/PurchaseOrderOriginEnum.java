package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PurchaseOrderOriginEnum implements BaseEnum {
    EXTERNAL_SUPPLIER((short) 0, "外部供應商"),
    HQ_SHIPMENT_FOR_TRANSFER((short) 1, "總倉調撥出貨");

    private final Short code;
    private final String description;

    public static PurchaseOrderOriginEnum fromCode(Short code) {
        for (PurchaseOrderOriginEnum origin : PurchaseOrderOriginEnum.values()) {
            if (origin.getCode().equals(code)) {
                return origin;
            }
        }
        return EXTERNAL_SUPPLIER; // Default or throw exception
    }
} 