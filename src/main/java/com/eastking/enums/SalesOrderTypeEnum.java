package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum SalesOrderTypeEnum implements BaseEnum {
    REGULAR_SALE((short) 0, "一般銷貨"),
    STORE_RETURN_TO_HQ((short) 1, "門市退貨回總倉");

    private final Short code;
    private final String description;

    public static SalesOrderTypeEnum fromCode(Short code) {
        for (SalesOrderTypeEnum type : SalesOrderTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return REGULAR_SALE; // Default or throw exception
    }
} 