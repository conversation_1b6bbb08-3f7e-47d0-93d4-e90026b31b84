package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum UserAccountTypeEnum {
    GENERAL_STAFF((short)0, "一般員工"),
    TECHNICIAN((short)1, "技師"),
    MANAGER((short)2, "主管"),
    SYSTEM_ADMIN((short)9, "系統管理員"); // Example, can adjust codes

    private final Short code;
    private final String description;

    public static UserAccountTypeEnum fromCode(Short code) {
        if (code == null) return null;
        for (UserAccountTypeEnum type : UserAccountTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return GENERAL_STAFF; // Default or throw exception
    }
} 