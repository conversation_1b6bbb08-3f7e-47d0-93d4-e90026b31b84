package com.eastking.enums;

import lombok.Getter;

@Getter
public enum StoreJournalTypeEnum implements BaseEnum {
    MISC_STORE_SUPPLIES(10, "雜費-門市用品"),
    MISC_INCENSE_MONEY(12, "雜費-香油錢"),
    MISC_OTHER(14, "雜費-其他"),
    WATER_BILL(20, "水費"),
    ELECTRICITY_BILL(22, "電費"),
    POSTAGE(30, "郵資"),
    PHONE_BILL(32, "電話費"),
    SALARY_EXPENSE(50, "薪資支出"),
    RENT_EXPENSE(60, "租金支出"),
    BUSINESS_TAX(70, "稅捐-營業稅"),
    EMPLOYEE_WELFARE(80, "職工福利"),
    HQ_WAREHOUSE_TRANSFER(90, "總倉往來");

    private final Short code;
    private final String description;

    StoreJournalTypeEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }

    public static StoreJournalTypeEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        for (StoreJournalTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
} 