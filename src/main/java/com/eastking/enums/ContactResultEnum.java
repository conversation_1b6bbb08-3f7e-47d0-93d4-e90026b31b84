package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum ContactResultEnum implements BaseEnum {
    SUCCESSFUL(1, "成功聯繫"),
    NO_ANSWER(2, "無人接聽"),
    BUSY(3, "忙線"),
    OTHER(4, "其他");

    @JsonValue
    private final Short code;
    private final String description;

    ContactResultEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }
    
    private static final Map<Short, ContactResultEnum> map =
            Arrays.stream(values()).collect(Collectors.toMap(ContactResultEnum::getCode, e -> e));

    public static ContactResultEnum fromCode(Short code) {
        return map.get(code);
    }
} 