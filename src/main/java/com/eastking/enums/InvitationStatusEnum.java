package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum InvitationStatusEnum implements BaseEnum {
    PENDING(10, "已邀請"),
    ACCEPTED(20, "已接受"),
    REJECTED(30, "已拒絕");

    @JsonValue
    private final Short code;
    private final String description;

    InvitationStatusEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }

    private static final Map<Short, InvitationStatusEnum> map =
            Arrays.stream(values()).collect(Collectors.toMap(InvitationStatusEnum::getCode, e -> e));

    public static InvitationStatusEnum fromCode(Short code) {
        return map.get(code);
    }
} 