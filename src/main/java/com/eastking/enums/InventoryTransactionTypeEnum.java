package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum InventoryTransactionTypeEnum {
    REGULAR_PURCHASE(1, "一般進貨"), // Store Purchase from external
    REGULAR_SALE(2, "一般銷貨"),
    RETURN_RESTOCK(3, "客戶退貨入庫"),
    STORE_RETURN_TO_HQ(4, "門市退貨回總倉 (調撥轉出)"),
    HQ_RECEIVE_FROM_STORE(5, "總倉收到門市退貨 (調撥轉出接收)"),
    HQ_SHIP_TO_STORE(6, "總倉出貨至門市 (調撥轉入)"),
    STORE_RECEIVE_FROM_HQ(7, "門市收到總倉出貨 (調撥轉入接收)"),
    ADJUSTMENT_ADD(8, "庫存調整 (增加)"),
    ADJUSTMENT_SUB(9, "庫存調整 (減少)"),
    DISPATCH_USE(10, "派工使用"),
    SALE(11, "門市銷售"),
    TRANSFER_IN(12, "調撥入庫"),
    TRANSFER_OUT(13, "調撥出庫"),
    INITIAL_STOCK(14, "期初庫存"),
    PURCHASE(15, "採購進貨");

    private final Short code;
    private final String description;

    InventoryTransactionTypeEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }
} 