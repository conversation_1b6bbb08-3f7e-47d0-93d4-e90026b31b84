package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum DispatchStatusEnum implements BaseEnum<Short> {
    DRAFT((short) 10, "草稿"),

    //裝機流程
    PENDING_ASSIGNMENT(20, "待接單"),
    CONTACTED(26, "待聯繫"),
    SCHEDULED(29, "待排單"),
    MATERIALS_COLLECTED(35, "待領料"),
    DEPARTED(40, "在途中"),
    WORKING(45, "裝修中"),
    PAYMENT_COLLECTED(55, "收款中"),
    SIGNED(60, "簽確中"),
    PROCESS_INFO(80, "處理方式"),
    ACCOUNT_CONFIRM(90, "會計確認中"),
    TECH_CONFIRM(93, "技術確認中"),
    INFO_CONFIRM(96, "資料確認中"),
    COMPLETED(99, "結單"), //這時派工商品訂單的派工單資料要寫回訂單

    //退機流程
    REFUND_PENDING_ASSIGNMENT((short) -20, "退機接單"),
    REFUND_CONTACTED(-26, "退機待聯繫"),
    REFUND_SCHEDULED(-29, "退機待排單"),
    REFUND_DEPARTED(-40, "退機在途中"),
    REFUND_PICKUP(-45, "退機收貨中"),
    REFUND_PAYMENT(-55, "退款中"),
    REFUND_SIGNED(-60, "退機簽確中"),
    REFUND_PROCESS_INFO(-80, "退機處理方式"),
    REFUND_MATERIALS_BACK(-85, "待退料"),
    REFUND_ACCOUNT_CONFIRM(-90, "退機會計確認中"),
    REFUND_TECH_CONFIRM(-93, "退機技術確認中"),
    REFUND_INFO_CONFIRM(-96, "退機資料確認中"),
    CANCEL_COMPLETED(-99, "退機結單");

    //PS1. 技師派工單可看到的流程：26~99, -26~-99
    //PS2. 派工流程可以因為轉單而回朔，派工單紀錄新的資料即可，所有變化要有記錄
    //取消流程未定
    //TRANSFERRED(120, "已轉單"); //轉單邏輯另解(分未領料、已領料，已領料要看直接轉還是先回倉庫)

    @JsonValue
    private final Short code;
    private final String description;

    DispatchStatusEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }

    private static final Map<Short, DispatchStatusEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(DispatchStatusEnum::getCode, Function.identity()));

    public static DispatchStatusEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        DispatchStatusEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 