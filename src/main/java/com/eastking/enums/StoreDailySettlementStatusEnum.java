package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum StoreDailySettlementStatusEnum implements BaseEnum {
    SETTLED((short)10, "已結算");

    private final Short code;
    private final String description;

    public static StoreDailySettlementStatusEnum fromCode(Short code) {
        for (StoreDailySettlementStatusEnum status : StoreDailySettlementStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 