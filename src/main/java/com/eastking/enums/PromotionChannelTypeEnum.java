package com.eastking.enums;

import lombok.Getter;

/**
 * 促銷活動管道類型枚舉
 */
@Getter
public enum PromotionChannelTypeEnum {
    HEAD_OFFICE("總公司", false, null),
    DIRECT_STORE("直營門市", true, "請選擇直營門市 (輸入ID或名稱)"),
    FRANCHISE_STORE("經銷門市", true, "請選擇經銷門市 (輸入ID或名稱)"),
    ONLINE_CHANNEL("網路通路", true, "請選擇網路通路 (輸入ID或名稱)"),
    OTHER("其他", true, "請輸入其他特定目標"),
    REGULAR_CUSTOMER("定期客戶群", true, "請選擇定期客戶群 (輸入ID或名稱)");

    private final String description;
    private final boolean allowTargetSelection;
    private final String targetPlaceholder;

    PromotionChannelTypeEnum(String description, boolean allowTargetSelection, String targetPlaceholder) {
        this.description = description;
        this.allowTargetSelection = allowTargetSelection;
        this.targetPlaceholder = targetPlaceholder;
    }

    // Getter for code (enum name, used by frontend)
    public String getCode() {
        return this.name();
    }

    // Getters for description, allowTargetSelection, targetPlaceholder are provided by Lombok @Getter

    public static PromotionChannelTypeEnum fromCode(String code) {
        for (PromotionChannelTypeEnum type : PromotionChannelTypeEnum.values()) {
            // Compare with enum name (which is what getCode() now returns)
            if (type.name().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }
} 