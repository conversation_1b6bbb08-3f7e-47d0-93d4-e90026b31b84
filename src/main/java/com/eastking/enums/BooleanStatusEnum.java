package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum BooleanStatusEnum {
    FALSE((short)0, "否"),
    TRUE((short)1, "是");

    private final Short code;
    private final String description;

    public static BooleanStatusEnum fromCode(Short code) {
        if (code == null) return null;
        for (BooleanStatusEnum status : BooleanStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null; // Or a default, or throw exception
    }

    public static BooleanStatusEnum fromBoolean(boolean value) {
        return value ? TRUE : FALSE;
    }
} 