package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum MaterialTypeStatusEnum implements BaseEnum {
    PICKING(0, "領料"),
    REFUND_PICKING(1, "退料");

    @JsonValue
    private final Short code;
    private final String description;

    MaterialTypeStatusEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }

    private static final Map<Short, MaterialTypeStatusEnum> map =
            Arrays.stream(values()).collect(Collectors.toMap(MaterialTypeStatusEnum::getCode, e -> e));

    public static MaterialTypeStatusEnum fromCode(Short code) {
        return map.get(code);
    }
} 