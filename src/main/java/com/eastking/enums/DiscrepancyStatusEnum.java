package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * (進貨)異常處理狀態枚舉
 */
@Getter
@RequiredArgsConstructor
public enum DiscrepancyStatusEnum {
    PENDING_RESOLUTION((short)0, "待處理"),
    RESOLVED((short)1, "已處理"),
    IN_PROGRESS((short)2, "處理中");

    private final Short code;
    private final String description;

    public static DiscrepancyStatusEnum fromCode(Short code) {
        if (code == null) return null;
        for (DiscrepancyStatusEnum status : DiscrepancyStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 