package com.eastking.enums;

import lombok.Getter;

@Getter
public enum ReservationStatusEnum implements BaseEnum {
    ACTIVE((short) 10, "有效"),
    CONSUMED((short) 20, "已消耗"),
    CANCELLED((short) 30, "已取消");

    private final Short code;
    private final String description;

    ReservationStatusEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    public static ReservationStatusEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        for (ReservationStatusEnum status : ReservationStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 