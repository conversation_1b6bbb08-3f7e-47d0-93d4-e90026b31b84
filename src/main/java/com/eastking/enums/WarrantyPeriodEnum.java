package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum WarrantyPeriodEnum implements BaseEnum {
    NONE(0, "無保固"),
    SIX_MONTH(6, "半年保"),
    ONE_YEAR(12, "一年保"),
    TWO_YEAR(24, "二年保"),
    FOREVER(999, "永久保");

    @JsonValue
    private final Short code;
    private final String description;

    WarrantyPeriodEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }

    private static final Map<Short, WarrantyPeriodEnum> map =
            Arrays.stream(values()).collect(Collectors.toMap(WarrantyPeriodEnum::getCode, e -> e));

    public static WarrantyPeriodEnum fromCode(Short code) {
        if (code == null) return null;
        return map.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        WarrantyPeriodEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 