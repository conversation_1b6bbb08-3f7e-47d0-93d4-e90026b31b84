package com.eastking.enums;

import lombok.Getter;

/**
 * 門市人員類型枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Getter
public enum StoreStaffTypeEnum implements BaseEnum {
    REGULAR_STAFF((short) 0, "正式員工"),
    SUBSTITUTE_STAFF((short) 1, "代班人員");

    private final Short code;
    private final String description;

    StoreStaffTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    public static StoreStaffTypeEnum fromCode(Short code) {
        if (code == null) return null;
        for (StoreStaffTypeEnum type : StoreStaffTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null; // Or throw an IllegalArgumentException
    }
} 