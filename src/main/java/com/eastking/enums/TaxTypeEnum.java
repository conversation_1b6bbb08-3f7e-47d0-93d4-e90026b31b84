package com.eastking.enums;

import lombok.Getter;
import java.util.stream.Stream;

@Getter
public enum TaxTypeEnum implements BaseEnum {
    EXCLUSIVE((short) 1, "外加"),
    INCLUSIVE((short) 2, "內含");

    private final Short code;
    private final String description;

    TaxTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    public static TaxTypeEnum fromCode(Short code) {
        return Stream.of(TaxTypeEnum.values())
                .filter(c -> c.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
