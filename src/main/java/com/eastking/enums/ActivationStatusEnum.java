package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 通用啟用狀態枚舉 (是/否，啟用/停用)
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Getter
@RequiredArgsConstructor
public enum ActivationStatusEnum {

    NO((short)0, "否"), // Also represents DISABLED
    YES((short)1, "是");  // Also represents ENABLED

    private final Short code;
    private final String description;

    public static ActivationStatusEnum fromCode(Short code) {
        for (ActivationStatusEnum status : ActivationStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的啟用狀態碼: " + code);
    }

    public static ActivationStatusEnum fromBoolean(Boolean value) {
        return value != null && value ? YES : NO;
    }

    public boolean booleanValue() {
        return this == YES;
    }
} 