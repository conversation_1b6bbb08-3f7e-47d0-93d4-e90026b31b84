package com.eastking.enums;

import lombok.Getter;

@Getter
public enum WarehouseTypeEnum implements BaseEnum {
    OTHER((short) 0, "其他倉"),
    MAIN((short) 1, "總倉");

    private final Short code;
    private final String description;

    WarehouseTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    public static WarehouseTypeEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        for (WarehouseTypeEnum type : WarehouseTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return OTHER; // Default or throw exception
    }
} 