package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PaymentMethodEnum implements BaseEnum {
    CASH((short) 1, "現金"),
    CREDIT_CARD((short) 2, "信用卡"),
    BANK_TRANSFER((short) 3, "匯款"),
    TECHNICIAN_COLLECTION((short) 4, "技師代收"), // 派工商品訂單適用
    STAFF_COLLECTION((short) 5, "人員代收"),   // 其他內部代收
    VOUCHER((short) 6, "禮券/抵用券"),
    EXCHANGE((short) 9, "換貨轉入"),
    LINE_PAY((short) 10, "LINE Pay"),
    APPLE_PAY((short) 11, "Apple Pay"),
    GOOGLE_PAY((short) 12, "Google Pay"),
    OTHER((short) 99, "其他");

    private final Short code;
    private final String description;

    public static PaymentMethodEnum fromCode(Short code) {
        if (code == null) return null;
        for (PaymentMethodEnum method : PaymentMethodEnum.values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }
        return null;
    }
} 