package com.eastking.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum OrderItemTypeEnum implements BaseEnum<Short> {
    MAIN_PRODUCT((short) 0, "主商品"),
    GIFT_ITEM((short) 1, "贈品"),
    ADD_ON_ITEM((short) 2, "加購品");
    // FUTURE_SERVICE_ITEM((short) 3, "後續服務項目(如待料補送)"); // 可考慮未來擴展

    private final Short code;
    private final String description;

    OrderItemTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, OrderItemTypeEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(OrderItemTypeEnum::getCode, Function.identity()));

    public static OrderItemTypeEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        OrderItemTypeEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 