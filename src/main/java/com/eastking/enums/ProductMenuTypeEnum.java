package com.eastking.enums;

import lombok.Getter;

/**
 * 商品選單類型枚舉
 */
@Getter
public enum ProductMenuTypeEnum {
    DISPATCH("DISPATCH", "派工商品清單"),
    STORE("STORE", "門市商品清單");
    // Add other menu types if needed in the future

    private final String code;
    private final String description;

    ProductMenuTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // Optional: method to get enum by code
    public static ProductMenuTypeEnum fromCode(String code) {
        for (ProductMenuTypeEnum type : ProductMenuTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null; // Or throw exception
    }
} 