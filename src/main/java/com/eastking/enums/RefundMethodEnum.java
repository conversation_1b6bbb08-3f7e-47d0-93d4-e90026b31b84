package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RefundMethodEnum implements BaseEnum {
    CASH(1, "現金退款"),
    CREDIT_CARD_REFUND(2, "信用卡刷退"),
    BANK_TRANSFER(3, "匯款退回"),
    NOTIFY_PLATFORM(4, "通知平台退款");

    @JsonValue
    private final Short code;
    private final String description;

    RefundMethodEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }
    
    private static final Map<Short, RefundMethodEnum> map =
            Arrays.stream(values()).collect(Collectors.toMap(RefundMethodEnum::getCode, e -> e));

    public static RefundMethodEnum fromCode(Short code) {
        return map.get(code);
    }
} 