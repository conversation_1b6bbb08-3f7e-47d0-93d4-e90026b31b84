package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 權限狀態枚舉
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Getter
@RequiredArgsConstructor
public enum PermissionStatusEnum {
    NO_PERMISSION((short)0, "無權限"),
    HAS_PERMISSION((short)1, "有權限");

    private final Short code;
    private final String description;

    public static PermissionStatusEnum fromCode(Short code) {
        if (code == null) return null;
        for (PermissionStatusEnum status : PermissionStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 