package com.eastking.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum WarrantyStatusEnum implements BaseEnum {
    IN_WARRANTY(1, "保固內"),
    OUT_OF_WARRANTY(2, "保固外"),
    UNDEFINED(3, "未定義");

    @JsonValue
    private final Short code;
    private final String description;

    WarrantyStatusEnum(int code, String description) {
        this.code = (short) code;
        this.description = description;
    }

    private static final Map<Short, WarrantyStatusEnum> map =
            Arrays.stream(values()).collect(Collectors.toMap(WarrantyStatusEnum::getCode, e -> e));

    public static WarrantyStatusEnum fromCode(Short code) {
        if (code == null) return null;
        return map.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        WarrantyStatusEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 