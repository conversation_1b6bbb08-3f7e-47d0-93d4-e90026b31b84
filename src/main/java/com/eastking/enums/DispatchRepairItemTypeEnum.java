package com.eastking.enums;

import lombok.Getter;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum DispatchRepairItemTypeEnum implements BaseEnum {

    REPAIR_PART((short) 1, "維修零件"),
    ADDITIONAL_PURCHASE((short) 2, "加購商品"),
    DISPATCH_ITEM((short) 3, "派工商品"),
    RETURN_ITEM((short) 4, "退貨商品"),
    AWAIT_MATERIAL((short) 5, "待料補貨");

    private final Short code;
    private final String description;

    DispatchRepairItemTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, DispatchRepairItemTypeEnum> CODE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(DispatchRepairItemTypeEnum::getCode, e -> e));

    public static DispatchRepairItemTypeEnum fromCode(Short code) {
        return CODE_MAP.get(code);
    }
} 