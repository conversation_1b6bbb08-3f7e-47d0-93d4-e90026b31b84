package com.eastking.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ItemConditionEnum implements BaseEnum {
    BRAND_NEW((short) 0, "全新未拆封"),
    OPENED_UNUSED((short) 1, "已拆封未使用"),
    SLIGHTLY_USED_GOOD((short) 2, "輕微使用痕跡良好"),
    USED_ACCEPTABLE((short) 3, "有使用痕跡可接受"),
    DEFECTIVE((short) 4, "瑕疵品"),
    DAMAGED((short) 5, "損壞品");

    private final Short code;
    private final String description;

    public static ItemConditionEnum fromCode(Short code) {
        if (code == null) return null;
        for (ItemConditionEnum condition : ItemConditionEnum.values()) {
            if (condition.getCode().equals(code)) {
                return condition;
            }
        }
        return null;
    }
} 