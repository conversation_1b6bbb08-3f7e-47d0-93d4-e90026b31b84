package com.eastking.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum OrderOperatorTypeEnum implements BaseEnum<Short> {
    STORE_STAFF((short) 0, "門市人員"),
    HQ_STAFF((short) 1, "總公司人員"),
    SALES_PERSON((short) 2, "業務人員");

    private final Short code;
    private final String description;

    OrderOperatorTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, OrderOperatorTypeEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(OrderOperatorTypeEnum::getCode, Function.identity()));

    public static OrderOperatorTypeEnum fromCode(Short code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }

    public static String getDescriptionByCode(Short code) {
        OrderOperatorTypeEnum anEnum = fromCode(code);
        return anEnum == null ? null : anEnum.getDescription();
    }
} 