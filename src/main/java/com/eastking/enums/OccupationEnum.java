package com.eastking.enums;

import lombok.Getter;

@Getter
public enum OccupationEnum implements BaseEnum {
    MANAGER((short) 1, "公司負責人/高階主管"),
    PROFESSIONAL((short) 2, "專業人士(律師/醫師/建築師等)"),
    PUBLIC_SERVANT((short) 3, "軍公教"),
    OFFICE_WORKER((short) 4, "一般職員"),
    SERVICE_INDUSTRY((short) 5, "服務業"),
    FREELANCER((short) 6, "自由業"),
    STUDENT((short) 7, "學生"),
    HOUSEWIFE((short) 8, "家管"),
    RETIRED((short) 9, "退休"),
    OTHER((short) 99, "其他");

    private final Short code;
    private final String description;

    OccupationEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }
} 