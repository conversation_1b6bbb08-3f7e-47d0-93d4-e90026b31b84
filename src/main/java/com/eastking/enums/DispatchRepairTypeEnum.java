package com.eastking.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum DispatchRepairTypeEnum implements BaseEnum {
    DISPATCH((short) 10, "派工"),
    REPAIR((short) 20, "維修"),
    RETUR<PERSON>((short) 30, "退機"),
    STOREBYCARRY((short) 40, "自載後門市派工");

    private final Short code;
    private final String description;

    DispatchRepairTypeEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Short, DispatchRepairTypeEnum> CODE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(DispatchRepairTypeEnum::getCode, e -> e));

    public static DispatchRepairTypeEnum fromCode(Short code) {
        return CODE_MAP.get(code);
    }
} 