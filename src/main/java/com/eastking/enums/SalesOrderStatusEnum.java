package com.eastking.enums;

import lombok.Getter;

/**
 * 銷貨單狀態枚舉(目前用於門市調撥單轉出回總倉)
 *
 * <AUTHOR> Developer
 * @date 2025/05/22
 */
@Getter
public enum SalesOrderStatusEnum implements BaseEnum {
    PROCESSING((short) 0, "處理中"),
    COMPLETED((short) 50, "已完成"),
    CANCELLED((short) -50, "已取消");
    // Add more statuses as needed, e.g., PENDING_PAYMENT, SHIPPED, etc.
    //AWAITING_DISPATCH((short) 60, "待派工"),
    //DISPATCH_IN_PROGRESS((short) 80, "派工中");

    private final Short code;
    private final String description;

    SalesOrderStatusEnum(Short code, String description) {
        this.code = code;
        this.description = description;
    }

    public static SalesOrderStatusEnum fromCode(Short code) {
        for (SalesOrderStatusEnum status : SalesOrderStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null; // Or throw exception
    }
} 