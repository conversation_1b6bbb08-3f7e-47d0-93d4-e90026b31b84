package com.eastking.erp.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Entity
@Table(name = "comPerson", schema = "dbo")
public class ComPerson {

    @Id
    @Column(name = "PersonID", length = 12)
    private String personId;

    @Column(name = "DepartID", length = 8)
    private String departId;

    @Column(name = "PersonName", length = 20)
    private String personName;

    @Column(name = "EngName", length = 60)
    private String engName;
    
    @Column(name = "GroupID", length = 6)
    private String groupId;

    @Column(name = "IdentityNumber", length = 20)
    private String identityNumber;

    @Column(name = "SexDistinction")
    private Boolean sexDistinction;

    @Column(name = "ProfTitle", length = 16)
    private String profTitle;
    
    @Column(name = "EngProfTitle", length = 30)
    private String engProfTitle;

    @Column(name = "Birthday")
    private Integer birthday;

    @Column(name = "OnJobDate")
    private Integer onJobDate;

    @Column(name = "Nationality", length = 16)
    private String nationality;

    @Column(name = "FamilyReg", length = 16)
    private String familyReg;

    @Column(name = "LeaveJobDate")
    private Integer leaveJobDate;

    @Column(name = "MaritalStatus")
    private Short maritalStatus;

    @Column(name = "Address1", length = 60)
    private String address1;

    @Column(name = "Address2", length = 60)
    private String address2;

    @Column(name = "Phone1", length = 16)
    private String phone1;

    @Column(name = "MobilePhone", length = 16)
    private String mobilePhone;

    @Column(name = "Email", length = 50)
    private String email;

    @Column(name = "Memo", length = 4000)
    private String memo;

    @Column(name = "UsrDef1", length = 50)
    private String usrDef1;

    @Column(name = "UsrDef2", length = 50)
    private String usrDef2;

    @Column(name = "BloodType", length = 20)
    private String bloodType;

    //登入密碼欄位
    @Column(name = "BPMachine", length = 15)
    private String bpMachine;

    @Column(name = "Country", length = 10)
    private String country;

    @Column(name = "PosiID", length = 10)
    private String posiId;

    @Column(name = "MergeOutState")
    private Short mergeOutState;

    @Column(name = "DataVer")
    private Integer dataVer;

    @Column(name = "YanChangIndex")
    private Short yanChangIndex;

    @Column(name = "UsePerms")
    private Boolean usePerms;

    @Column(name = "GUID", length = 38)
    private String guid;

    @Column(name = "IsImport")
    private Boolean isImport;

    @Column(name = "InitAge")
    private BigDecimal initAge;

    @Column(name = "FirstWorkDate")
    private Integer firstWorkDate;

    @Column(name = "UpdateKey")
    private Integer updateKey;

    @Column(name = "Udef1", length = 50)
    private String udef1;

    @Column(name = "Udef2", length = 50)
    private String udef2;

    @Column(name = "Udef3", length = 50)
    private String udef3;

    @Column(name = "Udef4", length = 50)
    private String udef4;

    @Column(name = "Udef5", length = 50)
    private String udef5;

    @Column(name = "Udef6")
    private Integer udef6;

    @Column(name = "Udef7")
    private Integer udef7;

    @Column(name = "Udef8")
    private Integer udef8;

    @Column(name = "Udef9")
    private Double udef9;

    @Column(name = "Udef10")
    private Double udef10;

    @Column(name = "Udef11")
    private Double udef11;

    @Column(name = "Udef12", length = 50)
    private String udef12;

    @Column(name = "Udef13", length = 50)
    private String udef13;

    @Column(name = "Udef14", length = 50)
    private String udef14;

    @Column(name = "Udef15", length = 50)
    private String udef15;

    @Column(name = "Udef16", length = 50)
    private String udef16;

    @Column(name = "Udef17", length = 50)
    private String udef17;

    @Column(name = "Udef18", length = 50)
    private String udef18;

    @Column(name = "Udef19", length = 50)
    private String udef19;

    @Column(name = "Udef20", length = 50)
    private String udef20;

    @Column(name = "Udef21", length = 50)
    private String udef21;

    @Column(name = "Udef22", length = 50)
    private String udef22;

    @Column(name = "Udef23", length = 50)
    private String udef23;

    @Column(name = "Udef24", length = 50)
    private String udef24;

    @Column(name = "Udef25", length = 50)
    private String udef25;

    @Column(name = "Udef26", length = 50)
    private String udef26;

    @Column(name = "Udef27")
    private Integer udef27;

    @Column(name = "Udef28")
    private Integer udef28;

    @Column(name = "Udef29")
    private Integer udef29;

    @Column(name = "Udef30")
    private Integer udef30;

    @Column(name = "Udef31")
    private Integer udef31;

    @Column(name = "Udef32")
    private Integer udef32;

    @Column(name = "Udef33")
    private Integer udef33;

    @Column(name = "Udef34")
    private Integer udef34;

    @Column(name = "Udef35")
    private Integer udef35;

    @Column(name = "Udef36")
    private Integer udef36;

    @Column(name = "Udef37")
    private Integer udef37;

    @Column(name = "Udef38")
    private Integer udef38;

    @Column(name = "Udef39")
    private Integer udef39;

    @Column(name = "Udef40")
    private Integer udef40;

    @Column(name = "Udef41")
    private Integer udef41;

    @Column(name = "Udef42")
    private Integer udef42;

    @Column(name = "Udef43")
    private Integer udef43;

    @Column(name = "Udef44")
    private Double udef44;

    @Column(name = "Udef45")
    private Double udef45;

    @Column(name = "Udef46")
    private Double udef46;

    @Column(name = "Udef47")
    private Double udef47;

    @Column(name = "Udef48")
    private Double udef48;

    @Column(name = "Udef49")
    private Double udef49;

    @Column(name = "Udef50")
    private Double udef50;

    @Column(name = "Udef51")
    private Double udef51;

    @Column(name = "Udef52")
    private Double udef52;

    @Column(name = "Udef53")
    private Double udef53;

    @Column(name = "Udef54")
    private Double udef54;

    @Column(name = "Udef55")
    private Double udef55;

    @Column(name = "Udef56")
    private Double udef56;

    @Column(name = "Udef57")
    private Double udef57;

    @Column(name = "Udef58")
    private Double udef58;

    @Column(name = "Udef59")
    private Double udef59;

    @Column(name = "Udef60")
    private Double udef60;

    @Column(name = "InvalidDate")
    private Integer invalidDate;

    @Column(name = "LastModify", length = 20)
    private String lastModify;
} 