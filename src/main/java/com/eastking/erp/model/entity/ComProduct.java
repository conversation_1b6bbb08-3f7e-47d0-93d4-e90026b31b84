package com.eastking.erp.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Entity
@Table(name = "comProduct", schema = "dbo")
public class ComProduct {

    @Id
    @Column(name = "ProdID", length = 40)
    private String prodId;

    @Column(name = "FOBCurrID", nullable = false, length = 4)
    private String fobCurrId;

    @Column(name = "CAvgCost", nullable = false)
    private Double cAvgCost;

    @Column(name = "SubID", nullable = false, length = 4)
    private String subId;

    @Column(name = "ClassID", nullable = false, length = 6)
    private String classId;

    @Column(name = "BarCodeID", nullable = false, length = 20)
    private String barCodeId;

    @Column(name = "UnitID", nullable = false, length = 8)
    private String unitId;

    @Column(name = "Unit", nullable = false, length = 8)
    private String unit;

    @Column(name = "ProdName", nullable = false, length = 200)
    private String prodName;

    @Column(name = "EngName", nullable = false, length = 200)
    private String engName;

    @Column(name = "CurrID", nullable = false, length = 4)
    private String currId;

    @Column(name = "SuggestPrice", nullable = false)
    private Double suggestPrice;

    @Column(name = "SalesPriceA", nullable = false)
    private Double salesPriceA;

    @Column(name = "SalesPriceB", nullable = false)
    private Double salesPriceB;

    @Column(name = "SalesPriceC", nullable = false)
    private Double salesPriceC;

    @Column(name = "SalesPriceD", nullable = false)
    private Double salesPriceD;

    @Column(name = "SalesPriceE", nullable = false)
    private Double salesPriceE;

    @Column(name = "StdPrice", nullable = false)
    private Double stdPrice;

    @Column(name = "ConverUnit", nullable = false, length = 8)
    private String converUnit;

    @Column(name = "ConverRate", nullable = false)
    private Double converRate;

    @Column(name = "PackAmt1", nullable = false)
    private BigDecimal packAmt1;

    @Column(name = "PackAmt2", nullable = false)
    private BigDecimal packAmt2;

    @Column(name = "PackUnit1", nullable = false, length = 8)
    private String packUnit1;

    @Column(name = "PackUnit2", nullable = false, length = 8)
    private String packUnit2;

    @Column(name = "InventoryID", nullable = false, length = 4)
    private String inventoryId;

    @Column(name = "BusiTaxRate", nullable = false)
    private Double busiTaxRate;

    @Column(name = "Excise", nullable = false)
    private Double excise;

    @Column(name = "ImpTaxRate", nullable = false)
    private Double impTaxRate;

    @Column(name = "BatchUsed", nullable = false)
    private Boolean batchUsed;

    @Column(name = "EffectDateUsed", nullable = false)
    private Boolean effectDateUsed;

    @Column(name = "ProdDesc", nullable = false, length = 4000)
    private String prodDesc;

    @Column(name = "ProdPic", nullable = false, length = 38)
    private String prodPic;

    @Column(name = "ProdForm", nullable = false)
    private Short prodForm;

    @Column(name = "BaseInc", nullable = false)
    private BigDecimal baseInc;

    @Column(name = "MinPurch", nullable = false)
    private BigDecimal minPurch;

    @Column(name = "SafeStock", nullable = false)
    private Boolean safeStock;

    @Column(name = "AdvanceDays", nullable = false)
    private BigDecimal advanceDays;

    @Column(name = "MaterialWare", nullable = false, length = 6)
    private String materialWare;

    @Column(name = "OverReceRate", nullable = false)
    private Double overReceRate;

    @Column(name = "PurchPolicy", nullable = false)
    private Short purchPolicy;

    @Column(name = "MajorSupplier", nullable = false, length = 20)
    private String majorSupplier;

    @Column(name = "BOutStockDay", nullable = false)
    private Integer bOutStockDay;

    @Column(name = "BInStockDay", nullable = false)
    private Integer bInStockDay;

    @Column(name = "BPurchDate", nullable = false)
    private Integer bPurchDate;

    @Column(name = "BSalesDate", nullable = false)
    private Integer bSalesDate;

    @Column(name = "SluggishDays", nullable = false)
    private Integer sluggishDays;

    @Column(name = "LatestIndate", nullable = false)
    private Integer latestIndate;

    @Column(name = "LatestOutDate", nullable = false)
    private Integer latestOutDate;

    @Column(name = "LatestPurchDate", nullable = false)
    private Integer latestPurchDate;

    @Column(name = "LatestSalesDate", nullable = false)
    private Integer latestSalesDate;

    @Column(name = "StopDate", nullable = false)
    private Integer stopDate;

    @Column(name = "Main_Des", nullable = false, length = 60)
    private String mainDes;

    @Column(name = "CCC_CODE", nullable = false, length = 20)
    private String cccCode;

    @Column(name = "EngUnit", nullable = false, length = 6)
    private String engUnit;

    @Column(name = "FOBPrice", nullable = false)
    private Double fobPrice;

    @Column(name = "CY20", nullable = false)
    private BigDecimal cy20;

    @Column(name = "CY40", nullable = false)
    private BigDecimal cy40;

    @Column(name = "InPackUnit", nullable = false, length = 8)
    private String inPackUnit;

    @Column(name = "InPackAmt", nullable = false)
    private BigDecimal inPackAmt;

    @Column(name = "OutPackUnit", nullable = false, length = 6)
    private String outPackUnit;

    @Column(name = "OutPackAmt", nullable = false)
    private BigDecimal outPackAmt;

    @Column(name = "VolumeUnit", nullable = false, length = 6)
    private String volumeUnit;

    @Column(name = "Volume", nullable = false)
    private BigDecimal volume;

    @Column(name = "NetWeightUnit", nullable = false, length = 6)
    private String netWeightUnit;

    @Column(name = "NetWeight", nullable = false)
    private BigDecimal netWeight;

    @Column(name = "GrossWeightUnit", nullable = false, length = 6)
    private String grossWeightUnit;

    @Column(name = "GrossWeigh", nullable = false)
    private BigDecimal grossWeigh;

    @Column(name = "MEAMTUnit", nullable = false, length = 6)
    private String meamtUnit;

    @Column(name = "MEAMT", nullable = false, length = 100)
    private String meamt;

    @Column(name = "BAvgCost", nullable = false)
    private Double bAvgCost;

    @Column(name = "BStdCost", nullable = false)
    private Double bStdCost;

    @Column(name = "MergeOutState", nullable = false)
    private Short mergeOutState;

    @Column(name = "IsCheck", nullable = false)
    private Boolean isCheck;

    @Column(name = "CTotalCost", nullable = false)
    private BigDecimal cTotalCost;

    @Column(name = "InvoProdName", nullable = false, length = 60)
    private String invoProdName;

    @Column(name = "DefValidDay", nullable = false)
    private Short defValidDay;

    @Column(name = "ValidDateUsed", nullable = false)
    private Boolean validDateUsed;

    @Column(name = "BackTaxRate", nullable = false)
    private Double backTaxRate;

    @Column(name = "DataVer", nullable = false)
    private Integer dataVer;

    @Column(name = "MoreRate", nullable = false)
    private Double moreRate;

    @Column(name = "UDef1", nullable = false, length = 50)
    private String uDef1;

    @Column(name = "UDef2", nullable = false, length = 50)
    private String uDef2;

    @Column(name = "RepairTerm", nullable = false)
    private Integer repairTerm;

    @Column(name = "PriceOfTax", nullable = false)
    private Boolean priceOfTax;

    @Column(name = "InsurRate", nullable = false)
    private Double insurRate;

    @Column(name = "InsurRateEx", nullable = false)
    private Double insurRateEx;

    @Column(name = "BTotalCost", nullable = false)
    private BigDecimal bTotalCost;

    @Column(name = "HQ40", nullable = false)
    private BigDecimal hq40;

    @Column(name = "CY45", nullable = false)
    private BigDecimal cy45;

    @Column(name = "PerDays", nullable = false)
    private Integer perDays;

    @Column(name = "UsePerms", nullable = false)
    private Boolean usePerms;

    @Column(name = "StdCost", nullable = false)
    private Double stdCost;

    @Column(name = "CheckMethod", nullable = false)
    private Short checkMethod;

    @Column(name = "CheckProjectID", nullable = false, length = 6)
    private String checkProjectId;

    @Column(name = "LevelId", nullable = false, length = 6)
    private String levelId;

    @Column(name = "StrictLimit", nullable = false)
    private Short strictLimit;

    @Column(name = "Long", nullable = false)
    private BigDecimal length;

    @Column(name = "Width", nullable = false)
    private BigDecimal width;

    @Column(name = "High", nullable = false)
    private BigDecimal high;

    @Column(name = "LUnit", nullable = false, length = 8)
    private String lUnit;

    @Column(name = "MVolume", nullable = false)
    private BigDecimal mVolume;

    @Column(name = "VUnit", nullable = false, length = 8)
    private String vUnit;

    @Column(name = "NWeight", nullable = false)
    private BigDecimal nWeight;

    @Column(name = "NUnit", nullable = false, length = 8)
    private String nUnit;

    @Column(name = "GWeight", nullable = false)
    private BigDecimal gWeight;

    @Column(name = "GUnit", nullable = false, length = 8)
    private String gUnit;

    @Column(name = "Area", nullable = false)
    private BigDecimal area;

    @Column(name = "AUnit", nullable = false, length = 8)
    private String aUnit;

    @Column(name = "IsLeftOverMat", nullable = false)
    private Boolean isLeftOverMat;

    @Column(name = "IsVirtual", nullable = false)
    private Boolean isVirtual;

    @Column(name = "IsOptional", nullable = false)
    private Boolean isOptional;

    @Column(name = "IsConfig", nullable = false)
    private Boolean isConfig;

    @Column(name = "IsCharacter", nullable = false)
    private Boolean isCharacter;

    @Column(name = "IsOptionalManual", nullable = false)
    private Boolean isOptionalManual;

    @Column(name = "IsOptionalAuto", nullable = false)
    private Boolean isOptionalAuto;

    @Column(name = "ProdCodeRuleType", nullable = false, length = 6)
    private String prodCodeRuleType;

    @Column(name = "UseOverReceRate", nullable = false)
    private Boolean useOverReceRate;

    @Column(name = "CodeRule", nullable = false, length = 6)
    private String codeRule;

    @Column(name = "UsePurseCheck", nullable = false)
    private Boolean usePurseCheck;

    @Column(name = "UseProdtCheck", nullable = false)
    private Boolean useProdtCheck;

    @Column(name = "CanModifyCheck", nullable = false)
    private Boolean canModifyCheck;

    @Column(name = "IsTaxProdt", nullable = false)
    private Boolean isTaxProdt;

    @Column(name = "MergerNo", nullable = false, length = 10)
    private String mergerNo;

    @Column(name = "UseInCtoms", nullable = false)
    private Boolean useInCtoms;

    @Column(name = "WareType", nullable = false)
    private Integer wareType;

    @Column(name = "CtmWeight", nullable = false)
    private BigDecimal ctmWeight;

    @Column(name = "CtmUnit", nullable = false, length = 8)
    private String ctmUnit;

    @Column(name = "CtmLeftover", nullable = false)
    private Boolean ctmLeftover;

    @Column(name = "CtmLostBegin", nullable = false)
    private Double ctmLostBegin;

    @Column(name = "CtmLostEnd", nullable = false)
    private Double ctmLostEnd;

    @Column(name = "UseChar", nullable = false)
    private Boolean useChar;

    @Column(name = "NotIncMRP", nullable = false)
    private Boolean notIncMRP;

    @Column(name = "NeedDeclare", nullable = false)
    private Boolean needDeclare;

    @Column(name = "DefCheckerID", nullable = false, length = 12)
    private String defCheckerId;

    @Column(name = "UseSerial", nullable = false)
    private Boolean useSerial;

    @Column(name = "UseSmallBatch", nullable = false)
    private Boolean useSmallBatch;

    @Column(name = "TraBoxQty", nullable = false)
    private BigDecimal traBoxQty;

    @Column(name = "TraBoxUnit", nullable = false, length = 8)
    private String traBoxUnit;

    @Column(name = "CostType", nullable = false)
    private Short costType;

    @Column(name = "GWMatID", nullable = false, length = 40)
    private String gwMatId;

    @Column(name = "TakeMatMultiple", nullable = false)
    private BigDecimal takeMatMultiple;

    @Column(name = "UseAuditing", nullable = false)
    private Boolean useAuditing;

    @Column(name = "InvoName", nullable = false, length = 60)
    private String invoName;

    @Column(name = "DefValidDays", nullable = false)
    private Short defValidDays;

    @Column(name = "MemberPrice", nullable = false)
    private Double memberPrice;

    @Column(name = "UpdateKey", nullable = false)
    private Integer updateKey;

    @Column(name = "TempDelete", nullable = false)
    private Boolean tempDelete;

    @Column(name = "Udef3", nullable = false, length = 50)
    private String udef3;

    @Column(name = "Udef4", nullable = false, length = 50)
    private String udef4;

    @Column(name = "Udef5", nullable = false, length = 50)
    private String udef5;

    @Column(name = "Udef6", nullable = false)
    private Integer udef6;

    @Column(name = "Udef7", nullable = false)
    private Integer udef7;

    @Column(name = "Udef8", nullable = false)
    private Integer udef8;

    @Column(name = "Udef9")
    private Double udef9;

    @Column(name = "Udef10", nullable = false)
    private Double udef10;

    @Column(name = "Udef11", nullable = false)
    private Double udef11;

    @Column(name = "Udef12", nullable = false, length = 50)
    private String udef12;

    @Column(name = "Udef13", nullable = false, length = 50)
    private String udef13;

    @Column(name = "Udef14", nullable = false, length = 50)
    private String udef14;

    @Column(name = "Udef15", nullable = false, length = 50)
    private String udef15;

    @Column(name = "Udef16", nullable = false, length = 50)
    private String udef16;

    @Column(name = "Udef17", nullable = false, length = 50)
    private String udef17;

    @Column(name = "Udef18", nullable = false, length = 50)
    private String udef18;

    @Column(name = "Udef19", nullable = false, length = 50)
    private String udef19;

    @Column(name = "Udef20", nullable = false, length = 50)
    private String udef20;

    @Column(name = "Udef21", nullable = false, length = 50)
    private String udef21;

    @Column(name = "Udef22", nullable = false)
    private String udef22;

    @Column(name = "Udef23", nullable = false, length = 50)
    private String udef23;

    @Column(name = "Udef24", nullable = false, length = 50)
    private String udef24;

    @Column(name = "Udef25", nullable = false, length = 50)
    private String udef25;

    @Column(name = "Udef26", nullable = false, length = 50)
    private String udef26;

    @Column(name = "Udef27", nullable = false)
    private Integer udef27;

    @Column(name = "Udef28", nullable = false)
    private Integer udef28;

    @Column(name = "Udef29", nullable = false)
    private Integer udef29;

    @Column(name = "Udef30", nullable = false)
    private Integer udef30;

    @Column(name = "Udef31", nullable = false)
    private Integer udef31;

    @Column(name = "Udef32", nullable = false)
    private Integer udef32;

    @Column(name = "Udef33", nullable = false)
    private Integer udef33;

    @Column(name = "Udef34", nullable = false)
    private Integer udef34;

    @Column(name = "Udef35", nullable = false)
    private Integer udef35;

    @Column(name = "Udef36", nullable = false)
    private Integer udef36;

    @Column(name = "Udef37", nullable = false)
    private Integer udef37;

    @Column(name = "Udef38", nullable = false)
    private Integer udef38;

    @Column(name = "Udef39", nullable = false)
    private Integer udef39;

    @Column(name = "Udef40", nullable = false)
    private Integer udef40;

    @Column(name = "Udef41", nullable = false)
    private Integer udef41;

    @Column(name = "Udef42", nullable = false)
    private Integer udef42;

    @Column(name = "Udef43", nullable = false)
    private Integer udef43;



    @Column(name = "Udef44", nullable = false)
    private Double udef44;

    @Column(name = "Udef45", nullable = false)
    private Double udef45;

    @Column(name = "Udef46", nullable = false)
    private Double udef46;

    @Column(name = "Udef47", nullable = false)
    private Double udef47;

    @Column(name = "Udef48", nullable = false)
    private Double udef48;

    @Column(name = "Udef49", nullable = false)
    private Double udef49;

    @Column(name = "Udef50", nullable = false)
    private Double udef50;

    @Column(name = "Udef51", nullable = false)
    private Double udef51;

    @Column(name = "Udef52", nullable = false)
    private Double udef52;

    @Column(name = "Udef53", nullable = false)
    private Double udef53;

    @Column(name = "Udef54", nullable = false)
    private Double udef54;

    @Column(name = "Udef55", nullable = false)
    private Double udef55;

    @Column(name = "Udef56", nullable = false)
    private Double udef56;

    @Column(name = "Udef57", nullable = false)
    private Double udef57;

    @Column(name = "Udef58", nullable = false)
    private Double udef58;

    @Column(name = "Udef59", nullable = false)
    private Double udef59;

    @Column(name = "Udef60", nullable = false)
    private Double udef60;

    @Column(name = "MinPackQty", nullable = false)
    private BigDecimal minPackQty;

    @Column(name = "UserDef1", nullable = false, length = 50)
    private String userDef1;

    @Column(name = "UserDef2", nullable = false, length = 50)
    private String userDef2;

    @Column(name = "LastModify", nullable = false, length = 20)
    private String lastModify;
} 