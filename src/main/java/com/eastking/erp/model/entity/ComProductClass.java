package com.eastking.erp.model.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Data
@Entity
@Table(name = "comProductClass", schema = "dbo")
public class ComProductClass {

    @Id
    @Column(name = "ClassID", length = 6)
    private String classId;

    @Column(name = "ClassName", length = 20)
    private String className;

    @Column(name = "EngName", length = 60)
    private String engName;

    @Column(name = "AccInventory", length = 16)
    private String accInventory;

    @Column(name = "AccPurchased", length = 16)
    private String accPurchased;

    @Column(name = "ReturnPurchase", length = 16)
    private String returnPurchase;

    @Column(name = "AccSale", length = 16)
    private String accSale;

    @Column(name = "AccSaleCost", length = 16)
    private String accSaleCost;

    @Column(name = "ReturnSale", length = 16)
    private String returnSale;

    @Column(name = "GiftExpense", length = 16)
    private String giftExpense;

    @Column(name = "MaterialWarehouse", length = 8)
    private String materialWarehouse;

    @Column(name = "MergeOutState")
    private Short mergeOutState;

    @Column(name = "DataVer")
    private Integer dataVer;

    @Column(name = "OtherExpense", length = 16)
    private String otherExpense;

    @Column(name = "OtherIncome", length = 16)
    private String otherIncome;

    @Column(name = "OtherCost", length = 16)
    private String otherCost;

    @Column(name = "UseAuditing")
    private Boolean useAuditing;

    @Column(name = "UpdateKey")
    private Integer updateKey;

    @Column(name = "TempDelete")
    private Boolean tempDelete;

    @Column(name = "Udef1", length = 50)
    private String udef1;

    @Column(name = "Udef2", length = 50)
    private String udef2;

    @Column(name = "Udef3", length = 50)
    private String udef3;

    @Column(name = "Udef4", length = 50)
    private String udef4;

    @Column(name = "Udef5", length = 50)
    private String udef5;

    @Column(name = "Udef6")
    private Integer udef6;

    @Column(name = "Udef7")
    private Integer udef7;

    @Column(name = "Udef8")
    private Integer udef8;

    @Column(name = "Udef9")
    private Double udef9;

    @Column(name = "Udef10")
    private Double udef10;

    @Column(name = "Udef11")
    private Double udef11;

    @Column(name = "Udef12", length = 50)
    private String udef12;

    @Column(name = "Udef13", length = 50)
    private String udef13;

    @Column(name = "Udef14", length = 50)
    private String udef14;

    @Column(name = "Udef15", length = 50)
    private String udef15;

    @Column(name = "Udef16", length = 50)
    private String udef16;

    @Column(name = "Udef17", length = 50)
    private String udef17;

    @Column(name = "Udef18", length = 50)
    private String udef18;

    @Column(name = "Udef19", length = 50)
    private String udef19;

    @Column(name = "Udef20", length = 50)
    private String udef20;

    @Column(name = "Udef21", length = 50)
    private String udef21;

    @Column(name = "Udef22", length = 50)
    private String udef22;

    @Column(name = "Udef23", length = 50)
    private String udef23;

    @Column(name = "Udef24", length = 50)
    private String udef24;

    @Column(name = "Udef25", length = 50)
    private String udef25;

    @Column(name = "Udef26", length = 50)
    private String udef26;

    @Column(name = "Udef27")
    private Integer udef27;

    @Column(name = "Udef28")
    private Integer udef28;

    @Column(name = "Udef29")
    private Integer udef29;

    @Column(name = "Udef30")
    private Integer udef30;

    @Column(name = "Udef31")
    private Integer udef31;

    @Column(name = "Udef32")
    private Integer udef32;

    @Column(name = "Udef33")
    private Integer udef33;

    @Column(name = "Udef34")
    private Integer udef34;

    @Column(name = "Udef35")
    private Integer udef35;

    @Column(name = "Udef36")
    private Integer udef36;

    @Column(name = "Udef37")
    private Integer udef37;

    @Column(name = "Udef38")
    private Integer udef38;

    @Column(name = "Udef39")
    private Integer udef39;

    @Column(name = "Udef40")
    private Integer udef40;

    @Column(name = "Udef41")
    private Integer udef41;

    @Column(name = "Udef42")
    private Integer udef42;

    @Column(name = "Udef43")
    private Integer udef43;

    @Column(name = "Udef44")
    private Double udef44;

    @Column(name = "Udef45")
    private Double udef45;

    @Column(name = "Udef46")
    private Double udef46;

    @Column(name = "Udef47")
    private Double udef47;

    @Column(name = "Udef48")
    private Double udef48;

    @Column(name = "Udef49")
    private Double udef49;

    @Column(name = "Udef50")
    private Double udef50;

    @Column(name = "Udef51")
    private Double udef51;

    @Column(name = "Udef52")
    private Double udef52;

    @Column(name = "Udef53")
    private Double udef53;

    @Column(name = "Udef54")
    private Double udef54;

    @Column(name = "Udef55")
    private Double udef55;

    @Column(name = "Udef56")
    private Double udef56;

    @Column(name = "Udef57")
    private Double udef57;

    @Column(name = "Udef58")
    private Double udef58;

    @Column(name = "Udef59")
    private Double udef59;

    @Column(name = "Udef60")
    private Double udef60;
} 