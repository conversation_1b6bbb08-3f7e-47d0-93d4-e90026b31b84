package com.eastking.erp.model.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "comCustomer", schema = "dbo")
@IdClass(ComCustomer.ComCustomerId.class)
public class ComCustomer {

    @Id
    @Column(name = "Flag")
    private Short flag;

    @Id
    @Column(name = "ID", length = 20)
    private String id;

    @Column(name = "FundsAttribution", length = 20)
    private String fundsAttribution;

    @Column(name = "TransNewID", length = 20)
    private String transNewId;

    @Column(name = "ClassID", length = 6)
    private String classId;

    @Column(name = "AreaID", length = 6)
    private String areaId;

    @Column(name = "CurrencyID", length = 4)
    private String currencyId;

    @Column(name = "FullName", length = 80)
    private String fullName;

    @Column(name = "IsTemp")
    private Boolean isTemp;

    @Column(name = "IsForeign")
    private Boolean isForeign;

    @Column(name = "ShortName", length = 12)
    private String shortName;

    @Column(name = "TaxNo", length = 20)
    private String taxNo;

    @Column(name = "ChiefName", length = 20)
    private String chiefName;

    @Column(name = "Capitalization")
    private BigDecimal capitalization;

    @Column(name = "LinkMan", length = 20)
    private String linkMan;

    @Column(name = "LinkManProf", length = 12)
    private String linkManProf;

    @Column(name = "Telephone1", length = 25)
    private String telephone1;

    @Column(name = "Telephone2", length = 25)
    private String telephone2;

    @Column(name = "Telephone3", length = 25)
    private String telephone3;

    @Column(name = "MobileTel", length = 16)
    private String mobileTel;

    @Column(name = "Moderm", length = 16)
    private String moderm;

    @Column(name = "FaxNo", length = 25)
    private String faxNo;

    @Column(name = "PersonID", length = 12)
    private String personId;

    @Column(name = "ServerID", length = 12)
    private String serverId;

    @Column(name = "DealerID", length = 10)
    private String dealerId;

    @Column(name = "IndustrialClass", length = 18)
    private String industrialClass;

    @Column(name = "Email", length = 50)
    private String email;

    @Column(name = "WebAddress", length = 50)
    private String webAddress;

    @Column(name = "MergeOutState")
    private Short mergeOutState;

    @Column(name = "IsFactory")
    private Boolean isFactory;

    @Column(name = "StartReceivable")
    private BigDecimal startReceivable;

    @Column(name = "PriceofTax")
    private Boolean priceofTax;

    @Column(name = "DirectCust")
    private Boolean directCust;

    @Column(name = "VIP")
    private Boolean vip;

    @Column(name = "VIPLevel", length = 6)
    private String vipLevel;

    @Column(name = "DataVer")
    private Integer dataVer;

    @Column(name = "MemberCodeNo", length = 50)
    private String memberCodeNo;

    @Column(name = "MembercodeDate")
    private Integer membercodeDate;

    @Column(name = "IdentityNO", length = 20)
    private String identityNO;

    @Column(name = "MaritalStatus")
    private Short maritalStatus;

    @Column(name = "SexDistinction")
    private Boolean sexDistinction;

    @Column(name = "Metier", length = 16)
    private String metier;

    @Column(name = "NativePlace", length = 16)
    private String nativePlace;

    @Column(name = "NativeAddress", length = 50)
    private String nativeAddress;

    @Column(name = "FamilyAddress", length = 50)
    private String familyAddress;

    @Column(name = "ZipCode", length = 16)
    private String zipCode;

    @Column(name = "InvoiceHead", length = 80)
    private String invoiceHead;

    @Column(name = "GatherOther", length = 20)
    private String gatherOther;

    @Column(name = "CheckOther", length = 20)
    private String checkOther;

    @Column(name = "InvoTax")
    private Boolean invoTax;

    @Column(name = "UsePerms")
    private Boolean usePerms;

    @Column(name = "SrcID")
    private Short srcId;

    @Column(name = "SrcName", length = 14)
    private String srcName;

    @Column(name = "PlanPerson", length = 12)
    private String planPerson;

    @Column(name = "MergeState")
    private Short mergeState;

    @Column(name = "Password", length = 20)
    private String password;

    @Column(name = "FactPwdMemo", length = 400)
    private String factPwdMemo;

    @Column(name = "GUID", length = 38)
    private String guid;

    @Column(name = "ElectronInvoice")
    private Short electronInvoice;

    @Column(name = "Hqh", length = 40)
    private String hqh;

    @Column(name = "HqDate")
    private Integer hqDate;

    @Column(name = "Hqw", length = 60)
    private String hqw;

    @Column(name = "Sjq", length = 90)
    private String sjq;

    @Column(name = "BToBOrToC")
    private Short bToBOrToC;

    @Column(name = "IdentifyNo", length = 4)
    private String identifyNo;

    @Column(name = "EIStoreTypeGLN", length = 13)
    private String eiStoreTypeGLN;

    @Column(name = "EIStoreNo", length = 2)
    private String eiStoreNo;

    @Column(name = "EICourseType", length = 2)
    private String eiCourseType;

    @Column(name = "EIStoreTypeGLN1", length = 20)
    private String eiStoreTypeGLN1;

    @Column(name = "EIUsePapInvoice")
    private Boolean eiUsePapInvoice;

    @Column(name = "EIPageType")
    private Integer eiPageType;

    @Column(name = "EISellerGLNGlide", length = 2)
    private String eiSellerGLNGlide;

    @Column(name = "IsContriBute")
    private Boolean isContriBute;

    @Column(name = "NPOBAN", length = 10)
    private String npoban;

    @Column(name = "IsBToCRegNo")
    private Boolean isBToCRegNo;

    @Column(name = "CarrierType", length = 6)
    private String carrierType;

    @Column(name = "CarrierId1", length = 400)
    private String carrierId1;

    @Column(name = "CarrierId2", length = 400)
    private String carrierId2;

    @Column(name = "EIEmail", length = 80)
    private String eiEmail;

    @Column(name = "EISellerGLN", length = 20)
    private String eiSellerGLN;

    @Column(name = "CommonCarrier")
    private Boolean commonCarrier;

    @Column(name = "EnterpriseTypeCode", length = 5)
    private String enterpriseTypeCode;

    @Column(name = "GMMemberAccount", length = 50)
    private String gmMemberAccount;

    @Column(name = "FYMIGIdentifyNo", length = 4)
    private String fymigIdentifyNo;

    @Column(name = "Udef1", length = 50)
    private String udef1;

    @Column(name = "Udef2", length = 50)
    private String udef2;

    @Column(name = "Udef3", length = 50)
    private String udef3;

    @Column(name = "Udef4", length = 50)
    private String udef4;

    @Column(name = "Udef5", length = 50)
    private String udef5;

    @Column(name = "Udef6")
    private Integer udef6;

    @Column(name = "Udef7")
    private Integer udef7;

    @Column(name = "Udef8")
    private Integer udef8;

    @Column(name = "Udef9")
    private Double udef9;

    @Column(name = "Udef10")
    private Double udef10;

    @Column(name = "Udef11")
    private Double udef11;

    @Column(name = "Udef12", length = 50)
    private String udef12;

    @Column(name = "Udef13", length = 50)
    private String udef13;

    @Column(name = "Udef14", length = 50)
    private String udef14;

    @Column(name = "Udef15", length = 50)
    private String udef15;

    @Column(name = "Udef16", length = 50)
    private String udef16;

    @Column(name = "Udef17", length = 50)
    private String udef17;

    @Column(name = "Udef18", length = 50)
    private String udef18;

    @Column(name = "Udef19", length = 50)
    private String udef19;

    @Column(name = "Udef20", length = 50)
    private String udef20;

    @Column(name = "Udef21", length = 50)
    private String udef21;

    @Column(name = "Udef22", length = 50)
    private String udef22;

    @Column(name = "Udef23", length = 50)
    private String udef23;

    @Column(name = "Udef24", length = 50)
    private String udef24;

    @Column(name = "Udef25", length = 50)
    private String udef25;

    @Column(name = "Udef26", length = 50)
    private String udef26;

    @Column(name = "Udef27")
    private Integer udef27;

    @Column(name = "Udef28")
    private Integer udef28;

    @Column(name = "Udef29")
    private Integer udef29;

    @Column(name = "Udef30")
    private Integer udef30;

    @Column(name = "Udef31")
    private Integer udef31;

    @Column(name = "Udef32")
    private Integer udef32;

    @Column(name = "Udef33")
    private Integer udef33;

    @Column(name = "Udef34")
    private Integer udef34;

    @Column(name = "Udef35")
    private Integer udef35;

    @Column(name = "Udef36")
    private Integer udef36;

    @Column(name = "Udef37")
    private Integer udef37;

    @Column(name = "Udef38")
    private Integer udef38;

    @Column(name = "Udef39")
    private Integer udef39;

    @Column(name = "Udef40")
    private Integer udef40;

    @Column(name = "Udef41")
    private Integer udef41;

    @Column(name = "Udef42")
    private Integer udef42;

    @Column(name = "Udef43")
    private Integer udef43;

    @Column(name = "Udef44")
    private Double udef44;

    @Column(name = "Udef45")
    private Double udef45;

    @Column(name = "Udef46")
    private Double udef46;

    @Column(name = "Udef47")
    private Double udef47;

    @Column(name = "Udef48")
    private Double udef48;

    @Column(name = "Udef49")
    private Double udef49;

    @Column(name = "Udef50")
    private Double udef50;

    @Column(name = "Udef51")
    private Double udef51;

    @Column(name = "Udef52")
    private Double udef52;

    @Column(name = "Udef53")
    private Double udef53;

    @Column(name = "Udef54")
    private Double udef54;

    @Column(name = "Udef55")
    private Double udef55;

    @Column(name = "Udef56")
    private Double udef56;

    @Column(name = "Udef57")
    private Double udef57;

    @Column(name = "Udef58")
    private Double udef58;

    @Column(name = "Udef59")
    private Double udef59;

    @Column(name = "Udef60")
    private Double udef60;

    @Column(name = "LastModify", length = 20)
    private String lastModify;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class ComCustomerId implements Serializable {
        private Short flag;
        private String id;
    }
} 