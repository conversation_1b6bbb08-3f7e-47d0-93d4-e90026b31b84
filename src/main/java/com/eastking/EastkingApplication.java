package com.eastking;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

// import java.util.UUID; // No longer needed here
// import com.eastking.config.AuditorAwareImpl; // No longer needed here

@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        JpaRepositoriesAutoConfiguration.class
})
// @EnableJpaAuditing(auditorAwareRef = "auditorProvider") // REMOVED: This is now solely handled by JpaAuditingConfiguration.java
public class EastkingApplication extends SpringBootServletInitializer {

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(EastkingApplication.class);
	}

	public static void main(String[] args) {
		SpringApplication.run(EastkingApplication.class, args);
	}

	// The AuditorAwareImpl is already a @Component, so Spring should pick it up.
	// Explicitly defining it as a @Bean here is an alternative if @Component scanning isn't configured as expected,
	// but typically @Component on AuditorAwareImpl is sufficient if it's in a scanned package.
	// @Bean
	// public AuditorAware<UUID> auditorProvider() {
	//     return new AuditorAwareImpl();
	// }
}
