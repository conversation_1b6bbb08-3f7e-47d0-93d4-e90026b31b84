package com.eastking.util;

import com.eastking.enums.OrderTypeEnum;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

public class OrderNumberUtil {

    private static final Random RANDOM = new Random();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final AtomicInteger sequence = new AtomicInteger(0);

    // Prevent instantiation
    private OrderNumberUtil() {}

    public static String generateOrderNumber(OrderTypeEnum orderType) {
        String prefix = "ORD";
        if (orderType != null) {
            switch (orderType) {
                case STORE_PRODUCT_ORDER: prefix = "M"; break;
                case DISPATCH_PRODUCT_ORDER: prefix = "D"; break;
                case WHOLESALE_ORDER: prefix = "W"; break;
            }
        }
        return generateNumber(prefix);
    }
    
    public static String generateNumber(String prefix) {
        String datePart = OffsetDateTime.now().format(DATE_FORMATTER);
        // Generate a 6-digit random number to reduce collision probability
        String randomPart = String.format("%06d", RANDOM.nextInt(1000000));
        
        // Optional: Incorporate storeCode if it's short and meaningful for uniqueness or routing
        // String storePrefix = (storeCode != null && storeCode.length() <= 3) ? storeCode.toUpperCase() + "-" : "";
        // return storePrefix + prefix + datePart + randomPart;
        return prefix + datePart + randomPart;
    }
} 