package com.eastking.util;

import com.eastking.security.UserDetailsImpl;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import java.util.Optional;
import java.util.UUID;

public class SecurityUtil {

    public static Optional<UserDetailsImpl> getCurrentUserDetails() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && authentication.getPrincipal() instanceof UserDetailsImpl) {
            return Optional.of((UserDetailsImpl) authentication.getPrincipal());
        }
        return Optional.empty();
    }

    public static Optional<UUID> getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
            return Optional.of(((UserDetailsImpl) authentication.getPrincipal()).getId());
        }
        return Optional.empty();
    }
    
    public static Optional<String> getCurrentUserEmployeeId() {
        return getCurrentUserDetails().map(UserDetailsImpl::getUsername);
    }

    public static Optional<String> getCurrentUserDisplayName() {
        return getCurrentUserDetails().map(UserDetailsImpl::getUserDisplayName);
    }
    
    // TODO: Add method to get current user's department/store name
    // This might involve fetching UserAccount or StoreStaffMap entities based on UserDetailsImpl.getId()
    // For now, returning a placeholder.
    public static Optional<String> getCurrentUserDepartmentName() {
        // Placeholder - in a real app, fetch this based on user's profile/store assignment
        return getCurrentUserDetails().map(ud -> "Default Department"); 
    }

    public static Optional<Short> getCurrentUserCompanyDivisionCode() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
            return Optional.ofNullable(((UserDetailsImpl) authentication.getPrincipal()).getCompanyDivisionCode());
        }
        return Optional.empty();
    }
} 