package com.eastking.util;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.util.StringUtils;

public class RequestUtil {

    public static String getClientIpAddress() {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (sra == null) {
            return null; 
        }
        HttpServletRequest request = sra.getRequest();
        
        String xForwardedForHeader = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedForHeader)) {
            return xForwardedForHeader.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
    
    public static String getTraceId() {
        // Placeholder: Implement logic to retrieve trace ID if available (e.g., from MDC or request headers)
        // For example, if using Spring Cloud Sleuth, it might be in MDC.
        // String traceId = org.slf4j.MDC.get("traceId");
        // return traceId;
        return null; 
    }
} 