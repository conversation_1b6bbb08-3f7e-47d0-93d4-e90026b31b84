package com.eastking.security;

import com.eastking.enums.DeleteStatusEnum;
import com.eastking.model.entity.UserAccount;
import com.eastking.model.entity.UserRoleMap;
import com.eastking.repository.UserAccountRepository;
import com.eastking.repository.UserRoleMapRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Spring Security UserDetailsService 實作
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserAccountRepository userAccountRepository;
    private final UserRoleMapRepository userRoleMapRepository;

    @Autowired
    public UserDetailsServiceImpl(UserAccountRepository userAccountRepository, 
                                UserRoleMapRepository userRoleMapRepository) {
        this.userAccountRepository = userAccountRepository;
        this.userRoleMapRepository = userRoleMapRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String employeeId) throws UsernameNotFoundException {
        UserAccount userAccount = userAccountRepository.findByEmployeeIdAndIsDeleted(
            employeeId, DeleteStatusEnum.NOT_DELETED.getCode())
                .orElseThrow(() -> 
                    new UsernameNotFoundException("未找到員工編號為 " + employeeId + " 的使用者或帳號已被刪除")
                );

        // 根據 backend_rules.md (5.1.1 權限與JWT)，角色存在資料庫中
        // 這裡我們獲取使用者對應的角色
        List<UserRoleMap> userRoleMaps = userRoleMapRepository.findByUserAccountAndIsDeleted(
            userAccount, DeleteStatusEnum.NOT_DELETED.getCode());
        
//        List<GrantedAuthority> authorities = userRoleMaps.stream()
//                .map(userRoleMap -> new SimpleGrantedAuthority("ROLE_" + userRoleMap.getRole().getRoleCode())) // Prefix with ROLE_ for Spring Security convention
//                .collect(Collectors.toList());
        List<GrantedAuthority> authorities = userRoleMaps.stream()
                .map(userRoleMap -> new SimpleGrantedAuthority(userRoleMap.getRole().getRoleCode()))
                .collect(Collectors.toList());
        // More fine-grained permissions (e.g., F001_READ_HO) would be loaded and checked separately, not typically as Spring GrantedAuthorities directly for every single permission.
        // Spring authorities are usually role-based.

        return UserDetailsImpl.build(userAccount, authorities);
    }
} 