package com.eastking.security;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTCreationException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * JWT <PERSON>ken 生成和驗證工具類
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Component
public class JwtTokenProvider {

    private static final Logger logger = LoggerFactory.getLogger(JwtTokenProvider.class);

    @Value("${app.security.jwt.secret}")
    private String jwtSecret;

    @Value("${app.security.jwt.expiration-ms}")
    private long jwtExpirationMs;

    @Value("${app.security.jwt.issuer}")
    private String jwtIssuer;

    private static final String SESSION_UUID_CLAIM = "sessionUuid";

    /**
     * 生成 JWT Token
     *
     * @param authentication Spring Security Authentication 物件
     * @param sessionUuid    使用者會話的 UUID
     * @return JWT Token 字串
     */
    public String generateToken(Authentication authentication, UUID sessionUuid) {
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpirationMs);
        
        // 生成唯一的 JWT ID
        String jwtId = UUID.randomUUID().toString();

        List<String> roles = userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList());

        // 記錄開始生成 JWT token
        logger.debug("Starting JWT token generation - Employee ID: {}, User Account ID: {}, Session UUID: {}, JWT ID: {}, Roles: {}", 
            userPrincipal.getUsername(), userPrincipal.getId(), sessionUuid, jwtId, roles);
        
        logger.info("JWT token generation parameters - Issuer: {}, Issued at: {}, Expires at: {}, Expiration duration: {} ms, JWT ID: {}", 
            jwtIssuer, now, expiryDate, jwtExpirationMs, jwtId);

        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            String token = JWT.create()
                    .withIssuer(jwtIssuer)
                    .withSubject(userPrincipal.getUsername()) // employeeId
                    .withIssuedAt(now)
                    .withExpiresAt(expiryDate)
                    .withJWTId(jwtId) // 加入唯一的 JWT ID
                    .withClaim("roles", roles)
                    .withClaim(SESSION_UUID_CLAIM, sessionUuid.toString()) // Store session UUID
                    .withClaim("userId", userPrincipal.getId().toString()) // Store user account UUID (userAccountId)
                    .sign(algorithm);
            
            // 記錄成功生成的 token 資訊
            if (token != null && logger.isDebugEnabled()) {
                int tokenLength = token.length();
                String tokenSnippet = tokenLength > 20 ? 
                    token.substring(0, 10) + "..." + token.substring(tokenLength - 10) : token;
                logger.debug("JWT token generated successfully - Token length: {}, Token snippet: {}, JWT ID: {}", 
                    tokenLength, tokenSnippet, jwtId);
            }
            
            logger.info("Successfully generated JWT token for employee: {} with session UUID: {} and JWT ID: {}", 
                userPrincipal.getUsername(), sessionUuid, jwtId);
            
            return token;
        } catch (JWTCreationException exception){
            logger.error("JWT token creation error for employee: {} - Error: {}", 
                userPrincipal.getUsername(), exception.getMessage(), exception);
            return null;
        }
    }

    /**
     * 從 HttpServletRequest 中解析 JWT Token
     * @param request HTTP 請求
     * @return JWT Token 字串或 null
     */
    public String resolveToken(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            String token = bearerToken.substring(7);
            
            // 記錄從 request header 解析出的 token
            if (logger.isTraceEnabled() && token != null) {
                int tokenLength = token.length();
                String tokenSnippet = tokenLength > 20 ? 
                    token.substring(0, 10) + "..." + token.substring(tokenLength - 10) : token;
                logger.trace("Resolved JWT token from Authorization header - Token length: {}, Token snippet: {}", 
                    tokenLength, tokenSnippet);
            }
            
            return token;
        }
        
        logger.trace("No JWT token found in Authorization header for request: {}", request.getRequestURI());
        return null;
    }

    /**
     * 驗證 JWT Token 並解碼
     * @param token JWT Token 字串
     * @return DecodedJWT 物件
     * @throws JWTVerificationException 如果驗證失敗
     */
    public DecodedJWT validateAndDecodeToken(String token) throws JWTVerificationException {
        logger.debug("Validating JWT token...");
        
        try {
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(jwtIssuer)
                    .build();
            DecodedJWT decodedJWT = verifier.verify(token);
            
            // 記錄驗證成功的 token 資訊
            logger.debug("JWT token validation successful - Subject: {}, Issued at: {}, Expires at: {}", 
                decodedJWT.getSubject(), decodedJWT.getIssuedAt(), decodedJWT.getExpiresAt());
            
            return decodedJWT;
        } catch (JWTVerificationException e) {
            logger.warn("JWT token validation failed - Error: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 從已解碼的 JWT 中獲取使用者員工編號 (Subject)
     * @param decodedJWT 已解碼的 JWT
     * @return 員工編號
     */
    public String getEmployeeIdFromJWT(DecodedJWT decodedJWT) {
        return decodedJWT.getSubject();
    }

    /**
     * 從已解碼的 JWT 中獲取會話 UUID
     * @param decodedJWT 已解碼的 JWT
     * @return 會話 UUID
     */
    public UUID getSessionUuidFromJWT(DecodedJWT decodedJWT) {
        String sessionUuidStr = decodedJWT.getClaim(SESSION_UUID_CLAIM).asString();
        if (sessionUuidStr != null) {
            try {
                return UUID.fromString(sessionUuidStr);
            }
            catch (IllegalArgumentException e) {
                logger.error("Invalid Session UUID format in JWT: {}", sessionUuidStr, e);
            }
        }
        return null;
    }
    
    /**
     * 從已解碼的 JWT 中獲取使用者帳號ID (userId claim)
     * @param decodedJWT 已解碼的 JWT
     * @return User Account ID (UUID)
     */
    public UUID getUserAccountIdFromJWT(DecodedJWT decodedJWT) {
        String userIdStr = decodedJWT.getClaim("userId").asString();
        if (userIdStr != null) {
            try {
                return UUID.fromString(userIdStr);
            }
            catch (IllegalArgumentException e) {
                logger.error("Invalid User ID format in JWT: {}", userIdStr, e);
            }
        }
        return null;
    }
} 