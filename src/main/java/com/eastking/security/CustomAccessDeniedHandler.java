package com.eastking.security;

import com.eastking.model.vo.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 自定義存取被拒絕處理器
 * 當認證成功的使用者嘗試存取其無權限的資源時觸發。
 *
 * <AUTHOR> Developer
 * @date 2025/05/17
 */
@Component
public class CustomAccessDeniedHandler implements AccessDeniedHandler {

    private static final Logger logger_denied = LoggerFactory.getLogger(CustomAccessDeniedHandler.class);
    private final ObjectMapper objectMapper;

    public CustomAccessDeniedHandler(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public void handle(HttpServletRequest request, 
                       HttpServletResponse response, 
                       AccessDeniedException accessDeniedException) throws IOException, ServletException {
        
        logger_denied.warn("存取被拒絕 for path {}: {}. User: {}", 
            request.getServletPath(), 
            accessDeniedException.getMessage(),
            request.getUserPrincipal() != null ? request.getUserPrincipal().getName() : "anonymousOrUnavailable");

        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);

        ApiResponse<Object> apiResponse = ApiResponse.error(
            HttpServletResponse.SC_FORBIDDEN,
            "權限不足，無法執行此操作: " + accessDeniedException.getMessage()
        );

        objectMapper.writeValue(response.getOutputStream(), apiResponse);
    }
} 