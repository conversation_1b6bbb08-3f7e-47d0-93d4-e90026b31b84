package com.eastking.security;

import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.eastking.model.entity.UserSessionPermission;
import com.eastking.service.UserSessionPermissionService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

/**
 * JWT 認證過濾器，用於驗證請求中的 JWT Token
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger_filter = LoggerFactory.getLogger(JwtAuthenticationFilter.class); 

    private final JwtTokenProvider tokenProvider;
    private final UserDetailsService userDetailsService; // UserDetailsServiceImpl
    private final UserSessionPermissionService userSessionPermissionService;
    private final ObjectMapper objectMapper;

    @Autowired
    public JwtAuthenticationFilter(JwtTokenProvider tokenProvider, UserDetailsService userDetailsService, UserSessionPermissionService userSessionPermissionService, ObjectMapper objectMapper) {
        this.tokenProvider = tokenProvider;
        this.userDetailsService = userDetailsService;
        this.userSessionPermissionService = userSessionPermissionService;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 自定義 Response Wrapper 以便在 response commit 前設置 header
     */
    private static class HeaderSettingResponseWrapper extends HttpServletResponseWrapper {
        private String jwtToken = null;
        private boolean headerSet = false;
        
        public HeaderSettingResponseWrapper(HttpServletResponse response) {
            super(response);
        }
        
        public void setJwtToken(String token) {
            this.jwtToken = token;
            // 立即嘗試設置 header
            setAuthorizationHeader();
        }
        
        private void setAuthorizationHeader() {
            if (jwtToken != null && !headerSet && !super.isCommitted()) {
                super.setHeader("Authorization", "Bearer " + jwtToken);
                headerSet = true;
                logger_filter.debug("Authorization header set with new JWT token");
            }
        }
        
        @Override
        public void flushBuffer() throws IOException {
            setAuthorizationHeader();
            super.flushBuffer();
        }
        
        @Override
        public ServletOutputStream getOutputStream() throws IOException {
            setAuthorizationHeader();
            return super.getOutputStream();
        }
        
        @Override
        public PrintWriter getWriter() throws IOException {
            setAuthorizationHeader();
            return super.getWriter();
        }
        
        @Override
        public void sendError(int sc) throws IOException {
            setAuthorizationHeader();
            super.sendError(sc);
        }
        
        @Override
        public void sendError(int sc, String msg) throws IOException {
            setAuthorizationHeader();
            super.sendError(sc, msg);
        }
        
        @Override
        public void sendRedirect(String location) throws IOException {
            setAuthorizationHeader();
            super.sendRedirect(location);
        }
        
        @Override
        public void setStatus(int sc) {
            setAuthorizationHeader();
            super.setStatus(sc);
        }
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
                                    @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain) throws ServletException, IOException {
        Authentication initialAuthAttempt = null;
        UUID sessionUuidFromToken = null;
        
        // 使用 response wrapper 來延遲 header 設置
        HeaderSettingResponseWrapper responseWrapper = new HeaderSettingResponseWrapper(response);

        try {
            String jwt = tokenProvider.resolveToken(request);
            if (StringUtils.hasText(jwt)) {
                logger_filter.debug("Request URI: {} - JWT found.", request.getRequestURI());
                
                // 記錄接收到的 JWT token（只顯示部分內容以保護安全）
                if (logger_filter.isDebugEnabled()) {
                    int tokenLength = jwt.length();
                    String tokenSnippet = tokenLength > 20 ? 
                        jwt.substring(0, 10) + "..." + jwt.substring(tokenLength - 10) : jwt;
                    logger_filter.debug("Received JWT token from frontend - URI: {}, Token length: {}, Token snippet: {}", 
                        request.getRequestURI(), tokenLength, tokenSnippet);
                }
                
                DecodedJWT decodedJWT = tokenProvider.validateAndDecodeToken(jwt);
                String employeeId = tokenProvider.getEmployeeIdFromJWT(decodedJWT);
                sessionUuidFromToken = tokenProvider.getSessionUuidFromJWT(decodedJWT);
                
                // 記錄解碼後的 JWT 資訊
                if (logger_filter.isDebugEnabled()) {
                    logger_filter.debug("Decoded JWT - Employee ID: {}, Session UUID: {}, Issued at: {}, Expires at: {}", 
                        employeeId, sessionUuidFromToken, 
                        decodedJWT.getIssuedAt(), 
                        decodedJWT.getExpiresAt());
                }
                
                if (employeeId != null && sessionUuidFromToken != null) {
                    UserDetails userDetails = userDetailsService.loadUserByUsername(employeeId);
                    Optional<UserSessionPermission> optSessionPerm = userSessionPermissionService.findValidSessionPermission(sessionUuidFromToken);

                    if (userDetails != null && userDetails.isEnabled() && userDetails.isAccountNonLocked() && optSessionPerm.isPresent()) {
                        UserSessionPermission sessionPermission = optSessionPerm.get();
                        List<String> functionCodes = parsePermissionsJson(sessionPermission.getPermissionsJson()); // Using helper method
                        
                        Collection<GrantedAuthority> authorities = new ArrayList<>(userDetails.getAuthorities() != null ? userDetails.getAuthorities() : Collections.emptyList());
                        if (functionCodes != null) {
                            functionCodes.forEach(code -> authorities.add(new SimpleGrantedAuthority(code)));
                        }
                        
                        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                                userDetails, null, authorities); 
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        logger_filter.debug("Authenticated user: {}, SessionUUID: {}, Authorities: {}. Setting security context.", 
                                            employeeId, sessionUuidFromToken, authorities);
                        initialAuthAttempt = authentication; 
                    } else {
                        if(userDetails == null || !userDetails.isEnabled() || !userDetails.isAccountNonLocked()){
                             logger_filter.warn("JWT valid but user {} details indicate not active/enabled/found.", employeeId);
                        }
                        if(optSessionPerm.isEmpty()){
                            logger_filter.warn("No valid session permission found for session UUID: {} for user {}. Token might be stale or session removed.", sessionUuidFromToken, employeeId);
                        }
                        SecurityContextHolder.clearContext();
                    }
                } else {
                    logger_filter.warn("JWT parsed but employeeId or sessionUuid is null. JWT: {}", jwt);
                    SecurityContextHolder.clearContext();
                }
            } else {
                logger_filter.trace("Request URI: {} - No JWT found.", request.getRequestURI());
            }
        } catch (JWTVerificationException ex) {
            logger_filter.warn("JWT Token validation error for URI {}: {}", request.getRequestURI(), ex.getMessage());
            SecurityContextHolder.clearContext();
        } catch (Exception ex) {
            logger_filter.error("Could not set user authentication in security context for URI {}:", request.getRequestURI(), ex);
            SecurityContextHolder.clearContext();
        }
        
        // 在 filter chain 執行前生成新 token 並設置到 wrapper
        if (initialAuthAttempt != null && sessionUuidFromToken != null && !isLoginOrPublicApi(request.getRequestURI())) {
            try {
                logger_filter.debug("Preparing to generate refresh token for user: {}", initialAuthAttempt.getName());
                
                // 記錄生成新 JWT token 的時機和原因
                logger_filter.info("Generating new JWT token - User: {}, Session UUID: {}, Request URI: {}, Reason: Token refresh on successful authentication", 
                    initialAuthAttempt.getName(), sessionUuidFromToken, request.getRequestURI());
                
                String newJwtToken = tokenProvider.generateToken(initialAuthAttempt, sessionUuidFromToken); 
                if (newJwtToken != null) {
                    // 立即設置到 wrapper
                    responseWrapper.setJwtToken(newJwtToken);
                    logger_filter.debug("New JWT token prepared and set to response wrapper for user: {}", initialAuthAttempt.getName());
                    
                    // 記錄新生成的 JWT token 詳細資訊
                    if (logger_filter.isDebugEnabled()) {
                        int tokenLength = newJwtToken.length();
                        String tokenSnippet = tokenLength > 20 ? newJwtToken.substring(0, 10) + "..." + newJwtToken.substring(tokenLength - 10) : newJwtToken;
                        logger_filter.debug("New JWT generated - User: {}, Token length: {}, Token snippet: {}", 
                            initialAuthAttempt.getName(), tokenLength, tokenSnippet);
                    }
                    
                    // 記錄成功生成新 token
                    logger_filter.info("Successfully generated new JWT token for user: {} with session UUID: {}", 
                        initialAuthAttempt.getName(), sessionUuidFromToken);
                } else {
                    logger_filter.warn("Token generation returned null for user: {}", initialAuthAttempt.getName());
                }
            } catch (Exception e) {
                logger_filter.error("Error generating refresh token for user {}: {}", initialAuthAttempt.getName(), e.getMessage(), e);
            }
        } else {
            // 記錄為什麼不生成新 token
            if (isLoginOrPublicApi(request.getRequestURI())) {
                logger_filter.trace("Skipping token refresh for login/public URI: {}", request.getRequestURI());
            } else if (initialAuthAttempt == null) {
                logger_filter.trace("Skipping token refresh, authentication failed for URI: {}", request.getRequestURI());
            } else if (sessionUuidFromToken == null) {
                logger_filter.trace("Skipping token refresh, no session UUID found for URI: {}", request.getRequestURI());
            }
        }
        
        logger_filter.debug("Before filterChain.doFilter. initialAuthAttempt is {}. sessionUuidFromToken is {}. Response committed: {}", 
                            (initialAuthAttempt != null ? "set" : "null"), 
                            (sessionUuidFromToken != null ? "set" : "null"),
                            responseWrapper.isCommitted());

        // 使用 wrapper 而不是原始 response
        filterChain.doFilter(request, responseWrapper);
        
        // 在 filter chain 執行後再次嘗試設置 header
        if (!responseWrapper.isCommitted()) {
            logger_filter.debug("Response not committed after filter chain, attempting to flush buffer");
            responseWrapper.flushBuffer();
        } else {
            logger_filter.debug("Response already committed after filter chain");
        }
        
        // 記錄 filter 處理完成
        logger_filter.trace("JWT filter processing completed for URI: {}, Response committed: {}", 
            request.getRequestURI(), responseWrapper.isCommitted());
    }

    private List<String> parsePermissionsJson(String permissionsJson) {
        if (!StringUtils.hasText(permissionsJson)) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(permissionsJson, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            logger_filter.error("Error parsing permissions JSON from session: {}", e.getMessage());
            return Collections.emptyList();
        }
    }
    
    private boolean isLoginOrPublicApi(String requestUri) {
        return requestUri.endsWith("/api/v1/auth/login") || 
               requestUri.contains("/api/v1/announcements/public"); // Add other public API patterns if any
    }
} 