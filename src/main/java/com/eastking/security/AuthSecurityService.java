package com.eastking.security;

import com.eastking.model.entity.UserAccount;
// Potentially import YourUserDetailsImpl if that's the principal
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service("authSecurityService")
public class AuthSecurityService {

    public boolean isSelf(Authentication authentication, UUID id) {
        if (authentication == null || !authentication.isAuthenticated() || id == null) {
            return false;
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetailsImpl) {
            return ((UserDetailsImpl) principal).getId().equals(id);
        } else if (principal instanceof UserAccount) { // Fallback if UserAccount is directly principal
            return ((UserAccount) principal).getUserAccountId().equals(id);
        }
        // Add other principal types if necessary
        return false;
    }
    
    // Add other custom security methods here if needed, e.g., checkStoreAccess, etc.
} 