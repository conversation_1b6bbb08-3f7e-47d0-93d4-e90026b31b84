package com.eastking.security;

import com.eastking.model.vo.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * REST API 認證入口點，處理未認證的請求
 * 當使用者嘗試存取受保護的資源但未提供有效的認證時，此類別將被觸發。
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
@Component
public class RestAuthenticationEntryPointImpl implements AuthenticationEntryPoint {

    private static final Logger logger_entrypoint = LoggerFactory.getLogger(RestAuthenticationEntryPointImpl.class);
    private final ObjectMapper objectMapper;

    public RestAuthenticationEntryPointImpl(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public void commence(HttpServletRequest request, 
                         HttpServletResponse response, 
                         AuthenticationException authException) throws IOException, ServletException {
        logger_entrypoint.error("未經授權的錯誤 for path {}: {}", request.getServletPath(), authException.getMessage());

        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        ApiResponse<Object> apiResponse = ApiResponse.error(
            HttpServletResponse.SC_UNAUTHORIZED, 
            "您需要登入才能執行此操作: " + authException.getMessage()
        );

        objectMapper.writeValue(response.getOutputStream(), apiResponse);
    }
} 