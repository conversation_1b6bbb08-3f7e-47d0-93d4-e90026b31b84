package com.eastking.security;

import com.eastking.model.entity.UserAccount;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * Spring Security 使用者詳細資訊實作
 *
 * <AUTHOR> Developer
 * @date 2025/05/15
 */
public class UserDetailsImpl implements UserDetails {
    private static final long serialVersionUID = 1L;

    private final UUID id; // This is userAccountId
    private final String employeeId;
    private final String userName;
    @JsonIgnore
    private final String password;
    private final boolean isActive;
    private final Short companyDivisionCode;
    private final Collection<? extends GrantedAuthority> authorities;

    public UserDetailsImpl(UUID id, String employeeId, String userName, String password, boolean isActive, Short companyDivisionCode, Collection<? extends GrantedAuthority> authorities) {
        this.id = id;
        this.employeeId = employeeId;
        this.userName = userName;
        this.password = password;
        this.isActive = isActive;
        this.companyDivisionCode = companyDivisionCode;
        this.authorities = authorities;
    }

    public static UserDetailsImpl build(UserAccount userAccount, List<GrantedAuthority> authorities) {
        return new UserDetailsImpl(
                userAccount.getUserAccountId(),
                userAccount.getEmployeeId(),
                userAccount.getUserName(),
                userAccount.getUserPassword(),
                userAccount.getIsActive() == 1,
                userAccount.getErpCompanyDivision(),
                authorities
        );
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    public UUID getId() {
        return id;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        // Spring Security's getUsername() will return employeeId in our case
        return employeeId;
    }
    
    public String getUserDisplayName() {
        return userName;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return isActive; // Consider is_active as account lock status
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return isActive;
    }

    public Short getCompanyDivisionCode() {
        return companyDivisionCode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserDetailsImpl user = (UserDetailsImpl) o;
        return Objects.equals(id, user.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
} 