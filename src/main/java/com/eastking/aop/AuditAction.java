package com.eastking.aop;

import com.eastking.enums.AuditActionTypeEnum;
import com.eastking.enums.AuditDataTypeEnum;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自訂註解，用於標記需要記錄稽核日誌的方法。
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuditAction {

    /**
     * 操作行為 (例如: CREATE, UPDATE, DELETE)。
     */
    AuditActionTypeEnum action();

    /**
     * 被操作的資料類型 (例如: USER_ACCOUNT, PRODUCT_SETTING)。
     */
    AuditDataTypeEnum dataType();
} 