package com.eastking.aop;

import com.eastking.model.vo.ApiResponse;
import com.eastking.service.AuditLogService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.UUID;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class AuditLogAspect {

    private final AuditLogService auditLogService;

    @AfterReturning(pointcut = "@annotation(auditAction)", returning = "result")
    public void logAuditAction(JoinPoint joinPoint, AuditAction auditAction, Object result) {
        try {
            String entityId = "N/A";
            String entityDescription = "N/A";

            // --- NEW LOGIC: Get description from @Operation annotation ---
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Operation operationAnnotation = method.getAnnotation(Operation.class);

            if (operationAnnotation != null && !operationAnnotation.summary().isEmpty()) {
                entityDescription = operationAnnotation.summary();
            } else {
                // Fallback if @Operation is missing or summary is empty
                entityDescription = auditAction.dataType().getDescription();
            }

            // --- EXISTING LOGIC to get entityId ---
            if (result instanceof ResponseEntity) {
                ResponseEntity<?> responseEntity = (ResponseEntity<?>) result;
                if (responseEntity.getBody() instanceof ApiResponse) {
                    ApiResponse<?> apiResponse = (ApiResponse<?>) responseEntity.getBody();
                    Object data = apiResponse.getData();
                    if (data != null) {
                        entityId = getFieldValue(data, "Id", auditAction.dataType().name());
                    }
                }
            }
            
            if (entityId == null || entityId.equals("N/A")) {
                Object[] args = joinPoint.getArgs();
                for (Object arg : args) {
                    if (arg instanceof UUID) {
                        entityId = arg.toString();
                        break;
                    }
                }
            }
             
            auditLogService.logAction(
                auditAction.action(),
                auditAction.dataType(),
                entityId,
                entityDescription,
                null // Details can be added later if needed
            );

        } catch (Exception e) {
            log.error("AOP Audit logging failed for method: " + joinPoint.getSignature().getName(), e);
        }
    }

    private String getFieldValue(Object obj, String... possibleSuffixes) {
        // First, try a direct getter for the simple suffix, e.g., getId(), getRoleId()
        for (String suffix : possibleSuffixes) {
             String simpleGetterName = "get" + suffix;
             try {
                 Method method = obj.getClass().getMethod(simpleGetterName);
                 Object value = method.invoke(obj);
                 if (value != null) {
                     return value.toString();
                 }
             } catch (NoSuchMethodException e) {
                 // Ignore and try next
             } catch (Exception e) {
                  log.error("Error getting field value via simple getter for audit log.", e);
             }
        }
        
        // If simple getters fail, try the combined name, e.g., getBonusItemId()
        String className = obj.getClass().getSimpleName().replace("Dto", "");
        for (String suffix : possibleSuffixes) {
            String combinedGetterName = "get" + className + suffix;
             try {
                 Method method = obj.getClass().getMethod(combinedGetterName);
                 Object value = method.invoke(obj);
                 if (value != null) {
                     return value.toString();
                 }
             } catch (NoSuchMethodException e) {
                // Ignore and try next
             } catch (Exception e) {
                  log.error("Error getting field value via combined getter for audit log.", e);
             }
        }
        return null;
    }
} 